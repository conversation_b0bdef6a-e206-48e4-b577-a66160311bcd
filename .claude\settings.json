{"permissions": {"allow": ["Read(**)", "<PERSON><PERSON>(claude -h)", "<PERSON><PERSON>(claude doctor)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git status:*)", "Bash(git diff:*)", "Bash(git log:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(git branch:*)", "Bash(git merge:*)", "Bash(git rebase:*)", "Bash(git stash:*)", "Bash(git reset:*)", "Bash(git remote:*)", "<PERSON><PERSON>(git clone:*)", "Bash(gh:*)", "Bash(pnpm typecheck:*)", "Bash(pnpm run typecheck:*)", "Bash(pnpm turbo typecheck:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(pnpm turbo:test:*)", "Bash(pnpm turbo:test check:fix typecheck test --parallel --force)", "Bash(pnpm test:*)", "Bash(pnpm run test:*)", "Bash(npm test:*)", "Bash(npm run test:*)", "Bash(yarn test:*)", "<PERSON><PERSON>(jest:*)", "Bash(ls:*)", "Bash(ls)", "Bash(cd:*)", "Bash(pwd)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(chmod:*)", "Bash(chown:*)", "Bash(pnpm env use:*)", "Bash(pnpm i:*)", "Bash(pnpm i)", "Bash(pnpm install:*)", "Bash(pnpm install)", "Bash(pnpm add:*)", "Bash(pnpm remove:*)", "Bash(pnpm update:*)", "Bash(pnpm copy-envs)", "Bash(pnpm turbo dev:*)", "Bash(pnpm turbo dev)", "Bash(pnpm turbo:*)", "Bash(pnpm turbo)", "Bash(pnpm --filter:*)", "Bash(pnpm dev:*)", "Bash(pnpm dev)", "Bash(pnpm run dev:*)", "Bash(pnpm run dev)", "Bash(npm run dev:*)", "Bash(npm run dev)", "Bash(yarn dev:*)", "Bash(yarn dev)", "Bash(bun dev:*)", "<PERSON><PERSON>(bun dev)", "Bash(pnpm build:*)", "Bash(pnpm build)", "Bash(pnpm run build:*)", "Bash(pnpm run build)", "Bash(npm run build:*)", "Bash(npm run build)", "Bash(yarn build:*)", "Bash(yarn build)", "Bash(pnpm start:*)", "Bash(pnpm start)", "Bash(pnpm run start:*)", "Bash(pnpm run start)", "Bash(npm run start:*)", "Bash(npm run start)", "Bash(yarn start:*)", "Bash(yarn start)", "Bash(pnpm check:*)", "Bash(pnpm check)", "Bash(pnpm check:fix:*)", "Bash(pnpm check:fix)", "Bash(pnpm lint:*)", "Bash(pnpm lint)", "Bash(pnpm lint:fix:*)", "Bash(pnpm lint:fix)", "Bash(pnpm format:*)", "Bash(pnpm format)", "Bash(pnpm format:fix:*)", "Bash(pnpm format:fix)", "Bash(pnpm remark:*)", "<PERSON><PERSON>(pnpm remark)", "Bash(pnpm remark:fix:*)", "<PERSON><PERSON>(pnpm remark:fix)", "Bash(pnpm syncpack:fix:*)", "Bash(pnpm syncpack:fix)", "Bash(pnpm fix:*)", "Bash(pnpm fix)", "Bash(biome:*)", "Bash(pnpm clean:*)", "Bash(pnpm clean)", "Bash(pnpm fresh:*)", "Bash(pnpm fresh)", "Bash(pnpm nuke:*)", "Bash(pnpm nuke)", "<PERSON><PERSON>(vercel:*)", "Bash(pnpm --filter crm exec vercel:*)", "Bash(pnpm turbo login:*)", "Bash(pnpm turbo login)", "Bash(pnpm turbo link:*)", "Bash(pnpm turbo link)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(brew install:*)", "Bash(brew:*)", "Bash(prisma:*)", "Bash(pnpm prisma:*)", "Bash(pnpm --filter db prisma:*)", "Bash(psql:*)", "Bash(pg_dump:*)", "Bash(pnpm --filter db supabase:*)", "Bash(supabase:*)", "<PERSON><PERSON>(task-master:*)", "Bash(node scripts/dev.js:*)", "Bash(pnpm parse-prd:*)", "Bash(pnpm list:*)", "Bash(pnpm list)", "Bash(pnpm generate:*)", "Bash(pnpm generate)", "Bash(inngest:*)", "<PERSON><PERSON>(novu:*)", "Bash(redis-cli:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx:*)", "<PERSON><PERSON>(tsx:*)", "Bash(ts-node:*)", "Bash(node:*)", "Bash(npm:*)", "Bash(yarn:*)", "Bash(pnpm email:dev:*)", "Bash(pnpm email:export:*)", "Bash(pnpm changeset:*)", "Bash(pnpm commitlint:*)", "Bash(pnpm lint:ws:*)", "Bash(pnpm sort-packages:*)", "Bash(pnpm prepare:*)", "Bash(pnpm postinstall:*)", "<PERSON><PERSON>(husky:*)", "<PERSON><PERSON>(sherif:*)", "Ba<PERSON>(shx:*)", "<PERSON><PERSON>(dotenv:*)", "<PERSON><PERSON>(source:*)", "Bash(export:*)", "Bash(unset:*)", "Bash(which:*)", "<PERSON><PERSON>(pv:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "Bash(wc:*)", "<PERSON><PERSON>(date:*)", "<PERSON><PERSON>(sleep:*)", "Bash(kill:*)", "Bash(ps:*)", "<PERSON><PERSON>(top:*)", "<PERSON><PERSON>(htop:*)", "Bash(df:*)", "<PERSON><PERSON>(du:*)", "<PERSON><PERSON>(free:*)", "Bash(uptime:*)", "<PERSON><PERSON>(whoami)", "<PERSON><PERSON>(hostname:*)", "Bash(uname:*)", "Bash(env:*)", "<PERSON><PERSON>(printenv:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(code:*)", "<PERSON><PERSON>(cursor:*)", "WebFetch", "Bash(cp:*)", "mcp__sequentialthinking__sequentialthinking"], "deny": []}}