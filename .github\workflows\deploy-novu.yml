name: Deploy Workflow State to Novu

on:
  push:
    branches:
      - develop
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Set NOVU_SECRET_KEY and NOVU_BRIDGE_URL based on branch
        id: set-env-vars
        run: |
          BRANCH="${{ github.ref_name }}"
          echo "Branch: $BRANCH"

          if [ "$BRANCH" == "main" ]; then
            echo "NOVU_SECRET_KEY=${{ secrets.NOVU_SECRET_KEY_MAIN }}" >> $GITHUB_ENV
            echo "NOVU_BRIDGE_URL=${{ secrets.ENVIRONMENT_URL_MAIN }}/api/novu" >> $GITHUB_ENV
          else
            echo "NOVU_SECRET_KEY=${{ secrets.NOVU_SECRET_KEY_PREVIEW }}" >> $GITHUB_ENV
            echo "NOVU_BRIDGE_URL=${{ secrets.ENVIRONMENT_URL_PREVIEW }}/api/novu" >> $GITHUB_ENV
          fi

      - name: Sync State to Novu
        uses: novuhq/actions-novu-sync@v2
        with:
          secret-key: ${{ env.NOVU_SECRET_KEY }}
          bridge-url: ${{ env.NOVU_BRIDGE_URL }}
          api-url: https://eu.api.novu.co
