# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"
# pnpm turbo build --log-order=grouped

# # Run in background and capture process IDs
# pnpm turbo test --log-order=grouped &
# PID_TEST=$!
# pnpm turbo test:cicd &
# PID_TEST_CICD=$!

# # Wait for all processes to complete and capture exit codes
# wait $PID_TEST
# EXIT_TEST=$?
# wait $PID_TEST_CICD
# EXIT_TEST_CICD=$?

# # Check exit codes and return non-zero status if any command failed
# [ $EXIT_TEST -eq 0 -a $EXIT_TEST_CICD -eq 0 ]
