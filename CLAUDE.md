# Watt Code Style Guide

## General

- Avoid using barrel index files and avoid re-exporting types. Instead update the files that used the type if it is moved during a refactor.
- Never use `any` or `as any` in code. Always carefully plan out the types and use the existing types from the library where applicable.
- Always write strongly typed, immutable, declarative idiomnatic code that is easy to understand and maintain.
- Always write components using composition and avoid overloading props
- Ensure the components can be styled when composing via className and do not require additional hacks to style them.
- When writing unit tests use the `createGenericBuilder<T>()` function to create test data and use test.each<T>() with strongly typed test cases.
- Avoid adding excessive additions to existing components and instead create new components that can be composed together.
- When using useEffect avoid adding multiple additional useEffects into a single component rationalise about the dependencies and the effect of the effect and if it makes more sense to create a hook to handle it.
- When writing forms use react-hook-form and our `useZodForm` hook to create the form and use Zod for the validation.
- When writing unit tests for the client use *.spec.ts or*.client.spec.ts. But for the server tests always use *.server.spec.ts.
- Never use things like `document.querySelector` always use the idiomatic React approach here a ref would be better.
- Always respect `Block statements are preferred in this position.` and make use of {} with if statements.
- When using try catch always use `catch (err) { const error = err as Error; }` so the type of error is typed
- Do not use `forEach` when using for loops use for of instead.
- Do not use template literals if interpolation and special-character handling are not needed.

## React 19 Guidelines

- **Never use `forwardRef`** - React 19 allows refs to be passed as regular props. Instead of using `forwardRef`, accept `ref` as a prop in your component's props interface.

  ```typescript
  // ❌ Don't do this
  const Component = forwardRef<HTMLDivElement, Props>((props, ref) => ...);

  // ✅ Do this instead
  function Component({ ref, ...props }: Props & { ref?: React.Ref<HTMLDivElement> }) { ... }
  ```

- See `/docs/react-19-forwardref-removal.md` for detailed migration guide and examples.

## Checking the quality

- Use `pnpm turbo:test test` to run the unit tests. Never run `pnpm test`
- Use `pnpm check:fix` to fix any linting errors.
- Use `pnpm typecheck` to check for any type errors.
- Use `pnpm turbo dev` to run the project in development mode.
