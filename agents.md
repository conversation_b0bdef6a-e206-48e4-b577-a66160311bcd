# Watt Utilities Project Overview

This document provides a comprehensive overview of the Watt Utilities project, its architecture, and key components to help AI assistants and developers understand the codebase.

## Project Overview

Watt Utilities is a monorepo containing multiple applications and shared packages for managing utility services. The primary applications are:

1. **CRM (Customer Relationship Management)** - A web application for internal business users managing customer relationships, utility contracts, and business operations.
2. **Quote** - A B2B self-service web application for customers to generate and manage utility quotes.

## Business Workflow

The system supports the following business workflow:

1. Sales agents (via CRM) or customers (via Quote app) can generate quotes for utility services
2. Quotes can be converted to written or verbal contracts and sent to customers
3. A signed contract becomes a "Deal" in the system
4. Deals are processed by the compliance officer team who approve or reject submissions
5. Deals submitted by customers through the Quote app are triaged by managers and assigned to sales agents before submission to compliance

## Repository Structure

The repository follows a monorepo structure using pnpm workspaces with the following organization:

```bash
watt/
├── apps/               # Main applications
│   ├── crm/            # CRM web application
│   └── quote/          # Quote web application
├── packages/           # Shared libraries and utilities
│   ├── api/            # API layer and endpoints
│   ├── common/         # Common utilities and components
│   ├── db/             # Database schema and utilities
│   ├── emails/         # Email templates and sending functionality
│   ├── events/         # Event handling (Inngest)
│   ├── external-apis/  # Integration with external APIs
│   ├── notifications/  # Notification system (Novu)
│   ├── pdfs/           # PDF generation and handling
│   └── redis/          # Redis caching functionality
└── tooling/            # Development and build tools
    ├── jest-ts/        # Jest configuration for TypeScript
    ├── jest-ts-react/  # Jest configuration for React + TypeScript
    ├── remark/         # Markdown processing tools
    ├── syncpack/       # Package version synchronization
    └── typescript/     # TypeScript configuration
```

## Technology Stack

### Core Technologies

- **Frontend**: React 19, Next.js 15, TailwindCSS, Radix UI components
- **Backend**: Next.js API routes, tRPC
- **Database**: PostgreSQL with Prisma ORM
- **State Management**: Zustand, Jotai, React Query
- **Authentication**: Supabase Auth
- **Build System**: Turborepo
- **Package Management**: pnpm with workspaces
- **Testing**: Jest
- **Linting/Formatting**: Biome

### Key Integrations

- **SendGrid**: For email delivery
- **Novu**: For in-app notifications
- **Inngest**: For event processing
- **Redis**: For caching
- **PostHog**: For analytics
- **Supabase**: For authentication and database

## Applications

### CRM Application

The CRM application (apps/crm) is the main customer relationship management system used by Watt Utilities staff. It provides functionality for:

- Managing customer information and contacts
- Tracking utility contracts and deals
- Handling communications (calls, emails)
- Managing quotes and pricing
- Document management
- Reporting and analytics

### Quote Application

The Quote application (apps/quote) is used for generating and managing utility quotes for customers. It provides:

- Quote generation for different utility types
- Customer information collection
- Quote comparison and selection
- Integration with the CRM system

## Shared Packages

### API (@watt/api)

Contains the tRPC API definitions and procedures that are shared between applications.

### Common (@watt/common)

Shared utilities, hooks, and components used across applications.

### Database (@watt/db)

Database schema definitions using Prisma, migrations, and database utilities.

### Emails (@watt/emails)

Email templates using React Email and email sending functionality.

### Events (@watt/events)

Event handling using Inngest for asynchronous processing.

### External APIs (@watt/external-apis)

Integration with external services and APIs.

### Notifications (@watt/notifications)

In-app notification system using Novu.

### PDFs (@watt/pdfs)

PDF generation and handling utilities.

### Redis (@watt/redis)

Redis caching functionality and utilities.

## Data Model

The application uses a complex data model with the following key entities:

- **Profile**: User profiles with roles and permissions
- **Company**: Customer companies
- **CompanyContact**: Contact persons at companies
- **CompanySite**: Physical locations of companies
- **SiteMeter**: Utility meters at company sites
- **Deal**: Business deals and contracts
- **Quote**: Utility quotes
- **Provider**: Utility providers
- **ActivityLog**: System activity tracking

## Development Workflow

The project uses Turborepo for managing the build pipeline with the following key commands:

- `pnpm turbo dev`: Start development environment
- `pnpm turbo build`: Build all packages and applications
- `pnpm turbo test`: Run tests
- `pnpm turbo typecheck`: Type-check all packages

**Before committing changes**: Always run `pnpm turbo check:fix` to apply Biome formatting and linting adjustments to your files.

## Environment Setup

The project uses multiple environment configurations:

- `.env.local`: Local development
- `.env.dev`: Development environment
- `.env.test`: Testing environment
- `.env.qat`: QA testing environment
- `.env.uat`: User acceptance testing environment
- `.env.prod`: Production environment

## Services

The application relies on several services that run in Docker:

1. **Redis**: For caching and rate limiting
2. **Inngest**: For event processing
3. **Novu**: For notification management
4. **Supabase**: For authentication and database

## Authentication and Authorization

The application uses Supabase for authentication with email-based auth flow. Authorization is handled through user roles defined in the Profile model.

## Key Features

- **Customer Management**: Track and manage customer information
- **Quote Generation**: Generate utility quotes for customers
- **Contract Management**: Manage utility contracts and deals
- **Communication**: Handle calls and emails with customers
- **Document Management**: Store and manage customer documents
- **Reporting**: Generate reports on business activities

## CI/CD and Deployment

The project uses GitHub Actions for CI/CD with workflows for:

- Running tests
- Type checking
- Linting
- Building and deploying to Vercel

The deployment process is managed through Vercel with different environments:

- Development: dev.crm.watt.co.uk
- Production: crm.watt.co.uk

## Testing Strategy

The project uses Jest for testing with the following types of tests:

- Unit tests for utility functions and components
- Integration tests for API endpoints
- End-to-end tests for critical user flows

## Common Issues and Troubleshooting

### Redis Connection Issues

Redis connection errors and Novu workflow discovery logs during 'next build' can occur despite these services only being needed during development. This is a known issue that needs to be addressed.

### Memory Issues in Tests

The @watt/crm package tests may fail in CI with JavaScript heap out of memory errors, suggesting memory optimization is needed for tests to run successfully in the CI environment.

### PostHog Configuration

PostHog in the CRM app should be configured to use Vercel rewrites as a reverse proxy with the correct api_host configuration to avoid browser blocking and 405 errors.

## Address Search and Data Model

The address search functionality uses both database and external Aperture API with Redis caching, exposing two API routes for searching by postcode and retrieving full address details by guid.

The system has a getAddressByGuid function that checks multiple sources (database, Aperture API, Redis cache) and can create an EntityAddress record if needed.

## Environment Variables and Novu Configuration

The monorepo has two NextJS apps (CRM and Quote) that share the @watt/notifications package but need different Novu environment variables while maintaining the same variable names.

## Quote App Authentication

The Quote app implements user account creation after collecting business details and contact information, using OTP verification instead of passwords.
