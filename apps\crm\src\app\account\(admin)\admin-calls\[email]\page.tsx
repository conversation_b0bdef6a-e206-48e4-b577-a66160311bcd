import { columns } from "@watt/crm/components/data-table/calls/columns";
import { DataTable } from "@watt/crm/components/data-table/calls/data-table";

export default async function UserCallsPage(props: {
  params: Promise<{ email: string }>;
}) {
  const params = await props.params;
  return (
    <div className="px-4">
      <DataTable
        columns={columns}
        isAdminCall={true}
        usersEmail={decodeURIComponent(params.email)}
      />
    </div>
  );
}
