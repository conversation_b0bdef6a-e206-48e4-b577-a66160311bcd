import { supabaseSessionUserQueryOptions } from "@watt/crm/utils/supabase/get-session-user";
import { notFound } from "next/navigation";
import { getQueryClient } from "../../../trpc/get-query-client";

import type { PropsWithChildren } from "react";

export default async function AdminLayout({ children }: PropsWithChildren) {
  const queryClient = getQueryClient();
  const user = await queryClient.fetchQuery(supabaseSessionUserQueryOptions);

  if (!user) {
    notFound();
  }

  return children;
}
