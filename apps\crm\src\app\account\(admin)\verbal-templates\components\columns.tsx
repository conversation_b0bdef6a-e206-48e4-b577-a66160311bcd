"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { GetAllVerbalContractTemplate } from "@watt/api/src/router/verbal-contract-templates";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { formatBytes } from "@watt/common/src/utils/format-bytes";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { IconMap } from "@watt/crm/icons/icon-map";
import { File } from "lucide-react";

import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { Badge } from "@watt/crm/components/ui/badge";

import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { DataTableRowActions } from "./data-table-row-actions";
import { PathCell } from "./path-cell";

export const columns: ColumnDef<GetAllVerbalContractTemplate>[] = [
  {
    accessorKey: "friendlyName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center space-x-2">
        <File className="size-7 text-muted-foreground" />
        <div className="flex flex-col">
          <span className="font-medium">{row.getValue("friendlyName")}</span>
          <div className="flex items-center space-x-2 text-muted-foreground text-xs">
            <Badge variant="secondary" className="h-5 px-1.5">
              {(() => {
                const parts = row.original.type.split("/");
                const fileType = parts[1];
                if (!fileType) {
                  return "UNKNOWN";
                }
                return fileType.toUpperCase();
              })()}
            </Badge>
            <span>{formatBytes(row.original.size)}</span>
          </div>
        </div>
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Name"
    }
  },
  {
    accessorKey: "path",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="File" />
    ),
    cell: ({ row }) => <PathCell path={row.getValue("path")} />,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "File"
    }
  },
  {
    accessorKey: "supplier",
    accessorFn: template => template.provider.udcoreId,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Supplier" />
    ),
    cell: ({ getValue, row }) => {
      const supplier = getValue() as string;
      const { logoFileName, displayName } = row.original.provider;

      return (
        <div className="flex flex-col items-center justify-center gap-1">
          <ProviderLogo
            logoFileName={logoFileName}
            displayName={displayName}
            className="h-auto w-[60px] object-scale-down"
          />
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Supplier"
    }
  },
  {
    accessorKey: "utilityTypes",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Utilities" />
    ),
    cell: ({ row }) => {
      const utilitiesManaged = row.getValue<string[]>("utilityTypes");
      return (
        <div className="flex w-[180px]">
          {Object.keys(IconMap).map(utility => {
            const isActive = utilitiesManaged.includes(utility);
            return (
              <div key={utility} className="mr-1">
                {isActive && IconMap[utility]}
              </div>
            );
          })}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Utilities"
    }
  },
  // {
  //   accessorKey: "utilitiesManaged",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Utilities Managed" />,
  //   cell: ({ row }) => {
  //     const utilitiesManaged = row.original.siteMeters.map(meter => meter.utilityType.toString());
  //     return (
  //       <div className="flex w-[180px]">
  //         {Object.keys(IconMap).map(utility => {
  //           const isActive = utilitiesManaged.includes(utility);
  //           return (
  //             <div key={utility} className="mr-1">
  //               {isActive
  //                 ? IconMap[utility]
  //                 : cloneElement(IconMap[utility], {
  //                     color: "grey"
  //                   })}
  //               {}
  //             </div>
  //           );
  //         })}
  //       </div>
  //     );
  //   }
  // },
  {
    accessorKey: "documentVersion",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Version" />
    ),
    cell: ({ row }) => <div>{row.getValue("documentVersion")}</div>,
    meta: {
      dropdownLabel: "Version"
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => (
      <div>
        {formatDate(row.getValue("createdAt"), dateFormats.DD_MM_YYYY_HH_MM)}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Created At"
    }
  },
  {
    accessorKey: "updatedAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated At" />
    ),
    cell: ({ row }) => (
      <div>
        {formatDate(row.getValue("updatedAt"), dateFormats.DD_MM_YYYY_HH_MM)}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Updated At"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <DataTableRowActions
        template={{
          id: row.original.id,
          filename: row.original.filename,
          type: row.original.type,
          size: row.original.size,
          friendlyName: row.original.friendlyName,
          productType: row.original.productType,
          version: row.original.documentVersion,
          supplier: row.original.provider.displayName,
          utilityTypes: row.original.utilityTypes,
          saleTypes: row.original.saleTypes
        }}
      />
    ),
    filterFn: textFilter
  }
];
