"use client";

import type { VerbalContractTemplate } from "@watt/api/src/types/verbal-contract-templates";
import { trpcClient } from "@watt/crm/utils/api";
import { Download, MoreHorizontal, Pen, Trash2, X } from "lucide-react";
import { useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@watt/crm/components/ui/alert-dialog";
import { Button, buttonVariants } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { toast } from "@watt/crm/components/ui/use-toast";
import { VerbalContractTemplateForm } from "./verbal-contract-template-form";

interface DataTableRowActionsProps {
  template: VerbalContractTemplate;
}

export function DataTableRowActions({ template }: DataTableRowActionsProps) {
  const [modalIsOpen, setModalOpen] = useState<boolean>(false);
  const [deleteDialogIsOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const updateTemplate =
    trpcClient.verbalContractTemplates.update.useMutation();
  const deleteTemplate =
    trpcClient.verbalContractTemplates.deleteFile.useMutation();

  const signedUrl = trpcClient.verbalContractTemplates.getSignedUrl.useQuery(
    {
      pdfFileName: template.filename
    },
    {
      enabled: false
    }
  );

  const handleUpdateTemplate = async (data: VerbalContractTemplate) => {
    if (!template.id) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Template ID is missing"
      });
      return;
    }

    try {
      await updateTemplate.mutateAsync({
        filename: data.filename,
        type: data.type,
        size: data.size,
        path: data.path ?? template.filename,
        friendlyName: data.friendlyName,
        productType: data.productType,
        id: template.id,
        version: data.version,
        supplier: data.supplier,
        utilityTypes: data.utilityTypes,
        saleTypes: data.saleTypes
      });
      setModalOpen(false);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update template, please try again"
      });
    }
  };

  const handleDeleteTemplate = async () => {
    if (!template.id) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Template ID is missing"
      });
      return;
    }

    try {
      await deleteTemplate.mutateAsync({ id: template.id });
      toast({
        title: "Success",
        description: "Template deleted successfully",
        variant: "success"
      });
      setDeleteDialogOpen(false);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete template, please try again"
      });
    }
  };

  const handleDownload = async () => {
    try {
      const signedUrlResponse = await signedUrl.refetch();

      if (!signedUrlResponse.data || !signedUrlResponse.data.signedUrl) {
        toast({
          title: "Error",
          description: "Failed to retrieve the download",
          variant: "destructive"
        });
        return;
      }

      const response = await fetch(signedUrlResponse.data.signedUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", template.filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (downloadError) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to download file, please try again"
      });
    }
  };

  return (
    <>
      <DropdownMenu open={dropdownIsOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only fixed">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem
            onClick={() => {
              setDropdownOpen(false);
              setModalOpen(true);
            }}
          >
            <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleDownload}>
            <Download className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Download
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              setDropdownOpen(false);
              setDeleteDialogOpen(true);
            }}
          >
            <Trash2 className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Drawer
        direction="right"
        onOpenChange={setModalOpen}
        open={modalIsOpen}
        dismissible={false}
      >
        <DrawerContentWithDirection
          variant="right"
          scroll={false}
          className="max-w-[700px]"
          onEscapeKeyDown={() => setModalOpen(false)}
        >
          <Button
            variant="dialog"
            className="top-6 right-6 h-auto p-0"
            onClick={() => setModalOpen(false)}
          >
            <X className="h-4 w-4" />
            <span className="sr-only fixed">Close</span>
          </Button>
          <div className="h-screen overflow-y-scroll p-8">
            <div className="flex flex-col space-y-4">
              <DrawerTitle className="text-xl">Edit Template</DrawerTitle>
              <DrawerDescription className="space-y-1 italic">
                <p>
                  Please use the following format when naming your template:
                </p>
                <p>
                  [SupplierCode][ScriptType][UtilityType][ProductType][Version]_[Date].pdf
                </p>
                <p>Example: EDF_ACQ_ELEC_FIXED_V01_20240101.pdf</p>
              </DrawerDescription>
              <VerbalContractTemplateForm
                submitText="Update Template"
                template={template}
                onSubmit={handleUpdateTemplate}
              />
            </div>
          </div>
        </DrawerContentWithDirection>
      </Drawer>

      <AlertDialog open={deleteDialogIsOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this template? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className={buttonVariants({ variant: "destructive" })}
              onClick={handleDeleteTemplate}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
