"use client";

import type { Table } from "@tanstack/react-table";
import type { VerbalContractTemplate } from "@watt/api/src/types/verbal-contract-templates";
import { log } from "@watt/common/src/utils/axiom-logger";
import { trpcClient } from "@watt/crm/utils/api";
import { getUniqueSupplierFilterOptions } from "@watt/crm/utils/get-unique-filter-options";
import { X } from "lucide-react";
import { useCallback, useState } from "react";
import { z } from "zod";

import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle,
  DrawerTrigger
} from "@watt/crm/components/ui/drawer";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import { VerbalContractTemplateForm } from "./verbal-contract-template-form";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
}

const queryParamsSchema = z.object({
  search: z.string().optional()
});

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const createVerbalContractTemplate =
    trpcClient.verbalContractTemplates.create.useMutation();

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  const handleCreateVerbalContractTemplate = useCallback(
    async (data: VerbalContractTemplate) => {
      if (!data.path) {
        toast({
          title: "Error",
          description: "File is required",
          variant: "destructive"
        });
        return;
      }
      try {
        await createVerbalContractTemplate.mutateAsync({
          filename: data.filename,
          type: data.type,
          size: data.size,
          path: data.path,
          friendlyName: data.friendlyName,
          productType: data.productType,
          version: data.version,
          supplier: data.supplier,
          utilityTypes: data.utilityTypes,
          saleTypes: data.saleTypes
        });
        setModalOpen(false);
      } catch (e) {
        const error = e as Error;
        log.error("Failed to create verbal contract template", error);

        toast({
          title: "Error",
          description:
            error.message ?? "Failed to create verbal contract template",
          variant: "destructive"
        });
      }
    },
    [createVerbalContractTemplate]
  );

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Search templates..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        <DataTableFacetedFilter
          column={table.getColumn("supplier")}
          title="Supplier"
          options={getUniqueSupplierFilterOptions("supplier", table, true)}
          onFilterChange={handleFilterChange}
        />
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center gap-2">
        <DataTableViewOptions table={table} />
        <Drawer
          direction="right"
          onOpenChange={setModalOpen}
          open={modalOpen}
          dismissible={false}
        >
          <DrawerTrigger asChild>
            <Button variant="secondary" size="sm">
              New Template
            </Button>
          </DrawerTrigger>
          <DrawerContentWithDirection
            variant="right"
            scroll={false}
            className="max-w-[700px]"
            onEscapeKeyDown={() => setModalOpen(false)}
          >
            <Button
              variant="dialog"
              className="top-6 right-6 h-auto p-0"
              onClick={() => setModalOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only fixed">Close</span>
            </Button>
            <div className="h-screen overflow-y-scroll p-8">
              <div className="flex flex-col space-y-4">
                <DrawerTitle className="text-xl">
                  Create Verbal Contract Template
                </DrawerTitle>
                <DrawerDescription className="space-y-1 italic">
                  <p>
                    Please use the following format when naming your template:
                  </p>
                  <p>
                    [SupplierCode][ScriptType][UtilityType][ProductType][Version]_[Date].pdf
                  </p>
                  <p>Example: EDF_ACQ_ELEC_FIXED_V01_20240101.pdf</p>
                </DrawerDescription>
                <VerbalContractTemplateForm
                  submitText="Confirm"
                  onSubmit={handleCreateVerbalContractTemplate}
                />
              </div>
            </div>
          </DrawerContentWithDirection>
        </Drawer>
      </div>
    </div>
  );
}
