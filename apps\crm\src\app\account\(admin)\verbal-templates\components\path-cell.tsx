import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";
import { trpcClient } from "@watt/crm/utils/api";

interface PathCellProps {
  path: string;
}

export const PathCell: React.FC<PathCellProps> = ({ path }) => {
  const signedUrl = trpcClient.verbalContractTemplates.getSignedUrl.useQuery(
    {
      pdfFileName: path
    },
    {
      enabled: false
    }
  );

  const handleClick = async () => {
    try {
      const response = await signedUrl.refetch();
      if (!response.data || !response.data.signedUrl) {
        throw new Error("Failed to get signed URL");
      }

      window.open(response.data.signedUrl, "_blank", "noopener,noreferrer");
    } catch (e) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to get signed URL"
      });
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="link"
            className="p-0 text-blue-600 hover:underline"
            onClick={handleClick}
          >
            View File
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Open file in new tab</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
