"use client";

import {
  type VerbalContractTemplate,
  VerbalContractTemplateSchema
} from "@watt/api/src/types/verbal-contract-templates";
import { FIVE_MB } from "@watt/common/src/constants/file-sizes";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import udProvidersList from "@watt/common/src/constants/ud-providers-list.json";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { Badge } from "@watt/crm/components/ui/badge";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from "@watt/crm/components/ui/command";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { SaleType, UtilityType } from "@watt/db/src/enums";
import { createClientComponentClient } from "@watt/db/src/supabase/client";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";

type VerbalContractTemplateFormProps = {
  submitText?: string;
  template?: VerbalContractTemplate;
  onSubmit: (data: VerbalContractTemplate) => Promise<void>;
};

function mimeTypeToFriendlyName(mimeType: string): string {
  const mimeTypeMap: { [key: string]: string } = {
    "application/pdf": "PDF",
    "image/jpeg": "JPEG",
    "image/gif": "GIF",
    "image/png": "PNG",
    "image/x-eps": "EPS"
  };
  return mimeTypeMap[mimeType] || mimeType;
}

export function VerbalContractTemplateForm({
  submitText = "Submit",
  template,
  onSubmit
}: VerbalContractTemplateFormProps) {
  const supabase = createClientComponentClient();
  const providerContainerRef = useRef<HTMLDivElement>(null);
  const [isProviderModalOpen, setIsProviderModalOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [fileInputKey] = useState(0);
  const [supplierSearch, setSupplierSearch] = useState("");
  const [isFormSubmitting, setIsFormSubmitting] = useState<boolean>(false);

  const verbalContractTemplatesUtils =
    trpcClient.useUtils().verbalContractTemplates;

  const createSignedUploadUrlMutation =
    trpcClient.files.createSignedUploadUrl.useMutation({
      onSuccess: async () => {
        await verbalContractTemplatesUtils.invalidate();
      }
    });

  const form = useZodForm({
    schema: VerbalContractTemplateSchema,
    defaultValues: template
      ? {
          filename: template.filename,
          type: template.type,
          size: template.size,
          friendlyName: template.friendlyName,
          supplier: template.supplier,
          utilityTypes: template.utilityTypes,
          saleTypes: template.saleTypes,
          version: template.version,
          productType: template.productType
        }
      : undefined
  });

  useEffect(() => {
    if (file) {
      form.setValue("filename", file.name);
      form.setValue("type", file.type);
      form.setValue("size", file.size);
    }
  }, [file, form]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) {
      return;
    }

    const acceptedTypes = event.target.accept.split(",");
    if (!acceptedTypes.includes(selectedFile.type)) {
      event.target.value = "";
      const friendlyTypes = acceptedTypes
        .map(mimeTypeToFriendlyName)
        .join(", ");
      form.setError("filename", {
        type: "fileType",
        message: `Only ${friendlyTypes} files are allowed`
      });
      return;
    }

    if (selectedFile.size > FIVE_MB) {
      event.target.value = "";
      form.setError("filename", {
        type: "fileSize",
        message: "File size must be less than 5MB"
      });
      return;
    }
    setFile(selectedFile);
    form.setValue("filename", selectedFile.name);
    form.setValue("type", selectedFile.type);
    form.setValue("size", selectedFile.size);
    form.clearErrors("filename");
  };

  const handleSubmit = async (
    verbalContractTemplate: VerbalContractTemplate
  ) => {
    try {
      // We handle form submit state manually to capture all process and disable the button
      setIsFormSubmitting(true);
      if (file) {
        const { data, error } = await createSignedUploadUrlMutation.mutateAsync(
          {
            bucketName: STORAGE_BUCKETS.VERBAL_CONTRACT_TEMPLATES,
            filename: file.name
          }
        );

        if (!data || error) {
          setIsFormSubmitting(false);
          toast({
            variant: "destructive",
            title: "Error",
            description: error.message ?? "Failed to create signed upload URL"
          });
          return;
        }

        const { token, path } = data;

        // Step 2: Upload file directly to Supabase
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.VERBAL_CONTRACT_TEMPLATES)
          .uploadToSignedUrl(file.name, token, file);

        if (!uploadData || uploadError) {
          setIsFormSubmitting(false);
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to upload file"
          });
          return;
        }

        // Step 3: Create database record
        await onSubmit({
          filename: file.name,
          type: file.type,
          size: file.size,
          path: path,
          friendlyName: verbalContractTemplate.friendlyName,
          productType: verbalContractTemplate.productType,
          supplier: verbalContractTemplate.supplier,
          utilityTypes: verbalContractTemplate.utilityTypes,
          saleTypes: verbalContractTemplate.saleTypes,
          version: verbalContractTemplate.version
        });

        setIsFormSubmitting(false);
        toast({
          title: "Success",
          description: template
            ? "Template updated successfully"
            : "Template created successfully",
          variant: "success"
        });
      } else if (template) {
        // Update existing template without changing the file
        await onSubmit(verbalContractTemplate);
        setIsFormSubmitting(false);
        toast({
          title: "Success",
          description: "Template updated successfully",
          variant: "success"
        });
      }
    } catch (error) {
      log.error("Error uploading file:", { error });
      setIsFormSubmitting(false);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          "Failed to upload file and create template, please try again"
      });
    }
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleSubmit}
      className="my-4 space-y-6"
    >
      <FormField
        control={form.control}
        name="filename"
        render={() => (
          <FormItem>
            <FormLabel>Upload PDF *</FormLabel>
            <FormControl>
              <Input
                key={fileInputKey}
                type="file"
                accept="application/pdf"
                onChange={handleFileChange}
              />
            </FormControl>
            <FormDescription className="text-xs">
              {template
                ? `Current file: ${template.filename}`
                : "Upload a PDF file for the verbal contract template."}
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="friendlyName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Friendly Name *</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Enter the friendly name"
                className="placeholder:italic"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="productType"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Product Type</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Enter the product type"
                className="placeholder:italic"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="supplier"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Supplier *</FormLabel>
            <FormControl>
              <div ref={providerContainerRef} className="flex w-full flex-col">
                <LookUp
                  open={isProviderModalOpen}
                  onOpenChange={setIsProviderModalOpen}
                >
                  <LookUpTrigger fieldValue={field.value ?? ""}>
                    <span
                      className={cn(
                        "font-normal",
                        !field.value && "text-muted-foreground italic"
                      )}
                    >
                      {field.value || "Select the supplier"}
                    </span>
                  </LookUpTrigger>
                  <LookUpContent
                    placeholder="Search supplier..."
                    searchInput={supplierSearch}
                    onSearchInputChange={supplier => {
                      setSupplierSearch(supplier);
                      if (!supplier) {
                        form.resetField("supplier");
                      }
                    }}
                    shouldFilter={true}
                    container={providerContainerRef.current}
                  >
                    <LookUpGroup>
                      {udProvidersList.map(providerName => (
                        <LookUpItem
                          value={providerName}
                          key={providerName}
                          onSelect={() => {
                            form.setValue("supplier", providerName);
                            setIsProviderModalOpen(false);
                          }}
                        >
                          {providerName}
                        </LookUpItem>
                      ))}
                    </LookUpGroup>
                  </LookUpContent>
                </LookUp>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="utilityTypes"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Utility Types *</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "justify-between",
                      (!field.value || field.value.length === 0) &&
                        "text-muted-foreground"
                    )}
                  >
                    <span className="max-w-[44ch] overflow-hidden text-ellipsis whitespace-nowrap">
                      {field.value?.length > 0 ? (
                        field.value.map(val => (
                          <Badge key={val} className="mr-1">
                            {humanize(val)}
                          </Badge>
                        ))
                      ) : (
                        <span className="font-normal italic">
                          Select applicable utilities
                        </span>
                      )}
                    </span>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <CommandInput placeholder="Search utility type..." />
                  <CommandEmpty>No utility type found.</CommandEmpty>
                  <CommandGroup>
                    {Object.values(UtilityType).map(type => (
                      <CommandItem
                        key={type}
                        onSelect={() => {
                          const updatedValue = field.value?.includes(type)
                            ? field.value.filter(t => t !== type)
                            : [...(field.value || []), type];
                          form.setValue("utilityTypes", updatedValue);
                        }}
                      >
                        <div
                          className={cn(
                            "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                            field.value?.includes(type)
                              ? "bg-primary text-primary-foreground"
                              : "opacity-50 [&_svg]:invisible"
                          )}
                        >
                          <Check className={cn("h-4 w-4")} />
                        </div>
                        {humanize(type)}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="saleTypes"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Sale Type *</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "justify-between",
                      (!field.value || field.value.length === 0) &&
                        "text-muted-foreground"
                    )}
                  >
                    <span className="max-w-[44ch] overflow-hidden text-ellipsis whitespace-nowrap">
                      {field.value?.length > 0 ? (
                        field.value.map(val => (
                          <Badge key={val} className="mr-1">
                            {humanize(val)}
                          </Badge>
                        ))
                      ) : (
                        <span className="font-normal italic">
                          Select applicable sale types
                        </span>
                      )}
                    </span>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <CommandInput placeholder="Search sale type..." />
                  <CommandEmpty>No sale type found.</CommandEmpty>
                  <CommandGroup>
                    {Object.values(SaleType).map(type => (
                      <CommandItem
                        key={type}
                        onSelect={() => {
                          const updatedValue = field.value?.includes(type)
                            ? field.value.filter(t => t !== type)
                            : [...(field.value || []), type];
                          form.setValue("saleTypes", updatedValue);
                        }}
                      >
                        <div
                          className={cn(
                            "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                            field.value?.includes(type)
                              ? "bg-primary text-primary-foreground"
                              : "opacity-50 [&_svg]:invisible"
                          )}
                        >
                          <Check className={cn("h-4 w-4")} />
                        </div>
                        {humanize(type)}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="version"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Version *</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Enter the version number"
                className="placeholder:italic"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <Button
        type="submit"
        variant="secondary"
        disabled={isFormSubmitting}
        className="w-full"
      >
        {isFormSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {submitText}
      </Button>
    </FormWrapper>
  );
}
