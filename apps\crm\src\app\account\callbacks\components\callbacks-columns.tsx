"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import Link from "next/link";

import type { CompanyContact } from "@prisma/client";
import type { MyCallbacks } from "@watt/api/src/router";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Button } from "@watt/crm/components/ui/button";
import { routes } from "@watt/crm/config/routes";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import { CallbackDataTableRowActions } from "./callbacks-data-table-row-actions";

const STATUS_LABELS = {
  OVERDUE: "Overdue",
  NOT_DUE: "Not Due"
};

export const callbacksColumns: ColumnDef<
  ExtractElementType<MyCallbacks["items"]>
>[] = [
  {
    accessorKey: "companyName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ row }) => {
      const { id: companyId, name: companyName } =
        row.original.companySite.company;
      const companyUrl = `${routes.company.replace("[id]", companyId)}/activity`;
      return (
        <Link href={companyUrl} target="_blank">
          <Button variant="link" className="flex space-x-2">
            <span className="max-w-[500px] truncate">
              {humanize(companyName)}
            </span>
          </Button>
        </Link>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "siteRef",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site ID" />
    ),
    cell: ({ row }) => {
      const {
        siteRefId,
        company: { id: companyId, name: companyName }
      } = row.original.companySite;
      const siteUrl = `${routes.company.replace("[id]", companyId)}/sites/${siteRefId}`;
      return (
        <Link href={siteUrl} target="_blank">
          <Button variant="link" className="p-2">
            {composeSiteRef(siteRefId)}
          </Button>
        </Link>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site ID"
    }
  },
  {
    accessorKey: "companySite.entityAddress",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site Address" />
    ),
    cell: ({ row }) => {
      return (
        <div className="w-[250px]">
          {getAddressDisplayName(row.original.companySite.entityAddress)}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site Address"
    }
  },
  {
    accessorKey: "callbackTime",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Callback Date" />
    ),
    cell: ({ row }) => {
      const callbackTime = row.original.callbackTime;
      if (callbackTime) {
        const formattedDate = formatDate(callbackTime, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(callbackTime, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    meta: {
      dropdownLabel: "Callback Date"
    }
  },

  {
    accessorKey: "overdue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const callbackTime = row.original.callbackTime;
      const isOverdue = !!(callbackTime && callbackTime < new Date());

      return (
        <div>{isOverdue ? STATUS_LABELS.OVERDUE : STATUS_LABELS.NOT_DUE}</div>
      );
    },
    meta: {
      dropdownLabel: "Status"
    }
  },

  {
    accessorKey: "companyContact",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Key Contact" />
    ),
    cell: ({ row }) => {
      const contact = row.getValue<CompanyContact>("companyContact");

      return `${contact.forename} ${contact.surname}`;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Key Contact"
    }
  },
  {
    accessorKey: "subject",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Subject" />
    ),
    cell: ({ row }) => {
      return <div className="max-w-[320px]">{row.original.subject}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Subject"
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => {
      const createdAt = row.original.createdAt;
      if (createdAt) {
        const formattedDate = formatDate(createdAt, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(createdAt, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    meta: {
      dropdownLabel: "Created At"
    }
  },
  {
    accessorKey: "cancelledAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cancelled At" />
    ),
    cell: ({ row }) => {
      const cancelledAt = row.original.cancelledAt;
      if (cancelledAt) {
        const formattedDate = formatDate(cancelledAt, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(cancelledAt, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    meta: {
      dropdownLabel: "Cancelled At"
    }
  },
  {
    accessorKey: "completedAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Completed At" />
    ),
    cell: ({ row }) => {
      const completedAt = row.original.completedAt;
      if (completedAt) {
        const formattedDate = formatDate(completedAt, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(completedAt, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    meta: {
      dropdownLabel: "Completed At"
    }
  },
  {
    accessorKey: "companySite.company._count.sites",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Total Sites" />
    ),
    cell: ({ row }) => (
      <div className="flex w-[80px] justify-center">
        {row.original.companySite.company._count.sites || "0"}
      </div>
    ),
    meta: {
      dropdownLabel: "Total Sites"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <CallbackDataTableRowActions
        callback={row.original}
        contacts={row.original.companySite.contacts}
      />
    ),
    filterFn: textFilter
  }
];
