"use client";

import { isDateOverdue } from "@watt/common/src/utils/format-date";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import {
  CheckCircle,
  ClipboardPen,
  Eye,
  MoreHorizontal,
  Trash2
} from "lucide-react";
import { useState } from "react";

import { CallbackStatus } from "@prisma/client";
import type { MyCallbacks } from "@watt/api/src/router";
import type { Contact } from "@watt/api/src/types/people";
import {
  CALLBACK_ACTIONS,
  type CallbackActionType
} from "@watt/crm/components/callback/callback-form";
import { CallbackModal } from "@watt/crm/components/callback/callback-modal";
import { CallbackViewModal } from "@watt/crm/components/callback/callback-view-modal";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";

type Callback = ExtractElementType<MyCallbacks["items"]>;
type CallbackDataTableRowActionsProps = {
  callback: Callback;
  contacts: Pick<
    Contact,
    "id" | "forename" | "surname" | "emails" | "phoneNumbers" | "siteId"
  >[];
};

export function CallbackDataTableRowActions({
  callback,
  contacts
}: CallbackDataTableRowActionsProps) {
  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [action, setAction] = useState<CallbackActionType>();

  const callbackStatus = callback.status;
  const isCallbackCompleted = callbackStatus === CallbackStatus.COMPLETE;
  const isCallbackCancelled = callbackStatus === CallbackStatus.CANCELLED;
  const isCallbackOverdue = isDateOverdue(callback.callbackTime);

  const {
    id: siteId,
    company: { id: companyId }
  } = callback.companySite;

  const handleCloseDropdown = () => {
    setOpenEditModal(false);
  };

  const handleMenuItemClick = (e: Event, action: CallbackActionType) => {
    e.preventDefault();
    setAction(action);
    setOpenEditModal(true);
  };

  const handleViewCallback = (e: Event) => {
    e.preventDefault();
    setOpenViewModal(true);
  };

  return (
    <div>
      <DropdownMenu open={dropdownIsOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only fixed">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[180px]">
          <DropdownMenuItem onSelect={e => handleViewCallback(e)}>
            <Eye className="mr-2 size-3.5" />
            View Callback
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled={isCallbackCompleted || isCallbackCancelled}
            onSelect={e => handleMenuItemClick(e, CALLBACK_ACTIONS.EDIT)}
          >
            <ClipboardPen className="mr-2 size-3.5" />
            Edit Callback
          </DropdownMenuItem>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <DropdownMenuItem
                    disabled={
                      // we don't need to mark as completed if the callback is not overdue
                      isCallbackCompleted ||
                      isCallbackCancelled ||
                      !isCallbackOverdue
                    }
                    onSelect={e =>
                      handleMenuItemClick(e, CALLBACK_ACTIONS.COMPLETE)
                    }
                  >
                    <CheckCircle className="mr-2 size-3.5" />
                    Complete Callback
                  </DropdownMenuItem>
                </div>
              </TooltipTrigger>
              <TooltipContent
                hidden={
                  isCallbackCancelled ||
                  isCallbackCompleted ||
                  isCallbackOverdue
                }
              >
                Cannot complete future callback. Update scheduled time if
                needed.
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <DropdownMenuItem
            disabled={
              isCallbackOverdue || isCallbackCompleted || isCallbackCancelled
            }
            onSelect={e => handleMenuItemClick(e, CALLBACK_ACTIONS.DELETE)}
          >
            <Trash2 className="mr-2 size-3.5" />
            Delete Callback
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <CallbackModal
        open={openEditModal}
        onOpenChange={setOpenEditModal}
        action={action}
        siteId={siteId}
        companyId={companyId}
        callback={callback}
        contacts={contacts}
        onSubmitForm={handleCloseDropdown}
      />

      <CallbackViewModal
        open={openViewModal}
        onOpenChange={setOpenViewModal}
        callback={callback}
        contact={contacts.find(c => c.id === callback.companyContactId)}
      />
    </div>
  );
}
