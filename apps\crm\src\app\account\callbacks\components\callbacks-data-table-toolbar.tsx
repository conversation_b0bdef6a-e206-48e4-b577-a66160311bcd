"use client";

import type { Table } from "@tanstack/react-table";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { createDateObjectSchema } from "@watt/common/src/utils/zod-literal-union";
import { X } from "lucide-react";
import { z } from "zod";

import {
  CALLBACK_CUSTOM_STATUS_FILTER_OPTIONS,
  type CallbackCustomStatusFilterOptionKey
} from "@watt/api/src/types/callback";
import { DataTableDateRangeFilter } from "@watt/crm/components/data-table/data-table-date-range-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
  children?: React.ReactNode;
}

export function CallbacksDataTableToolbar<TData>({
  table,
  isFiltered,
  children
}: DataTableToolbarProps<TData>) {
  const queryParamsSchema = z.object({
    search: z.string().optional(),
    createdAt: createDateObjectSchema(),
    callbackTime: createDateObjectSchema()
  });

  const extendedQueryParamsSchema = queryParamsSchema.extend({
    filter: z
      .enum(
        Object.keys(CALLBACK_CUSTOM_STATUS_FILTER_OPTIONS) as [
          CallbackCustomStatusFilterOptionKey,
          ...CallbackCustomStatusFilterOptionKey[]
        ]
      )
      .optional()
  });

  const { queryParams, setQueryParams } =
    useQueryParams<z.infer<typeof extendedQueryParamsSchema>>();

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="ml-auto flex flex-col gap-2">
      <div className="inline-block">
        <div className="mb-2 inline-flex space-x-4 rounded-md bg-muted-foreground/10 px-2 py-1.5">
          {Object.keys(CALLBACK_CUSTOM_STATUS_FILTER_OPTIONS).map(key => (
            <Button
              key={key}
              variant={queryParams.filter === key ? "outline" : "ghost"}
              onClick={() =>
                setQueryParams({
                  filter: key as CallbackCustomStatusFilterOptionKey
                })
              }
              className={cn(
                "h-6 whitespace-nowrap rounded-sm border-none text-muted-foreground/60 hover:bg-transparent hover:text-muted-foreground/60",
                queryParams.filter === key &&
                  "text-selected hover:bg-background hover:text-selected"
              )}
            >
              {
                CALLBACK_CUSTOM_STATUS_FILTER_OPTIONS[
                  key as CallbackCustomStatusFilterOptionKey
                ]
              }
            </Button>
          ))}
        </div>
      </div>
      <div className="flex items-center justify-between gap-2">
        <div className="flex items-center space-x-2">
          <SearchInput
            placeholder="Type to search..."
            value={searchValue}
            onChange={handleSearchChange}
            className="h-9 w-[250px]"
          />
          {table.getColumn("callbackTime") && (
            <DataTableDateRangeFilter
              // biome-ignore lint/suspicious/noExplicitAny: <fix later>
              column={table.getColumn("callbackTime") as any}
              title="Callback Date"
              allowFutureDates
              onFilterChange={handleFilterChange}
            />
          )}
          {table.getColumn("createdAt") && (
            <DataTableDateRangeFilter
              // biome-ignore lint/suspicious/noExplicitAny: <fix later>
              column={table.getColumn("createdAt") as any}
              title="Created At"
              onFilterChange={handleFilterChange}
            />
          )}
          {isFiltered && (
            <Button variant="ghost" onClick={resetFilters} size="sm">
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="flex space-x-2">
          <DataTableViewOptions table={table} />
          {children}
        </div>
      </div>
    </div>
  );
}
