"use client";

import { PhoneCall } from "lucide-react";

import {
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { trpcClient } from "@watt/crm/utils/api";
import React, { useEffect, useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";

import type { CallbackCustomStatusFilterOptionKey } from "@watt/api/src/types/callback";
import type { CustomFiltersType } from "@watt/api/src/types/common";
import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { callbacksColumns } from "./callbacks-columns";
import { CallbacksDataTableToolbar } from "./callbacks-data-table-toolbar";

type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

export function CallbacksDataTable() {
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    id: false,
    overdue: true,
    completedAt: true,
    cancelledAt: true
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const [debouncedCustomFilters, setDebouncedCustomFilters] =
    useState<CustomFiltersType>([
      {
        id: "filter",
        value: "DueToday"
      }
    ]);
  const [globalFilter, setGlobalFilter] = useState("");
  const { queryParams, setQueryParams } = useQueryParams<{
    filter: CallbackCustomStatusFilterOptionKey;
  }>();

  useEffect(() => {
    if (!queryParams.filter) {
      setQueryParams({
        filter: "DueToday"
      });
    }
  }, [queryParams, setQueryParams]);

  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = trpcClient.callback.myCallbacks.useInfiniteQuery(
    {
      searchFilters: {
        columnFilters: debouncedColumnFilters,
        customFilters: debouncedCustomFilters,
        globalFilter
      }
    },
    {
      getNextPageParam: lastPage => lastPage.nextCursor,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      placeholderData: prev => prev,
      trpc: {
        abortOnUnmount: true
      }
    }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useDebounce(
    () => {
      if (queryParams.filter) {
        setDebouncedCustomFilters([
          {
            id: "filter",
            value: queryParams.filter
          }
        ]);
      }
    },
    100,
    [queryParams.filter]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = data?.pages?.[0]?.meta?.totalRowCount ?? 0;
  const totalFetched = allItems.length;

  const table = useReactTable({
    data: allItems,
    columns: callbacksColumns,
    state: {
      sorting,
      columnVisibility: {
        ...columnVisibility,
        overdue: Boolean(
          columnVisibility.overdue && queryParams.filter === "DueToday"
        ),
        completedAt: Boolean(
          columnVisibility.completedAt && queryParams.filter === "Completed"
        ),
        cancelledAt: Boolean(
          columnVisibility.cancelledAt && queryParams.filter === "Cancelled"
        )
      },
      columnFilters,
      rowSelection,
      globalFilter,
      columnPinning: { right: ["actions"] }
    },
    enableRowSelection: false,
    onGlobalFilterChange: setGlobalFilter,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    debugTable: false,
    manualFiltering: true
  });

  const emptyStatePanel = useMemo(() => {
    const statusFilter = debouncedCustomFilters?.find(
      ({ id }) => id === "filter"
    );
    const filter = statusFilter?.value;
    if (isFetching) {
      return <></>;
    }
    let description = "";
    switch (filter) {
      case "DueToday":
        description = "It looks like you don't have any callbacks due today.";
        break;
      case "Overdue":
        description =
          "It looks like you don't have any overdue callbacks. Well done!";
        break;
      case "Future":
        description = "It looks like you don't have any upcoming callbacks.";
        break;
      case "Completed":
        description = "It looks like you don't have any completed callbacks.";
        break;
      case "Cancelled":
        description = "It looks like you don't have any cancelled callbacks.";
        break;
      default:
        description =
          "It looks like you haven't created any callbacks yet. Get started by creating your first callback.";
    }
    return (
      <EmptyStatePanel
        title="No callbacks found"
        description={description}
        Icon={PhoneCall}
      />
    );
  }, [debouncedCustomFilters, isFetching]);

  if (isLoading) {
    return (
      <div className="space-y-4 py-4">
        <h1 className="font-bold text-xl tracking-tight">My Callbacks</h1>
        <DataTableSkeleton
          columnCount={table.getAllColumns().length}
          searchableColumnCount={1}
          filterableColumnCount={3}
          cellWidths={["12rem", "14rem"]}
          withPagination={false}
          shrinkZero
        />
      </div>
    );
  }

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
      emptyStatePanel={emptyStatePanel}
    >
      <h1 className="font-bold text-xl tracking-tight">My Callbacks</h1>
      <CallbacksDataTableToolbar table={table} isFiltered={isFiltered} />
    </InfiniteScrollDataTable>
  );
}
