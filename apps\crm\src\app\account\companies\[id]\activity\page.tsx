import { HydrateClient, tRPCServerApi } from "@watt/crm/trpc/server";

import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import type { Metadata } from "next";
import { Suspense } from "react";
import { LogDataTable } from "../components/log-table/log-data-table";

export const metadata: Metadata = {
  title: "Company Activity",
  description: "Company activity."
};

export default async function CompanyActivity(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  await tRPCServerApi.activity.getCompanyActivities.prefetch({
    companyId: params.id
  });

  return (
    <HydrateClient>
      <Suspense
        fallback={
          <div className="space-y-4 p-2 pt-8">
            <DataTableSkeleton
              columnCount={7}
              searchableColumnCount={1}
              filterableColumnCount={2}
              cellWidths={["12rem", "14rem"]}
              withPagination={true}
              shrinkZero
            />
          </div>
        }
      >
        <LogDataTable companyId={params.id} />
      </Suspense>
    </HydrateClient>
  );
}
