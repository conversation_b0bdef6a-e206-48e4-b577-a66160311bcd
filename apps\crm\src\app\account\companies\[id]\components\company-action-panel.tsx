"use client";

import { useMemo } from "react";

import "react-resizable/css/styles.css";

import { SiteStatus } from "@prisma/client";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { formatWebsiteUrl } from "@watt/common/src/utils/format-website-url";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { CompanyDetailsUpdateModal } from "@watt/crm/components/company/company-edit-details-modal";
import { Button } from "@watt/crm/components/ui/button";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { trpcClient } from "@watt/crm/utils/api";
import {
  getBusinessType,
  getRegistrationNumberLabel
} from "@watt/db/src/maps/business-type-map";

type CompanyActionPanelProps = {
  companyId: string;
};

function displayValue<T>(
  value: T | undefined | null | "",
  defaultValue = "-"
): string {
  return value ? String(value) : defaultValue;
}

export function CompanyActionPanel({ companyId }: CompanyActionPanelProps) {
  const { setQueryParams } = useQueryParams();
  const [companyData] = trpcClient.company.find.useSuspenseQuery({
    id: companyId
  });

  const activeSiteCount = useMemo(() => {
    return companyData?.sites.filter(site => site.status === SiteStatus.ACTIVE)
      .length;
  }, [companyData?.sites]);

  const inactiveSiteCount = useMemo(() => {
    return companyData?.sites.filter(site => site.status === SiteStatus.CEASED)
      .length;
  }, [companyData?.sites]);

  const activeMeterCount = useMemo(() => {
    return companyData?.sites.reduce((count, site) => {
      return count + (site.siteMeters ? site.siteMeters.length : 0);
    }, 0);
  }, [companyData?.sites]);

  const handleGenerateLOA = () => {
    setQueryParams({
      modal: "generate-loa",
      companyName: companyData?.name,
      companyAddress: getAddressDisplayName(companyData?.entityAddress)
    });
  };

  return (
    <div className="mt-4 flex flex-col gap-2">
      <div className="flex justify-end gap-2">
        <Button
          variant="secondary"
          size="sm"
          className="gap-2"
          onClick={handleGenerateLOA}
        >
          Generate LOA
        </Button>
      </div>
      <div className="flex flex-col gap-4">
        <div className="group space-y-2">
          <div className="flex items-center">
            <div className="flex px-4">
              <h4 className="mr-4 font-semibold text-lg">
                Company Information
              </h4>
            </div>
            <CompanyDetailsUpdateModal companyData={companyData} />
          </div>
          <div className="flex flex-col space-y-3 px-4">
            <div className="space-y-2">
              <p>
                <span className="text-muted-foreground">Company Name</span>
                <br />
                {displayValue(humanize(companyData?.name))}
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex gap-4">
                <p>
                  <span className="text-muted-foreground">Business Type</span>
                  <br />
                  {displayValue(
                    getBusinessType(companyData?.businessType).title
                  )}
                </p>
              </div>

              {getRegistrationNumberLabel(companyData?.businessType) && (
                <p>
                  <span className="text-muted-foreground">
                    {getRegistrationNumberLabel(companyData?.businessType)}
                  </span>
                  <br />
                  {displayValue(companyData?.registrationNumber)}
                </p>
              )}
              <div className="space-y-2">
                <p>
                  <span className="text-muted-foreground">
                    Registered Office Address
                  </span>
                  <br />
                  <span className="whitespace-pre-line">
                    {displayValue(
                      getAddressDisplayName(companyData?.entityAddress, {
                        multiLine: true
                      })
                    )}
                  </span>
                </p>
              </div>
              <p>
                <span className="text-muted-foreground">Phone</span>
                <br />
                {displayValue(
                  formatPhoneNumber(companyData?.companyDetails?.officeNumber)
                )}
              </p>
              <p>
                <span className="text-muted-foreground">Phone Secondary</span>
                <br />
                {displayValue(
                  formatPhoneNumber(companyData?.companyDetails?.officeNumber2)
                )}
              </p>

              <p>
                <span className="text-muted-foreground">Date Incorporated</span>
                <br />
                {displayValue(
                  companyData?.companyDetails?.dateIncorporated &&
                    formatDate(
                      companyData.companyDetails.dateIncorporated,
                      dateFormats.DD_MM_YYYY
                    )
                )}
              </p>
              <p>
                <span className="text-muted-foreground">Date Moved In</span>
                <br />
                {displayValue(
                  companyData?.companyDetails?.dateMovedIn &&
                    formatDate(
                      companyData.companyDetails.dateMovedIn,
                      dateFormats.DD_MM_YYYY
                    )
                )}
              </p>
              <p>
                <span className="text-muted-foreground">Company Website</span>
                <br />
                {displayValue(
                  formatWebsiteUrl(companyData?.companyDetails?.website)
                )}
              </p>
              <p>
                <span className="text-muted-foreground">
                  Nature of the Business
                </span>
                <br />
                {displayValue(companyData?.companyDetails?.natureOfBusiness)}
              </p>
              <p>
                <span className="text-muted-foreground">Active Site Count</span>
                <br />
                {displayValue(activeSiteCount)}
              </p>
              <p>
                <span className="text-muted-foreground">
                  Inactive Site Count
                </span>
                <br />
                {displayValue(inactiveSiteCount, "0")}
              </p>
              <p>
                <span className="text-muted-foreground">
                  Active Meter Count
                </span>
                <br />
                {displayValue(activeMeterCount)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const companyInfoFields = [
  "companyName",
  "businessType",
  "registeredOfficeAddress",
  "officeNumber",
  "officeNumber2",
  "dateIncorporated",
  "dateMovedIn",
  "companyWebsite",
  "natureOfBusiness",
  "activeSiteCount",
  "inactiveSiteCount",
  "activeMeterCount"
];

export function CompanyActionPanelSkeleton() {
  return (
    <div className="mt-4 flex flex-col gap-2">
      <div className="flex justify-end gap-2">
        <Button variant="secondary" size="sm" className="gap-2" disabled>
          Generate LOA
        </Button>
      </div>
      <div className="flex flex-col gap-4">
        <div className="group space-y-2">
          <div className="flex items-center">
            <div className="flex px-4">
              <h4 className="mr-4 font-semibold text-lg">
                Company Information
              </h4>
            </div>
          </div>
          <div className="flex flex-col space-y-3 px-4">
            {companyInfoFields.map(field => (
              <div key={field} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                {field === "registeredOfficeAddress" ? (
                  <div className="space-y-1">
                    <Skeleton className="h-5 w-48" />
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                ) : (
                  <Skeleton className="h-5 w-48" />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
