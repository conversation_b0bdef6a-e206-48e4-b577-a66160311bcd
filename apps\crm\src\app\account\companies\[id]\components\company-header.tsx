"use client";

import type { CompaniesWith_Add_Con_Sit } from "@watt/api/src/router/company";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@watt/crm/components/ui/breadcrumb";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { routes } from "@watt/crm/config/routes";
import { trpcClient } from "@watt/crm/utils/api";
import { capitalize } from "lodash";
import { ChevronRight } from "lucide-react";
import { usePathname } from "next/navigation";
import React from "react";
import { CompanyNavLink } from "./company-nav-link";

type CompanyHeaderProps = {
  companyId: string;
};

export function CompanyHeader({ companyId }: CompanyHeaderProps) {
  const pathname = usePathname();

  // Extract currentTab and siteId from the pathname
  const { currentTab, siteId } = parsePathname(pathname);

  const siteRefId = siteId ? composeSiteRef(Number(siteId), "Ref. ") : null;

  const [companies] = trpcClient.company.all.useSuspenseQuery();

  const [companyData] = trpcClient.company.find.useSuspenseQuery({
    id: companyId
  });

  const index = companyData
    ? companies.findIndex(company => company.id === companyData.id) + 1
    : 0;

  const currentIndex = index;
  const totalCount = companies.length;
  const prevCompany = companies[index - 2];
  const nextCompany = companies[index];

  const companyPath = `/account/companies/${companyData.id}`;
  const tabPath = `${companyPath}/${currentTab}`;

  return (
    <header className="w-full">
      <div className="flex flex-nowrap items-center justify-between gap-2 px-8 py-4">
        <CompanyHeaderBreadcrumb
          companyName={companyData.name}
          companyPath={companyPath}
          tabPath={tabPath}
          currentTab={currentTab}
          siteRefId={siteRefId}
          siteId={siteId}
        />
        <CompanyHeaderNavigation
          currentIndex={currentIndex}
          totalCount={totalCount}
          prevCompany={prevCompany}
          nextCompany={nextCompany}
          currentTab={currentTab}
        />
      </div>
    </header>
  );
}

function parsePathname(pathname: string): {
  currentTab: string;
  siteId?: string;
} {
  // Split the pathname into segments
  const segments = pathname.split("/").filter(Boolean);

  // Find the index of 'companies' and extract following segments
  const companiesIndex = segments.indexOf("companies");

  let currentTab = "activity";
  let siteId: string | undefined;

  if (companiesIndex !== -1) {
    // The current tab is the segment after the company ID
    currentTab = segments[companiesIndex + 2] || "activity";

    // Check if 'sites' is part of the path to extract siteId
    const sitesIndex = segments.indexOf("sites", companiesIndex);
    if (sitesIndex !== -1 && segments.length > sitesIndex + 1) {
      siteId = segments[sitesIndex + 1];
    }
  }

  return { currentTab, siteId };
}

function CompanyHeaderBreadcrumb({
  companyName,
  companyPath,
  tabPath,
  currentTab,
  siteRefId,
  siteId
}: {
  companyName: string;
  companyPath: string;
  tabPath: string;
  currentTab: string;
  siteRefId: string | null;
  siteId?: string;
}) {
  const breadcrumbItems = [
    { label: "Companies", path: routes.companies },
    { label: capitalize(companyName), path: companyPath },
    { label: capitalize(currentTab), path: tabPath }
  ];

  if (siteRefId && siteId) {
    breadcrumbItems.push({
      label: siteRefId,
      path: `${companyPath}/sites/${siteId}`
    });
  }

  return (
    <div className="flex min-w-0 flex-1 items-center overflow-hidden sm:flex-initial">
      <Breadcrumb className="flex min-w-0 flex-1 items-center overflow-hidden sm:flex-initial">
        <BreadcrumbList className="flex flex-wrap items-center text-muted-foreground text-sm">
          {breadcrumbItems.map((item, index) => {
            const isLast = index === breadcrumbItems.length - 1;
            return (
              // Wrap sibling elements in a Fragment
              <React.Fragment key={item.path}>
                <BreadcrumbItem
                  className={cn(
                    "flex items-center font-bold text-xl tracking-tight",
                    isLast
                      ? "text-muted-foreground"
                      : "text-muted-foreground/60"
                  )}
                >
                  {isLast ? (
                    <BreadcrumbPage>{item.label}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={item.path}>
                      {item.label}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {/* Move BreadcrumbSeparator outside of BreadcrumbItem */}
                {!isLast && (
                  <BreadcrumbSeparator>
                    <ChevronRight className="size-4 text-muted-foreground/60" />
                  </BreadcrumbSeparator>
                )}
              </React.Fragment>
            );
          })}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}

function CompanyHeaderNavigation({
  currentIndex,
  totalCount,
  prevCompany,
  nextCompany,
  currentTab
}: {
  currentIndex: number;
  totalCount: number;
  prevCompany: CompaniesWith_Add_Con_Sit[number] | undefined;
  nextCompany: CompaniesWith_Add_Con_Sit[number] | undefined;
  currentTab: string;
}) {
  return (
    <div className="flex flex-wrap-reverse items-center justify-end gap-2">
      <div className="whitespace-nowrap font-semibold text-muted-foreground">
        {currentIndex} of {totalCount} in All Companies
      </div>
      <div className="flex flex-row gap-2">
        <CompanyNavLink
          direction="prev"
          href={
            prevCompany
              ? `${routes.company.replace("[id]", "")}${prevCompany.id}/${currentTab}`
              : undefined
          }
        />
        <CompanyNavLink
          direction="next"
          href={
            nextCompany
              ? `${routes.company.replace("[id]", "")}${nextCompany.id}/${currentTab}`
              : undefined
          }
        />
        <CompanyNavLink direction="close" href={routes.companies} />
      </div>
    </div>
  );
}

function CompanyHeaderNavigationSkeleton() {
  return (
    <div className="flex flex-wrap-reverse items-center justify-end gap-2">
      <div className="whitespace-nowrap font-semibold text-muted-foreground">
        <Skeleton className="h-6 w-40" />
      </div>
      <div className="flex flex-row gap-2">
        <Skeleton className="h-8 w-8 rounded-full" />
        <Skeleton className="h-8 w-8 rounded-full" />
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
    </div>
  );
}

export function CompanyHeaderSkeleton() {
  return (
    <header className="w-full">
      <div className="flex flex-nowrap items-center justify-between gap-2 px-8 py-4">
        <div className="flex min-w-0 flex-1 items-center overflow-hidden sm:flex-initial">
          <Breadcrumb className="flex min-w-0 flex-1 items-center overflow-hidden sm:flex-initial">
            <BreadcrumbList className="flex flex-wrap items-center text-muted-foreground text-sm">
              <React.Fragment>
                <BreadcrumbItem className="flex items-center font-bold text-muted-foreground/60 text-xl tracking-tight">
                  <BreadcrumbLink href={routes.companies}>
                    Companies
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  <ChevronRight className="size-4 text-muted-foreground/60" />
                </BreadcrumbSeparator>
              </React.Fragment>
              <React.Fragment>
                <BreadcrumbItem className="flex items-center font-bold text-muted-foreground/60 text-xl tracking-tight">
                  <Skeleton className="h-6 w-32" />
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  <ChevronRight className="size-4 text-muted-foreground/60" />
                </BreadcrumbSeparator>
              </React.Fragment>
              <BreadcrumbItem className="flex items-center font-bold text-muted-foreground text-xl tracking-tight">
                <Skeleton className="h-6 w-32" />
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <CompanyHeaderNavigationSkeleton />
      </div>
    </header>
  );
}
