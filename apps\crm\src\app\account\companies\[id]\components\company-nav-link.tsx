import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { CircleChevronDown, CircleChevronUp, CircleX } from "lucide-react";
import Link from "next/link";

type CompanyNavLinkProps = {
  direction: "prev" | "next" | "close";
  href?: string;
};

export function CompanyNavLink({ direction, href }: CompanyNavLinkProps) {
  const icon = {
    prev: <CircleChevronUp className="size-6 text-muted-foreground" />,
    next: <CircleChevronDown className="size-6 text-muted-foreground" />,
    close: <CircleX className="size-6 text-muted-foreground" />
  }[direction];

  const srText = {
    prev: "Go to previous company",
    next: "Go to next company",
    close: "Close company page"
  }[direction];

  const isDisabled = !href;

  const commonProps = {
    className: cn(
      buttonVariants({ variant: isDisabled ? "none" : "ghost", size: "smr" }),
      "size-6 p-0",
      isDisabled && "cursor-not-allowed opacity-50"
    ),
    "aria-disabled": isDisabled
  };

  if (!isDisabled) {
    return (
      <Link href={href} {...commonProps}>
        <>
          <span className="sr-only fixed">{srText}</span>
          {icon}
        </>
      </Link>
    );
  }

  return (
    <div {...commonProps}>
      <span className="sr-only fixed">{srText}</span>
      {icon}
    </div>
  );
}
