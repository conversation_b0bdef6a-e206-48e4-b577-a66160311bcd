"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";
import { routes } from "@watt/crm/config/routes";
import { featureToggles } from "@watt/crm/feature-toggles";
import Link from "next/link";
import { useSelectedLayoutSegment } from "next/navigation";

type CompanyTabsNavigationProps = {
  companyId: string;
  children: React.ReactNode;
};

export function CompanyTabsNavigation({
  companyId,
  children
}: CompanyTabsNavigationProps) {
  const currentSegment = useSelectedLayoutSegment();

  const tabItems: {
    label: string;
    value: string;
    link: string;
    enabled?: boolean;
    className?: string;
  }[] = [
    {
      label: "Activity",
      value: "activity",
      link: routes.companyActivity.replace("[id]", companyId)
    },
    {
      label: "Notes",
      value: "notes",
      link: routes.companyNotes.replace("[id]", companyId),
      enabled: featureToggles.routes.notes,
      className: "overflow-y-hidden pr-0"
    },
    {
      label: "Files",
      value: "files",
      link: routes.companyFiles.replace("[id]", companyId),
      enabled: featureToggles.routes.companiesFiles
    },
    {
      label: "Sites",
      value: "sites",
      link: routes.companySites.replace("[id]", companyId)
    }
  ];

  const currentTab = currentSegment || "activity";

  const enabledTabs = tabItems.filter(
    tab => tab.enabled || tab.enabled === undefined
  );

  return (
    <Tabs
      defaultValue="activity"
      value={currentTab}
      className="flex h-full flex-col"
    >
      <TabsList variant="outline" className="px-6">
        {enabledTabs.map(tab => (
          <TabsTrigger
            variant="outline"
            key={tab.value}
            value={tab.value}
            asChild
          >
            <Link href={tab.link}>{tab.label}</Link>
          </TabsTrigger>
        ))}
      </TabsList>
      {enabledTabs.map(tab => (
        <TabsContent
          key={tab.value}
          value={tab.value}
          className={cn("h-full overflow-y-auto bg-muted px-4", tab.className)}
        >
          {children}
        </TabsContent>
      ))}
    </Tabs>
  );
}

export function CompanyTabsNavigationSkeleton() {
  return (
    <div className="flex h-auto flex-col">
      <div className="flex space-x-2 px-6">
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-20" />
      </div>
      <div className="h-full bg-muted p-4">
        <Skeleton className="h-[200px] w-full" />
      </div>
    </div>
  );
}
