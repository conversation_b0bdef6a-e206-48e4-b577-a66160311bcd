"use client";

import type { CompanyWith_Add_Con_Sit } from "@watt/api/src/router/company";
import { LOAModel } from "@watt/db/prisma/zod";
import { add } from "date-fns";
import { z } from "zod";

import { getPrimaryEmail } from "@watt/api/src/utils/get-primary-email";
import { getPrimaryPhoneNumber } from "@watt/api/src/utils/get-primary-phone-number";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

type CompanyData = NonNullable<CompanyWith_Add_Con_Sit>;

type ContactType = NonNullable<CompanyWith_Add_Con_Sit>["contacts"][0];

export type LOASubmittedCompanyData = z.infer<typeof LOAModel>;

export interface GenerateLOAFormProps {
  companyData: CompanyData;
  onSubmit: (data: LOASubmittedCompanyData) => void;
}

export function GenerateLOAForm({
  companyData,
  onSubmit
}: GenerateLOAFormProps) {
  const formSchema = LOAModel.merge(
    z.object({
      // Extend formSchema with additional fields to handle locally then we remove them before submitting
      contactName: z.string().nonempty()
    })
  );

  const form = useZodForm({
    schema: formSchema,
    defaultValues: {
      companyId: companyData.id,
      companyName: companyData.name,
      contactForename: "",
      contactSurname: "",
      startDate: new Date(),
      expiryDate: undefined,
      companyEntityAddress: getAddressDisplayName(companyData.entityAddress)
    }
  });

  // TODO: Review handle contact selection - currently using index to find and populate the contact details
  const handleContactSelection = (selectedIndex: string) => {
    const contact = companyData.contacts[Number.parseInt(selectedIndex)];
    if (!contact) {
      console.error("Contact not found at index:", selectedIndex);
      return;
    }
    const primaryEmail = getPrimaryEmail(contact.emails);
    form.setValue("contactName", getFullname(contact));
    form.setValue("contactForename", contact.forename);
    form.setValue("contactSurname", contact.surname);
    form.setValue("contactEmail", primaryEmail);
    form.setValue("contactPosition", contact.position ?? "N/A");
  };

  const handleLoaSelection = (value: string) => {
    const loaValidPeriod = Number.parseInt(value);
    form.setValue("loaValidPeriod", loaValidPeriod);
    const expiryDate = add(new Date(), {
      years: loaValidPeriod
    });
    form.setValue("expiryDate", expiryDate);
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={data => {
        onSubmit(
          (() => {
            return {
              ...data,
              contractName: undefined
            };
          })()
        );
      }}
      className="my-4 space-y-4"
    >
      <FormField
        control={form.control}
        name="companyName"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Business Name</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="contactName"
        render={() => (
          <FormItem className="flex flex-col">
            <FormLabel>Contact Name</FormLabel>
            <FormControl>
              <Select onValueChange={handleContactSelection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a contact" />
                </SelectTrigger>
                <SelectContent>
                  {companyData.contacts.map((contact, i) => (
                    <SelectItem
                      value={i.toString()}
                      key={getPrimaryPhoneNumber(contact.phoneNumbers)}
                    >
                      {getFullname(contact)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="contactEmail"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Contact Email</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="contactPosition"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Contact Role</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="companyEntityAddress"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Business Address</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="loaValidPeriod"
        render={() => (
          <FormItem className="flex flex-col">
            <FormLabel>LOA Valid Period (years)</FormLabel>
            <FormControl>
              <Select onValueChange={handleLoaSelection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select LOA period" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map(value => (
                    <SelectItem value={value.toString()} key={value}>
                      {yearsLabel(value)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="meterNumber"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Meter Number</FormLabel>
            <FormControl>
              <Input
                {...field}
                value={field.value ?? undefined}
                className="col-span-3"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <Button
        type="submit"
        variant="secondary"
        className="button-click-animation w-full"
      >
        Submit
      </Button>
    </FormWrapper>
  );
}

function getFullname(contact: ContactType): string {
  if (!contact || !contact.forename || !contact.surname) {
    return "";
  }

  return `${contact.forename} ${contact.surname}`;
}

function yearsLabel(years: number): string {
  return `${years} ${`Year${years > 1 ? "s" : ""}`}`;
}
