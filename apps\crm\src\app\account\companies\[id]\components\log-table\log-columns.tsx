"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { Company_Activities } from "@watt/api/src/router";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatPostcode } from "@watt/common/src/utils/format-postcode";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { SiteReferenceCell } from "../site-table/site-columns";

export const logColumns: ColumnDef<Company_Activities[0]>[] = [
  {
    accessorKey: "createdAt",
    accessorFn: row => formatDate(row.createdAt, dateFormats.DD_MM_YYYY_HH_MM),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Created At"
        disableSorting
      />
    ),
    cell: ({ row }) => <div>{row.getValue("createdAt")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Created At"
    }
  },
  {
    accessorKey: "activityType",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Type" disableSorting />
    ),
    cell: ({ row }) => <div>{humanize(row.getValue("activityType"))}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Type"
    }
  },
  {
    accessorKey: "siteReference",
    accessorFn: row => composeSiteRef(row.companySite?.siteRefId),
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site ID" disableSorting />
    ),
    cell: ({ row }) => {
      const siteRefId = row.original.companySite?.siteRefId;
      const siteReference = row.getValue<string>("siteReference");
      const companyId = row.original.company.id;
      if (!siteRefId || !siteReference) {
        return;
      }

      return (
        <SiteReferenceCell
          id={siteRefId}
          siteReference={siteReference}
          companyId={companyId}
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site ID"
    }
  },
  {
    accessorKey: "postcode",
    accessorFn: row =>
      row.companySite?.entityAddress.postcode ||
      row.company.entityAddress.postcode,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Postcode" disableSorting />
    ),
    cell: ({ row }) => (
      <div>{formatPostcode(row.getValue<string>("postcode"))}</div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Postcode"
    }
  },
  {
    accessorKey: "activityUser",
    accessorFn: row =>
      row.createdBy ? `${row.createdBy.forename} ${row.createdBy.surname}` : "",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="User" disableSorting />
    ),
    cell: ({ row }) => <div>{row.getValue("activityUser")}</div>,
    meta: {
      dropdownLabel: "User"
    }
  },
  {
    accessorKey: "activityUserRole",
    accessorFn: row => humanize(row.activityUser),
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Role" disableSorting />
    ),
    cell: ({ row }) => <div>{row.getValue("activityUserRole")}</div>,
    meta: {
      dropdownLabel: "Role"
    }
  },
  {
    accessorKey: "description",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Description"
        disableSorting
      />
    ),
    cell: ({ row }) => (
      <div className="w-[250px]">{row.getValue("description")}</div>
    ),
    meta: {
      dropdownLabel: "Description"
    }
  }
];
