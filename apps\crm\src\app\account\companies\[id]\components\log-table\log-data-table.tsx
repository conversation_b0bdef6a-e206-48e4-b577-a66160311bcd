"use client";
import {
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { ChartGantt, Table as TableIcon } from "lucide-react";
import { useState } from "react";

import { PaginatedDataTable } from "@watt/crm/components/data-table/paginated-data-table";
import { Button } from "@watt/crm/components/ui/button";
import { featureToggles } from "@watt/crm/feature-toggles";
import { trpcClient } from "@watt/crm/utils/api";
import { logColumns as columns } from "./log-columns";
import { DataTableToolbar } from "./log-data-table-toolbar";
import { Timeline } from "./timeline";

type LogDataTableProps = {
  companyId: string;
};

export function LogDataTable({ companyId }: LogDataTableProps) {
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [viewMode, setViewMode] = useState<"table" | "timeline">("table");

  const [data] = trpcClient.activity.getCompanyActivities.useSuspenseQuery({
    companyId
  });

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters
    },
    enableRowSelection: false,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });

  const renderDataTableToolbar = (_table: typeof table) => (
    <DataTableToolbar table={_table}>
      {featureToggles.features.activityLogTimelineView && (
        <Button
          variant="outline"
          size="sm"
          className="ml-auto hidden gap-1 lg:flex"
          onClick={() =>
            setViewMode(prev => (prev === "table" ? "timeline" : "table"))
          }
        >
          {viewMode === "table" ? (
            <TableIcon className="h-4 w-4" />
          ) : (
            <ChartGantt className="h-4 w-4" />
          )}
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {viewMode === "table" ? "Table" : "Timeline"}
          </span>
        </Button>
      )}
    </DataTableToolbar>
  );

  if (viewMode === "timeline") {
    return (
      <div className="space-y-4 p-2">
        {renderDataTableToolbar(table)}
        <Timeline data={data} />
      </div>
    );
  }

  return (
    <PaginatedDataTable table={table}>
      {renderDataTableToolbar(table)}
    </PaginatedDataTable>
  );
}
