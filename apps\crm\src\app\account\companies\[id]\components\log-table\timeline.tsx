import { formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { Badge } from "@watt/crm/components/ui/badge";
import { Card, CardContent } from "@watt/crm/components/ui/card";
import { ScrollArea } from "@watt/crm/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { format, formatDistanceToNow } from "date-fns";
import {
  Briefcase,
  Building,
  FileText,
  Mail,
  MapPin,
  Phone,
  User
} from "lucide-react";

export type ActivityItem = {
  description: string;
  company: {
    entityAddress: {
      postcode: string;
    };
  };
  companySite: {
    entityAddress: {
      postcode: string;
    };
    siteRefId: number;
  } | null;
  createdAt: Date;
  createdById: string | null;
  createdBy: {
    forename: string;
    surname: string;
  } | null;
  activityType: string;
  activityUser: string;
};

type TimelineProps = {
  data: ActivityItem[];
};

export function Timeline({ data }: TimelineProps) {
  console.log(data);
  const groupedData = data.reduce(
    (acc, item) => {
      const year = format(item.createdAt, "yyyy");
      const month = format(item.createdAt, "MMMM");

      if (!acc[year]) {
        acc[year] = {};
      }
      if (!acc[year][month]) {
        acc[year][month] = [];
      }
      acc[year][month].push(item);
      return acc;
    },
    {} as Record<string, Record<string, ActivityItem[]>>
  );

  return (
    <div className="flex h-full flex-col">
      <ScrollArea className="flex-grow">
        <div className="p-4">
          {Object.entries(groupedData)
            .sort(([a], [b]) => Number(b) - Number(a))
            .map(([year, monthData]) => (
              <div key={year} className="mb-8">
                <h2 className="mb-4 font-bold text-2xl">{year}</h2>
                {Object.entries(monthData)
                  .sort(([a], [b]) => {
                    const monthOrder = [
                      "December",
                      "November",
                      "October",
                      "September",
                      "August",
                      "July",
                      "June",
                      "May",
                      "April",
                      "March",
                      "February",
                      "January"
                    ];
                    return monthOrder.indexOf(a) - monthOrder.indexOf(b);
                  })
                  .map(([month, activities]) => (
                    <div key={`${year}-${month}`} className="mb-6">
                      <div className="mb-2 flex items-center">
                        <Badge variant="secondary" className="mr-2">
                          {month}
                        </Badge>
                        <hr className="flex-grow border-gray-300 border-t" />
                      </div>
                      <div className="space-y-8">
                        {activities.map((item, index) => (
                          <div
                            key={item.createdAt.toISOString()}
                            className="flex"
                          >
                            <div className="mr-4 flex flex-col items-center">
                              <div className="h-full w-px bg-border" />
                              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                                {getTypeIcon(item.activityType)}
                              </div>
                            </div>
                            <Card className="flex-grow">
                              <CardContent className="pt-6">
                                <div className="mb-2 flex flex-wrap items-start justify-between">
                                  <h3 className="mr-2 font-semibold text-lg">
                                    {item.description}
                                  </h3>
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Badge
                                          variant="secondary"
                                          className="mt-1 sm:mt-0"
                                        >
                                          {formatDistanceToNow(item.createdAt, {
                                            addSuffix: true
                                          })}
                                        </Badge>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{formatDate(item.createdAt)}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                                <div className="grid grid-cols-1 gap-2 text-sm sm:grid-cols-2">
                                  <div className="flex items-center">
                                    <User className="mr-2" size={16} />
                                    <span className="font-medium">User:</span>
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger>
                                          <span className="ml-1 cursor-help">
                                            {item.createdBy
                                              ? `${item.createdBy.forename} ${item.createdBy.surname}`
                                              : "Unknown"}
                                          </span>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>
                                            {humanize(item.activityUser) ||
                                              "No role"}
                                          </p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </div>
                                  <div className="flex items-center">
                                    <MapPin className="mr-2" size={16} />
                                    <span className="font-medium">
                                      Postcode:
                                    </span>
                                    <span className="ml-1">
                                      {item.company.entityAddress.postcode ||
                                        item.companySite?.entityAddress
                                          .postcode ||
                                        "-"}
                                    </span>
                                  </div>
                                  <div className="flex items-center">
                                    <Building className="mr-2" size={16} />
                                    <span className="font-medium">
                                      Site ID:
                                    </span>
                                    <span className="ml-1">
                                      {item.companySite?.siteRefId?.toString() ||
                                        "-"}
                                    </span>
                                  </div>
                                  <div className="flex items-center">
                                    <FileText className="mr-2" size={16} />
                                    <span className="font-medium">Type:</span>
                                    <span className="ml-1">
                                      {humanize(item.activityType)}
                                    </span>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>
            ))}
        </div>
      </ScrollArea>
    </div>
  );
}

function getTypeIcon(type: string) {
  switch (type.toLowerCase()) {
    case "system":
      return <Building size={24} />;
    case "call":
      return <Phone size={24} />;
    case "email":
      return <Mail size={24} />;
    case "site":
      return <MapPin size={24} />;
    case "profile":
      return <User size={24} />;
    case "deal":
      return <Briefcase size={24} />;
    default:
      return <FileText size={24} />;
  }
}
