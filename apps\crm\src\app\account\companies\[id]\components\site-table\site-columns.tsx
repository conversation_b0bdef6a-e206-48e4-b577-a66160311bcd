"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { Company_Sites } from "@watt/api/src/router";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import Link from "next/link";

import type { CompanyContact } from "@prisma/client";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Avatar, AvatarFallback } from "@watt/crm/components/ui/avatar";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { IconMap } from "@watt/crm/icons/icon-map";

export const siteColumns: ColumnDef<ExtractElementType<Company_Sites>>[] = [
  {
    accessorKey: "siteReference",
    accessorFn: row => composeSiteRef(row.siteRefId),
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site ID" disableSorting />
    ),
    cell: ({ row }) => {
      const siteRefId = row.original.siteRefId;
      const siteReference = row.getValue<string>("siteReference");
      const companyId = row.original.companyId;
      return (
        <SiteReferenceCell
          id={siteRefId}
          siteReference={siteReference}
          companyId={companyId}
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site ID"
    }
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" disableSorting />
    ),
    cell: ({ row }) => <div>{humanize(row.getValue("status"))}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status"
    }
  },
  {
    accessorKey: "contacts",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Key Contact"
        disableSorting
      />
    ),
    cell: ({ row }) => {
      const contacts = row.getValue<CompanyContact[]>("contacts");
      const primaryContact = contacts.find(
        contact => contact.isPrimarySiteContact
      );

      return (
        <div className="flex min-w-[180px]">
          {primaryContact ? (
            <div className="flex items-center gap-2">
              <Avatar className="size-7">
                <AvatarFallback className="bg-primary text-background">
                  {`${primaryContact.forename.slice(0, 1)}${primaryContact.surname.slice(0, 1)}`}
                </AvatarFallback>
              </Avatar>
              {`${primaryContact.forename} ${primaryContact.surname}`}
            </div>
          ) : (
            "No primary contact"
          )}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Key Contact"
    }
  },
  {
    accessorKey: "entityAddress",
    accessorFn: row => row.entityAddress.displayName || "N/A",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Address" disableSorting />
    ),
    cell: ({ row }) => (
      <div>{getAddressDisplayName(row.original.entityAddress)}</div>
    ),
    meta: {
      dropdownLabel: "Address"
    }
  },
  {
    accessorKey: "utilities",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Utility" disableSorting />
    ),
    cell: ({ row }) => {
      const utilities = row.original.siteMeters.map(meter =>
        meter.utilityType.toString()
      );
      return (
        <div className="flex min-w-[80px]">
          {Object.keys(IconMap).map(utility => {
            const isActive = utilities.includes(utility);
            return (
              <div key={utility} className="mr-1">
                {isActive ? IconMap[utility] : null}
                {}
              </div>
            );
          })}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Utility"
    }
  },
  {
    accessorKey: "createdAt",
    accessorFn: row => formatDate(row.createdAt, dateFormats.DD_MM_YYYY_HH_MM),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Created At"
        disableSorting
      />
    ),
    cell: ({ row }) => <div>{row.getValue("createdAt")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Created At"
    }
  }
];

export const SiteReferenceCell: React.FC<{
  id: number;
  siteReference: string;
  companyId: string;
}> = ({ id, siteReference, companyId }) => {
  const link = `/account/companies/${companyId}/sites/${id}`;
  return (
    <Link
      className={buttonVariants({ variant: "link", className: "!px-0" })}
      href={link}
    >
      {siteReference}
    </Link>
  );
};
