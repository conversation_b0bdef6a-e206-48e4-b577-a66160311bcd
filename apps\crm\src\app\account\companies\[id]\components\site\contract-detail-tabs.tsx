import type { SiteWith_Com_Add_Con } from "@watt/api/src/router/site";
import { ONE_DAY_MS } from "@watt/common/src/constants/time-durations";
import { calculateCommission } from "@watt/common/src/utils/calculate-commission";
import { formatCurrency } from "@watt/common/src/utils/format-currency";
import { formatStringToDateShort } from "@watt/common/src/utils/format-date";
import { formatkWhPerYear } from "@watt/common/src/utils/format-kwh-consumption";
import {
  formatPencePerDay,
  formatPencePerKwh
} from "@watt/common/src/utils/format-pounds-per-kwh";
import { humanize } from "@watt/common/src/utils/humanize-string";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";
import { ContractDetailsCard } from "./contract-details-card";

type ContractDetailTabsProps = {
  contract: NonNullable<SiteWith_Com_Add_Con>["contracts"][number];
};

export function ContractDetailTabs({ contract }: ContractDetailTabsProps) {
  const currentContract = contract;
  if (!currentContract) {
    return <p>No contract data available.</p>;
  }

  const contractDetailsData: {
    title: string;
    details: Record<string, string>;
  }[] = [
    {
      title: "Contract Duration",
      details: {
        "Start Date":
          formatStringToDateShort(currentContract.startDate.toDateString()) ||
          "N/A",
        "End Date":
          formatStringToDateShort(currentContract.endDate.toDateString()) ||
          "N/A",
        Status: currentContract.endDate
          ? `${Math.max(
              0,
              Math.ceil(
                (new Date(currentContract.endDate).getTime() - Date.now()) /
                  ONE_DAY_MS
              )
            )} Days Left`
          : "N/A"
      }
    },
    {
      title: "Contract Overview",
      details: {
        Type: humanize(currentContract.type) || "N/A",
        Product:
          currentContract.quote?.electricQuote?.contractType ||
          currentContract.quote?.gasQuote?.contractType ||
          "N/A",
        "Sold By":
          `${currentContract.deal?.createdBy?.forename || ""} ${currentContract.deal?.createdBy?.surname || ""}`.trim() ||
          "N/A",
        "Signed on":
          formatStringToDateShort(
            currentContract.deal?.createdAt?.toDateString() ?? ""
          ) || "N/A"
      }
    },
    {
      title: "Pricing Details",
      details: {
        "Standing Charge":
          formatPencePerDay(
            currentContract.quote?.electricQuote?.standingCharge ||
              currentContract.quote?.gasQuote?.standingCharge
          ) || "N/A",
        "Unit Rate":
          formatPencePerKwh(
            currentContract.quote?.electricQuote?.unitRate ||
              currentContract.quote?.gasQuote?.unitRate
          ) || "N/A",
        "Night Rate":
          formatPencePerKwh(
            currentContract.quote?.electricQuote?.nightUnitRate
          ) || "N/A",
        "Off-peak Rate":
          formatPencePerKwh(
            currentContract.quote?.electricQuote?.weekendUnitRate
          ) || "N/A"
      }
    },
    {
      title: "Usage and Commission",
      details: {
        Consumption:
          formatkWhPerYear(
            currentContract.quote?.quoteList?.electricityUsage?.totalUsage ||
              currentContract.quote?.quoteList?.gasUsage?.totalUsage
          ) || "N/A",
        Uplift:
          formatPencePerKwh(
            currentContract.quote?.electricQuote?.unitRateUplift ||
              currentContract.quote?.gasQuote?.unitRateUplift
          ) || "N/A",
        Term: `${currentContract.term / 12 || "N/A"} Year(s)`,
        Commission: calculateContractCommission(currentContract)
      }
    }
  ];

  return (
    <Tabs defaultValue="current" className="flex flex-col space-x-6">
      <TabsList className="justify-evenly">
        <TabsTrigger className="w-full" value="previous" disabled>
          Previous
        </TabsTrigger>
        <TabsTrigger className="w-full" value="current">
          Current
        </TabsTrigger>
        <TabsTrigger className="w-full" value="pending" disabled>
          Pending
        </TabsTrigger>
      </TabsList>
      <div>
        <TabsContent value="previous">
          <div className="mt-4 grid grid-cols-1 gap-4 xl:grid-cols-2">
            {contractDetailsData.map(contract => (
              <ContractDetailsCard
                key={`previous-${contract.title}`}
                title={contract.title}
                details={contract.details}
              />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="current">
          <div className="mt-4 grid grid-cols-1 gap-4 xl:grid-cols-2">
            {contractDetailsData.map(contract => (
              <ContractDetailsCard
                key={`current-${contract.title}`}
                title={contract.title}
                details={contract.details}
              />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="pending">
          <div className="mt-4 grid grid-cols-1 gap-4 xl:grid-cols-2">
            {contractDetailsData.map(contract => (
              <ContractDetailsCard
                key={`pending-${contract.title}`}
                title={contract.title}
                details={contract.details}
              />
            ))}
          </div>
        </TabsContent>
      </div>
    </Tabs>
  );
}

function calculateContractCommission(
  contract: NonNullable<SiteWith_Com_Add_Con>["contracts"][number]
) {
  const totalUsage = contract.quote?.quoteList?.electricityUsage?.totalUsage;
  const unitRateUplift =
    contract.quote?.electricQuote?.unitRateUplift ||
    contract.quote?.gasQuote?.unitRateUplift;
  const standingChargeUplift =
    contract.quote?.electricQuote?.standingChargeUplift ||
    contract.quote?.gasQuote?.standingChargeUplift;
  const duration = contract.term / 12;

  const commission = calculateCommission(
    totalUsage,
    unitRateUplift,
    standingChargeUplift,
    duration
  );

  return formatCurrency(commission);
}
