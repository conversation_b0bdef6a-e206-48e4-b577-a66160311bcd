import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";

type ContractDetailsCardProps = {
  title: string;
  details: Record<string, string>;
};

export function ContractDetailsCard({
  title,
  details
}: ContractDetailsCardProps) {
  return (
    <Card className="shadow-lg shadow-muted-foreground/5">
      <CardHeader>
        <CardTitle className="text-center">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mt-4 space-y-2">
          {Object.entries(details).map(([key, value]) => (
            <div key={key} className="flex">
              <p className="text-muted-foreground">{key}</p>
              <p className="ml-auto">{value}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
