import type { SiteWith_Com_Add_Con } from "@watt/api/src/router/site";
import { CardContent } from "@watt/crm/components/ui/card";
import { featureToggles } from "@watt/crm/feature-toggles";
import { ContractDetailTabs } from "./contract-detail-tabs";
import { QuoteDealContractInfoTabs } from "./quote-deal-contract-info-tabs";

type ContractSummaryProps = {
  siteMeterId: string;
  contract: NonNullable<SiteWith_Com_Add_Con>["contracts"][number];
};

export function ContractSummary({
  siteMeterId,
  contract
}: ContractSummaryProps) {
  return (
    <div className="mt-4">
      <CardContent>
        {featureToggles.features.contractDetailTabs && (
          <ContractDetailTabs contract={contract} />
        )}

        <QuoteDealContractInfoTabs siteMeterId={siteMeterId} />
      </CardContent>
    </div>
  );
}
