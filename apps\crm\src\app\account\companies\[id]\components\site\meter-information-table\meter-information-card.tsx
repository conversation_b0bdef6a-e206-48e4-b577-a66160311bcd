import { TRPCClientError } from "@trpc/client";
import type { SiteWith_Com_Add_Con } from "@watt/api/src/router/site";
import { getFullMeterIdentifier } from "@watt/common/src/utils/get-full-meter-identifier";
import { ConfirmationDialog } from "@watt/crm/components/confirmation-dialog";
import { MpxnInputField } from "@watt/crm/components/mpxn/mpxn-input-field";
import { AlertDialogTrigger } from "@watt/crm/components/ui/alert-dialog";
import { Button } from "@watt/crm/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useQuoteModal } from "@watt/crm/hooks/use-quote-modal";
import { trpcClient } from "@watt/crm/utils/api";
import { UtilityType } from "@watt/db/src/enums";
import {
  Ellipsis,
  Loader2,
  Pencil,
  Search,
  TextQuote,
  Trash,
  Zap,
  ZapOff
} from "lucide-react";
import { useMemo } from "react";
import { ContractSummary } from "../contract-summary";
import { UtilityTypeIcon } from "../profile";

export type SiteMeter = NonNullable<SiteWith_Com_Add_Con>["siteMeters"][number];

type MeterInformationCardProps = {
  siteMeter: SiteMeter;
  contract: NonNullable<SiteWith_Com_Add_Con>["contracts"][number];
  company: NonNullable<SiteWith_Com_Add_Con>["company"];
  siteAddress: NonNullable<SiteWith_Com_Add_Con>["entityAddress"];
};

export function MeterInformationCard({
  siteMeter,
  contract,
  company,
  siteAddress
}: MeterInformationCardProps) {
  const { setQueryParams } = useQueryParams();
  const { openQuoteModal } = useQuoteModal();
  const editSiteMeterMutation =
    trpcClient.siteMeter.editSiteMeter.useMutation();
  const removeSiteMeterMutation =
    trpcClient.siteMeter.removeSiteMeter.useMutation();

  const handleGetQuote = () => {
    if (siteMeter.isDeEnergised) {
      toast({
        variant: "destructive",
        title: "Meter is de-energised",
        description: "Please energise the meter before getting a quote"
      });
      return;
    }
    openQuoteModal("get-quotes", {
      businessSearch: company.name,
      selectedBusinessRef: company.registrationNumber,
      addressSearch: siteAddress.postcode,
      selectedAddressId: siteAddress.id,
      businessType: company.businessType,
      utilityType: siteMeter.utilityType,
      meterIdentifier:
        siteMeter.electricSiteMeter?.mpan.value ||
        siteMeter.gasSiteMeter?.mprnValue
    });
  };

  const { utilityType, electricSiteMeter, gasSiteMeter, isDeEnergised } =
    siteMeter;

  const fullMeterIdentifier = useMemo(
    () =>
      getFullMeterIdentifier(
        utilityType === UtilityType.ELECTRICITY
          ? electricSiteMeter?.mpan
          : gasSiteMeter?.mprnValue,
        electricSiteMeter?.mpan.value
      ),
    [electricSiteMeter, gasSiteMeter, utilityType]
  );

  function handleEditMeterDetails() {
    if (siteMeter.isDeEnergised) {
      toast({
        variant: "destructive",
        title: "Meter is de-energised",
        description: "Please energise the meter before editing"
      });
      return;
    }

    setQueryParams({ modal: "edit-meter-details", siteMeterId: siteMeter.id });
  }

  function handleCheckMeterDetails() {
    if (siteMeter.isDeEnergised) {
      toast({
        variant: "destructive",
        title: "Meter is de-energised",
        description: "Please energise the meter before checking meter details"
      });
      return;
    }

    setQueryParams({
      modal: "check-meter-details",
      siteMeterId: siteMeter.id
    });
  }

  const handleConfirmDeEnergise = async () => {
    await editSiteMeterMutation.mutateAsync({
      id: siteMeter.id,
      isDeEnergised: !isDeEnergised,
      // meter data is only for logging activity
      electricSiteMeter: {
        mpan: {
          value: electricSiteMeter?.mpan.value
        }
      },
      gasSiteMeter: {
        mprn: {
          value: gasSiteMeter?.mprnValue
        }
      }
    });

    toast({
      title: `${
        siteMeter.utilityType === UtilityType.ELECTRICITY ? "Electric" : "Gas"
      } meter updated`,
      variant: "success",
      description: `Meter has been ${
        isDeEnergised ? "energised" : "de-energised"
      }`
    });
  };

  const handleConfirmRemoveMeter = async () => {
    try {
      await removeSiteMeterMutation.mutateAsync({
        id: siteMeter.id
      });

      toast({
        title: `${
          siteMeter.utilityType === UtilityType.ELECTRICITY ? "Electric" : "Gas"
        } meter removed`,
        variant: "success",
        description: `Meter ${
          siteMeter.electricSiteMeter?.mpanValue ||
          siteMeter.gasSiteMeter?.mprnValue
        } has been removed`
      });
    } catch (e) {
      const error = e as Error;
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while removing the meter. Please try again."
          : error.message;
      toast({
        title: "Unable to remove meter",
        description,
        variant: "destructive"
      });
    }
  };

  return (
    <Card>
      <CardHeader className="rounded-t-lg py-4">
        <CardTitle className="flex justify-between">
          <div className="flex items-center gap-4 font-bold text-2xl text-foreground">
            <div className="flex flex-row items-center gap-4">
              Meter Information <UtilityTypeIcon utilityType={utilityType} />
            </div>
            <MpxnInputField
              meterIdentifier={fullMeterIdentifier}
              utilityType={utilityType}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <Ellipsis className="size-4" />
                <span className="sr-only fixed">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleGetQuote}>
                <TextQuote className="mr-2 size-4" />
                Get Quote
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEditMeterDetails}>
                <Pencil className="mr-2 size-4" />
                Edit Meter Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCheckMeterDetails}>
                <Search className="mr-2 size-4" />
                Check Meter Details
              </DropdownMenuItem>
              <ConfirmationDialog
                title={isDeEnergised ? "Energise Meter" : "De-energise Meter"}
                description={`Are you sure you want to ${
                  isDeEnergised ? "energise" : "de-energise"
                } this meter? Please note this will not impact the energisation status retrieved when checking the meter data.`}
                onConfirm={handleConfirmDeEnergise}
              >
                <DropdownMenuItem
                  disabled={editSiteMeterMutation.isPending}
                  asDialogTrigger
                >
                  <AlertDialogTrigger className="flex flex-1 flex-row items-center">
                    {editSiteMeterMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        Loading...
                      </>
                    ) : isDeEnergised ? (
                      <>
                        <Zap className="mr-2 size-4" />
                        Energise
                      </>
                    ) : (
                      <>
                        <ZapOff className="mr-2 size-4" />
                        De-energise
                      </>
                    )}
                  </AlertDialogTrigger>
                </DropdownMenuItem>
              </ConfirmationDialog>
              <DropdownMenuSeparator />
              <ConfirmationDialog
                title="Remove Meter"
                description="Are you sure you want to remove this meter?"
                onConfirm={handleConfirmRemoveMeter}
              >
                <DropdownMenuItem
                  disabled={removeSiteMeterMutation.isPending}
                  asDialogTrigger
                >
                  <AlertDialogTrigger className="flex flex-1 flex-row items-center">
                    {removeSiteMeterMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      <>
                        <Trash className="mr-2 size-4" />
                        Remove Meter
                      </>
                    )}
                  </AlertDialogTrigger>
                </DropdownMenuItem>
              </ConfirmationDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <ContractSummary siteMeterId={siteMeter.id} contract={contract} />
      </CardContent>
    </Card>
  );
}

export function MeterInformationCardSkeleton() {
  return (
    <Card>
      <CardHeader className="rounded-t-lg py-4">
        <CardTitle className="flex justify-between">
          {/* Skeleton for Card Title */}
          <div className="flex items-center gap-4 font-bold text-2xl text-foreground">
            {/* Skeleton for "Meter Information" text */}
            <Skeleton className="h-8 w-48" />
            {/* Skeleton for Utility Icons */}
            <div className="flex gap-2">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
            </div>
            {/* Skeleton for MpxnInputField */}
            <Skeleton className="h-10 w-56" />
          </div>
          {/* Skeleton for Dropdown Menu Button */}
          <Skeleton className="h-8 w-8" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        {/* Skeleton for ContractSummary */}
        <Skeleton className="h-40 w-full" />
      </CardContent>
    </Card>
  );
}
