import type { Table } from "@tanstack/react-table";
import { ChevronLeft, ChevronRight } from "lucide-react";

import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";

/**
 * Represents a row of data in the table, extending the generic TData type.
 *
 * @template TData The base type for the row data.
 *
 */

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  setOriginalPageSize?: (pageSize: number) => void;
}

export function DataTablePagination<TData>({
  table,
  setOriginalPageSize = undefined
}: DataTablePaginationProps<TData>) {
  return (
    <div className="flex items-center gap-2">
      <Select
        value={`${table.getState().pagination.pageSize}`}
        onValueChange={value => {
          const newSize = Number(value);
          table.setPageSize(newSize);
          setOriginalPageSize?.(newSize);
        }}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <SelectTrigger className="h-8 xl:w-40">
              <SelectValue>
                <span className="text-muted-foreground">Show meters: </span>
                {table.getState().pagination.pageSize}
              </SelectValue>
            </SelectTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Number of meters per page</p>
          </TooltipContent>
        </Tooltip>
        <SelectContent side="top">
          {[3, 5, 10, 15].map(pageSize => (
            <SelectItem key={pageSize} value={`${pageSize}`}>
              {pageSize}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="flex items-center space-x-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only fixed">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Go to previous page</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only fixed">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Go to next page</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}
