import type { Table } from "@tanstack/react-table";
import { CircleHelp, X } from "lucide-react";

import { DataTableSelectFilter } from "@watt/crm/components/data-table/data-table-select-filter";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { getUniqueFilterOptions } from "@watt/crm/utils/get-unique-filter-options";
import { DataTablePagination } from "./meter-information-data-table-pagination";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  children
}: DataTableToolbarProps<TData>) {
  const uniqueUtilities = getUniqueFilterOptions({
    columnId: "utilityType",
    table,
    humanizeValues: true
  });

  const resetFilters = () => {
    table.resetColumnFilters();
  };

  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  return (
    <div className="mt-5 ml-auto flex flex-col justify-between gap-2 lg:flex-row lg:items-center">
      <div className="flex items-center gap-4">
        <DataTableSelectFilter
          column={table.getColumn("utilityType")}
          title="Utility"
          options={uniqueUtilities}
        />
        <Tooltip>
          <TooltipTrigger>
            <CircleHelp className="h-5 w-5 opacity-50" />
          </TooltipTrigger>
          <TooltipContent>
            Select to filter meters by utility: Electricity, Gas, or Water
          </TooltipContent>
        </Tooltip>
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex space-x-2">
        <DataTablePagination table={table} />
        {children}
      </div>
    </div>
  );
}
