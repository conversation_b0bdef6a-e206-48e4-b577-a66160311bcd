"use client";

import {
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { SiteWith_Com_Add_Con } from "@watt/api/src/router/site";
import { EmptyStatePanel } from "@watt/crm/app/account/quotes/components/empty-state";
import { AddNewMeterModal } from "@watt/crm/components/quick-actions/meter/add-new-meter-modal";
import { Button } from "@watt/crm/components/ui/button";
import { Card, CardContent } from "@watt/crm/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { Separator } from "@watt/crm/components/ui/separator";
import { toast } from "@watt/crm/components/ui/use-toast";
import { GaugeIcon } from "lucide-react";
import { useState } from "react";
import { MeterInformationCard } from "./meter-information-card";
import { meterColumns as columns } from "./meter-information-columns";
import { DataTableToolbar } from "./meter-information-data-table-toolbar";

interface MeterInformationDataTableProps {
  siteData: SiteWith_Com_Add_Con;
}

export function MeterInformationDataTable({
  siteData
}: MeterInformationDataTableProps) {
  const [isMeterModalOpen, setIsMeterModalOpen] = useState(false);
  const table = useReactTable({
    data: siteData?.siteMeters ?? [],
    columns,
    initialState: {
      pagination: {
        pageSize: 3
      }
    },
    enableRowSelection: false,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getPaginationRowModel: getPaginationRowModel()
  });

  const handleSubmitAddMeter = () => {
    toast({
      title: "Meter added successfully",
      variant: "success",
      description: "The meter has been added to the site."
    });
  };

  if (!siteData) {
    return null;
  }

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="flex data-[state=open]:bg-muted"
            >
              Meter
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <p className="m-2 font-semibold text-sm">Meter Actions</p>
            <Separator />
            <DropdownMenuItem onSelect={() => setIsMeterModalOpen(true)}>
              <GaugeIcon className="mr-2 size-3.5" />
              Add New Meter
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </DataTableToolbar>
      <AddNewMeterModal
        isModalOpen={isMeterModalOpen}
        setIsModalOpen={setIsMeterModalOpen}
        addressId={siteData.entityAddress.id}
        companyReg={siteData.company.registrationNumber}
        onSubmit={handleSubmitAddMeter}
      />
      <div className="space-y-6">
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map(row => {
            const firstContract = siteData.contracts[0];
            if (!firstContract) {
              return null;
            }
            return (
              <div key={row.id}>
                <div className="px-0">
                  <MeterInformationCard
                    key={row.original.id}
                    siteMeter={row.original}
                    contract={firstContract}
                    company={siteData.company}
                    siteAddress={siteData.entityAddress}
                  />
                </div>
              </div>
            );
          })
        ) : (
          <div>
            <div className="px-0">
              <Card>
                <CardContent>
                  <EmptyStatePanel
                    title="No meters found"
                    description="There are no meters for this site. Add a new meter to get started."
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
