import { TRPCClientError } from "@trpc/client";
import { trpcClient } from "@watt/crm/utils/api";
import { revalidatePath } from "@watt/crm/utils/revalidate-path";
import { PaymentMethod } from "@watt/db/src/enums";
import { getPaymentMethod } from "@watt/db/src/maps/payment-method-map";
import { AlertTriangle, Info, Loader2, Save, Undo2 } from "lucide-react";
import {
  type ChangeEvent,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";
import { z } from "zod";

import { log } from "@watt/common/src/utils/axiom-logger";
import { BankNameInput } from "@watt/crm/components/input/bank-name-input";
import {
  SortCodeInput,
  formatSortCode
} from "@watt/crm/components/input/sort-code-input";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@watt/crm/components/ui/alert";
import { Button } from "@watt/crm/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectWrapper
} from "@watt/crm/components/ui/select";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { accountNumberSchema } from "@watt/crm/lib/validations/accountNumber";
import { sortCodeSchema } from "@watt/crm/lib/validations/sortcode";

const paymentMethodSchema = {
  paymentMethod: z.nativeEnum(PaymentMethod)
};

// pasha: if we remove the optionl properties the FormField name property will show a typescript error
const BaseFormSchema = z.object({
  ...paymentMethodSchema,
  accountNumber: accountNumberSchema.optional(),
  sortCode: sortCodeSchema.optional(),
  bankName: z.string().optional(),
  accountHolderName: z.string().optional()
});

const PaymentDetailsFormSchema = z.object({
  ...paymentMethodSchema,
  accountNumber: accountNumberSchema,
  sortCode: sortCodeSchema,
  bankName: z.string().min(1, "Please enter a valid bank name"),
  accountHolderName: z
    .string()
    .min(1, "Please enter a valid account holder name")
});

type OptionalBankDetailsFields = keyof Omit<
  z.infer<typeof PaymentDetailsFormSchema>,
  "paymentMethod"
>;

const optionalBankDetailsFields: {
  field: OptionalBankDetailsFields;
  label: string;
}[] = [
  { label: "Account Holder", field: "accountHolderName" },
  { label: "Bank Name", field: "bankName" },
  { label: "Account Number", field: "accountNumber" },
  { label: "Sort Code", field: "sortCode" }
];

const getSchema = (showBankDetails: boolean) => {
  return showBankDetails ? PaymentDetailsFormSchema : BaseFormSchema;
};

const bankDetailsConditionList = [
  PaymentMethod.MONTHLY_DIRECT_DEBIT as string,
  PaymentMethod.QUARTERLY_DIRECT_DEBIT as string,
  PaymentMethod.BACS as string
];

type PaymentDetailsCardProps = {
  siteId: string;
  companyId: string;
  isAuthorised: boolean;
  banking: {
    paymentMethod: PaymentMethod;
    accountHolderName: string | null;
    accountNumber: string | null;
    sortCode: string | null;
    bankName: string | null;
    id: string | null;
  };
};

export function PaymentDetailsCard({
  banking,
  siteId,
  companyId,
  isAuthorised
}: PaymentDetailsCardProps) {
  const addBankDetailsMutation =
    trpcClient.banking.createCompanyBankingDetail.useMutation();
  const [showBankDetails, setShowBankDetails] = useState(false);
  const [accountNumberPlaceholder, setAccountNumberPlaceholder] = useState("");
  const [accountSortCodePlaceholder, setSortCodePlaceholder] = useState("");
  const schema = useMemo(() => getSchema(showBankDetails), [showBankDetails]);

  const setPlaceholders = useCallback(() => {
    // set redacted sensitive info as placeholders for non-authorized users
    if (!isAuthorised) {
      if (banking?.accountNumber) {
        setAccountNumberPlaceholder(banking.accountNumber);
      }
      if (banking?.sortCode) {
        setSortCodePlaceholder(banking.sortCode);
      }
    }
  }, [isAuthorised, banking]);

  const defaultValues = useMemo(() => {
    const {
      accountHolderName,
      accountNumber,
      sortCode,
      bankName,
      paymentMethod
    } = banking || {};
    return {
      accountHolderName: accountHolderName || "",
      accountNumber: isAuthorised && accountNumber ? accountNumber : "",
      sortCode: isAuthorised && sortCode ? formatSortCode(sortCode) : "",
      bankName: bankName || "",
      paymentMethod: paymentMethod || ""
    };
  }, [banking, isAuthorised]);

  const paymentDetailsForm = useZodForm({
    schema,
    defaultValues
  });

  const {
    watch,
    unregister,
    register,
    reset,
    formState: { isDirty, isSubmitSuccessful }
  } = paymentDetailsForm;

  const resetPaymentDetailsForm = useCallback(() => {
    // register the bank details in case they were unregister
    const hasBankDetails = bankDetailsConditionList.includes(
      defaultValues.paymentMethod || ""
    );
    if (hasBankDetails) {
      for (const item of optionalBankDetailsFields) {
        register(item.field);
      }
    }
    // set the default values for the form, using Object.assign to fix the issue with missing values on second reset
    const resetDefaultValues = Object.assign({}, defaultValues);
    reset(resetDefaultValues);
    setPlaceholders();
    setShowBankDetails(hasBankDetails);
  }, [defaultValues, reset, register, setPlaceholders]);

  const handleFormSubmit = async () => {
    const {
      paymentMethod,
      sortCode,
      accountHolderName,
      accountNumber,
      bankName
    } = paymentDetailsForm.getValues();

    if (siteId === undefined) {
      toast({
        title: "Payment Details Not Saved",
        description: "Unable to save new payment details. Site ID is missing.",
        variant: "destructive",
        duration: 10000
      });
      return;
    }

    const payload = {
      id: banking?.id || undefined,
      siteId,
      companyId,
      paymentMethod,
      ...(showBankDetails && {
        accountHolderName,
        accountNumber,
        bankName,
        sortCode: sortCode?.replace(/-/g, "")
      })
    };

    try {
      await addBankDetailsMutation.mutateAsync(payload);
      toast({
        title: "Success",
        description: "Payment details saved",
        variant: "success"
      });
      await revalidatePath(`/account/companies/${companyId}/site/${siteId}`);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while adding the new payment details. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to save new payment details",
        description,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  const onAccountNumberChange = useCallback(
    (
      event: ChangeEvent<HTMLInputElement>,
      onInputChange: (value: string) => void
    ) => {
      const inputValue = event.target.value;
      // Limit the input value to a maximum of 8 characters
      const truncatedValue = inputValue.slice(0, 8);
      onInputChange(truncatedValue);
    },
    []
  );

  const paymentMethodOptions = useMemo(
    () =>
      Object.keys(PaymentMethod)
        .filter(key => key !== PaymentMethod.UNKNOWN)
        .map(key => ({
          value: key as PaymentMethod,
          label: getPaymentMethod(key as PaymentMethod).title
        })),
    []
  );

  useEffect(() => {
    setShowBankDetails(
      bankDetailsConditionList.includes(banking?.paymentMethod || "")
    );
    setPlaceholders();
  }, [banking?.paymentMethod, setPlaceholders]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      resetPaymentDetailsForm();
    }
  }, [isSubmitSuccessful, resetPaymentDetailsForm]);

  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      // on paymentMethod change
      if (name === "paymentMethod" && type === "change") {
        const hasBankDetails = bankDetailsConditionList.includes(
          value.paymentMethod || ""
        );
        setShowBankDetails(hasBankDetails);
        if (!hasBankDetails) {
          for (const item of optionalBankDetailsFields) {
            unregister(item.field, { keepDefaultValue: false });
          }
          setSortCodePlaceholder("");
          setAccountNumberPlaceholder("");
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, unregister]);

  return (
    <Card>
      <CardHeader className="rounded-t-lg py-4">
        <CardTitle className="flex justify-between">
          <div className="flex items-center gap-4 font-bold text-2xl text-foreground">
            Payment Details
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <FormWrapper
          form={paymentDetailsForm}
          handleSubmit={handleFormSubmit}
          className="p-4"
        >
          <div className="flex">
            <p className="mt-1 mr-4 w-[13ch] font-semibold text-xl">
              Payment Method
            </p>
            <FormField
              control={paymentDetailsForm.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem className=" flex w-56 flex-col items-center">
                  <FormControl>
                    <SelectWrapper>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={banking?.paymentMethod}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Payment Method Options" />
                        </SelectTrigger>

                        <SelectContent position="popper">
                          {paymentMethodOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </SelectWrapper>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {isDirty && (
              <div className="ml-auto">
                <Button
                  className="button-click-animation"
                  variant="ghost"
                  size="sm"
                  disabled={addBankDetailsMutation.isPending}
                  onClick={resetPaymentDetailsForm}
                >
                  <Undo2 />
                </Button>
                <Button
                  type="submit"
                  className="button-click-animation"
                  variant="ghost"
                  size="sm"
                  disabled={addBankDetailsMutation.isPending}
                >
                  {addBankDetailsMutation.isPending ? (
                    <Loader2 className="animate-spin" />
                  ) : (
                    <Save />
                  )}
                </Button>
              </div>
            )}
          </div>
          {showBankDetails && (
            <div className="mt-[40px] flex justify-between gap-4">
              <p className="mt-1 mr-4 min-w-[9ch] font-semibold text-xl">
                Bank details
              </p>

              <FormField
                control={paymentDetailsForm.control}
                name="bankName"
                render={({ field }) => {
                  return (
                    <FormItem className="flex w-40 flex-col overflow-hidden">
                      <FormControl>
                        <BankNameInput
                          {...field}
                          ref={field.ref}
                          placeholder="Bank name"
                        />
                      </FormControl>
                      <FormMessage className="self-end" />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={paymentDetailsForm.control}
                name="accountHolderName"
                render={({ field }) => {
                  return (
                    <FormItem className="flex w-44 flex-col">
                      <FormControl>
                        <Input {...field} placeholder="Account Holder Name" />
                      </FormControl>
                      <FormMessage className="self-end" />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={paymentDetailsForm.control}
                name="accountNumber"
                render={({ field }) => {
                  const { onChange } = field;
                  return (
                    <FormItem className="flex w-36 flex-col">
                      <FormControl>
                        <Input
                          {...field}
                          onChange={ev => onAccountNumberChange(ev, onChange)}
                          placeholder={
                            accountNumberPlaceholder || "Account Number"
                          }
                        />
                      </FormControl>
                      <FormMessage className="self-end" />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={paymentDetailsForm.control}
                name="sortCode"
                render={({ field }) => {
                  return (
                    <FormItem className="flex w-28 flex-col">
                      <FormControl>
                        <SortCodeInput
                          {...field}
                          ref={field.ref}
                          placeholder={
                            accountSortCodePlaceholder || "Sort Code"
                          }
                        />
                      </FormControl>
                      <FormMessage className="self-end" />
                    </FormItem>
                  );
                }}
              />
            </div>
          )}
        </FormWrapper>
        <div className="w-full pb-4">
          {isDirty && (
            <Alert variant="default" className="text-muted-foreground">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Saving changes to payment details is irreversible. Previous
                values cannot be restored after saving.
              </AlertDescription>
            </Alert>
          )}
          {!isAuthorised && showBankDetails && !isDirty && (
            <Alert variant="default" className="text-muted-foreground">
              <Info className="h-4 w-4" />
              <AlertTitle>Note</AlertTitle>
              <AlertDescription>
                Payment details are redacted for privacy reasons. Only
                authorised personnel can view full information.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function PaymentDetailsCardSkeleton() {
  return (
    <Card>
      <CardHeader className="rounded-t-lg py-4">
        <CardTitle>
          {/* Skeleton for Card Title */}
          <Skeleton className="h-8 w-48" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        {/* Skeleton for Payment Method Selection */}
        <div className="flex">
          <Skeleton className="mt-1 mr-4 h-6 w-[13ch]" />
          <Skeleton className="h-10 w-56" />
          <div className="ml-auto flex gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
        {/* Skeleton for Bank Details */}
        <div className="mt-[40px] flex justify-between gap-4">
          <Skeleton className="mt-1 mr-4 h-6 w-[9ch]" />
          <Skeleton className="h-10 w-40" />
          <Skeleton className="h-10 w-44" />
          <Skeleton className="h-10 w-36" />
          <Skeleton className="h-10 w-28" />
        </div>
        {/* Skeleton for Alert */}
        <div className="w-full pb-4">
          <Skeleton className="h-14 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}
