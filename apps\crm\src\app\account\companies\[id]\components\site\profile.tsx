"use client";

import type { UtilityType } from "@prisma/client";
import type { CallbackWithUserFlagOutput } from "@watt/api/src/types/callback";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { CallbackBanner } from "@watt/crm/components/callback/callback-banner";
import { CallbackDropdownMenu } from "@watt/crm/components/callback/callback-dropdown-menu";
import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { routes } from "@watt/crm/config/routes";
import { IconMap } from "@watt/crm/icons/icon-map";
import { trpcClient } from "@watt/crm/utils/api";
import {
  ActivitySquareIcon,
  BatteryChargingIcon,
  Building2Icon,
  CoinsIcon,
  DropletIcon,
  FlameIcon,
  LightbulbIcon,
  type LucideIcon,
  MapPinIcon,
  PhoneIcon,
  SunIcon,
  WifiIcon
} from "lucide-react";
import Link from "next/link";
import type { ComponentProps } from "react";
import { MeterInformationCardSkeleton } from "./meter-information-table/meter-information-card";
import { MeterInformationDataTable } from "./meter-information-table/meter-information-data-table";
import {
  PaymentDetailsCard,
  PaymentDetailsCardSkeleton
} from "./payment-details-card";
import {
  SiteInformationCard,
  SiteInformationCardSkeleton
} from "./site-information-card";
import {
  type SiteMarkingKey,
  type SiteMarkingPrompts,
  SiteMarkings
} from "./status";

interface Meters {
  utilityType: UtilityType;
}

interface SiteMarkingData {
  key: SiteMarkingKey;
  icon: LucideIcon;
  activeFill: string;
  prompts?: SiteMarkingPrompts;
}

const siteStatusData: SiteMarkingData[] = [
  // (pasha) hidden until we implement backendfunctionality as per TAS-1614
  // {
  //   key: "hasPreviousDeal",
  //   icon: Icons.deal,
  //   activeFill: "#93BD22"
  // },
  // {
  //   key: "hasCallbackSet",
  //   icon: Icons.phoneOutgoing,
  //   activeFill: "#93BD22"
  // }
];

export function UtilityIcons({ meters }: { meters: Meters[] | undefined }) {
  const uniqueUtilityTypes = Array.from(
    new Set(meters?.map(meter => meter.utilityType) ?? [])
  );

  return (
    <div className="flex">
      {uniqueUtilityTypes.map(utilityType => (
        <div key={utilityType} className="mr-1">
          {IconMap[utilityType]}
        </div>
      ))}
    </div>
  );
}

type UtilityTypeIconProps = {
  utilityType: UtilityType;
} & ComponentProps<"svg">;

export const UtilityTypeIcon: React.FC<UtilityTypeIconProps> = ({
  utilityType,
  ...props
}) => {
  const iconMap: Record<string, { Component: LucideIcon; color: string }> = {
    ELECTRICITY: {
      Component: LightbulbIcon,
      color: "#fec800"
    },
    GAS: {
      Component: FlameIcon,
      color: "#D13125"
    },
    WATER: {
      Component: DropletIcon,
      color: "#0586CC"
    },
    TELECOM: {
      Component: PhoneIcon,
      color: "#FD6E01"
    },
    INTERNET: {
      Component: WifiIcon,
      color: "#13BFB2"
    },
    SOLAR: {
      Component: SunIcon,
      color: "#ff914d"
    },
    BATTERY_STORAGE: {
      Component: BatteryChargingIcon,
      color: "hsl(76, 70%, 44%)"
    },
    ENERGY_AUDIT: {
      Component: ActivitySquareIcon,
      color: "#7336ee"
    },
    FINANCE: {
      Component: CoinsIcon,
      color: "#ff66c4"
    },
    BUSINESS_INSURANCE: {
      Component: Building2Icon,
      color: "#093c5b"
    }
  };

  const iconConfig = iconMap[utilityType];

  if (!iconConfig) {
    return null;
  }

  const { Component, color } = iconConfig;

  return <Component color={color} {...props} />;
};

export function SiteProfile({
  companyId,
  siteRefId
}: {
  companyId: string;
  siteRefId: string;
}) {
  const [siteData] = trpcClient.site.find.useSuspenseQuery({
    id: Number.parseInt(siteRefId)
  });

  const { data: latestCallback } =
    trpcClient.callback.companyLatestScheduledCallbackWithCount.useQuery({
      companyId
    });

  if (!siteData) {
    return <CompanySiteErrorPanel companyId={companyId} />;
  }

  // TODO (Stephen): We should not need to cast this type at all
  const siteCallback = siteData.callbacks[0] as CallbackWithUserFlagOutput;

  const hasCallbackOnAnotherSite =
    siteData.callbacks.length === 0 && !!latestCallback?.callback;

  const scheduledCallbackStatus = {
    hasCallbackOnAnotherSite,
    activeCount: latestCallback?.activeCount || 0,
    overdueCount: latestCallback?.overdueCount || 0
  };

  return (
    <div className="flex flex-col gap-y-6 pb-4">
      <CallbackBanner
        callback={siteCallback || latestCallback?.callback}
        scheduledCallbackStatus={scheduledCallbackStatus}
      />
      <div className="flex flex-col gap-y-3">
        <div className="flex w-full flex-col gap-y-4">
          <div className="flex gap-4">
            {/* Hiding badge until we wire up segments and pools */}
            {/* <Badge className="rounded-xl font-medium text-base">
              {"BG in Window < 50k"}
            </Badge> */}
            <div className="flex gap-x-2 text-muted-foreground">
              {siteStatusData.map(status => (
                <SiteMarkings
                  key={status.key}
                  siteId={siteData.id}
                  status={status.key}
                  isEnabled={siteData[status.key] || false}
                  Icon={status.icon}
                  activeFill={status.activeFill}
                  prompts={status.prompts}
                />
              ))}
            </div>
          </div>
          <div className="flex w-full items-center justify-between">
            <span className="mx-6 font-bold text-muted-foreground text-xl">
              {composeSiteRef(siteData?.siteRefId, "Ref. ")}
            </span>
            <CallbackDropdownMenu
              siteId={siteData.id}
              companyId={companyId}
              callback={siteCallback}
              latestCallback={latestCallback?.callback}
              contacts={siteData.contacts}
              hasOtherUsersCallbacks={siteData.hasOtherUsersCallbacks}
              hasCallbackOnAnotherSite={hasCallbackOnAnotherSite}
            />
          </div>
        </div>
        <SiteInformationCard siteData={siteData} companyId={companyId} />
      </div>
      <MeterInformationDataTable siteData={siteData} />
      {siteData.banking[0] && (
        <PaymentDetailsCard
          banking={siteData.banking[0]}
          siteId={siteData.id}
          companyId={companyId}
          isAuthorised={siteData.isAuthorised}
        />
      )}
    </div>
  );
}

export function SiteProfileSkeleton() {
  return (
    <div className="flex flex-col gap-y-6 pb-4">
      <div className="flex flex-col gap-y-3">
        <div className="mt-5 flex w-full flex-col gap-y-4">
          <div className="flex gap-4">
            {/* Skeleton for Badge */}
            <Skeleton className="h-6 w-48 rounded-xl" />
            {/* Skeleton for SiteMarkings */}
            <div className="flex gap-x-2">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
            </div>
          </div>
          <div className="flex w-full items-center justify-between">
            {/* Skeleton for Site Reference */}
            <Skeleton className="mx-6 h-6 w-32" />
            {/* Skeleton for Set Callback Button */}
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
        <SiteInformationCardSkeleton />
      </div>
      {/* Skeleton for MeterInformationCard */}
      <MeterInformationCardSkeleton />
      {/* Skeleton for PaymentDetailsCard */}
      <PaymentDetailsCardSkeleton />
    </div>
  );
}

function CompanySiteErrorPanel({ companyId }: { companyId: string }) {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <EmptyStatePanel
        title="Company site not found"
        description="Please check the URL for typos. The company site may have been deleted from our database."
        Icon={MapPinIcon}
      >
        <Link
          href={routes.companySites.replace("[id]", companyId)}
          className={cn(buttonVariants({ variant: "outline" }), "font-medium")}
        >
          View All Company Sites
        </Link>
      </EmptyStatePanel>
    </div>
  );
}
