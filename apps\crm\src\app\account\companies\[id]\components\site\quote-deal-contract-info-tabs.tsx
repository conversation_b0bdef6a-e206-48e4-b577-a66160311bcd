import { SiteContractsDataTable } from "@watt/crm/components/site/site-contracts/site-contracts-data-table";
import { SiteDealsDataTable } from "@watt/crm/components/site/site-deals/site-deals-data-table";
import { SiteQuotesDataTable } from "@watt/crm/components/site/site-quotes/site-quotes-data-table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";
import { featureToggles } from "@watt/crm/feature-toggles";

type QuoteDealContractInfoTabsProps = {
  siteMeterId: string;
};

export function QuoteDealContractInfoTabs({
  siteMeterId
}: QuoteDealContractInfoTabsProps) {
  return (
    <Tabs defaultValue="quote" className="mt-10 flex flex-col gap-6">
      <TabsList className="justify-evenly">
        <TabsTrigger className="w-full" value="quote">
          Quote
        </TabsTrigger>
        <TabsTrigger
          className="w-full"
          value="deal"
          disabled={!featureToggles.features.siteDeal}
        >
          Deal
        </TabsTrigger>
        <TabsTrigger
          className="w-full"
          value="contract"
          disabled={!featureToggles.features.siteContract}
        >
          Contract
        </TabsTrigger>
      </TabsList>
      <div>
        <TabsContent value="quote">
          <SiteQuotesDataTable siteMeterId={siteMeterId} />
        </TabsContent>
        <TabsContent value="deal">
          <SiteDealsDataTable siteMeterId={siteMeterId} />
        </TabsContent>
        <TabsContent value="contract">
          <SiteContractsDataTable siteMeterId={siteMeterId} />
        </TabsContent>
      </div>
    </Tabs>
  );
}
