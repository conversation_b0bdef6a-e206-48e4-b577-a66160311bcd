"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { Contact } from "@watt/api/src/types/people";
import { getPrimaryEmail } from "@watt/api/src/utils/get-primary-email";
import { getSliceOfPhoneNumbers } from "@watt/api/src/utils/get-primary-phone-number";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeCompanyContact } from "@watt/common/src/utils/company-contact";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Badge } from "@watt/crm/components/ui/badge";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import { getCurrentAddress } from "@watt/crm/utils/get-current-contact-address";
import { KeySquareIcon } from "lucide-react";
import { SiteContactsDataTableRowActions } from "./site-contacts-data-table-row-actions";

export const siteContactsColumns: ColumnDef<ExtractElementType<Contact[]>>[] = [
  {
    accessorKey: "isKey",
    header: "",
    cell: ({ row }) =>
      row.original.isPrimarySiteContact && <KeySquareIcon className="size-4" />,
    meta: {
      dropdownLabel: "Is Key"
    }
  },
  {
    accessorKey: "name",
    header: "Name",
    accessorFn: row =>
      composeCompanyContact(row.forename, row.surname, row.salutation),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Name"
    }
  },
  {
    accessorKey: "position",
    header: "Position",
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Position"
    }
  },
  {
    accessorKey: "email",
    header: "Email",
    accessorFn: row => getPrimaryEmail(row.emails).toLowerCase(),
    cell: ({ row }) => (
      <div
        className={cn(
          row.index !== 0 &&
            "blur-sm transition-all duration-300 hover:blur-none"
        )}
      >
        {row.getValue("email")}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Email"
    }
  },
  {
    accessorKey: "phoneNumbers",
    header: "Phone Number",
    accessorFn: row =>
      row.phoneNumbers.map(p => formatPhoneNumber(p.phoneNumber)).join(" "),
    cell: ({ row }) => {
      const phoneNumbers = getSliceOfPhoneNumbers(row.original.phoneNumbers, 2);

      return (
        <div
          className={cn(
            "flex flex-col",
            row.index !== 0 &&
              "blur-sm transition-all duration-300 hover:blur-none"
          )}
        >
          {phoneNumbers.length > 0
            ? phoneNumbers.map(phone => (
                <div
                  key={phone.phoneNumber}
                  className="flex items-center space-x-2 pt-1"
                >
                  <span>{formatPhoneNumber(phone.phoneNumber)}</span>
                  <Badge>{humanize(phone.type)}</Badge>
                </div>
              ))
            : "N/A"}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Phone Number"
    }
  },
  {
    accessorKey: "addresses",
    header: "Current Address",
    cell: ({ row }) => {
      const currentAddress = getCurrentAddress(row.original.addresses ?? []);

      return currentAddress ? (
        <div className="flex flex-col">
          <span>{currentAddress.displayName}</span>
        </div>
      ) : (
        "N/A"
      );
    },
    meta: {
      dropdownLabel: "Current Address"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <SiteContactsDataTableRowActions contact={row.original} />
    )
  }
];
