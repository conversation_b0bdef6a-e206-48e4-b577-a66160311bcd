"use client";

import type { Contact } from "@watt/api/src/types/people";
import { EditContactModal } from "@watt/crm/components/contact/edit-contact-modal";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { MoreHorizontalIcon, PencilIcon } from "lucide-react";

type SiteContactsDataTableRowActionsProps = {
  contact: Contact;
};

export function SiteContactsDataTableRowActions({
  contact
}: SiteContactsDataTableRowActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex size-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontalIcon className="size-4" />
          <span className="sr-only fixed">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <EditContactModal contact={contact}>
          <DropdownMenuItem onSelect={e => e.preventDefault()}>
            <PencilIcon className="mr-2 size-3.5 text-muted-foreground/70" />
            Edit Contact
          </DropdownMenuItem>
        </EditContactModal>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
