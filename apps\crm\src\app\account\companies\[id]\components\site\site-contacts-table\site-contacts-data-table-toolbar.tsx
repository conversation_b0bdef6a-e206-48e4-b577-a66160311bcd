"use client";

import type { Table } from "@tanstack/react-table";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { XIcon } from "lucide-react";
import React, { useCallback, useMemo } from "react";
import { useDebounce } from "react-use";

type SiteContactsDataTableToolbarProps<TData> = {
  table: Table<TData>;
};

export function SiteContactsDataTableToolbar<TData>({
  table
}: SiteContactsDataTableToolbarProps<TData>) {
  const [debouncedSearchValue, setDebouncedSearchValue] = React.useState("");
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  useDebounce(
    () => {
      table.setGlobalFilter(debouncedSearchValue);
    },
    500,
    [debouncedSearchValue]
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearchValue(event.target.value);
  };

  const resetFilters = useCallback(() => {
    table.resetColumnFilters();
    table.resetGlobalFilter();

    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
    setDebouncedSearchValue("");
  }, [table]);

  const isFiltered = useMemo(() => {
    return (
      table.getPreFilteredRowModel().rows.length >
      table.getFilteredRowModel().rows.length
    );
  }, [table]);

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <p className="font-semibold text-xl">Contacts</p>
          <SearchInput
            placeholder="Type to search..."
            onChange={handleSearchChange}
            className="h-9 w-[250px]"
            ref={searchInputRef}
          />
          {isFiltered && (
            <Button variant="ghost" onClick={resetFilters} size="sm">
              Reset
              <XIcon className="ml-2 size-4" />
            </Button>
          )}
        </div>
        <DataTableViewOptions table={table} />
      </div>
      <p className="text-muted-foreground text-sm italic">
        Primary contact is prioritised for display. Hover to reveal other
        contacts&apos; details.
      </p>
    </div>
  );
}
