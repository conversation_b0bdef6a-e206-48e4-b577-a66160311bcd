"use client";

import {
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { Contact } from "@watt/api/src/types/people";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { EmptyStatePanel } from "@watt/crm/app/account/quotes/components/empty-state";
import { DataTablePagination } from "@watt/crm/components/data-table/data-table-pagination";
import { Button } from "@watt/crm/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";
import { getCommonPinningStyles } from "@watt/crm/utils/common-pinning-styles";
import React from "react";
import { siteContactsColumns as columns } from "./site-contacts-columns";
import { SiteContactsDataTableToolbar } from "./site-contacts-data-table-toolbar";

type SiteContactsDataTableProps = {
  contacts: Contact[];
  addContact: () => void;
};

export function SiteContactsDataTable({
  contacts,
  addContact
}: SiteContactsDataTableProps) {
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const table = useReactTable({
    data: contacts,
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      columnPinning: { right: ["actions"] }
    },
    enableRowSelection: false,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });

  return (
    <div className="space-y-4">
      <SiteContactsDataTableToolbar table={table} />
      <div className="rounded-md border bg-background">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  className={cn(
                    "select-none transition-all duration-300 hover:bg-muted-foreground/30",
                    rowIndex % 2 === 0 && "bg-muted"
                  )}
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell
                      key={cell.id}
                      className="h-12"
                      style={getCommonPinningStyles({
                        column: cell.column,
                        backgroundColor:
                          rowIndex % 2 === 0
                            ? "hsl(var(--muted))"
                            : "hsl(var(--background))"
                      })}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <EmptyStatePanel
                    title="No contacts found"
                    description="There are no contacts for this site. Add a new contact to get started."
                  >
                    <Button
                      variant="outline"
                      className="font-medium"
                      onClick={addContact}
                    >
                      Add Contact
                    </Button>
                  </EmptyStatePanel>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
