"use client";

import type { SiteWith_Com_Add_Con } from "@watt/api/src/router/site";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { BillingAddressModal } from "@watt/crm/components/billing-address/billing-address-modal";
import {
  type ContactModalQueryParams,
  ContactModalTypes
} from "@watt/crm/components/quick-actions/contact/contact-provider";
import { Button } from "@watt/crm/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { MoreVerticalIcon, UserIcon } from "lucide-react";
import { useMemo } from "react";
import { UtilityIcons } from "./profile";
import { SiteContactsDataTable } from "./site-contacts-table/site-contacts-data-table";

type SiteInformationCardQueryParams = {
  companyId: string;
  siteId: string;
  setAsPrimaryContact: boolean;
} & ContactModalQueryParams;

type SiteInformationCardProps = {
  siteData: NonNullable<SiteWith_Com_Add_Con>;
  companyId: SiteInformationCardQueryParams["companyId"];
};

export function SiteInformationCard({
  siteData,
  companyId
}: SiteInformationCardProps) {
  const { setQueryParams } = useQueryParams<SiteInformationCardQueryParams>();

  const billingAddress = useMemo(
    () => siteData?.billingAddresses[0],
    [siteData?.billingAddresses]
  );

  const setAsPrimaryContact = useMemo(
    () => !siteData?.contacts.some(contact => contact.isPrimarySiteContact),
    [siteData?.contacts]
  );

  const addContact = () => {
    setQueryParams({
      companyId,
      siteId: siteData.id,
      setAsPrimaryContact,
      modal: ContactModalTypes.addNewContact
    });
  };

  return (
    <Card>
      <CardHeader className="rounded-t-lg py-4">
        <CardTitle className="flex justify-between">
          <div className="flex items-center gap-4 font-bold text-2xl text-foreground">
            Site Information <UtilityIcons meters={siteData.siteMeters} />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVerticalIcon className="size-4 rotate-90" />
                <span className="sr-only fixed">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={addContact}>
                <UserIcon className="mr-2 size-4" />
                Add Contact
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <div className="flex">
          <div className="flex flex-row items-center gap-6">
            <p className="font-semibold text-xl">Site Address</p>
            <p>{getAddressDisplayName(siteData.entityAddress)}</p>
          </div>
        </div>
        <div className="group">
          <div className="flex flex-row items-center gap-6">
            <p className="font-semibold text-xl">Billing Address</p>
            {getAddressDisplayName(billingAddress?.entityAddress) || (
              <span className="text-muted-foreground italic">N/A</span>
            )}
            <BillingAddressModal
              companyId={companyId}
              siteId={siteData.id}
              billingAddressId={billingAddress?.id || null}
              billingEntityAddress={billingAddress?.entityAddress}
            />
          </div>
        </div>
        <SiteContactsDataTable
          contacts={siteData.contacts}
          addContact={addContact}
        />
      </CardContent>
    </Card>
  );
}

export function SiteInformationCardSkeleton() {
  return (
    <Card>
      <CardHeader className="rounded-t-lg py-4">
        <CardTitle className="flex justify-between">
          <div className="flex items-center gap-4 font-bold text-2xl text-foreground">
            {/* Skeleton for Card Title */}
            <Skeleton className="h-8 w-40" />
            {/* Skeleton for Utility Icons */}
            <div className="flex gap-2">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
            </div>
          </div>
          {/* Skeleton for Dropdown Menu Button */}
          <Skeleton className="h-8 w-8" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <div className="flex">
          <div className="flex flex-row items-center gap-6">
            {/* Skeleton for "Site Address" Label */}
            <Skeleton className="h-6 w-32" />
            {/* Skeleton for Address */}
            <Skeleton className="h-6 w-64" />
          </div>
        </div>
        <div className="group">
          <div className="flex flex-row items-center gap-6">
            {/* Skeleton for "Billing Address" Label */}
            <Skeleton className="h-6 w-32" />
            {/* Skeleton for Billing Address */}
            <Skeleton className="h-6 w-64" />
            {/* Skeleton for Billing Address Modal Button */}
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
        {/* Skeleton for Contacts Data Table */}
        <Skeleton className="h-40 w-full" />
      </CardContent>
    </Card>
  );
}
