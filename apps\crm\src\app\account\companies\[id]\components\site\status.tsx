import { Badge } from "@watt/crm/components/ui/badge";
import { trpcClient } from "@watt/crm/utils/api";
import type { LucideIcon } from "lucide-react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@watt/crm/components/ui/alert-dialog";
import { Button, buttonVariants } from "@watt/crm/components/ui/button";

export type SiteMarkingKey = "hasPreviousDeal";

interface Prompts {
  title: string;
  description: string;
}

export interface SiteMarkingPrompts {
  enable: Prompts;
  disable: Prompts;
}

type SiteStatusesProps = {
  siteId: string;
  status: SiteMarkingKey;
  Icon: LucideIcon;
  isEnabled: boolean;
  activeFill: string;
  prompts: SiteMarkingPrompts | undefined;
};

export function SiteMarkings({
  siteId,
  status,
  Icon,
  isEnabled,
  activeFill,
  prompts
}: SiteStatusesProps) {
  const { mutate } = trpcClient.site.update.useMutation();

  const handleSubmit = () => {
    mutate({ id: siteId, [status]: !isEnabled });
  };

  if (!prompts) {
    return (
      <Button variant="ghost" className="p-2">
        <Icon fill={isEnabled ? activeFill : "none"} />
      </Button>
    );
  }

  const promptTitle = isEnabled ? prompts.disable.title : prompts.enable.title;
  const promptDescription = isEnabled
    ? prompts.disable.description
    : prompts.enable.description;

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="ghost" className="p-2">
          <Icon fill={isEnabled ? activeFill : "none"} />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{promptTitle}</AlertDialogTitle>
          <AlertDialogDescription>{promptDescription}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className={buttonVariants({ variant: "outline" })}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleSubmit}
            className={buttonVariants({ variant: "secondary" })}
          >
            Confirm
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
