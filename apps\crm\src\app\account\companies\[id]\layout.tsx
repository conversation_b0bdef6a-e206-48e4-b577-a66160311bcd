import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { routes } from "@watt/crm/config/routes";
import { HydrateClient, tRPCServerApi } from "@watt/crm/trpc/server";
import { Building2Icon } from "lucide-react";
import { cookies } from "next/headers";
import Link from "next/link";
import type { PropsWithChildren } from "react";
import { Suspense } from "react";
import {
  CompanyActionPanel,
  CompanyActionPanelSkeleton
} from "./components/company-action-panel";
import {
  CompanyHeader,
  CompanyHeaderSkeleton
} from "./components/company-header";
import {
  CompanyTabsNavigation,
  CompanyTabsNavigationSkeleton
} from "./components/company-tab-navigation";
import { ResizableLayout } from "./resizable-layout";

type CompanyLayoutProps = PropsWithChildren<{
  params: { id: string };
}>;

const RESIZABLE_KEY = "company" as const;

export default async function CompanyLayout(props: CompanyLayoutProps) {
  const params = await props.params;

  const { id: companyId } = params;

  const { children } = props;

  const layout = (await cookies()).get(
    `react-resizable-panels-${RESIZABLE_KEY}:layout`
  );
  const defaultLayout = layout ? JSON.parse(layout.value) : [70, 30];

  await tRPCServerApi.company.all.prefetch();

  let error!: string;

  try {
    // TODO (Stephen):This is a big problem, we're mixing prefetch and normal `fetch`.
    // `<CompanyHeader/>` is using useSuspenseQuery but the data for company.find is no longer prefetched.
    // This means the all pages from company/[id] get a massive performance hit as they need to render on the server and client
    // And the reason is just to call company.find to see if the company exists, the data is not even used beyond catching the error.
    //
    // Most users loading a page from /company/[id] and down (e.g. sites) will always be impacted by this
    // even if their companyId does exist.
    await tRPCServerApi.company.find({ id: companyId });
  } catch {
    error =
      "Please check the URL for typos. The company may have been deleted from our database.";
  }

  return (
    <ResizableLayout
      resizableKey={RESIZABLE_KEY}
      defaultLayout={defaultLayout}
      left={
        !error ? (
          <div className="flex h-full flex-col">
            <HydrateClient>
              <Suspense fallback={<CompanyHeaderSkeleton />}>
                <CompanyHeader companyId={companyId} />
              </Suspense>
            </HydrateClient>
            <div className="flex-1 overflow-hidden">
              <HydrateClient>
                <Suspense fallback={<CompanyTabsNavigationSkeleton />}>
                  <CompanyTabsNavigation companyId={companyId}>
                    {children}
                  </CompanyTabsNavigation>
                </Suspense>
              </HydrateClient>
            </div>
          </div>
        ) : (
          <div className="relative flex h-full flex-col">
            <CompanyHeaderSkeleton />
            <div className="flex h-full w-full items-center justify-center border-t bg-muted">
              <EmptyStatePanel
                title="Company not found"
                description={error}
                Icon={Building2Icon}
              >
                <Link
                  href={routes.companies}
                  className={cn(
                    buttonVariants({ variant: "outline" }),
                    "font-medium"
                  )}
                >
                  View All Companies
                </Link>
              </EmptyStatePanel>
            </div>
          </div>
        )
      }
      right={
        !error ? (
          <Suspense fallback={<CompanyActionPanelSkeleton />}>
            <CompanyActionPanel companyId={companyId} />
          </Suspense>
        ) : (
          <CompanyActionPanelSkeleton />
        )
      }
    />
  );
}
