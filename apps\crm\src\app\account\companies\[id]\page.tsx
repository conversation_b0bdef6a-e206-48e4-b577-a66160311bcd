import { routes } from "@watt/crm/config/routes";
import type { Metadata } from "next";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Company",
  description: "Company details."
};

export default async function CompanyPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  redirect(routes.companyActivity.replace("[id]", params.id));
}
