"use client";

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup
} from "@watt/crm/components/ui/resizable";

type ResizableLayoutProps = {
  resizableKey: string;
  left: React.ReactNode;
  right: React.ReactNode;
  defaultLayout: [number, number];
};

export function ResizableLayout({
  resizableKey,
  left,
  right,
  defaultLayout
}: ResizableLayoutProps) {
  const handleLayoutChange = (sizes: number[]) => {
    document.cookie = `react-resizable-panels-${resizableKey}:layout=${JSON.stringify(
      sizes
    )}; path=/`;
  };

  return (
    <ResizablePanelGroup
      direction="horizontal"
      onLayout={handleLayoutChange}
      className="h-full items-stretch pr-4"
    >
      <ResizablePanel
        defaultSize={defaultLayout[0]}
        minSize={30}
        className="!overflow-y-auto h-full w-full"
      >
        {left}
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel
        defaultSize={defaultLayout[1]}
        minSize={20}
        maxSize={30}
        className="!overflow-y-auto"
      >
        {right}
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
