import { HydrateClient, tRPCServerApi } from "@watt/crm/trpc/server";
import type { Metadata } from "next";
import { Suspense } from "react";
import {
  SiteProfile,
  SiteProfileSkeleton
} from "../../components/site/profile";

export const metadata: Metadata = {
  title: "Site",
  description: "Site details."
};

export default async function CompanySite(props: {
  params: Promise<{ id: string; siteRefId: string }>;
}) {
  const params = await props.params;
  await tRPCServerApi.site.find.prefetch({
    id: Number.parseInt(params.siteRefId)
  });

  return (
    <HydrateClient>
      <Suspense fallback={<SiteProfileSkeleton />}>
        <SiteProfile companyId={params.id} siteRefId={params.siteRefId} />
      </Suspense>
    </HydrateClient>
  );
}
