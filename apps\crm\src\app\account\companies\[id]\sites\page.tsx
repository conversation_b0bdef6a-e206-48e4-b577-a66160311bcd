import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { HydrateClient, tRPCServerApi } from "@watt/crm/trpc/server";

import { Suspense } from "react";
import { SiteDataTable } from "../components/site-table/site-data-table";

import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Company Sites",
  description: "Company sites."
};

export default async function CompanySites(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  await tRPCServerApi.site.getAllCompanySites.prefetch({
    companyId: params.id
  });

  return (
    <HydrateClient>
      <Suspense
        fallback={
          <div className="space-y-4 p-2 pt-8">
            <DataTableSkeleton
              columnCount={5}
              searchableColumnCount={1}
              filterableColumnCount={1}
              cellWidths={["12rem", "14rem"]}
              withPagination={true}
              shrinkZero
            />
          </div>
        }
      >
        <SiteDataTable companyId={params.id} />
      </Suspense>
    </HydrateClient>
  );
}
