"use client";

import type { Prisma } from "@prisma/client";
import type { ColumnDef, SortingFn } from "@tanstack/react-table";
import type { CompaniesWith_Add_Con_Sit } from "@watt/api/src/router/company";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import Link from "next/link";

import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import {
  countFilter,
  textFilter
} from "@watt/crm/components/data-table/data-table-filter-functions";
import { Badge } from "@watt/crm/components/ui/badge";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { routes } from "@watt/crm/config/routes";
import { getBadgeVariantFromLabel } from "../../interactions/components/interactions-list";

// Extend the CellContext type to add custom props
// https://github.com/TanStack/table/discussions/4391#discussioncomment-3683627
// type CellContext<TData extends RowData, TValue> = TanCellContext<TData, TValue> & {
//   onClick: () => void;
// };

const countSorting: SortingFn<ExtractElementType<CompaniesWith_Add_Con_Sit>> = (
  rowA,
  rowB,
  _columnId: string
): number => {
  const siteCountA = rowA.original._count.sites;
  const siteCountB = rowB.original._count.sites;
  if (siteCountA > siteCountB) {
    return 1; // rowA should be ordered after rowB
  }

  if (siteCountA < siteCountB) {
    return -1; // rowA should be ordered before rowB
  }

  return 0; // siteCountA and siteCountB are equal
};

export const columns: ColumnDef<
  ExtractElementType<CompaniesWith_Add_Con_Sit>
>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex w-[80px] justify-center">{row.getValue("id")}</div>
      );
    },
    meta: {
      dropdownLabel: "ID"
    }
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ row }) => {
      const id = row.getValue("id") as string;
      return (
        <Link
          href={`${routes.company.replace("[id]", "")}${id}/activity`}
          className={cn(
            buttonVariants({
              variant: "link"
            }),
            "!justify-start w-[180px]"
          )}
        >
          {humanize(row.getValue("name"))}
        </Link>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "businessType",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Type" />
    ),
    cell: ({ row }) => {
      return (
        <Badge variant={getBadgeVariantFromLabel(row.getValue("businessType"))}>
          {humanize(row.getValue("businessType"))}
        </Badge>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Type"
    }
  },
  {
    accessorKey: "entityAddress",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Address" />
    ),
    cell: ({ row }) => {
      return (
        <div className="w-[250px]">
          {getAddressDisplayName(row.original.entityAddress)}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Address"
    }
  },
  // TODO (pasha): Add back in when we have a primary contact for a company (commented out by TAS-2036)
  // {
  //   accessorKey: "contacts",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Key Contact" />
  //   ),
  //   cell: ({ row }) => {
  //     const contacts = row.getValue<CompanyContact[]>("contacts");
  //     const primaryContact = contacts.find(
  //       contact => contact.isPrimarySiteContact
  //     );

  //     return primaryContact ? (
  //       <TooltipProvider>
  //         <Tooltip>
  //           <TooltipTrigger>
  //             <div className="flex items-center gap-2">
  //               <Avatar className="size-7">
  //                 <AvatarFallback className="bg-primary text-white">
  //                   {`${primaryContact.forename.slice(0, 1)}${primaryContact.surname.slice(0, 1)}`}
  //                 </AvatarFallback>
  //               </Avatar>
  //               {`${primaryContact.forename} ${primaryContact.surname}`}
  //             </div>
  //           </TooltipTrigger>
  //           <TooltipContent>{`${primaryContact.phoneNumber}`}</TooltipContent>
  //         </Tooltip>
  //       </TooltipProvider>
  //     ) : (
  //       "No primary contact"
  //     );
  //   },
  //   meta: {
  //     dropdownLabel: "Key Contact"
  //   }
  // },
  {
    accessorKey: "_count",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Total Sites" />
    ),
    cell: ({ row }) => (
      <div>
        {row.getValue<Prisma.CompanyCountOutputType>("_count")?.sites || "0"}
      </div>
    ),
    sortingFn: countSorting,
    filterFn: countFilter,
    meta: {
      dropdownLabel: "Total Sites"
    }
  }
  // {
  //   accessorKey: "_count_contracts_active",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Active Contracts" />,
  //   cell: ({ row }) => (
  //     <div className="flex w-[80px] justify-center">
  //       {row.getValue<Prisma.CompanyCountOutputType>("_count")?.contracts || "0"}
  //     </div>
  //   ),
  //   filterFn: countFilter,
  //   enableHiding: true
  // },
  // {
  //   accessorKey: "_count_contracts_due_for_renewal",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Contracts Due For Renewal" />
  //   ),
  //   cell: ({ row }) => (
  //     <div className="flex w-[80px] justify-center">
  //       {row.getValue<Prisma.CompanyCountOutputType>("_count")?.contracts || "0"}
  //     </div>
  //   ),
  //   filterFn: countFilter
  // }
];
