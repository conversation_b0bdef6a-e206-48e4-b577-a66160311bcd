"use client";

import type { Table } from "@tanstack/react-table";
import { createZodEnumArray } from "@watt/common/src/utils/zod-literal-union";
import { X } from "lucide-react";
import { z } from "zod";

import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import { businessTypes } from "@watt/db/src/enumerable-types";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered,
  children
}: DataTableToolbarProps<TData>) {
  const queryParamsSchema = z.object({
    businessType: createZodEnumArray(businessTypes),
    search: z.string().optional()
  });

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          value={searchValue}
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
        />
        {table.getColumn("businessType") && (
          <DataTableFacetedFilter
            column={table.getColumn("businessType")}
            title="Company Type"
            options={businessTypes}
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <DataTableViewOptions table={table} />
        {children}
      </div>
    </div>
  );
}
