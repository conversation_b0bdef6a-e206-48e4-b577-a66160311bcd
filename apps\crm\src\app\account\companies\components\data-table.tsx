"use client";

import {
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { trpcClient } from "@watt/crm/utils/api";
import React, { useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";

import { DataTableToolbar } from "../components/data-table-toolbar";
import { columns } from "./columns";
import { EmptyStatePanel } from "./empty-state";

type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

export function DataTable() {
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    id: false
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");

  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = trpcClient.company.companyFilteredList.useInfiniteQuery(
    {
      searchFilters: {
        columnFilters: debouncedColumnFilters,
        globalFilter
      }
    },
    {
      getNextPageParam: lastPage => lastPage.nextCursor,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      placeholderData: prev => prev,
      trpc: {
        abortOnUnmount: true
      }
    }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = data?.pages?.[0]?.meta?.totalRowCount ?? 0;
  const totalFetched = allItems.length;

  const table = useReactTable({
    data: allItems,
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      rowSelection,
      globalFilter
    },
    enableRowSelection: false,
    onGlobalFilterChange: setGlobalFilter,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    debugTable: false,
    manualFiltering: true
  });

  if (isLoading) {
    return (
      <div className="space-y-4 py-4">
        <h1 className="font-bold text-xl tracking-tight">Companies</h1>
        <DataTableSkeleton
          columnCount={table.getAllColumns().length}
          searchableColumnCount={1}
          filterableColumnCount={1}
          cellWidths={["12rem", "14rem"]}
          withPagination={false}
          shrinkZero
        />
      </div>
    );
  }

  const emptyStatePanel = () => (
    <EmptyStatePanel
      title="No companies found"
      description="It looks like you haven't created any companies yet. Get started by creating your first company by quoting a customer."
    />
  );

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
      emptyStatePanel={emptyStatePanel()}
    >
      <h1 className="font-bold text-xl tracking-tight">Companies</h1>
      <DataTableToolbar table={table} isFiltered={isFiltered} />
    </InfiniteScrollDataTable>
  );
}
