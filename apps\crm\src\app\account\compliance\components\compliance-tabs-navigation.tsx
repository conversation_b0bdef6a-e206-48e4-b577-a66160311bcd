"use client";

import { Separator } from "@watt/crm/components/ui/separator";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";
import { routes } from "@watt/crm/config/routes";
import Link from "next/link";
import { useSelectedLayoutSegment } from "next/navigation";

const tabItems = [
  {
    label: "New",
    value: "new",
    link: routes.compliance.new
  },
  {
    label: "In Progress",
    value: "in-progress",
    link: routes.compliance.inProgress
  },
  {
    label: "Rejected by CO",
    value: "rejected-by-co",
    link: "#"
  },
  {
    label: "CC Required",
    value: "cc-required",
    link: "#"
  },
  {
    label: "Resubmitted to CO",
    value: "resubmitted-to-co",
    link: "#"
  },
  {
    label: "Await Submission",
    value: "await-submission",
    link: "#"
  },
  {
    label: "Submitted to Supplier",
    value: "submitted-to-supplier",
    link: "#"
  },
  {
    label: "Cancelled by CO",
    value: "cancelled-by-co",
    link: "#"
  },
  {
    label: "Dead by CO",
    value: "dead-by-co",
    link: "#"
  }
] as const;

type ComplianceTabsNavigationProps = {
  children: React.ReactNode;
};

export function ComplianceTabsNavigation({
  children
}: ComplianceTabsNavigationProps) {
  const currentSegment = useSelectedLayoutSegment();
  const currentTab = currentSegment || "new";

  return (
    <Tabs value={currentTab} defaultValue="new" className="h-screen">
      <div className="flex flex-col items-start justify-between gap-2 px-4 py-4">
        <h1 className="font-bold text-xl">Compliance</h1>
        <TabsList className="h-9 bg-muted-foreground/10">
          {tabItems.map(tab => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              asChild
              className="h-6"
            >
              <Link href={tab.link}>{tab.label}</Link>
            </TabsTrigger>
          ))}
        </TabsList>
      </div>
      <Separator />
      <div className="px-4">
        {tabItems.map(tab => (
          <TabsContent key={tab.value} value={tab.value}>
            {children}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  );
}
