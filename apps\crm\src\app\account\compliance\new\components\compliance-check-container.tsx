import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { trpcClient } from "@watt/crm/utils/api";
import { ComplianceCheckForm } from "./compliance-check-form";

export type ComplianceCheckContainerProps = {
  dealId: string;
  onSubmit: () => void;
};

export function ComplianceCheckContainer({
  dealId,
  onSubmit
}: ComplianceCheckContainerProps) {
  const { data: dealData, isLoading } =
    trpcClient.compliance.getDealDetails.useQuery(
      { dealId },
      { enabled: !!dealId }
    );

  if (isLoading) {
    return <ComplianceCheckSkeleton />;
  }

  if (!dealData) {
    return (
      <div className="text-center text-muted-foreground">
        Failed to load deal data
      </div>
    );
  }

  return <ComplianceCheckForm dealData={dealData} onSubmit={onSubmit} />;
}

function ComplianceCheckSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-2 w-full" />
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-[600px] w-full" />
      <div className="flex justify-between">
        <Skeleton className="h-10 w-52" />
        <Skeleton className="h-10 w-52" />
      </div>
    </div>
  );
}
