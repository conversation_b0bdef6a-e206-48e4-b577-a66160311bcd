import type { ComplianceDealDetails } from "@watt/api/src/router/compliance";
import type { ComplianceCheck } from "@watt/api/src/types/compliance";
import { ComplianceCheckSchema } from "@watt/api/src/types/compliance";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { DealDetailsSection } from "@watt/crm/components/site/site-deals/deal-submission/form/sections/deal-details-section";
import { FormWrapper } from "@watt/crm/components/ui/form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { ComplianceCheckMeterDetails } from "./compliance-check-meter-details";
import { ComplianceReviewSummary } from "./compliance-review-summary";
import { SupportingDocuments } from "./compliance-supporting-documents";
import { SubmissionDetails } from "./submission-details";
import { SubmissionNotes } from "./submission-notes";

type ComplianceCheckFormProps = {
  dealData: NonNullable<ComplianceDealDetails>;
  onSubmit: () => void;
};

export function ComplianceCheckForm({
  dealData,
  onSubmit
}: ComplianceCheckFormProps) {
  const form = useZodForm({
    schema: ComplianceCheckSchema,
    defaultValues: {
      dealId: dealData.id,
      companyId: dealData.companyId,
      siteId: dealData.siteId,
      companyName: dealData.companyName,
      companyNumber: dealData.companyNumber,
      businessType: dealData.businessType,
      siteAddress: dealData.siteAddress,
      billingAddressId: dealData.billingAddressId,
      billingEntityAddress: dealData.billingEntityAddress,
      contact: {
        ...dealData.signedBy,
        salutation: dealData.signedBy.salutation ?? null
      },
      supplier: dealData.newSupplier?.name || "",
      consumption: dealData.consumption,
      term: dealData.term,
      startDate: formatDate(dealData.startDate, dateFormats.YYYY_MM_DD_HYPHEN),
      endDate: formatDate(dealData.endDate, dateFormats.YYYY_MM_DD_HYPHEN),
      quoteId: dealData.quoteId,
      quoteType: dealData.quoteType,
      uplift: dealData.uplift,
      standingCharge: dealData.standingCharge,
      standingChargeUplift: dealData.standingChargeUplift,
      unitRate: dealData.unitRate,
      nightRate: dealData.nightRate ?? undefined,
      weekendRate: dealData.weekendRate ?? undefined,
      annualCost: dealData.annualCost,
      commission: dealData.commission,
      contractReviewResult: [],
      loaReviewResult: [],
      creditCheckReviewResult: "",
      complianceNextStage: "",
      complianceReviewNotes: "",
      isChangeOfTenancy: dealData.isChangeOfTenancy,
      creditCheck: dealData.creditCheck,
      loa: dealData.loa
    }
  });

  function handleSave() {
    toast({
      title: "Success",
      description: "Saved changes"
    });
    onSubmit();
  }

  async function handleSubmit(data: ComplianceCheck) {
    try {
      // TODO: Implement submission logic
      toast({
        title: "Success",
        description: "Compliance check submitted successfully"
      });
      onSubmit();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit compliance check",
        variant: "destructive"
      });
    }
  }

  return (
    <FormWrapper form={form} handleSubmit={handleSubmit}>
      <div className="space-y-6">
        <SubmissionDetails />
        <DealDetailsSection {...form.getValues()} />
        {dealData.utilityType && dealData.meterNumber && (
          <ComplianceCheckMeterDetails
            utilityType={dealData.utilityType}
            meterNumber={dealData.meterNumber}
          />
        )}
        <SupportingDocuments />
        <SubmissionNotes submissionNotes={dealData.submissionNotes} />
        <ComplianceReviewSummary onSave={handleSave} />
      </div>
    </FormWrapper>
  );
}
