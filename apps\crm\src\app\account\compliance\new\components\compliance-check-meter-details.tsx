import type { UtilityType } from "@prisma/client";
import { CheckMeterDetails } from "@watt/crm/components/site/check-meter-details";
import { Button } from "@watt/crm/components/ui/button";
import { Card, CardContent } from "@watt/crm/components/ui/card";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { trpcClient } from "@watt/crm/utils/api";
import { Search } from "lucide-react";

export function ComplianceCheckMeterDetails({
  utilityType,
  meterNumber
}: {
  utilityType: UtilityType;
  meterNumber: string;
}) {
  const meterInfo = trpcClient.siteMeter.checkMeterInformation.useQuery(
    {
      utilityType,
      meterNumber: meterNumber
    },
    {
      enabled: false
    }
  );

  const handleCheckMeterInformation = () => {
    if (meterInfo.isFetching || meterInfo.data) {
      return;
    }
    meterInfo.refetch();
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <h3 className="font-medium">Check Meter Details</h3>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={handleCheckMeterInformation}
        >
          <Search className="size-4" />
        </Button>
      </div>
      {meterInfo.isFetching ? (
        <Skeleton className="h-10 min-h-60 w-full" />
      ) : (
        <Card>
          <CardContent className="flex min-h-60 flex-col justify-center p-6">
            {meterInfo.data ? (
              <CheckMeterDetails
                utilityType={utilityType}
                meterInfo={meterInfo.data}
              />
            ) : (
              <p className="flex items-center justify-center gap-2 text-muted-foreground text-sm">
                <Search className="size-4" />
                Click the search icon to fetch meter details
              </p>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
