"use client";

import type { ComplianceNewDeals } from "@watt/api/src/router/compliance";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { ComplianceCheckContainer } from "./compliance-check-container";

type ComplianceCheckModalProps = {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  dealData: ComplianceNewDeals["items"][number];
};

export function ComplianceCheckModal({
  openModal,
  setOpenModal,
  dealData
}: ComplianceCheckModalProps) {
  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto p-10">
        <DialogHeader>
          <DialogTitle className="font-medium text-2xl">
            {dealData.companyName}
          </DialogTitle>
          <DialogDescription>
            Review and complete the compliance check for this deal.
          </DialogDescription>
        </DialogHeader>
        <ComplianceCheckContainer
          dealId={dealData.id}
          onSubmit={() => setOpenModal(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
