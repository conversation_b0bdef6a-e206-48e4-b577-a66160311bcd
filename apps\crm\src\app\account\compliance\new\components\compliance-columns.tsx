"use client";

import { Lock, LockOpen } from "lucide-react";

import type { ColumnDef } from "@tanstack/react-table";
import type { ComplianceNewDeals } from "@watt/api/src/router/compliance";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { UserAvatar } from "@watt/crm/components/avatar/user-avatar";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { MpxnInputField } from "@watt/crm/components/mpxn/mpxn-input-field";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { routes } from "@watt/crm/config/routes";
import Link from "next/link";
import { DataTableRowActions } from "./compliance-data-table-row-actions";

export const complianceColumns: ColumnDef<
  ComplianceNewDeals["items"][number]
>[] = [
  {
    accessorKey: "companyName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex flex-row gap-3">
          <Button
            variant={row.original.access ? "secondary" : "destructive"}
            size="smr"
            className="size-6 p-0"
          >
            {row.original.access ? (
              <LockOpen className="size-3" />
            ) : (
              <Lock className="size-3" />
            )}
          </Button>
          <div className="flex flex-col gap-2">
            <Link
              href={routes.company.replace("[id]", row.original.id)}
              className="hover:underline"
            >
              {row.original.companyName}
            </Link>
            <MpxnInputField
              meterIdentifier={row.original.meterNumber}
              utilityType={row.original.utilityType}
              className="w-[190px] px-0"
            />
          </div>
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "eacAq",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="EAC/AQ" />
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "EAC/AQ"
    }
  },
  {
    accessorKey: "signedDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Signed" />
    ),
    cell: ({ row }) =>
      formatDate(row.original.signedDate, dateFormats.DD_MM_YYYY),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Signed"
    }
  },
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Exp. Live" />
    ),
    cell: ({ row }) =>
      formatDate(row.original.startDate, dateFormats.DD_MM_YYYY),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Exp. Live"
    }
  },
  {
    accessorKey: "statusSince",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status Since" />
    ),
    cell: ({ row }) =>
      formatDate(row.original.statusSince, dateFormats.DD_MM_YYYY),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status Since"
    }
  },
  {
    accessorKey: "agentName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="SA" />
    ),
    cell: ({ row }) => (
      <Tooltip>
        <TooltipTrigger>
          <UserAvatar fullName={row.original.agentName} />
        </TooltipTrigger>
        <TooltipContent>{row.original.agentName}</TooltipContent>
      </Tooltip>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "SA"
    }
  },
  {
    accessorKey: "currentSupplier",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Current" />
    ),
    cell: ({ row }) => {
      const currentSupplier = row.original.currentSupplier;
      const { logoFileName, name } = currentSupplier;
      return (
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={name}
          width={50}
          height={17}
          className="h-auto w-[50px] object-scale-down"
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Current"
    }
  },
  {
    accessorKey: "newSupplier",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="New" />
    ),
    cell: ({ row }) => {
      const newSupplier = row.original.newSupplier;
      const { logoFileName, name } = newSupplier;
      return (
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={name}
          width={50}
          height={17}
          className="h-[17px] w-[50px] object-scale-down"
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "New"
    }
  },
  {
    accessorKey: "isChangeOfTenancy",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="COT" />
    ),
    cell: ({ row }) => (row.original.isChangeOfTenancy ? "Yes" : "No"),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "COT"
    }
  },
  {
    accessorKey: "verificationStatus",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Checks" />
    ),
    cell: ({ row }) => {
      const items = [
        { label: "Credit Check", value: row.original.creditCheck },
        { label: "LOA", value: row.original.loa },
        { label: "Contract Verify", value: row.original.contractVerify }
      ];

      return (
        <div className="space-y-1.5 py-2">
          {items.map(({ label, value }) => (
            <div key={label} className="flex items-center gap-2">
              <span className="min-w-[100px] font-medium text-muted-foreground text-xs">
                {label}:
              </span>
              <span className="text-xs">{value}</span>
            </div>
          ))}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Checks"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions dealData={row.original} />,
    filterFn: textFilter
  }
];
