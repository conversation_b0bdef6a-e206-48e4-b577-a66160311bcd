"use client";

import { FileSearch, MoreHorizontal } from "lucide-react";
import { useState } from "react";

import type { ComplianceNewDeals } from "@watt/api/src/router/compliance";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { ComplianceCheckModal } from "./compliance-check-modal";

type DataTableRowActionsProps = {
  dealData: ComplianceNewDeals["items"][number];
};

export function DataTableRowActions({ dealData }: DataTableRowActionsProps) {
  const [openComplianceCheckModal, setOpenComplianceCheckModal] =
    useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only fixed">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem onClick={() => setOpenComplianceCheckModal(true)}>
            <FileSearch className="mr-2 size-3.5" />
            Start Check
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ComplianceCheckModal
        openModal={openComplianceCheckModal}
        setOpenModal={setOpenComplianceCheckModal}
        dealData={dealData}
      />
    </>
  );
}
