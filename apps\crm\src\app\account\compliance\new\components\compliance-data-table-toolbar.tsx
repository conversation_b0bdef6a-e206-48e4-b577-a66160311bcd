"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";

import udProvidersList from "@watt/common/src/constants/ud-providers-list.json";
import { DataTableDateRangeFilter } from "@watt/crm/components/data-table/data-table-date-range-filter";
import {
  DataTableFacetedFilter,
  type DataTableOption
} from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { trpcClient } from "@watt/crm/utils/api";

interface ComplianceDataTableToolbarProps<TData> {
  table: Table<TData>;
}

export function ComplianceDataTableToolbar<TData>({
  table
}: ComplianceDataTableToolbarProps<TData>) {
  const { data: allDirectDials } =
    trpcClient.userProfile.getAllDirectDialsAndFullNames.useQuery();

  const agentLists = allDirectDials?.length
    ? allDirectDials
    : ([{}, {}] as DataTableOption[]);
  const isFiltered = table.getState().columnFilters.length > 0;

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <SearchInput
          placeholder="Type to search..."
          value={(table.getState().globalFilter as string) ?? ""}
          onChange={event => table.setGlobalFilter(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {table.getColumn("signedDate") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("signedDate") as any}
            title="Signed"
          />
        )}
        {table.getColumn("startDate") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("startDate") as any}
            title="Exp. Live"
          />
        )}
        {table.getColumn("statusSince") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("statusSince") as any}
            title="Status Since"
          />
        )}
        {table.getColumn("agentName") && (
          <DataTableFacetedFilter
            column={table.getColumn("agentName")}
            title="SA"
            options={agentLists}
          />
        )}
        <DataTableFacetedFilter
          column={table.getColumn("currentSupplier")}
          title="Current"
          options={udProvidersList.map(supplier => ({
            label: supplier,
            value: supplier
          }))}
        />
        <DataTableFacetedFilter
          column={table.getColumn("newSupplier")}
          title="New"
          options={udProvidersList.map(supplier => ({
            label: supplier,
            value: supplier
          }))}
        />
        <DataTableFacetedFilter
          column={table.getColumn("isChangeOfTenancy")}
          title="COT"
          // Temp options for mock data this requires db solution and using actual options
          options={[
            { label: "Yes", value: "true" },
            { label: "No", value: "false" }
          ]}
        />
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
