"use client";

import { FileCheck2 } from "lucide-react";

import {
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";
import { trpcClient } from "@watt/crm/utils/api";
import { complianceColumns } from "./compliance-columns";
import { ComplianceDataTableToolbar } from "./compliance-data-table-toolbar";

type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

export function ComplianceDataTable() {
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");

  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = trpcClient.compliance.newDeals.useInfiniteQuery(
    {
      searchFilters: {
        columnFilters: debouncedColumnFilters,
        globalFilter
      }
    },
    {
      getNextPageParam: lastPage => lastPage.nextCursor,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      placeholderData: prev => prev,
      trpc: {
        abortOnUnmount: true
      }
    }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = data?.pages?.[0]?.meta?.totalRowCount ?? 0;
  const totalFetched = allItems.length;

  const table = useReactTable({
    data: allItems,
    columns: complianceColumns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      rowSelection,
      globalFilter,
      columnPinning: { right: ["actions"] }
    },
    enableRowSelection: false,
    onGlobalFilterChange: setGlobalFilter,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    manualFiltering: true
  });

  if (isLoading) {
    return (
      <DataTableSkeleton
        columnCount={table.getAllColumns().length}
        searchableColumnCount={1}
        filterableColumnCount={3}
        withPagination={false}
      />
    );
  }

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
      emptyStatePanel={
        <EmptyStatePanel
          title="No compliance records found"
          description="No compliance records match your current filters."
          Icon={FileCheck2}
        />
      }
    >
      <ComplianceDataTableToolbar table={table} />
    </InfiniteScrollDataTable>
  );
}
