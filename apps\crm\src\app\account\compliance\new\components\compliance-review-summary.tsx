import {
  CONTRACT_REVIEW_RESULT,
  CREDIT_CHECK_REVIEW_RESULT,
  type ComplianceCheck,
  type ContractReviewOption,
  LOA_REVIEW_RESULT,
  type LoaReviewOption,
  NEXT_STAGE_OPTIONS
} from "@watt/api/src/types/compliance";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  DropdownMultiSelect,
  DropdownMultiSelectContent,
  DropdownMultiSelectGroup,
  DropdownMultiSelectItem,
  DropdownMultiSelectTrigger
} from "@watt/crm/components/dropdown-checkbox/dropdown-multi-select";
import { Button } from "@watt/crm/components/ui/button";
import { Card, CardContent } from "@watt/crm/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/crm/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { Check } from "lucide-react";
import { useFormContext } from "react-hook-form";

type ComplianceReviewSummaryProps = {
  onSave: () => void;
};

export function ComplianceReviewSummary({
  onSave
}: ComplianceReviewSummaryProps) {
  const form = useFormContext<ComplianceCheck>();

  const toggleContractReviewOption = (
    reviewKey: ContractReviewOption,
    values: ContractReviewOption[]
  ) => {
    const newValues = values.includes(reviewKey)
      ? values.filter(selectedItem => selectedItem !== reviewKey)
      : [...values, reviewKey];

    form.setValue("contractReviewResult", newValues);
  };

  const toggleLoaReviewOption = (
    reviewKey: LoaReviewOption,
    values: LoaReviewOption[]
  ) => {
    const newValues = values.includes(reviewKey)
      ? values.filter(selectedItem => selectedItem !== reviewKey)
      : [...values, reviewKey];

    form.setValue("loaReviewResult", newValues);
  };

  return (
    <>
      <h3 className="font-medium">Compliance Review Summary</h3>
      <Card>
        <CardContent className="space-y-6 p-8">
          <FormField
            control={form.control}
            name="contractReviewResult"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contract Review Result</FormLabel>
                <FormControl>
                  <DropdownMultiSelect>
                    <DropdownMultiSelectTrigger
                      placeholder="Please select one or multiple statement for your review result"
                      fieldValue={field.value.map(
                        value =>
                          CONTRACT_REVIEW_RESULT[
                            value as keyof typeof CONTRACT_REVIEW_RESULT
                          ] || value
                      )}
                      className="not-italic"
                      childClassName="lg:max-w-[80ch]"
                    />
                    <DropdownMultiSelectContent className="w-[38ch]">
                      <DropdownMultiSelectGroup>
                        {Object.entries(CONTRACT_REVIEW_RESULT).map(
                          ([key, value]) => {
                            const reviewKey = key as ContractReviewOption;
                            return (
                              <DropdownMultiSelectItem
                                key={reviewKey}
                                onSelect={() =>
                                  toggleContractReviewOption(
                                    reviewKey,
                                    field.value
                                  )
                                }
                              >
                                <div className="flex items-center">
                                  <div
                                    className={`mr-2 flex h-4 w-4 items-center justify-center rounded-sm border ${
                                      field.value.includes(reviewKey)
                                        ? "bg-primary text-primary-foreground"
                                        : "opacity-50 [&_svg]:invisible"
                                    }`}
                                  >
                                    <Check className="size-4" />
                                  </div>
                                  {value}
                                </div>
                              </DropdownMultiSelectItem>
                            );
                          }
                        )}
                      </DropdownMultiSelectGroup>
                    </DropdownMultiSelectContent>
                  </DropdownMultiSelect>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="loaReviewResult"
            render={({ field }) => (
              <FormItem>
                <FormLabel>LOA Review Result</FormLabel>
                <FormControl>
                  <DropdownMultiSelect>
                    <DropdownMultiSelectTrigger
                      placeholder="Please select one or multiple statement for your review result"
                      fieldValue={field.value.map(
                        value =>
                          LOA_REVIEW_RESULT[
                            value as keyof typeof LOA_REVIEW_RESULT
                          ] || value
                      )}
                      className="not-italic"
                      childClassName="lg:max-w-[80ch]"
                    />
                    <DropdownMultiSelectContent className="w-[38ch]">
                      <DropdownMultiSelectGroup>
                        {Object.entries(LOA_REVIEW_RESULT).map(
                          ([key, value]) => {
                            const reviewKey = key as LoaReviewOption;
                            return (
                              <DropdownMultiSelectItem
                                key={reviewKey}
                                onSelect={() =>
                                  toggleLoaReviewOption(reviewKey, field.value)
                                }
                              >
                                <div className="flex items-center">
                                  <div
                                    className={`mr-2 flex h-4 w-4 items-center justify-center rounded-sm border ${
                                      field.value.includes(reviewKey)
                                        ? "bg-primary text-primary-foreground"
                                        : "opacity-50 [&_svg]:invisible"
                                    }`}
                                  >
                                    <Check className="size-4" />
                                  </div>
                                  {value}
                                </div>
                              </DropdownMultiSelectItem>
                            );
                          }
                        )}
                      </DropdownMultiSelectGroup>
                    </DropdownMultiSelectContent>
                  </DropdownMultiSelect>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="creditCheckReviewResult"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Credit Check Review Result</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger
                      className={cn(!field.value && "text-muted-foreground")}
                    >
                      <SelectValue placeholder="Please select one statement for your review result" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(CREDIT_CHECK_REVIEW_RESULT).map(
                        ([key, value]) => (
                          <SelectItem key={key} value={key}>
                            {value}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="complianceNextStage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Next Stage</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger
                      className={cn(!field.value && "text-muted-foreground")}
                    >
                      <SelectValue placeholder="Please select the next stage for this deal" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(NEXT_STAGE_OPTIONS).map(
                        ([key, value]) => (
                          <SelectItem key={key} value={key}>
                            {value}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="complianceReviewNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Compliance Review Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Please input a note for admin team reference or specify what changes are required"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex w-full flex-col gap-2">
            <Button type="submit" variant="secondary">
              Complete and Notify
            </Button>
            <Button type="button" variant="outline" onClick={() => onSave()}>
              Save and Continue
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
