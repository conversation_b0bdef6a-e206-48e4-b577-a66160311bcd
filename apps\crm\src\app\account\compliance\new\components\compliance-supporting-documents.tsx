import { FileSelectDropdown } from "@watt/crm/components/site/site-deals/deal-submission/file-select-dropdown";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from "@watt/crm/components/ui/form";
import { FolderDown } from "lucide-react";
import { useFormContext } from "react-hook-form";

export function SupportingDocuments() {
  const { control } = useFormContext();

  return (
    <>
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <h3 className="font-medium">Supporting Documents</h3>
          <Button type="button" variant="ghost" size="icon">
            <FolderDown className="size-4" />
          </Button>
        </div>
        <p className="mb-4 text-muted-foreground text-xs">
          Click "Download All" in the header to review content, or use the link
          icon to attach supporting documents from company, site, or meter
          files.
        </p>
      </div>

      <div className="space-y-6">
        <FormField
          control={control}
          name="contractDocumentIds"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FileSelectDropdown
                  label="Contract"
                  value={field.value}
                  onFileSelect={field.onChange}
                  allowMultiple
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="loaDocumentIds"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FileSelectDropdown
                  label="LOA"
                  value={field.value}
                  onFileSelect={field.onChange}
                  allowMultiple
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="otherDocumentIds"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FileSelectDropdown
                  label="Others"
                  value={field.value}
                  onFileSelect={field.onChange}
                  allowMultiple
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </>
  );
}
