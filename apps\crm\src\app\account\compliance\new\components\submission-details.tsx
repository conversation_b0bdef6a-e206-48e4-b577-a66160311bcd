import type { ComplianceCheck } from "@watt/api/src/types/compliance";
import { useFormContext } from "react-hook-form";

export function SubmissionDetails() {
  const { getValues } = useFormContext<ComplianceCheck>();
  const values = getValues();

  return (
    <div className="flex flex-col gap-6">
      <h3 className="font-medium">Submission Details</h3>
      <div className="flex justify-between">
        <p className="text-muted-foreground">COT Status</p>
        <p className="font-medium">
          {values.isChangeOfTenancy ? "Yes" : "Not a COT"}
        </p>
      </div>
      <div className="flex justify-between">
        <p className="text-muted-foreground">Credit Check</p>
        <p className="font-medium">{values.creditCheck}</p>
      </div>
      <div className="flex justify-between">
        <p className="text-muted-foreground">LOA</p>
        <p className="font-medium">{values.loa}</p>
      </div>
    </div>
  );
}
