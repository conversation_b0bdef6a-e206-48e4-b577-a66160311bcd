import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@watt/crm/components/ui/avatar";
import { Badge } from "@watt/crm/components/ui/badge";
import { Card, CardContent } from "@watt/crm/components/ui/card";

type SubmissionNotesProps = {
  submissionNotes: {
    id: string;
    note: string;
    createdAt: Date;
    createdBy: string;
    status: string;
  }[];
};
export function SubmissionNotes({ submissionNotes }: SubmissionNotesProps) {
  return (
    <>
      <h3 className="font-medium">Submission Notes</h3>
      <div className="space-y-4">
        {submissionNotes.map(note => (
          <div key={note.id} className="space-y-4">
            <div className="flex items-center gap-1.5 text-xs">
              <Avatar className="mr-2 size-6">
                <AvatarImage src="/avatars/01.png" alt={note.createdBy} />
                <AvatarFallback>SC</AvatarFallback>
              </Avatar>
              <p className="font-medium">{note.createdBy}</p>
              <p className="text-muted-foreground">
                {formatDate(note.createdAt, dateFormats.DD_MM_YYYY)}
              </p>
              <Badge
                className="bg-purple-200 text-purple-700 hover:bg-purple-300"
                variant="outline"
              >
                {note.status}
              </Badge>
            </div>
            <Card>
              <CardContent className="p-4 text-sm">{note.note}</CardContent>
            </Card>
          </div>
        ))}
      </div>
    </>
  );
}
