import { MicroPhonePermissions } from "@watt/common/src/components/svgs/microphone-permissions.svg";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { Lock } from "lucide-react";

export function MicroPhonePermissionsDialog(
  props: React.ComponentProps<typeof Dialog>
) {
  return (
    <Dialog {...props}>
      <DialogContent className="flex min-w-[725px] flex-row-reverse">
        <DialogHeader className="mt-10 flex flex-col gap-10">
          <DialogTitle className="t text-xl">
            <PERSON> has been blocked from using your microphone
          </DialogTitle>
          <DialogDescription>
            <ol className="list-inside list-decimal space-y-10 text-base">
              <li>
                Click the <Lock className="inline h-4 w-4" /> lock icon in your
                browser&apos;s address bar
              </li>
              <li>Turn on microphone</li>
            </ol>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <MicroPhonePermissions className="mx-auto h-[322px w-[322px] text-primary" />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
