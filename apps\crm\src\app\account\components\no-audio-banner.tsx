"use client";

import {
  <PERSON><PERSON><PERSON>riangle,
  ExternalLink,
  Lock,
  Settings,
  Volume2
} from "lucide-react";

import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";
import { useCanPlayAudio } from "@watt/crm/hooks/use-can-play-audio";

export function NoAudioBanner() {
  const canPlay = useCanPlayAudio();

  if (canPlay) {
    return null;
  }

  return (
    <div className="pointer-events-none absolute bottom-0 left-0 z-10 flex h-auto w-full items-center justify-center pb-4">
      <Card className="pointer-events-auto">
        <CardHeader className="gap-2">
          <div className="flex flex-row gap-1">
            <AlertTriangle />
            <CardTitle>You won&apos;t hear it ringing</CardTitle>
          </div>
          <CardDescription className="flex flex-col gap-2">
            <span>
              1. Click the{" "}
              <strong>
                padlock <Lock className="inline h-3 w-3" />
              </strong>{" "}
              to the left of the url.
            </span>
            <span>
              2. Click &lsquo;
              <strong>
                <Settings className="inline h-3 w-3" /> Site settings{" "}
                <ExternalLink className="inline h-3 w-3" />
              </strong>
              &rsquo;
            </span>
            <span>
              2. Set &lsquo;
              <strong>
                <Volume2 className="inline h-3 w-3" /> sound
              </strong>
              &rsquo; to &lsquo;<strong>allow</strong>&rsquo;
            </span>
          </CardDescription>
        </CardHeader>
      </Card>
    </div>
  );
}
