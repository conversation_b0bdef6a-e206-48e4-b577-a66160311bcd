import type { Notification } from "@novu/react";
import { log } from "@watt/common/src/utils/axiom-logger";
import { getTimeElapsed } from "@watt/common/src/utils/format-date";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { routes } from "@watt/crm/config/routes";
import { useNavigateToRoute } from "@watt/crm/hooks/use-navigate-to-route";
import {
  isNotificationEventsPayload,
  isNotificationPayload
} from "@watt/crm/utils/notification-utils";
import { parseDataProperties } from "@watt/crm/utils/parse-html-string";
import { truncateMessage } from "@watt/crm/utils/truncate-message";
import { truncateTitle } from "@watt/crm/utils/truncate-title";
import { NotificationType } from "@watt/db/src/enums";
import {
  Archive,
  ArchiveRestore,
  CheckSquare,
  MessageSquareDot
} from "lucide-react";
import { NotificationOverdueCallback } from "./notifications/notification-overdue-callback";
import { NotificationTodayCallbacks } from "./notifications/notification-today-callbacks";
import { NotificationUpcomingCallback } from "./notifications/notification-upcoming-callback";
import { NotificationVerifyCallback } from "./notifications/notification-verify-callback";

const unknownNotificationData = {
  subject: "Unknown Notification",
  redirectUrl: "/",
  body: <p className="text-sm">No additional details available.</p>
};

export function getNotificationDetails(notification: Notification) {
  if (!notification.data) {
    return unknownNotificationData;
  }

  const { payload, events } = notification.data;

  if (!isNotificationPayload(payload)) {
    log.error(`Malformed payload for notification: ${notification.id}`);
    return unknownNotificationData;
  }

  const formattedPayload = parseDataProperties(payload);

  switch (formattedPayload.type) {
    case NotificationType.UPCOMING_CALLBACK_NOTIFICATION: {
      return {
        subject: `Upcoming Callback Alert - ${formattedPayload.companyName}`,
        redirectUrl: `${routes.notifications}?filter=callbacks&notificationId=${notification.id}`,
        body: <NotificationUpcomingCallback payload={formattedPayload} />
      };
    }
    case NotificationType.TODAY_CALLBACK_NOTIFICATION:
      if (!isNotificationEventsPayload(events)) {
        log.error(
          "Malformed TODAY_CALLBACK_NOTIFICATION notification missing events"
        );
        return unknownNotificationData;
      }
      return {
        subject: "Today's Callback Reminder",
        redirectUrl: `${routes.notifications}?filter=callbacks&notificationId=${notification.id}`,
        body: <NotificationTodayCallbacks events={events} />
      };

    case NotificationType.VERIFY_CALLBACK_NOTIFICATION: {
      return {
        subject: `Verify Callback Completion - ${formattedPayload.companyName}`,
        redirectUrl: `${routes.notifications}?filter=callbacks&notificationId=${notification.id}`,
        body: <NotificationVerifyCallback notification={notification} />
      };
    }

    case NotificationType.OVERDUE_CALLBACK_NOTIFICATION: {
      return {
        subject: `Overdue Warning - ${formattedPayload.companyName}`,
        redirectUrl: `${routes.notifications}?filter=callbacks&notificationId=${notification.id}`,
        body: <NotificationOverdueCallback payload={formattedPayload} />
      };
    }

    case NotificationType.ANNOUNCEMENT_NOTIFICATION:
      return {
        subject: truncateTitle(formattedPayload.subject),
        body: (
          <p className="mt-2 text-sm">
            {truncateMessage(formattedPayload.message)}
          </p>
        ),
        redirectUrl: `${routes.notifications}?filter=announcements&notificationId=${notification.id}`
      };

    default:
      return unknownNotificationData;
  }
}

export function NotificationItem({
  notification
}: {
  notification: Notification;
}) {
  const navigateToRoute = useNavigateToRoute();
  const { isRead, createdAt, isArchived } = notification;
  const { subject, redirectUrl, body } = getNotificationDetails(notification);

  const onClick = () => {
    if (!isRead) {
      notification.read();
    }
    if (redirectUrl) {
      navigateToRoute(redirectUrl);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter" || e.key === " ") {
      onClick();
    }
  };

  const renderTimeElapsed = ({ createdAt }: { createdAt: string }) => {
    const timeElapsed = getTimeElapsed(createdAt);
    return (
      <span className="text-muted-foreground text-xs ">{`${timeElapsed} ago`}</span>
    );
  };

  return (
    <div
      className="group relative flex cursor-pointer items-center bg-background px-2 py-4 hover:bg-muted"
      onClick={onClick}
      onKeyDown={handleKeyDown}
    >
      <div className="mt-1 mr-2 flex-shrink-0 self-start">
        <div
          className={`size-3 rounded-full ${isRead ? "bg-foreground/30" : "bg-secondary"}`}
        />
      </div>
      <div className="min-w-0 flex-grow pr-2">
        <div className="relative flex items-center justify-between">
          <p className="font-medium text-sm">{subject}</p>
          {renderTimeElapsed({ createdAt })}

          <TooltipProvider>
            <div className="absolute right-0 rounded-md bg-muted opacity-0 transition-opacity group-hover:opacity-100">
              {!isRead && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      className="m-1 size-6 p-1"
                      onClick={e => {
                        e.stopPropagation();
                        notification.read();
                      }}
                    >
                      <CheckSquare className="size-4" />
                      <span className="sr-only">Mark as read</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Mark as read</TooltipContent>
                </Tooltip>
              )}
              {isRead && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      className="m-1 size-6 p-1"
                      onClick={e => {
                        e.stopPropagation();
                        notification.unread();
                      }}
                    >
                      <MessageSquareDot className="size-4" />
                      <span className="sr-only">Mark as unread</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Mark as unread</TooltipContent>
                </Tooltip>
              )}

              {!isArchived && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      className="m-1 size-6 p-1"
                      onClick={e => {
                        e.stopPropagation();
                        notification.archive();
                      }}
                    >
                      <Archive className="size-4" />
                      <span className="sr-only">Archive</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent hidden={isArchived}>
                    Archive notification
                  </TooltipContent>
                </Tooltip>
              )}
              {isArchived && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      className="m-1 size-6 p-1"
                      onClick={e => {
                        e.stopPropagation();
                        notification.unarchive();
                      }}
                    >
                      <ArchiveRestore className="size-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent hidden={!isArchived}>
                    Unarchive notification
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </TooltipProvider>
        </div>
        <div className="mr-2">{body}</div>
      </div>
    </div>
  );
}
