"use client";

import { Bell, Inbox, InboxContent, type Notification } from "@novu/react";
import { dark } from "@novu/react/themes";
import { env } from "@watt/common/src/config/env";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { buttonVariants } from "@watt/crm/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { NOTIFICATION_TABS } from "@watt/crm/config/notifications";
import { useAppStore } from "@watt/crm/store/app-store";
import { BellIcon } from "lucide-react";
import { useTheme } from "next-themes";
import { useState } from "react";
import { NotificationItem } from "./notification-item";

function NotificationBell() {
  return (
    <div className={buttonVariants({ variant: "ghost", size: "icon" })}>
      <Bell
        renderBell={unreadCount => (
          <div className="relative pr-0">
            <BellIcon className="size-4" />
            {unreadCount > 0 && (
              <span
                className={cn(
                  "absolute",
                  "items-center justify-center rounded-full bg-red-500 font-bold text-white text-xs",
                  unreadCount > 1
                    ? "-top-2 -right-2 size-4"
                    : "-top-1 -right-1 size-2",
                  unreadCount > 9 && "-top-2 -right-4 h-4 w-6"
                )}
              >
                {unreadCount > 9 ? "9+" : unreadCount > 1 ? unreadCount : ""}
              </span>
            )}
          </div>
        )}
      />
    </div>
  );
}

export function NotificationMenu() {
  const [isInboxOpen, setIsInboxOpen] = useState(false);
  const { resolvedTheme } = useTheme();
  const { user } = useAppStore(state => state.userData);
  const renderNotification = (notification: Notification) => {
    return <NotificationItem notification={notification} />;
  };

  /**
   * Novu react's limitation: `InboxContent` component does not automatically update the notification state when a notification is marked as read/unread in another window
   * Or through an external action for e.g Notifications table row action.
   * TODO: Investigate using Novu's event system or custom refresh mechanism instead of key manipulation
   */
  const handleOpenChange = (open: boolean) => {
    setIsInboxOpen(open);
  };

  if (!user) {
    return <NotificationMenuSkeleton />;
  }

  const id = user.id;

  return (
    <>
      <Inbox
        appearance={{
          baseTheme: {
            ...(resolvedTheme === "dark" ? dark : {}),
            variables: {
              ...(resolvedTheme === "dark" ? dark.variables : {}),
              colorPrimary: "var(--badge-green)"
            }
          },
          elements: {
            notificationListEmptyNoticeContainer: {
              position: "relative",
              justifyContent: "center",
              height: "300px"
            },
            notificationsTabsTriggerCount: {
              minHeight: "20px",
              minWidth: "20px"
            }
          }
        }}
        applicationIdentifier={env.NEXT_PUBLIC_NOVU_APPLICATION_IDENTIFIER}
        backendUrl={env.NEXT_PUBLIC_NOVU_API_URL}
        socketUrl={env.NEXT_PUBLIC_NOVU_SOCKET_URL}
        subscriberId={id}
        tabs={NOTIFICATION_TABS}
      >
        <Popover open={isInboxOpen} onOpenChange={handleOpenChange}>
          <PopoverTrigger>
            <NotificationBell />
          </PopoverTrigger>
          <PopoverContent className="w-[400px] p-0">
            <div className="h-[500px] overflow-auto">
              <InboxContent renderNotification={renderNotification} />
            </div>
          </PopoverContent>
        </Popover>
      </Inbox>
    </>
  );
}

export function NotificationMenuSkeleton() {
  return (
    <div className="flex size-10 items-center justify-center">
      <Skeleton className="size-6 rounded-md" />
    </div>
  );
}
