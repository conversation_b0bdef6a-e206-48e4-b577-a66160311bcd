import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import type { OverdueCallbackPayload } from "@watt/notifications/src/novu";

export const NotificationOverdueCallback = ({
  payload
}: {
  payload: OverdueCallbackPayload;
}) => {
  return (
    <p className="mt-2 text-sm">
      {composeSiteRef(payload.siteRefId)}{" "}
      <span className="font-medium">Action Required:</span> Follow up within 24
      hours or callback will be released by system.
    </p>
  );
};
