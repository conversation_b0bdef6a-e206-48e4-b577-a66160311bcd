import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { parseDataProperties } from "@watt/crm/utils/parse-html-string";
import type { CallbackNotificationEvents } from "@watt/notifications/src/novu";

export const NotificationTodayCallbacks = ({
  events
}: {
  events: CallbackNotificationEvents;
}) => {
  return events.map(event => {
    const formattedPayload = parseDataProperties(event.payload);
    const { companyName, siteRefId, callbackScheduledTime, contactName } =
      formattedPayload;
    return (
      <p className="mt-2 text-sm leading-tight" key={event.id}>
        <span className="font-medium">{companyName || ""}</span> -{" "}
        {composeSiteRef(siteRefId)} at {callbackScheduledTime} with{" "}
        {contactName}
      </p>
    );
  });
};
