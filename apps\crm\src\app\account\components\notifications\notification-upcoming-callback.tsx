import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import type { UpcomingCallbackPayload } from "@watt/notifications/src/novu";

export const NotificationUpcomingCallback = ({
  payload
}: {
  payload: UpcomingCallbackPayload;
}) => {
  const { siteRefId, callbackScheduledTime, contactName } = payload || {};
  return (
    <p className="mt-2 text-sm">
      {composeSiteRef(siteRefId)} - {callbackScheduledTime} with {contactName}
    </p>
  );
};
