import type { Notification } from "@novu/react";
import { isCallbackDismissed } from "@watt/common/src/utils/callback-dates";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";
import { trpcClient } from "@watt/crm/utils/api";
import type { VerifyCallbackPayload } from "@watt/notifications/src/novu";

export const NotificationVerifyCallback = ({
  notification
}: {
  notification: Notification;
}) => {
  const completeCallbackMutation = trpcClient.callback.complete.useMutation();
  const { payload } = notification.data as { payload: VerifyCallbackPayload };
  const isDismissed = isCallbackDismissed(payload.callbackTime);

  const onCompleteCallbackPress = async (
    e: React.MouseEvent<HTMLButtonElement>
  ) => {
    e.stopPropagation();
    notification.read();
    if (!payload || completeCallbackMutation.isPending) {
      return;
    }
    try {
      await completeCallbackMutation.mutateAsync({
        id: payload.callbackId
      });
      toast({
        title: "Callback completed",
        description: `Call with ${payload.companyName} has been marked as completed`,
        variant: "success",
        duration: 10000
      });
    } catch (error) {
      const errorMessage =
        (error as Error).message ?? "Unable to complete callback";
      toast({
        title: "Callback unavailable",
        description: errorMessage,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  return (
    <>
      <p className="mt-2 text-sm">
        {composeSiteRef(payload.siteRefId)}{" "}
        <span className="font-medium">Action Required</span>: Click Complete to
        confirm callback completion
      </p>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="secondary"
              className="mt-4"
              disabled={isDismissed}
              onClick={onCompleteCallbackPress}
            >
              Complete
            </Button>
          </TooltipTrigger>
          <TooltipContent hidden={!isDismissed}>
            <p className="font-medium">Callback unavailable:</p>
            <p className="my-2 text-muted-foreground text-sm">
              Automatically cancelled due to overdue status
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};
