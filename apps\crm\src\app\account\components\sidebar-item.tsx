"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { ExternalLink, Sparkle, icons } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import type { NavigationItemData } from "./sidebar";

interface SideBarItemProps
  extends NavigationItemData,
    React.HTMLAttributes<HTMLDivElement> {
  isCollapsed: boolean;
}

export function SideBarItem({
  name,
  label,
  href,
  target,
  disabled,
  isNew,
  iconName,
  isCollapsed
}: SideBarItemProps) {
  const pathname = usePathname();

  // If the sidebar href is more than 2 levels deep, we need to check if the pathname includes the first 2 levels of the href
  // E.g /account/notes/all and /account/notes/mine are both active if the pathname is /account/notes
  const twoLevelHref = href.split("/").slice(0, 3).join("/");
  const activeTab = pathname.includes(twoLevelHref);

  const Icon = icons[iconName as keyof typeof icons];
  const buttonContent = isCollapsed ? (
    <Tooltip delayDuration={0}>
      <TooltipTrigger asChild>
        <Button
          disabled={disabled}
          variant={activeTab ? "secondary" : "ghost"}
          size="icon"
          className={cn("h-9 w-9")}
        >
          <Icon className="h-4 w-4" />
          <span className="sr-only fixed">{name}</span>
        </Button>
      </TooltipTrigger>
      <TooltipContent side="right" className="flex items-center gap-4">
        {name}
        {label && (
          <span className="ml-auto text-muted-foreground">{label}</span>
        )}
        {isNew && (
          <Badge className="bg-blue-400 px-1 hover:bg-blue-500">
            <div className="flex flex-row items-center justify-center gap-1 align-middle text-white">
              <Sparkle className="h-3 w-3" />
              <span className="text-xs">New</span>
            </div>
          </Badge>
        )}
        {target && (
          <div className="">
            <ExternalLink className="h-4 w-4" />
          </div>
        )}
      </TooltipContent>
    </Tooltip>
  ) : (
    <Button
      variant={activeTab ? "secondary" : "ghost"}
      disabled={disabled}
      className={cn("w-full justify-start")}
    >
      <span className="flex w-full flex-shrink-0 items-center justify-start gap-2">
        <Icon className="h-4 w-4" />
        {name}
      </span>
      {label && (
        <span
          className={cn(
            "ml-auto",
            activeTab && "text-background dark:text-white"
          )}
        >
          {label}
        </span>
      )}
      {isNew && (
        <Badge className="relative top-0 right-12 bg-blue-400 px-1 hover:bg-blue-500">
          <div className="flex flex-row items-center justify-center gap-1 align-middle text-white ">
            <Sparkle className="h-3 w-3" />
            <span className="text-xs">New</span>
          </div>
        </Badge>
      )}
      {target && (
        <div className="relative top-0 right-2 ">
          <ExternalLink className="h-4 w-4" />
        </div>
      )}
    </Button>
  );

  return disabled ? (
    buttonContent
  ) : (
    <Link href={href} {...(target ? { target } : {})}>
      {buttonContent}
    </Link>
  );
}
