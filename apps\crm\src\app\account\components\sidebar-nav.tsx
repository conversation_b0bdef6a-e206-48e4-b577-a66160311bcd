"use client";

import type { NavigationItemData } from "./sidebar";
import { SideBarItem } from "./sidebar-item";

interface NavProps {
  isCollapsed: boolean;
  links: NavigationItemData[];
}

export function SidebarNav({ links, isCollapsed }: NavProps) {
  return (
    <div
      data-collapsed={isCollapsed}
      className="group flex flex-col gap-4 py-2 data-[collapsed=true]:py-2"
    >
      <nav className="grid gap-1 px-2 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2">
        {links.map(link => (
          <SideBarItem key={link.href} isCollapsed={isCollapsed} {...link} />
        ))}
      </nav>
    </div>
  );
}
