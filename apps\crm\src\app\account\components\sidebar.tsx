import { env } from "@watt/common/src/config/env";
import { capitalize } from "@watt/common/src/utils/capitalise";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { isProductionEnvironment } from "@watt/common/src/utils/is-production-environment";
import { CommandMenu } from "@watt/crm/components/command-menu";
import { ScrollArea } from "@watt/crm/components/ui/scroll-area";
import { Separator } from "@watt/crm/components/ui/separator";
import { routes } from "@watt/crm/config/routes";
import { featureToggles } from "@watt/crm/feature-toggles";
import type { icons } from "lucide-react";
import { UserNav } from "../components/user-nav";
import { NotificationMenu } from "./notification-menu";
import { SidebarNav } from "./sidebar-nav";

export type NavigationItemData = {
  name: string;
  label?: string;
  href: string;
  target?: string;
  iconName: keyof typeof icons;
  disabled?: boolean;
  isNew?: boolean;
};

const navigationItems: Record<string, NavigationItemData[]> = {
  main: [
    {
      name: "Dashboard",
      href: routes.dashboard,
      iconName: "LayoutDashboard",
      disabled: !featureToggles.routes.dashboard
    },
    { name: "Calls", href: routes.calls, iconName: "Phone" },
    {
      name: "Interactions",
      href: routes.interactions,
      iconName: "MessagesSquare",
      disabled: !featureToggles.routes.interactions
    },
    {
      name: "Notes",
      href: routes.notes,
      iconName: "FileText",
      disabled: !featureToggles.routes.notes
    },
    { name: "Users", href: routes.users, iconName: "Users" },
    {
      name: "Notifications",
      href: routes.notifications,
      iconName: "Bell",
      disabled: !featureToggles.routes.notifications
    },
    {
      name: "Price Lists",
      href: routes.priceLists,
      iconName: "CircleDollarSign",
      disabled: !featureToggles.routes.priceLists
    },
    {
      name: "Compliance",
      href: routes.compliance.new,
      iconName: "FileCheck2",
      disabled: !featureToggles.routes.compliance
    }
  ],
  pool: [
    {
      name: "Companies",
      href: routes.companies,
      iconName: "Building2",
      disabled: !featureToggles.routes.companies,
      isNew: true
    },
    {
      name: "Sites",
      href: routes.sites,
      iconName: "MapPin",
      disabled: !featureToggles.routes.sites
    },
    // Hide people for now - TAS-2204
    // {
    //   name: "People",
    //   href: routes.people,
    //   Icon: Icons.people,<FilePen />
    //   disabled: !featureToggles.routes.people
    // },
    {
      name: "Quotes",
      href: routes.quotes,
      iconName: "TextQuote",
      disabled: !featureToggles.routes.quotes
    },
    // Hide credit check for now - TAS-2204
    // {
    //   name: "Credit Check",
    //   href: routes.creditCheck,
    //   Icon: Icons.creditCheck,
    //   disabled: !featureToggles.routes.creditCheck
    // },
    {
      name: "Contracts",
      href: routes.contracts,
      iconName: "FilePen",
      disabled: !featureToggles.routes.contracts
    },
    {
      name: "LOA",
      href: routes.loa,
      iconName: "Scale",
      disabled: !featureToggles.routes.loa
    }
  ],
  lists: [
    {
      name: "My Callbacks",
      href: routes.callbacks,
      iconName: "PhoneCall",
      disabled: !featureToggles.routes.callbacks,
      isNew: true
    }
  ],
  admin: [
    // Hide statistics for now - TAS-2204
    // {
    //   name: "Statistics",
    //   href: routes.statistics,
    //   target: "_blank",
    //   iconName: "statistics",
    //   disabled: !featureToggles.routes.statistics
    // },
    { name: "All Calls", href: routes.adminCalls, iconName: "Phone" },
    {
      name: "Verbal Templates",
      href: routes.verbalTemplates,
      iconName: "SmartphoneNfc"
    }
  ]
};

type SidebarProps = {
  isCollapsed: boolean;
  isAuthorised: boolean;
};

function getEnvironmentDisplay(): string | null {
  if (isProductionEnvironment()) {
    return null;
  }

  return capitalize(env.NEXT_PUBLIC_ENVIRONMENT);
}

export function Sidebar({ isCollapsed, isAuthorised }: SidebarProps) {
  const environmentDisplay = getEnvironmentDisplay();

  return (
    <div className="relative flex h-screen flex-shrink-0 flex-col">
      <div
        className={cn(
          "flex flex-row items-center justify-center gap-2 px-2 py-2",
          isCollapsed && "flex-col",
          environmentDisplay && "environment-indicator-stripe"
        )}
      >
        <UserNav isCollapsed={isCollapsed} />
        <div
          className={cn(
            !isCollapsed && "flex h-9 items-center justify-center pr-2",
            !featureToggles.routes.notifications && "hidden"
          )}
        >
          <NotificationMenu />
        </div>
      </div>

      <Separator />
      <div
        className={cn(
          "flex flex-col justify-center gap-2 px-2 pt-4",
          isCollapsed && "items-center"
        )}
      >
        <CommandMenu isCollapsed={isCollapsed} />
      </div>

      <ScrollArea>
        <SidebarNav
          isCollapsed={isCollapsed}
          links={navigationItems.main || []}
        />
        <Separator />
        {!isCollapsed && (
          <h2 className="mt-2 px-2 font-semibold text-md tracking-tight">
            Pool
          </h2>
        )}
        <SidebarNav
          isCollapsed={isCollapsed}
          links={navigationItems.pool || []}
        />
        {!isCollapsed && (
          <h2 className="mt-2 px-2 font-semibold text-md tracking-tight">
            Lists
          </h2>
        )}
        <SidebarNav
          isCollapsed={isCollapsed}
          links={navigationItems.lists || []}
        />
        {isAuthorised && (
          <>
            <Separator />
            {!isCollapsed && (
              <h2 className="mt-2 px-2 font-semibold text-md tracking-tight">
                Admin
              </h2>
            )}
            <SidebarNav
              isCollapsed={isCollapsed}
              links={navigationItems.admin || []}
            />
          </>
        )}
      </ScrollArea>

      {/* Environment indicator at the bottom right of the sidebar */}
      {environmentDisplay && (
        <div className="environment-indicator-stripe absolute right-2 bottom-2 rounded px-3 py-2 font-medium text-muted-foreground text-xs">
          {environmentDisplay}
        </div>
      )}
    </div>
  );
}
