import * as Tooltip from "@radix-ui/react-tooltip";
import type { PropsWithChildren } from "react";

type SmallTooltipProps = PropsWithChildren<{
  tooltipText: string;
}>;

export function SmallTooltip(props: SmallTooltipProps) {
  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>{props.children}</Tooltip.Trigger>
      <Tooltip.Content
        side="bottom"
        className="z-50 rounded-md bg-[#4B4B4B] bg-opacity-100 p-2 text-white shadow-lg"
      >
        <p>{props.tooltipText}</p>
      </Tooltip.Content>
    </Tooltip.Root>
  );
}
