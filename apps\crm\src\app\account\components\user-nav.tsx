"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { UserAvatar } from "@watt/crm/components/avatar/user-avatar";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { routes } from "@watt/crm/config/routes";
import { useAuthentication } from "@watt/crm/hooks/use-authentication";
import { useAppStore } from "@watt/crm/store/app-store";
import type { UserRole } from "@watt/db/src/enums";
import type { SupabaseProfileData } from "@watt/db/src/supabase/user-type";
import { getRoleDisplayName } from "@watt/db/src/utils/get-role-display-name";
import { Match, pipe } from "effect";
import {
  ChevronsUpDown,
  Laptop,
  LogOut,
  Moon,
  Settings,
  Sun,
  SunMoon
} from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";
import { useEffect } from "react";

function extractProfileData(profileData: SupabaseProfileData | null) {
  return pipe(
    Match.value(profileData),
    // Match a "non-nullish" profileData
    Match.when(
      (pd): pd is NonNullable<typeof pd> => pd != null,
      pd => ({
        email: pd.email,
        forename: pd.forename,
        surname: pd.surname,
        directDial: pd.directDial,
        directDialE164: pd.directDialE164,
        // If “role” can be null or undefined, further refine here.
        // Otherwise, assume it’s always valid when pd != null.
        role: pd.role,
        huntGroups: pd.huntGroups ?? [],
        disabled: pd.disabled,
        userId: pd.userId
      })
    ),
    // Fallback for null or undefined profileData
    Match.orElse(() => ({
      id: "",
      email: "",
      forename: "",
      surname: "",
      directDial: "",
      directDialE164: "",
      // Default to “SALES_AGENT” if no role is found
      role: "SALES_AGENT" as UserRole,
      huntGroups: [],
      disabled: false,
      userId: "",
      createdAt: new Date(),
      updatedAt: new Date()
    }))
  );
}

/**
 * Subcomponent that displays the user profile portion of the dropdown menu.
 * If `profileData` or `device` is missing, it shows skeleton placeholders.
 */
function UserNavDisplay({
  profileData
}: {
  profileData: SupabaseProfileData;
}) {
  // Pattern match once. We get a consistent object every time.
  const { forename, email, directDial, role, huntGroups } =
    extractProfileData(profileData);

  return (
    <>
      <DropdownMenuLabel className="font-normal">
        {/* Top Section: Name / Role / Email / Phone */}
        <div className="flex flex-col space-y-1">
          <div className="flex justify-between gap-2 text-center">
            {/* forename is always a string now (could be "" if no profile) */}
            {!forename ? <Skeleton className="h-4 w-[6ch]" /> : forename}
            {role ? (
              <Badge className="border-purple-500 bg-purple-500/25 text-foreground hover:bg-purple-500/50">
                {getRoleDisplayName(role)}
              </Badge>
            ) : (
              <Skeleton className="h-5 w-[4ch] rounded" />
            )}
          </div>

          {/* Email */}
          {!email ? (
            <Skeleton className="h-3 w-[14ch]" />
          ) : (
            <p className="text-muted-foreground text-xs leading-none">
              {email}
            </p>
          )}

          {/* Phone */}
          {directDial && (
            <p className="text-muted-foreground text-xs leading-none">
              {formatPhoneNumber(directDial)}
            </p>
          )}
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
    </>
  );
}

/**
 * Subcomponent for user nav actions:
 * - device status (offline/online)
 * - theme selector
 * - settings
 * - logout
 * This is always rendered functionally so that even if data is missing, the user
 * can still perform these actions.
 */
function UserNavActions({
  theme,
  setTheme
}: {
  theme: string | undefined;
  setTheme: (theme: string) => void;
}) {
  const { handleSignOut } = useAuthentication();
  return (
    <>
      <DropdownMenuGroup>
        {/* <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Globe className="mr-2 h-4 w-4" />
            <span>Status {capitalize(device?.state || "Offline")}</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              <DropdownMenuItem onClick={onSetOffline}>
                <CircleOff className="mr-2 h-4 w-4" />
                <span>Offline</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onSetOnline}>
                <Circle className="mr-2 h-4 w-4" />
                <span>Available</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub> */}

        <Link
          href={routes.settings}
          className="font-medium text-sm transition-colors hover:text-primary"
        >
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </DropdownMenuItem>
        </Link>

        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <SunMoon className="mr-2 h-4 w-4" />
            <span>Theme</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              <DropdownMenuItem onClick={() => setTheme("light")}>
                <Sun className="mr-2 h-4 w-4" />
                <span className={cn(theme === "light" && "font-semibold")}>
                  Light
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("dark")}>
                <Moon className="mr-2 h-4 w-4" />
                <span className={cn(theme === "dark" && "font-semibold")}>
                  Dark
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("system")}>
                <Laptop className="mr-2 h-4 w-4" />
                <span className={cn(theme === "system" && "font-semibold")}>
                  System
                </span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem onClick={handleSignOut}>
        <LogOut className="mr-2 h-4 w-4" />
        <span>Log out</span>
      </DropdownMenuItem>
    </>
  );
}

interface UserNavProps {
  isCollapsed: boolean;
}

export function UserNav({ isCollapsed }: UserNavProps) {
  const { theme, setTheme } = useTheme();

  const { profileData: profileDataFromStore } = useAppStore(
    state => state.userData
  );

  const profileData = extractProfileData(profileDataFromStore);

  const { forename, surname } = profileData;

  useEffect(() => {
    // Rehydrate the store on the client side
    useAppStore.persist.rehydrate();
  }, []);

  // A separate subcomponent for the "trigger" (the button you click to open the menu)
  // If data is pending, we show partial skeleton for the user's name, but still show an avatar ring.
  function renderTrigger() {
    return (
      <Button
        variant="ghost"
        className={cn("flex-1 justify-between p-2")}
        size="sm"
      >
        {/* TODO: (Sohail) - class variance authority approach here instead of making different versions of Avatar. */}
        {/* TODO: (Sohail) - need to add fullName in future */}
        <UserAvatar
          fullName={`${forename} ${surname}`}
          className="h-8 w-8 border-2 border-secondary"
        />

        {/* If not collapsed, we either show the name or a skeleton */}
        {!isCollapsed && (
          <div className="flex items-center pl-2">
            {/* Fixed-width container so skeleton & name occupy the same space */}
            <div className="inline-block w-[8ch]">
              {!forename ? (
                // Skeleton with height & width matching approximate line height and length
                <Skeleton className="h-5 w-full" />
              ) : (
                <div className="block overflow-hidden truncate">{forename}</div>
              )}
            </div>
          </div>
        )}

        {/* Chevron */}
        {!isCollapsed && (
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        )}
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{renderTrigger()}</DropdownMenuTrigger>
      <DropdownMenuContent className="mx-2 w-56" align="end" forceMount>
        {/* Profile display portion: skeleton placeholders if data is missing */}
        <UserNavDisplay profileData={profileData} />
        {/* Always functional actions */}
        <UserNavActions theme={theme} setTheme={setTheme} />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
