"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { FindManyContractsGetPayload } from "@watt/api/src/service/contract";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { ContractStatusBadge } from "@watt/crm/components/quick-actions/quote/status/contract-status-badge";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { routes } from "@watt/crm/config/routes";
import type { ContractStatus } from "@watt/db/src/enums";
import Link from "next/link";
import { DataTableRowActions, type MyContract } from "./data-table-row-actions";

type ContractQuote = FindManyContractsGetPayload["quote"];
type UtilityQuote = ContractQuote["electricQuote"] | ContractQuote["gasQuote"];

function extractQuoteValue<T>(
  quote: ContractQuote,
  extractor: (utilityQuote: UtilityQuote) => T | undefined
): T | undefined {
  return quote.electricQuote
    ? extractor(quote.electricQuote)
    : quote.gasQuote
      ? extractor(quote.gasQuote)
      : undefined;
}

export const columns: ColumnDef<MyContract>[] = [
  {
    accessorKey: "companyName",
    accessorFn: row => row.company.name,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ row, getValue }) => {
      const companyId = row.original.company.id;
      const siteRefId = row.original.site.siteRefId;
      const companyName = humanize(getValue<string>());

      return (
        <div className="flex flex-wrap items-center gap-2">
          <Link
            href={`${routes.company.replace("[id]", "")}${companyId}/activity`}
            className={cn(
              buttonVariants({ variant: "link", size: "sm" }),
              "h-auto px-0"
            )}
          >
            {companyName}
          </Link>
          {siteRefId && companyId && (
            <Link
              href={`${routes.company.replace("[id]", "")}${companyId}/sites/${siteRefId}`}
              className={cn(
                buttonVariants({ variant: "link", size: "sm" }),
                "h-auto px-0"
              )}
            >
              {composeSiteRef(siteRefId)}
            </Link>
          )}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "entityAddress",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site Address" />
    ),
    cell: ({ row }) => {
      const entityAddress = row.original.site.entityAddress;
      return (
        <div className="w-[250px]">{getAddressDisplayName(entityAddress)}</div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site Address"
    }
  },
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Start Date" />
    ),
    cell: ({ getValue }) => {
      const contractStartDate = getValue<Date | null>();
      if (contractStartDate) {
        const formattedDate = formatDate(
          contractStartDate,
          dateFormats.DD_MM_YYYY
        );
        return <span>{formattedDate}</span>;
      }
      return <div>N/A</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Start Date"
    }
  },
  {
    accessorKey: "endDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="End Date" />
    ),
    cell: ({ getValue }) => {
      const contractEndDate = getValue<Date | null>();
      if (contractEndDate) {
        const formattedDate = formatDate(
          contractEndDate,
          dateFormats.DD_MM_YYYY
        );
        return <span>{formattedDate}</span>;
      }
      return <div>N/A</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "End Date"
    }
  },
  {
    accessorKey: "term",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Term" />
    ),
    cell: ({ getValue }) => {
      return <div>{getValue<string>()}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Term"
    }
  },
  {
    accessorKey: "supplier",
    accessorFn: contract => contract.quote.provider.udcoreId,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Supplier" />
    ),
    cell: ({ getValue, row }) => {
      const supplier = getValue() as string;
      const { logoFileName, displayName } = row.original.quote.provider;

      return (
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={displayName}
          className="h-auto w-[60px] object-scale-down"
          responsive
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Supplier"
    }
  },
  {
    accessorKey: "unitRateUplift",
    accessorFn: contract =>
      extractQuoteValue(contract.quote, q => q?.unitRateUplift),
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Uplift" />
    ),
    cell: ({ getValue }) => {
      return <div>{getValue<string>()}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Uplift"
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Signed Date" />
    ),
    cell: ({ getValue }) => {
      const createdAt = getValue<Date | null>();
      if (createdAt) {
        const formattedDate = formatDate(createdAt, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(createdAt, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Signed Date"
    }
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue<ContractStatus>("status");

      return <ContractStatusBadge status={status} />;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <DataTableRowActions
        contractId={row.original.id}
        contractType={row.original.type}
      />
    ),
    filterFn: textFilter
  }
];
