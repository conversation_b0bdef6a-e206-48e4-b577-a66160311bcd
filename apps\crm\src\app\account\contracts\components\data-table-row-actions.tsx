"use client";

import { ContractType } from "@prisma/client";
import type { MyContracts } from "@watt/api/src/router/contract";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import {
  ContractModalTypes,
  useContractModal
} from "@watt/crm/hooks/use-contract-modal";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import { EyeIcon, MoreHorizontalIcon } from "lucide-react";

export type MyContract = ExtractElementType<MyContracts["items"]>;

type DataTableRowActionsProps = {
  contractId: string;
  contractType: ContractType;
};

export function DataTableRowActions({
  contractId,
  contractType
}: DataTableRowActionsProps) {
  const { openContractModal } = useContractModal();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex size-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontalIcon className="size-4" />
          <span className="sr-only fixed">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuItem
          onClick={() => {
            openContractModal(
              contractType === ContractType.VERBAL
                ? ContractModalTypes.viewVerbalContract
                : ContractModalTypes.viewWrittenContract,
              { contractId }
            );
          }}
        >
          <EyeIcon className="mr-2 size-3.5 text-muted-foreground/70" />
          View Contract
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
