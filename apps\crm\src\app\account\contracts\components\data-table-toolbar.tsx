"use client";

import type { Table } from "@tanstack/react-table";
import {
  createDateObjectSchema,
  createZodEnumArray
} from "@watt/common/src/utils/zod-literal-union";
import { statuses } from "@watt/crm/common-data/contracts/data";
import { getUniqueSupplierFilterOptions } from "@watt/crm/utils/get-unique-filter-options";
import { X } from "lucide-react";
import { z } from "zod";

import { DataTableDateRangeFilter } from "@watt/crm/components/data-table/data-table-date-range-filter";
import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered,
  children
}: DataTableToolbarProps<TData>) {
  const uniqueSuppliers = getUniqueSupplierFilterOptions(
    "supplier",
    table,
    true
  );
  const queryParamsSchema = z.object({
    status: createZodEnumArray(statuses),
    startDate: createDateObjectSchema(),
    endDate: createDateObjectSchema(),
    search: z.string().optional(),
    createdAt: createDateObjectSchema(),
    ...(uniqueSuppliers.length && {
      supplier: createZodEnumArray(uniqueSuppliers)
    })
  });

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        {table.getColumn("startDate") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("startDate") as any}
            title="Start Date"
            allowFutureDates
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("endDate") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("endDate") as any}
            title="End Date"
            allowFutureDates
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("supplier") && (
          <DataTableFacetedFilter
            column={table.getColumn("supplier")}
            title="Supplier"
            options={uniqueSuppliers}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("createdAt") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("createdAt") as any}
            title="Signed Date"
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("status") && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={statuses}
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex space-x-2">
        <DataTableViewOptions table={table} />
        {children}
      </div>
    </div>
  );
}
