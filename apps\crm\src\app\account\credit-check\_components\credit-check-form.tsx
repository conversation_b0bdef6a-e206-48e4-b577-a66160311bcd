"use client";

import { Loader2 } from "lucide-react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useCreditCheckStore } from "@watt/crm/store/credit-check";
import { trpcClient } from "@watt/crm/utils/api";
import type { BusinessTargetResult } from "@watt/external-apis/src/libs/experian/business-targeter";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useDebounce } from "react-use";
import * as z from "zod";

import { isValidBusinessReference } from "@watt/common/src/regex/company-number";
import { isValidPostcode } from "@watt/common/src/regex/postcode";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  CompanySelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/crm/components/react-hook-form/form";
import { Button } from "@watt/crm/components/ui/button";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";

const CreditCheckFormSchema = z.object({
  companyNumber: z.string().refine(isValidBusinessReference, {
    message: "Please enter a valid business reference."
  })
});

type CreditCheckFormValues = z.infer<typeof CreditCheckFormSchema>;

interface QueryParams {
  search: string;
  selectedRef: string;
}

type ResultGroups<T = BusinessTargetResult[]> = {
  active: T;
  disolved: T;
};

function groupSearchResults(results: BusinessTargetResult[]): ResultGroups {
  return {
    active: results.filter(company => company.businessStatus !== "D"),
    disolved: results.filter(company => company.businessStatus === "D")
  };
}

export function CreditCheckForm() {
  const [isCompanyModalOpen, setIsCompanyModalOpen] = useState(false);
  const { queryParams, setQueryParams } =
    useQueryParams<Partial<QueryParams>>();
  const creditCheckUtils = trpcClient.useUtils().creditCheck;

  const {
    companySearchResults,
    setCompanySearchResults,
    creditCheckData,
    setCreditCheckData,
    search,
    setSearch,
    selectedRef,
    setSelectedRef
  } = useCreditCheckStore(state => ({
    companySearchResults: state.companySearchResults,
    setCompanySearchResults: state.setCompanySearchResults,
    creditCheckData: state.creditCheckData,
    setCreditCheckData: state.setCreditCheckData,
    search: state.search,
    setSearch: state.setSearch,
    selectedRef: state.selectedRef,
    setSelectedRef: state.setSelectedRef
  }));

  const creditCheckMutation = trpcClient.creditCheck.check.useMutation({
    onSuccess: async () => {
      await creditCheckUtils.invalidate();
    }
  });

  const businessTargetMutation = trpcClient.businessTarget.get.useMutation();

  const form = useForm<CreditCheckFormValues>({
    resolver: zodResolver(CreditCheckFormSchema),
    mode: "onChange",
    defaultValues: {
      companyNumber: queryParams.selectedRef ?? ""
    }
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function setQueryParamsForExistingCreditCheckData() {
    if (!creditCheckData) {
      return;
    }

    if (
      queryParams.search === search &&
      queryParams.selectedRef === selectedRef
    ) {
      return;
    }

    setQueryParams({ search, selectedRef });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function defaultSearchToSelectedReferenceOnMount() {
    if (queryParams.search || !queryParams.selectedRef) {
      return;
    }

    setQueryParams({ search: queryParams.selectedRef });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(
    function clearUnavailableSelectedRef() {
      const selectedRef = queryParams.selectedRef;
      if (!selectedRef || !companySearchResults?.active.length) {
        return;
      }

      if (
        companySearchResults.active.find(
          company => company.businessRef === selectedRef.toLocaleUpperCase()
        )
      ) {
        return;
      }

      if (queryParams.search) {
        setQueryParams({ selectedRef: undefined });
      }
    },
    [
      companySearchResults?.active,
      queryParams.search,
      queryParams.selectedRef,
      setQueryParams
    ]
  );

  function fullReset() {
    form.setValue("companyNumber", "");
    setQueryParams({ search: "" });
    setQueryParams({ selectedRef: "" });
    setCompanySearchResults({
      active: [],
      disolved: []
    });
  }

  useDebounce(
    async () => {
      try {
        if (!queryParams.search) {
          fullReset();
          return;
        }

        const isBusinessRef = isValidBusinessReference(queryParams.search);
        const isPostcode = isValidPostcode(queryParams.search);

        const postcode = isPostcode ? queryParams.search : undefined;
        const businessRef = isBusinessRef ? queryParams.search : undefined;

        // If search query is a postcode then name must also be set to the postcode to get results
        const name = postcode ?? queryParams.search;

        const result = await businessTargetMutation.mutateAsync({
          ...(businessRef ? { businessRef } : { name }),
          ...(isPostcode ? { postcode } : {})
        });

        setCompanySearchResults(groupSearchResults(result));

        if (queryParams.selectedRef) {
          form.setValue("companyNumber", queryParams.selectedRef);
        }
      } catch (e) {
        const error = e as Error;
        if (error) {
          fullReset();
        }
      }
    },
    500,
    [queryParams.search]
  );

  async function onSubmit(data: CreditCheckFormValues) {
    try {
      setCreditCheckData(null);

      const result = await creditCheckMutation.mutateAsync({
        companyNumber: data.companyNumber,
        companyType: !data.companyNumber.includes("UI") ? 1 : 2
      });
      setCreditCheckData(result);
      queryParams.search && setSearch(queryParams.search);
      queryParams.selectedRef && setSelectedRef(queryParams.selectedRef);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      toast({
        title: "Unable to check credit",
        description: "No credit check data was found for this company."
      });
    }
  }

  const selectedCompany = useMemo(
    () =>
      companySearchResults?.active.find(
        company =>
          company.businessRef === queryParams.selectedRef?.toLocaleUpperCase()
      ),
    [queryParams.selectedRef, companySearchResults?.active]
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-row items-start gap-1"
      >
        <FormField
          control={form.control}
          name="companyNumber"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Company *</FormLabel>
              <FormControl>
                <LookUp
                  open={isCompanyModalOpen}
                  onOpenChange={setIsCompanyModalOpen}
                >
                  <LookUpTrigger
                    fieldValue={field.value}
                    isLoading={businessTargetMutation.isPending}
                  >
                    {selectedCompany ? (
                      <CompanySelection {...selectedCompany} />
                    ) : (
                      "Select a company, charity or sole trader"
                    )}
                  </LookUpTrigger>
                  <LookUpContent
                    placeholder="Search by Company name/number, Charity name, or Sole trader name."
                    searchInput={queryParams.search}
                    onSearchInputChange={search => setQueryParams({ search })}
                    isLoading={businessTargetMutation.isPending}
                  >
                    <LookUpGroup className="px-0">
                      {companySearchResults?.active.map(company => (
                        <LookUpItem
                          key={company.businessRef}
                          value={company.businessRef}
                          disabled={company.businessStatus !== "A"}
                          onSelect={value => {
                            setQueryParams({ selectedRef: value });
                            form.setValue("companyNumber", value);
                            setIsCompanyModalOpen(false);
                          }}
                        >
                          <CompanySelection {...company} />
                        </LookUpItem>
                      ))}
                    </LookUpGroup>
                    {!!companySearchResults?.disolved.length && (
                      <LookUpGroup heading="Disolved" className="px-0">
                        {companySearchResults.disolved.map(company => (
                          <LookUpItem
                            key={company.businessRef}
                            value={company.businessRef}
                            disabled={company.businessStatus !== "A"}
                          >
                            <CompanySelection {...company} />
                          </LookUpItem>
                        ))}
                      </LookUpGroup>
                    )}
                  </LookUpContent>
                </LookUp>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          disabled={creditCheckMutation.isPending}
          className="mt-[22px]"
        >
          {creditCheckMutation.isPending ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <span className="text-sm leading-tight">Check Credit</span>
          )}
        </Button>
      </form>
    </Form>
  );
}
