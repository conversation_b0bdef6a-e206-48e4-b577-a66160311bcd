"use client";

import {
  type ColumnDef,
  type SortingState,
  flexRender,
  getCoreRowModel,
  useReactTable
} from "@tanstack/react-table";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatCurrency } from "@watt/common/src/utils/format-currency";
import { formatStringToDateLong } from "@watt/common/src/utils/format-date";
import { useCreditCheckStore } from "@watt/crm/store/credit-check";
import type { CommercialDelphiScoreModel } from "@watt/db/prisma/zod";
import { useMemo, useState } from "react";
import type { z } from "zod";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";

type CreditCheckData = z.infer<typeof CommercialDelphiScoreModel>;

const creditCheckColumns: ColumnDef<CreditCheckData["history"][number]>[] = [
  {
    accessorKey: "scoreHistoryDate",
    header: "Date",
    cell: info => formatStringToDateLong(info.getValue<string>())
  },
  {
    accessorKey: "score",
    header: "Commercial Delphi Score (out of 100)",
    cell: info => info.getValue()
  },
  {
    accessorKey: "creditLimit",
    header: "Credit Limit",
    cell: info => formatCurrency(info.getValue<number>())
  },
  {
    accessorKey: "creditRating",
    header: "Credit Rating",
    cell: info => formatCurrency(info.getValue<number>())
  }
];

export function CreditCheckHistoryTable() {
  const [sorting, setSorting] = useState<SortingState>([]);

  // Fetch the credit check data from the store
  const creditCheckData = useCreditCheckStore(state => state.creditCheckData);

  // Use memoization to ensure the data isn't recalculated unnecessarily
  const historyData = useMemo(
    () => creditCheckData?.history ?? [],
    [creditCheckData]
  );

  const table = useReactTable({
    data: historyData,
    columns: creditCheckColumns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      sorting
    },
    onSortingChange: setSorting
  });

  if (!creditCheckData) {
    return <>Loading...</>;
  }

  return (
    <div className="mb-10 w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row, rowIndex) => (
              <TableRow
                className={cn(
                  "transition-all duration-300 hover:bg-muted-foreground/30",
                  rowIndex % 2 === 0 && "bg-muted"
                )}
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map(cell => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
