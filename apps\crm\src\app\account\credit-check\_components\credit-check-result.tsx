"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>hart,
  CreditCard,
  <PERSON><PERSON>,
  PoundSterling
} from "lucide-react";

import { ScrollArea } from "@radix-ui/react-scroll-area";
import { useCreditCheckStore } from "@watt/crm/store/credit-check";

import { formatCurrency } from "@watt/common/src/utils/format-currency";
import { CreditCheckHistoryTable } from "./credit-check-history-table";
import { CreditLimitRatingChart } from "./credit-limit-rating-chart";
import { CreditReport } from "./credit-report";
import { DelphiRiskScoreChart } from "./delphi-risk-score-chart";
import { KpiCard } from "./kpi-card";

export function CreditCheckResult() {
  const {
    creditCheckData,
    creditRatingPercentageChangeSinceLastMonth,
    creditLimitPercentageChangeSinceLastMonth
  } = useCreditCheckStore(state => ({
    creditCheckData: state.creditCheckData,
    creditRatingPercentageChangeSinceLastMonth:
      state.creditRatingPercentageChangeSinceLastMonth,
    creditLimitPercentageChangeSinceLastMonth:
      state.creditLimitPercentageChangeSinceLastMonth
  }));

  if (!creditCheckData) {
    return null;
  }

  return (
    <ScrollArea className="flex flex-col gap-4 overflow-x-hidden overflow-y-scroll">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Delphi Credit Score"
          value={creditCheckData.score.toString()}
          description={creditCheckData.bandText}
          icon={CreditCard}
        />
        <KpiCard
          title="Credit Limit"
          value={formatCurrency(creditCheckData.creditLimit)}
          description={`${creditLimitPercentageChangeSinceLastMonth} from last month`}
          icon={PoundSterling}
        />
        <KpiCard
          title="Credit Rating"
          value={formatCurrency(creditCheckData.creditRating)}
          description={`${creditRatingPercentageChangeSinceLastMonth} from last month`}
          icon={CandlestickChart}
        />
        <KpiCard
          title="Stability Odds"
          value={creditCheckData.stabilityOdds}
          description="Failure odds (next 12 months)"
          icon={Dices}
        />
      </div>
      <span className="text-muted-foreground">
        Conclusion: {creditCheckData.conclusionText}
      </span>

      <div className="flex flex-row">
        <div>
          <CreditReport />
        </div>
        <div className="flex w-full flex-col">
          <DelphiRiskScoreChart />
          <CreditLimitRatingChart />
        </div>
      </div>

      <h2 className="font-bold text-lg tracking-tight">
        Commercial Delphi History up to Last 12 Months
      </h2>
      <CreditCheckHistoryTable />
    </ScrollArea>
  );
}
