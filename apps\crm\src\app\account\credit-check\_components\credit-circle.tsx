"use client";

import { useMemo } from "react";

function computeCoordinates(angle: number, radius: number, offset: number) {
  const angleInRadians = (angle * Math.PI) / 180;
  const x = radius + radius * Math.cos(angleInRadians) + offset;
  const y = radius + radius * Math.sin(angleInRadians) + offset;
  return { x, y };
}

function computeAngle(
  startAngle: number,
  endAngle: number,
  index: number,
  length: number
) {
  return startAngle + ((endAngle - startAngle) / length) * index;
}

type CreditCircleScoreMarkerProps = {
  scorePositionAngle: number;
  radius: number;
  strokeWidth: number;
};

function CreditCircleScoreMarker({
  scorePositionAngle,
  radius,
  strokeWidth
}: CreditCircleScoreMarkerProps) {
  const { x, y } = computeCoordinates(scorePositionAngle, radius, strokeWidth);
  return (
    <circle
      cx={x}
      cy={y}
      r={strokeWidth / 2}
      fill="rgba(255, 255, 255, 0.33)"
      stroke="#f2f2f2"
      strokeWidth={2}
    />
  );
}

type CreditCircleSegmentsProps = {
  segments: SegmentProps[];
  startAngle: number;
  endAngle: number;
  radius: number;
  strokeWidth: number;
};

function CreditCircleSegments({
  segments,
  startAngle,
  endAngle,
  radius,
  strokeWidth
}: CreditCircleSegmentsProps) {
  const circumference = 2 * Math.PI * radius;

  return (
    <>
      {segments
        .map(({ color }, index: number) => {
          const computedStartAngle = computeAngle(
            startAngle,
            endAngle,
            index,
            segments.length
          );
          const computedEndAngle = computeAngle(
            startAngle,
            endAngle,
            index + 1,
            segments.length
          );

          const startOffset = (computedStartAngle / 360) * circumference;
          const endOffset = (computedEndAngle / 360) * circumference;
          const segmentLength = endOffset - startOffset;

          return (
            <circle
              key={color}
              cx="50%"
              cy="50%"
              r={radius}
              fill="none"
              stroke={color}
              strokeWidth={strokeWidth}
              strokeDasharray={`${segmentLength} ${circumference - segmentLength}`}
              strokeDashoffset={-startOffset}
              strokeLinecap="round"
              className="transition-all"
            />
          );
        })
        .reverse()}
    </>
  );
}

type CreditCircleLabelsProps = {
  segments: SegmentProps[];
  startAngle: number;
  endAngle: number;
  radius: number;
  strokeWidth: number;
};

function CreditCircleLabels({
  segments,
  startAngle,
  endAngle,
  radius,
  strokeWidth
}: CreditCircleLabelsProps) {
  const computeAngle = (startAngle: number, index: number, length: number) => {
    return startAngle + ((endAngle - startAngle) / length) * index;
  };

  return (
    <>
      {[0, ...segments.map(({ upperBound }) => upperBound)].map(
        (label, index: number) => {
          const computedStartAngle = computeAngle(
            startAngle,
            index,
            segments.length
          );
          const computedEndAngle = computeAngle(
            startAngle,
            index + 1,
            segments.length
          );
          const midpointAngle =
            computedStartAngle + (computedEndAngle - computedStartAngle) / 2;
          const { x, y } = computeCoordinates(
            computedStartAngle,
            radius,
            strokeWidth
          );

          return (
            <text
              key={label}
              x={x}
              y={y}
              textAnchor="middle"
              alignmentBaseline={
                midpointAngle > 0 && midpointAngle < 180
                  ? "hanging"
                  : "baseline"
              }
              fontSize={strokeWidth / 3}
              className="transition-all"
              fill="white"
            >
              {label}
            </text>
          );
        }
      )}
    </>
  );
}

type SegmentProps = {
  color: string;
  lowerBound: number;
  upperBound: number;
};

type CreditCircleProps = {
  score: number;
  segments: SegmentProps[];
  startAngle: number;
  endAngle: number;
  radius: number;
  strokeWidth?: number;
};

type ComputeScorePositionAngleParams = {
  score: number;
  segments: SegmentProps[];
  startAngle: number;
  endAngle: number;
};

const computeScorePositionAngle = ({
  score,
  segments,
  startAngle,
  endAngle
}: ComputeScorePositionAngleParams): number => {
  const finalSegmentData = segments.reduce(
    (acc, { lowerBound, upperBound }, index) => {
      if (acc.found) {
        return acc;
      }

      if (score >= lowerBound && score <= upperBound) {
        return {
          angle:
            ((endAngle - startAngle) / segments.length) *
              (index + (score - lowerBound) / (upperBound - lowerBound)) +
            startAngle,
          found: true
        };
      }

      // Continue to the next segment
      return acc;
    },
    { angle: startAngle, found: false }
  );

  return finalSegmentData.angle;
};

export function CreditCircle({
  score,
  startAngle,
  endAngle,
  segments,
  radius,
  strokeWidth = 25
}: CreditCircleProps) {
  const scorePositionAngle = useMemo(
    () => computeScorePositionAngle({ score, segments, startAngle, endAngle }),
    [score, segments, startAngle, endAngle]
  );

  const size = radius * 2 + strokeWidth * 2;
  const centerX = size / 2;
  const centerY = size / 2;

  return (
    <svg width="100%" height="100%" viewBox={`0 0 ${size} ${size}`}>
      <title>Credit Score Circle Graph</title>
      <CreditCircleSegments
        segments={segments}
        startAngle={startAngle}
        endAngle={endAngle}
        radius={radius}
        strokeWidth={strokeWidth}
      />
      <CreditCircleLabels
        segments={segments}
        startAngle={startAngle}
        endAngle={endAngle}
        radius={radius}
        strokeWidth={strokeWidth}
      />
      <CreditCircleScoreMarker
        scorePositionAngle={scorePositionAngle}
        radius={radius}
        strokeWidth={strokeWidth}
      />
      <text
        x={centerX}
        y={centerY - 20}
        textAnchor="middle"
        alignmentBaseline="middle"
        fontSize="20"
        fill="currentColor"
      >
        Business
        <tspan x={centerX} dy="1.2em">
          Credit Score
        </tspan>
      </text>
    </svg>
  );
}
