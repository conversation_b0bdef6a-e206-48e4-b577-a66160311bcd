"use client";

import { getMonthYear } from "@watt/common/src/utils/format-date";
import { useCreditCheckStore } from "@watt/crm/store/credit-check";
import { useMemo } from "react";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  XAxis,
  YAxis
} from "recharts";

export function CreditLimitRatingChart() {
  const creditCheckData = useCreditCheckStore(state => state.creditCheckData);

  const data = useMemo(() => {
    return creditCheckData?.history.map(history => ({
      date: getMonthYear(history.scoreHistoryDate),
      creditLimit: history.creditLimit,
      creditRating: history.creditRating
    }));
  }, [creditCheckData]);

  if (!creditCheckData || !creditCheckData.history) {
    return null;
  }

  return (
    <ResponsiveContainer width="100%" height={150}>
      <LineChart
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis domain={[0, 100]} />
        <Legend />

        <Line
          type="linear"
          dataKey="creditRating"
          stroke="#94BD21"
          name="Credit Rating"
        />
        <Line
          type="linear"
          dataKey="creditLimit"
          stroke="#073348"
          name="Credit Limit"
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
