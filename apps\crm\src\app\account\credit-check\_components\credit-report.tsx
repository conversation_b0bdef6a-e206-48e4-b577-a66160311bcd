"use client";

import { useCreditCheckStore } from "@watt/crm/store/credit-check";

import { CreditCircle } from "./credit-circle";

const segments = [
  {
    color: "#690407",
    lowerBound: 0,
    upperBound: 15
  },
  {
    color: "#D13125",
    lowerBound: 15,
    upperBound: 25
  },
  {
    color: "#FF914D",
    lowerBound: 25,
    upperBound: 50
  },
  {
    color: "#FBC903",
    lowerBound: 50,
    upperBound: 80
  },
  {
    color: "#94BD21",
    lowerBound: 80,
    upperBound: 90
  },
  {
    color: "#01923F",
    lowerBound: 90,
    upperBound: 100
  }
];

export function CreditReport() {
  const { creditCheckData } = useCreditCheckStore(state => ({
    creditCheckData: state.creditCheckData
  }));

  if (!creditCheckData) {
    return null;
  }

  return (
    <div className="flex w-[300px] items-center justify-center">
      <CreditCircle
        score={creditCheckData.score}
        startAngle={-220}
        endAngle={40}
        segments={segments}
        radius={100}
      />
    </div>
  );
}
