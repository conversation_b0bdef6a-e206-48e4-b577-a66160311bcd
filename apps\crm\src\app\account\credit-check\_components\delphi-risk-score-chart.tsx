"use client";

import { getMonthYear } from "@watt/common/src/utils/format-date";
import { useCreditCheckStore } from "@watt/crm/store/credit-check";
import React, { useMemo } from "react";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ReferenceArea,
  ResponsiveContainer,
  XAxis,
  YAxis
} from "recharts";

export function DelphiRiskScoreChart() {
  const creditCheckData = useCreditCheckStore(state => state.creditCheckData);

  const data = useMemo(() => {
    return creditCheckData?.history.map(history => ({
      date: getMonthYear(history.scoreHistoryDate),
      delphi: history.score
    }));
  }, [creditCheckData]);

  if (!creditCheckData || !creditCheckData.history) {
    return null;
  }

  return (
    <ResponsiveContainer width="100%" height={150}>
      <LineChart
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis domain={[0, 100]} />
        <Legend />
        <ReferenceArea
          y1={0}
          y2={30}
          strokeOpacity={0}
          fillOpacity={0.75}
          fill="#C52A36"
          label="High Risk"
        />
        <ReferenceArea
          y1={30}
          y2={80}
          strokeOpacity={0}
          fillOpacity={0.75}
          fill="#F9B975"
          label="Medium Risk"
        />
        <ReferenceArea
          y1={80}
          y2={100}
          strokeOpacity={1}
          fillOpacity={0.75}
          fill="#4DB64E"
          label="Low Risk"
        />
        <Line
          type="linear"
          dataKey="delphi"
          stroke="#ffffff"
          name="Delphi Score"
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
