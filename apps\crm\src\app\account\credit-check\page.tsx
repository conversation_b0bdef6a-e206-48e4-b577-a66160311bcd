import { featureToggles } from "@watt/crm/feature-toggles";
import { Building2, HelpingHand, MapPin, UserRound } from "lucide-react";
import { notFound } from "next/navigation";
import { CreditCheckForm } from "./_components/credit-check-form";
import { CreditCheckResult } from "./_components/credit-check-result";

export default function CreditCheckPage() {
  if (!featureToggles.routes.creditCheck) {
    notFound();
  }

  return (
    <div className="flex h-screen flex-col gap-4 px-4 py-4">
      <h1 className="font-bold text-xl tracking-tight">Credit Check</h1>
      <p className="text-muted-foreground text-sm">
        Enter the companies reference number or name or even the postcode to
        perform a soft credit check otherwise known as a Delphi score. Supports
        limited companies, charities and sole traders.
      </p>

      <div className="flex flex-row items-center gap-2 text-muted-foreground text-sm italic">
        <span className="contents">
          <Building2 className="h-3 w-3" /> Examples 12345678,
        </span>
        <span className="contents">
          <HelpingHand className="h-3 w-3" /> CE123456 or CS123456
        </span>
        <span className="contents">
          <MapPin className="h-3 w-3" /> SW1A 1AA
        </span>
        <span className="contents">
          <UserRound className="h-3 w-3" /> UI12345678, Bob Ross&apos;s fish and
          chips,
        </span>
      </div>

      <div>
        <CreditCheckForm />
      </div>

      <CreditCheckResult />
    </div>
  );
}
