"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";

import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";

interface TabStat {
  title: string;
  value: number;
  color: string;
}

export interface DashboardStatCardProps {
  title: string;
  stats: TabStat[];
}

const scaleXWidth = (x: number) => {
  // So that the width of the bar only increases slower as the value increases
  return Math.log(x);
};

export function DashboardStatCard(props: DashboardStatCardProps) {
  return (
    <Card className="select-none shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        {/* TODO fix hard coded color, use tailwind colors */}
        <CardTitle className="font-bold text-2xl text-[#515151]">
          {props.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {props.stats.map(stat => {
          const maxValue = scaleXWidth(
            Math.max(...props.stats.map(stat => stat.value))
          );
          let percentage = (scaleXWidth(stat.value) / maxValue) * 100;
          if (percentage < 10) {
            percentage = 20;
          }
          return (
            <div
              className="flex flex-row items-center justify-between space-y-0 p-2"
              key={stat.title}
            >
              <div className="button-click-animation w-full rounded bg-gray-200">
                <div
                  style={{ width: `${percentage}%` }}
                  className={cn(
                    "h-full",
                    stat.color,
                    "rounded p-2 text-sm text-white"
                  )}
                >
                  <span>
                    {stat.title}
                    <span className="float-right">{stat.value}</span>
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
