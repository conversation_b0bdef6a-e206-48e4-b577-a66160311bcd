import { cn } from "@watt/common/src/utils/classname-tailwind-merge";

export function MainNav({
  className,
  ...props
}: React.HTMLAttributes<HTMLElement>) {
  return (
    <nav
      className={cn("flex items-center space-x-4 lg:space-x-6", className)}
      {...props}
    >
      <a
        href="/examples/dashboard"
        className="font-medium text-sm transition-colors hover:text-primary"
      >
        Overview
      </a>
      <a
        href="/examples/dashboard"
        className="font-medium text-muted-foreground text-sm transition-colors hover:text-primary"
      >
        Customers
      </a>
      <a
        href="/examples/dashboard"
        className="font-medium text-muted-foreground text-sm transition-colors hover:text-primary"
      >
        Products
      </a>
      <a
        href="/examples/dashboard"
        className="font-medium text-muted-foreground text-sm transition-colors hover:text-primary"
      >
        Settings
      </a>
    </nav>
  );
}
