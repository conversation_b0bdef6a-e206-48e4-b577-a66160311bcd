"use client";

import {
  type LucideIcon,
  PhoneCall,
  PhoneIncoming,
  PhoneOutgoing,
  Timer,
  UserX2
} from "lucide-react";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import Image from "next/image";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";

export interface MyKPICardProps {
  dialAttempts: number;
  missedCalls: number;
  connectedCalls: number;
  unansweredCalls: number;
  avgCallTime: Date;
}

interface KPIItemProps {
  title: string;
  icon: LucideIcon;
  value: string | number;
  bgColor: string;
}

export function MyKPICard(props: MyKPICardProps) {
  // For average duration
  const hrs = props.avgCallTime.getHours();
  const mins = props.avgCallTime.getMinutes();
  const secs = props.avgCallTime.getSeconds();

  const data: KPIItemProps[] = [
    {
      title: "Dial Attempts",
      value: props.dialAttempts,
      bgColor: "bg-dark-theme",
      icon: PhoneOutgoing
    },
    {
      title: "Missed Calls",
      value: props.missedCalls,
      bgColor: "bg-red-theme",
      icon: PhoneIncoming
    },
    {
      title: "Connected Calls",
      value: props.connectedCalls,
      bgColor: "bg-dark-theme",
      icon: PhoneCall
    },
    {
      title: "Unanswered Calls",
      value: props.unansweredCalls,
      bgColor: "bg-red-theme",
      icon: UserX2
    },
    {
      title: "Average Call Time",
      value: `${hrs}h ${mins}m ${secs}s`,
      bgColor: "bg-dark-theme",
      icon: Timer
    }
  ];
  return (
    <Card className="select-none border-none shadow-none">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        {/* TODO hard coded colour, move this to tailwind */}
        <CardTitle className="font-bold text-2xl text-[#515151]">
          <div className="flex flex-row">
            <Image
              draggable={false}
              src="/img/bill-tie.png"
              width={200}
              height={200}
              className="w-16"
              alt="Bill"
            />
            My KPI Today
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="m-1 grid grid-cols-2">
          {data.map(stat => {
            return (
              <div key={stat.title}>
                <KPIItem
                  title={stat.title}
                  icon={stat.icon}
                  value={stat.value}
                  bgColor={stat.bgColor}
                />
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

function KPIItem(props: KPIItemProps) {
  return (
    <div
      key={props.title}
      className="flex w-full flex-row items-center gap-4 space-y-0 rounded p-2 text-sm"
    >
      <div className={cn("rounded-full bg-dark-theme ", props.bgColor)}>
        <props.icon color="white" size={38} className="p-2" />
      </div>
      <div className="flex flex-col">
        <p>{props.title}</p>
        <span className="text-gray-600">{props.value}</span>
      </div>
    </div>
  );
}
