import { setHours, setMinutes, setSeconds } from "date-fns";
import { GanttChartSquare } from "lucide-react";
import type { Metadata } from "next";

import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";

import {
  DashboardStatCard,
  type DashboardStatCardProps
} from "./components/dashboard-stat-card";
import { CalendarDateRangePicker } from "./components/date-range-picker";
import { MyKPICard, type MyKPICardProps } from "./components/my-kpi-card";

export const metadata: Metadata = {
  title: "Dashboard",
  description: "Example dashboard app using the components."
};

export default function DashboardPage() {
  const dashboardStatsCardsData: DashboardStatCardProps[] = [
    {
      title: "My Pool",
      stats: [
        {
          title: "ASSIGNED TO ME",
          value: 2100,
          color: "bg-green-theme"
        },
        {
          title: "UNCONTACTED",
          value: 70,
          color: "bg-red-theme"
        },
        {
          title: "CONTACTED",
          value: 30,
          color: "bg-green-theme"
        },
        {
          title: "SIGNED DEAL",
          value: 75,
          color: "bg-dark-theme"
        }
      ]
    },
    {
      title: "Re-Engagement",
      stats: [
        {
          title: "OBJECTION",
          value: 7,
          color: "bg-red-theme"
        },
        {
          title: "FAILED FINANCE CHECK",
          value: 20,
          color: "bg-red-theme"
        },
        {
          title: "WRONG PRICE",
          value: 36,
          color: "bg-red-theme"
        }
      ]
    },
    {
      title: "Leads",
      stats: [
        {
          title: "ATTENTION NEEDED",
          value: 730,
          color: "bg-red-theme"
        },
        {
          title: "MY DEALS",
          value: 30,
          color: "bg-green-theme"
        }
      ]
    },
    {
      title: "My Space",
      stats: [
        {
          title: "OVERDUE TASKS",
          value: 25,
          color: "bg-red-theme"
        },
        {
          title: "UNFINISHED DEAL",
          value: 140,
          color: "bg-red-theme"
        }
      ]
    },
    {
      title: "My Callback",
      stats: [
        {
          title: "DUE TODAY",
          value: 57,
          color: "bg-red-theme"
        },
        {
          title: "COMPLETED",
          value: 1210,
          color: "bg-green-theme"
        }
      ]
    }
  ];

  // Mockup KPI data
  const myKPICardData: MyKPICardProps = {
    dialAttempts: 0,
    connectedCalls: 0,
    avgCallTime: setSeconds(setMinutes(setHours(new Date(0), 0), 12), 45),
    missedCalls: 0,
    unansweredCalls: 0
  };

  return (
    <div className="flex-col px-4 md:flex">
      <div className="flex-1">
        <div className="flex items-center justify-between space-y-2">
          <h1 className="font-bold text-xl tracking-tight">Dashboard</h1>

          <div className="flex items-center space-x-2">
            <CalendarDateRangePicker />
            <Button size="sm">
              <GanttChartSquare className="mr-2 h-4 w-4" />
              View
            </Button>
          </div>
        </div>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList variant="outline">
            <TabsTrigger variant="outline" value="overview">
              Overview
            </TabsTrigger>
            <TabsTrigger variant="outline" value="analytics">
              Analytics
            </TabsTrigger>
            <TabsTrigger variant="outline" value="reports">
              Reports
            </TabsTrigger>
          </TabsList>
          <TabsContent variant="outline" value="overview" className="space-y-4">
            <div className="grid gap-10 gap-y-24 lg:grid-cols-2 xl:grid-cols-3">
              {dashboardStatsCardsData.map((item, i) => (
                <DashboardStatCard {...item} key={item.title} />
              ))}
              <MyKPICard {...myKPICardData} />
            </div>
          </TabsContent>
          <TabsContent
            variant="outline"
            value="analytics"
            className="space-y-4"
          >
            Coming soon
          </TabsContent>
          <TabsContent variant="outline" value="reports" className="space-y-4">
            Coming soon
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
