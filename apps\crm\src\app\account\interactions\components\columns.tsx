"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import type { Profile } from "@watt/db/src/types/profile";

export const columns: ColumnDef<Profile>[] = [
  {
    accessorKey: "id",
    filterFn: textFilter
  },
  {
    accessorKey: "event",
    filterFn: textFilter
  },
  {
    accessorKey: "name",
    filterFn: textFilter
  },
  {
    accessorKey: "date",
    filterFn: textFilter
  },
  {
    accessorKey: "subject",
    filterFn: textFilter
  },
  {
    accessorKey: "htmlContent"
  },
  {
    accessorKey: "textContent",
    filterFn: textFilter
  },
  {
    accessorKey: "labels",
    filterFn: textFilter
  }
];
