"use client";

import type { Table } from "@tanstack/react-table";
import { z } from "zod";

import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
}

const queryParamsSchema = z.object({
  search: z.string().optional()
});

export function DataTableToolbar<TData>({
  table
}: DataTableToolbarProps<TData>) {
  const { searchValue, handleSearchChange } = useSyncTableFilterWithQueryParams(
    table,
    queryParamsSchema
  );

  return (
    <div className="ml-auto items-center gap-2 bg-background pt-4 pb-2">
      <div className="w-full flex-1 items-center space-x-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="z-[100] h-8 flex-grow"
          value={searchValue}
        />
      </div>
    </div>
  );
}
