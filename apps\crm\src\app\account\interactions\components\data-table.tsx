"use client";

import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import type { AllCommunicationInteractions } from "@watt/api/src/router";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { useInteractionsStore } from "@watt/crm/store/interactions";
import { trpcClient } from "@watt/crm/utils/api";
import { formatDistanceToNow } from "date-fns";
import { type UIEvent, useCallback, useMemo, useRef, useState } from "react";
import { useDebounce } from "react-use";

import { Badge } from "@watt/crm/components/ui/badge";
import { ScrollArea } from "@watt/crm/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableLoading,
  TableRow
} from "@watt/crm/components/ui/table";

import { DataTableToolbar } from "./data-table-toolbar";
import { getBadgeVariantFromLabel } from "./interactions-list";

const ITEM_HEIGHT = 80;

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
}

export function DataTable<TData, TValue>({
  columns
}: DataTableProps<TData, TValue>) {
  const { selectedInteraction, setSelectedInteraction } = useInteractionsStore(
    state => ({
      selectedInteraction: state.selectedInteraction,
      setSelectedInteraction: state.setSelectedInteraction
    })
  );

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([]);
  // we need a reference to the scrolling element for logic down below
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const { data, isLoading, fetchNextPage, isFetching, hasNextPage } =
    trpcClient.interactions.all.useInfiniteQuery(
      {
        searchFilters: {
          columnFilters: debouncedColumnFilters,
          globalFilter
        }
      },
      {
        getNextPageParam: lastPage => lastPage.nextCursor,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        placeholderData: prev => prev,
        trpc: {
          abortOnUnmount: true
        }
      }
    );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  // we must flatten the array of arrays from the useInfiniteQuery hook
  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]) as AllCommunicationInteractions["items"];

  const totalDBRowCount = data?.pages?.[0]?.meta?.totalRowCount ?? 0;
  const totalFetched = allItems.length;

  // called on scroll and possibly on mount to fetch more data as the user scrolls and reaches bottom of table
  const fetchMoreOnBottomReached = useCallback(
    (event: UIEvent<HTMLDivElement>) => {
      const containerRefElement = event.currentTarget;

      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        if (
          scrollHeight - scrollTop - clientHeight < 10 &&
          !isFetching &&
          totalFetched < totalDBRowCount
        ) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, isFetching, totalFetched, totalDBRowCount]
  );

  const table = useReactTable({
    // TODO fix possible undefined, and type this properly via backend type not 'callDetailsSchema'
    data: allItems as unknown as TData[],
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      globalFilter
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    debugTable: false,
    manualFiltering: true
  });

  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => ITEM_HEIGHT,
    count: hasNextPage ? rows.length + 1 : rows.length,
    overscan: 10
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  const paddingTop = virtualRows?.[0]?.start ?? 0;
  const paddingBottom =
    totalSize - (virtualRows?.[virtualRows.length - 1]?.end ?? 0); // test

  return (
    <div className="h-screen">
      <div className="sticky top-0 mb-2 space-y-2 px-4">
        <DataTableToolbar table={table} isFiltered={isFiltered} />
      </div>
      <ScrollArea className="h-full">
        <div className="flex flex-col gap-2 px-4 pb-[140px]">
          <div
            className="flex-1 overflow-y-auto"
            onScroll={fetchMoreOnBottomReached}
            ref={tableContainerRef}
          >
            <Table outerClassName="overflow-visible">
              <TableBody>
                {(isLoading || isFetching) && (
                  <TableLoading colSpan={table.getAllLeafColumns().length} />
                )}
                {table.getRowModel().rows?.length ? (
                  <>
                    {paddingTop > 0 && (
                      <TableRow>
                        <td style={{ height: `${paddingTop}px` }} />
                      </TableRow>
                    )}
                    <div className="flex flex-col gap-2">
                      {virtualRows.map(virtualRow => {
                        // TODO fix type properly as cast is not a fix
                        const row = rows[virtualRow.index];

                        if (!row) {
                          return null;
                        }

                        const rowOriginal =
                          row.original as AllCommunicationInteractions["items"][number];

                        return (
                          <TableRow
                            key={row.id}
                            className={cn(
                              "flex flex-col items-start gap-2 rounded-lg border p-3 text-left text-sm transition-all hover:bg-accent",
                              selectedInteraction?.id === row.getValue("id") &&
                                "bg-muted"
                            )}
                            onClick={() => {
                              const interaction = allItems.find(
                                interaction =>
                                  interaction.id === row.getValue("id")
                              );
                              if (interaction) {
                                setSelectedInteraction(interaction);
                              }
                            }}
                            data-state={row.getIsSelected() && "selected"}
                          >
                            <div className="flex w-full flex-col gap-1">
                              <div className="flex items-center">
                                <div className="flex items-center gap-2">
                                  <div className="font-semibold">
                                    {row.getValue("name")}
                                  </div>
                                </div>
                                <div
                                  className={cn(
                                    "ml-auto text-xs",
                                    selectedInteraction?.id ===
                                      row.getValue("id")
                                      ? "text-foreground"
                                      : "text-muted-foreground"
                                  )}
                                >
                                  {formatDistanceToNow(rowOriginal.createdAt, {
                                    addSuffix: true
                                  })}
                                </div>
                              </div>
                              <div className="font-medium text-xs">
                                {rowOriginal.emailTransaction.subject}
                              </div>
                            </div>
                            <div className="line-clamp-2 text-muted-foreground text-xs">
                              {rowOriginal.emailTransaction.textContent
                                .substring(0, 300)
                                .replaceAll("-", "")}
                            </div>
                            <div className="flex w-full flex-1 flex-row justify-between gap-2">
                              <div>
                                {row.getValue<string[]>("labels").length ? (
                                  <div className="flex items-center gap-2">
                                    {row
                                      .getValue<string[]>("labels")
                                      .map(label => (
                                        <Badge
                                          key={label}
                                          variant={getBadgeVariantFromLabel(
                                            label
                                          )}
                                        >
                                          {label}
                                        </Badge>
                                      ))}
                                  </div>
                                ) : null}
                              </div>
                              <span className="text-foreground">
                                {humanize(
                                  rowOriginal.emailTransaction.event ?? ""
                                )}
                              </span>
                            </div>
                          </TableRow>
                        );
                      })}
                    </div>
                    {!hasNextPage && (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          {isLoading || isFetching ? "" : "No more results."}
                        </TableCell>
                      </TableRow>
                    )}
                    {paddingBottom > 0 && (
                      <TableRow>
                        <td style={{ height: `${paddingBottom}px` }} />
                      </TableRow>
                    )}
                  </>
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      {isLoading || isFetching ? "" : "No results."}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
