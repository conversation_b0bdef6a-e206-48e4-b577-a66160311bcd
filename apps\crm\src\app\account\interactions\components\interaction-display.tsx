"use client";

import { useInteractionsStore } from "@watt/crm/store/interactions";
import { trpcClient } from "@watt/crm/utils/api";
import format from "date-fns/format";

import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@watt/crm/components/ui/avatar";
import { Separator } from "@watt/crm/components/ui/separator";
import { Skeleton } from "@watt/crm/components/ui/skeleton";

import { MailAttachment } from "./mail-attachment";

import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import type { PropsWithChildren } from "react";

const InteractionLayout = ({ children }: PropsWithChildren) => (
  <div className="flex h-full flex-col">
    <div className="flex min-h-[56px] items-center p-2" />
    <Separator />
    {children}
  </div>
);

export function InteractionDisplay() {
  const { selectedInteraction } = useInteractionsStore(state => ({
    selectedInteraction: state.selectedInteraction
  }));

  const { data: interactionDetails, isLoading } =
    trpcClient.interactions.get.useQuery(
      { id: selectedInteraction?.id ?? "" },
      {
        enabled: !!selectedInteraction,
        refetchOnWindowFocus: false
      }
    );

  if (!selectedInteraction) {
    return null;
  }

  if (isLoading) {
    return (
      <InteractionLayout>
        <div className="flex flex-1 flex-col">
          <div className="flex items-start p-4">
            <div className="flex items-start gap-4 text-sm">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="grid gap-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-48" />
                <Skeleton className="h-3 w-48" />
              </div>
            </div>
            <Skeleton className="ml-auto h-4 w-24" />
          </div>
          <Separator />

          <div className="w-full flex-1 bg-muted">
            <Skeleton className="h-full w-full" />
          </div>

          <Separator className="mt-auto" />
          <div className="p-4">
            <div className="flex flex-row gap-2">
              <Skeleton className="h-12 w-12" />
              <Skeleton className="h-12 w-12" />
              <Skeleton className="h-12 w-12" />
            </div>
          </div>
        </div>
      </InteractionLayout>
    );
  }

  return (
    <InteractionLayout>
      {selectedInteraction ? (
        <div className="flex flex-1 flex-col">
          <div className="flex items-start p-4">
            <div className="flex items-start gap-4 text-sm">
              <Avatar>
                <AvatarImage alt={selectedInteraction.name} />
                <AvatarFallback>
                  {selectedInteraction.name
                    .split(" ")
                    .map(chunk => chunk[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="grid gap-1">
                <div className="font-semibold">{selectedInteraction.name}</div>
                <div className="line-clamp-1 text-xs">
                  {selectedInteraction.emailTransaction.subject}
                </div>
                <div className="line-clamp-1 text-xs">
                  <span className="font-medium">Reply-To:</span>{" "}
                  {selectedInteraction.emailTransaction.to}
                </div>
              </div>
            </div>
            {selectedInteraction.emailTransaction.dateSent && (
              <div className="ml-auto text-muted-foreground text-xs">
                {formatDate(
                  selectedInteraction.emailTransaction.dateSent,
                  dateFormats.DD_MM_YYYY_HH_MM
                )}
              </div>
            )}
          </div>
          <Separator />
          {/* <div className="flex-1 p-4 text-sm whitespace-pre-wrap">{interaction.text}</div> */}

          <div className="w-full flex-1">
            <iframe
              width={"100%"}
              height={"100%"}
              srcDoc={interactionDetails?.emailTransaction.htmlContent}
              title="interaction"
            />
          </div>

          {interactionDetails &&
            interactionDetails.emailTransaction.attachments.length > 0 && (
              <>
                <Separator className="mt-auto" />
                <div className="p-4">
                  <div className="flex flex-row gap-2">
                    {interactionDetails.emailTransaction.attachments.map(
                      attachment =>
                        attachment.url ? (
                          <MailAttachment
                            key={attachment.id}
                            name={attachment.filename}
                            type={attachment.type}
                            url={attachment.url}
                            size={attachment.size}
                          />
                        ) : null
                    )}
                  </div>
                </div>
              </>
            )}
        </div>
      ) : (
        <div className="p-8 text-center text-muted-foreground">
          No message selected
        </div>
      )}
    </InteractionLayout>
  );
}
