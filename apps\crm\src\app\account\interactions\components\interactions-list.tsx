import type { ComponentProps } from "react";

import type { Badge } from "@watt/crm/components/ui/badge";

import { columns } from "./columns";
import { DataTable } from "./data-table";

export function InteractionsList() {
  return <DataTable columns={columns} />;
}

export function getBadgeVariantFromLabel(
  label: string
): ComponentProps<typeof Badge>["variant"] {
  if (["work"].includes(label.toLowerCase())) {
    return "default";
  }

  if (["personal"].includes(label.toLowerCase())) {
    return "outline";
  }

  return "secondary";
}
