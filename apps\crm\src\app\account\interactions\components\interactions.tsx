"use client";

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup
} from "@watt/crm/components/ui/resizable";
import { Separator } from "@watt/crm/components/ui/separator";
import { TooltipProvider } from "@watt/crm/components/ui/tooltip";

import { InteractionDisplay } from "./interaction-display";
import { InteractionsList } from "./interactions-list";

interface InteractionProps {
  defaultLayout?: number[];
  defaultCollapsed?: boolean;
}

export function Interaction({ defaultLayout = [30, 70] }: InteractionProps) {
  return (
    <TooltipProvider delayDuration={0}>
      <ResizablePanelGroup
        direction="horizontal"
        onLayout={(sizes: number[]) => {
          document.cookie = `react-resizable-panels-interactions:layout=${JSON.stringify(sizes)}; path=/`;
        }}
        className="h-full items-stretch"
      >
        <ResizablePanel defaultSize={defaultLayout[0]} minSize={30}>
          <div>
            <div className="flex min-h-[56px] items-center px-4 py-2">
              <h1 className="font-bold text-xl">Interactions</h1>
            </div>
            <Separator />
            <InteractionsList />
          </div>
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={defaultLayout[1]} minSize={30}>
          <InteractionDisplay />
        </ResizablePanel>
      </ResizablePanelGroup>
    </TooltipProvider>
  );
}
