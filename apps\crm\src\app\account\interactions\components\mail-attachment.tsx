import { FileText } from "lucide-react";
import Link from "next/link";

type MailAttachmentProps = {
  name: string;
  type: string | null;
  url: string;
  size: number;
};

export function MailAttachment({ name, url, size }: MailAttachmentProps) {
  return (
    <Link
      target="_blank"
      rel="noopener noreferrer"
      className="flex flex-col gap-2 rounded-md bg-primary px-2 py-1 text-primary-foreground"
      href={url}
    >
      <div className="flex flex-row gap-2">
        <div className="rounded bg-red-500 p-2">
          <FileText className="h-6 w-6" />
        </div>
        <div className="flex flex-col">
          <span className="text-sm">{name}</span>
          <span className="text-xs">{humanizeFileSize(size)}</span>
        </div>
      </div>
    </Link>
  );
}

function humanizeFileSize(size: number) {
  const i = Math.floor(Math.log(size) / Math.log(1024));
  return `${(size / 1024 ** i).toFixed(2)} ${["B", "kB", "MB", "GB", "TB"][i]}`;
}
