import { cookies } from "next/headers";

import type { <PERSON>ada<PERSON> } from "next";

import { Interaction } from "./components/interactions";

export const metadata: Metadata = {
  title: "Interactions",
  description: "Interactions."
};

export default async function InteractionsPage() {
  const layout = (await cookies()).get(
    "react-resizable-panels-interactions:layout"
  );
  const defaultLayout = layout ? JSON.parse(layout.value) : undefined;

  return <Interaction defaultLayout={defaultLayout} />;
}
