import { cookies } from "next/headers";
import type { PropsWithChildren } from "react";

import { QuickActionProviders } from "@watt/crm/components/quick-actions/quick-action-providers";

import { getSupabaseUser } from "@watt/db/src/supabase/get-user";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../idle-checker";
import { ResizableLayout } from "./resizable-layout";

export default async function RootLayout({ children }: PropsWithChildren) {
  const cookiesStore = await cookies();
  const { permissions } = await getSupabaseUser();
  const { isAuthorised, isSystemUser } = permissions;

  const layout = cookiesStore.get("react-resizable-panels-layout:layout");
  const defaultLayout = layout ? JSON.parse(layout.value) : undefined;
  const collapsed = cookiesStore.get("react-resizable-panels-layout:collapsed");
  const defaultCollapsed = collapsed ? JSON.parse(collapsed.value) : false;

  return (
    <>
      <IdleChecker isSystemUser={isSystemUser} />
      <QuickActionProviders />
      {/* <NoAudioBanner /> */}

      <div className="flex h-screen">
        <ResizableLayout
          defaultLayout={defaultLayout}
          defaultCollapsed={defaultCollapsed}
          isAuthorised={isAuthorised}
        >
          {children}
        </ResizableLayout>
      </div>
    </>
  );
}
