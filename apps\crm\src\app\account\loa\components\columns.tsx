"use client";

import type {
  ColumnDef,
  RowData,
  CellContext as <PERSON><PERSON>ellContext
} from "@tanstack/react-table";
import { format } from "date-fns";

import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";

import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Button } from "@watt/crm/components/ui/button";
import type { LOAPageDataRow } from "./data-table";
import { DataTableRowActions } from "./data-table-row-actions";
import { ToggleCompanyGrouping } from "./toggle-company-grouping";
type CellContext<TData extends RowData, TValue> = TanCellContext<
  TData,
  TValue
> & {
  showCompanyProfile: () => void;
};

export const columns: ColumnDef<LOAPageDataRow>[] = [
  {
    accessorKey: "companyName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: props => {
      const propsCasted = props as CellContext<LOAPageDataRow, string>;

      return (
        <div className="group flex cursor-pointer items-center gap-2 hover:underline">
          {propsCasted.row.depth === 0 && (
            <Button variant="none" onClick={propsCasted.showCompanyProfile}>
              {propsCasted.row.getValue("companyName")}
            </Button>
          )}

          {propsCasted.row.getCanExpand() && (
            <Button variant="none" size="icon" className="rounded-full">
              <ToggleCompanyGrouping row={propsCasted.row} />
            </Button>
          )}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "companyEntityAddress",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site Address" />
    ),
    cell: ({ row }) => <div>{row.getValue("companyEntityAddress")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site Address"
    }
  },
  {
    accessorKey: "meterNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Meter Number" />
    ),
    cell: ({ row }) => <div>{row.getValue("meterNumber")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Meter Number"
    }
  },
  {
    accessorKey: "signedDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Signed Date" />
    ),
    cell: ({ row }) => (
      <div>
        {(() => {
          const value: string = row.getValue("signedDate");
          if (!value) {
            return "Not signed";
          }
          const date = new Date(value);
          const formattedDate = format(date, "dd/MM/yyyy");

          return formattedDate;
        })()}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Signed Date"
    }
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => (
      <div>
        {(() => {
          const signedDate: string = row.getValue("signedDate");
          const loaValidPeriod: string = row.getValue("loaValidPeriod");

          // Calculate the expiry date
          const expiryDate = new Date(signedDate);
          expiryDate.setFullYear(
            expiryDate.getFullYear() + Number.parseInt(loaValidPeriod)
          );

          // Check if expired
          if (expiryDate < new Date()) {
            return "Expired";
          }

          return "Valid";
        })()}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status"
    }
  },
  {
    accessorKey: "loaValidPeriod",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valid Period" />
    ),
    cell: props => {
      return <div>{props.row.getValue("loaValidPeriod")}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Valid Period"
    }
  },
  {
    id: "actions",
    cell: () => <DataTableRowActions />
  }
];
