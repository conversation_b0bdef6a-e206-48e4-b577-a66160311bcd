"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import { getUniqueFilterOptions } from "@watt/crm/utils/get-unique-filter-options";

import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  children
}: DataTableToolbarProps<TData>) {
  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  const filterableColumns = [
    { id: "companyName", title: "Company Name", humanizeValues: true },
    { id: "signedDate", title: "Signed Date" },
    { id: "status", title: "Status" },
    { id: "loaValidPeriod", title: "Valid Period" }
  ];

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          value={
            (table.getColumn("companyName")?.getFilterValue() as string) ?? ""
          }
          onChange={event =>
            table.getColumn("companyName")?.setFilterValue(event.target.value)
          }
          className="h-9 w-[250px]"
        />
        {filterableColumns.map(column => (
          <DataTableFacetedFilter
            key={column.id}
            column={table.getColumn(column.id)}
            title={column.title}
            options={getUniqueFilterOptions({
              columnId: column.id,
              table,
              humanizeValues: column.humanizeValues
            })}
          />
        ))}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            size="sm"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <DataTableViewOptions table={table} />
        {children}
      </div>
    </div>
  );
}
