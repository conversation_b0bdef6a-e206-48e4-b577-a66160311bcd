"use client";

import {
  type ColumnDef,
  type ColumnFiltersState,
  type ExpandedState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { LOA_All_Output } from "@watt/api/src/router/loa";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { trpcClient } from "@watt/crm/utils/api";
import {
  type BaseTableRow,
  groupedDataRestructure
} from "@watt/crm/utils/dropdown-restructure";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import React from "react";

import { DataTablePagination } from "@watt/crm/components/data-table/data-table-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";
import { routes } from "@watt/crm/config/routes";
import { useNavigateToRoute } from "@watt/crm/hooks/use-navigate-to-route";

import { DataTableToolbar } from "./data-table-toolbar";

export type LOAPageDataRow = ExtractElementType<LOA_All_Output> &
  BaseTableRow<LOAPageDataRow>;

export interface LOATableProps {
  columns: ColumnDef<LOAPageDataRow>[];
}

export function LOATable({ columns }: LOATableProps) {
  const navigateToRoute = useNavigateToRoute();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [expanded, setExpanded] = React.useState<ExpandedState>({});

  const loaQuery = trpcClient.loa.all.useQuery();
  const loas = React.useMemo(() => loaQuery.data ?? [], [loaQuery.data]);

  // Group the backend data by companyName
  const data: LOAPageDataRow[] = React.useMemo(
    () =>
      groupedDataRestructure<LOAPageDataRow>(
        loas,
        (item, currentValue) => item.companyName === currentValue.companyName
      ),
    [loas]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      expanded
    },
    onExpandedChange: setExpanded,
    getSubRows: (row: LOAPageDataRow) => row.subRows,
    enableRowSelection: false,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });

  return (
    <div className="space-y-4 py-4">
      <h1 className="font-bold text-xl tracking-tight">LOA</h1>
      <DataTableToolbar table={table} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    <span className="flex gap-2">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </span>
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  className={cn(
                    "transition-all duration-300 hover:bg-muted-foreground/30",
                    rowIndex % 2 === 0 && "bg-muted"
                  )}
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, {
                        ...cell.getContext(),
                        showCompanyProfile: () =>
                          navigateToRoute(routes.company, {
                            dynamicPaths: { id: row.original.id }
                          })
                      })}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
