import type { Row } from "@tanstack/react-table";
import { ChevronDown, ChevronUp } from "lucide-react";
import type { JSX } from "react";

import type { LOAPageDataRow } from "./data-table";

interface ToggleCompanyGroupingProps {
  row: Row<LOAPageDataRow>;
}

export function ToggleCompanyGrouping({
  row
}: ToggleCompanyGroupingProps): JSX.Element {
  const ChevronIcon = row.getIsExpanded() ? ChevronUp : ChevronDown;

  return (
    <ChevronIcon className="h-4 w-4 rounded-full p-0 text-primary transition-all duration-300 group-hover:bg-muted-foreground/30 group-hover:text-white" />
  );
}
