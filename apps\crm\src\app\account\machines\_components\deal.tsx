"use client";

import { create<PERSON><PERSON>erIns<PERSON>or } from "@statelyai/inspect";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { convertToCamel } from "@watt/common/src/utils/record-to-camel-case";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { Label } from "@watt/crm/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectWrapper
} from "@watt/crm/components/ui/select";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useCallback, useEffect, useRef, useState } from "react";
import { and, assign, createActor, setup } from "xstate";

type CreditCheck = "PASS" | "FAILED" | "NOT_REQUIRED";
type ContractCheck =
  | "CONTRACT_ACCEPTED"
  | "CONTRACT_ACCEPTED__WITH_CONFIRMATION_CALL"
  | "CONTRACT_REJECTED_WITH_CONFIRMATION_CALL_REQUIRED"
  | "CONTRACT_REJECTED";
type LoaCheck =
  | "LOA_ACCEPTED"
  | "LOA_REJECTED_NOT_FOUND"
  | "LOA_REJECTED_INVALID_OR_INCORRECT"
  | "LOA_REJECTED_INSUFFICIENT_COVER_PERIOD";

type UserRole = "SALES_AGENT" | "COMPLIANCE_OFFICER" | null;

type SalesAgentIsCot = "YES" | "NO";
type SalesAgentCreditCheck =
  | "SALES_PASSED"
  | "COMPLIANCE_REQUIRED"
  | "NOT_REQUIRED";
type SalesAgentLoaStatus =
  | "WRITTEN_ATTACH"
  | "WRITTEN_INBOX"
  | "VERBAL_RECORDING"
  | "EXISTING";

type NextStage =
  | "NEXT_STAGE_SUBMITTED_TO_SUPPLIER"
  | "NEXT_STAGE_AWAITING_SUBMISSION"
  | "NEXT_STAGE_REJECTED_BY_COMPLIANCE"
  | "NEXT_STAGE_CANCELLED_BY_COMPLIANCE"
  | "NEXT_STAGE_DEAD_BY_COMPLIANCE";

type DealEvents =
  | { type: "salesAgent.cancelDeal" }
  | {
      type: "salesAgent.submitDeal";
      payload: {
        isCot: SalesAgentIsCot;
        agentCreditCheck: SalesAgentCreditCheck;
        agentLoaStatus: SalesAgentLoaStatus;
        submissionNotes: string;
      };
    }
  | {
      type: "complianceOfficer.reviewDeal";
      payload: {
        creditCheck: CreditCheck;
        contractCheck: ContractCheck;
        loaCheck: LoaCheck;
      };
    }
  | {
      type: "complianceOfficer.saveChecks";
    }
  | {
      type: "complianceOfficer.submitNextStage";
    }
  | {
      type: "complianceOfficer.setNextStage";
      payload: { nextStage: NextStage };
    }
  | { type: "setCurrentRole"; role: Exclude<UserRole, null> }
  | {
      type: "salesAgent.saveInitialInfo";
      payload: {
        isCot: SalesAgentIsCot;
        agentCreditCheck: SalesAgentCreditCheck;
        agentLoaStatus: SalesAgentLoaStatus;
        submissionNotes: string;
      };
    }
  | { type: "resetDirtyFlag" };

interface DealContext {
  currentRole: UserRole;

  creditCheck: CreditCheck;
  contractCheck: ContractCheck;
  loaCheck: LoaCheck;

  // NEW SALES AGENT FIELDS
  isCot: SalesAgentIsCot;
  agentCreditCheck: SalesAgentCreditCheck;
  agentLoaStatus: SalesAgentLoaStatus;
  submissionNotes: string;

  nextStage?: NextStage;
  isContextDirty: boolean;
}

const { inspect } = createBrowserInspector({
  // Comment out the line below to start the inspector
  autoStart: false
});

// For demonstration, a fake "save" function:
async function saveMachineStateAndContext() {
  return new Promise<void>(resolve => {
    setTimeout(() => {
      resolve();
    }, 1000);
  });
}

/**
 * Checks if the current role in upper snake case matches
 * the first part of the partial event descriptor type name.
 */
function checkEventTypeForRole(eventType: string, currentRole: UserRole) {
  return eventType.split(".")[0] === convertToCamel(currentRole);
}

const dealMachine = setup({
  types: {
    context: {} as DealContext,
    events: {} as DealEvents
  },
  guards: {
    isAuthorised: ({ context, event }) => {
      return checkEventTypeForRole(event.type, context.currentRole);
    }
  }
}).createMachine({
  id: "dealFlow",
  initial: "DEAL_INITIATED",
  context: {
    currentRole: "SALES_AGENT",

    // Default new fields (COMPLIANCE)
    creditCheck: "NOT_REQUIRED",
    contractCheck: "CONTRACT_ACCEPTED",
    loaCheck: "LOA_ACCEPTED",

    // NEW SALES AGENT FIELDS (DEFAULTS)
    isCot: "NO",
    agentCreditCheck: "NOT_REQUIRED",
    agentLoaStatus: "WRITTEN_ATTACH",
    submissionNotes: "",

    // No nextStage chosen by default
    nextStage: undefined,

    // The new dirty flag
    isContextDirty: false
  },
  on: {
    setCurrentRole: {
      actions: assign({
        currentRole: ({ event }) => event.role
      })
    },
    resetDirtyFlag: {
      actions: assign({
        isContextDirty: _ => false
      })
    },
    "complianceOfficer.saveChecks": {
      guard: "isAuthorised",
      target: ".DEAL_COMPLIANCE_REVIEWING"
    },
    // If compliance officer changes the next stage
    "complianceOfficer.setNextStage": {
      guard: "isAuthorised",
      actions: assign({
        nextStage: ({ event }) => event.payload.nextStage
      })
    },
    "salesAgent.saveInitialInfo": {
      guard: "isAuthorised",
      actions: assign({
        isCot: ({ event }) => event.payload.isCot,
        agentCreditCheck: ({ event }) => event.payload.agentCreditCheck,
        agentLoaStatus: ({ event }) => event.payload.agentLoaStatus,
        submissionNotes: ({ event }) => event.payload.submissionNotes
      })
    }
  },
  states: {
    DEAL_INITIATED: {
      initial: "DEAL_CREATED",
      states: {
        DEAL_CREATED: {
          meta: {
            authorizedRoles: ["SALES_AGENT"],
            description:
              "Deal is new, only a Sales Agent can cancel or submit it."
          },
          on: {
            "salesAgent.cancelDeal": {
              guard: "isAuthorised",
              target: "DEAL_CANCELLED"
            },
            "salesAgent.submitDeal": {
              guard: "isAuthorised",
              target: "#DEAL_SUBMITTED",
              actions: assign({
                isCot: ({ event }) => event.payload.isCot,
                agentCreditCheck: ({ event }) => event.payload.agentCreditCheck,
                agentLoaStatus: ({ event }) => event.payload.agentLoaStatus,
                submissionNotes: ({ event }) => event.payload.submissionNotes
              })
            }
          }
        },

        DEAL_CANCELLED: {
          meta: {
            authorizedRoles: ["SALES_AGENT"],
            description: "Deal is canceled, only a Sales Agent can reinstate."
          },
          on: {
            "salesAgent.cancelDeal": {
              guard: "isAuthorised",
              target: "hist"
            }
          }
        },
        // The history sub-state for DEAL_INITIATED:
        hist: {
          type: "history",
          history: "shallow",
          target: "DEAL_CREATED"
        }
      }
    },
    DEAL_SUBMITTED: {
      id: "DEAL_SUBMITTED",
      meta: {
        authorizedRoles: ["COMPLIANCE_OFFICER", "SALES_AGENT"],
        description:
          "Deal is submitted; a Compliance Officer can now finalize it."
      },
      on: {
        // If the compliance officer saves any checks here,
        // we transition to DEAL_COMPLIANCE_REVIEWING
        "complianceOfficer.reviewDeal": {
          guard: "isAuthorised",
          actions: assign({
            creditCheck: ({ event }) => event.payload.creditCheck,
            contractCheck: ({ event }) => event.payload.contractCheck,
            loaCheck: ({ event }) => event.payload.loaCheck,
            isContextDirty: _ => true
          })
        },
        // If the Sales Agent wants to cancel (and we haven't moved to compliance reviewing),
        // we transition back to DEAL_INITIATED
        "salesAgent.cancelDeal": {
          guard: "isAuthorised",
          target: "DEAL_INITIATED.hist"
        },
        "complianceOfficer.submitNextStage": [
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_SUBMITTED_TO_SUPPLIER"
            ]),
            target: "DEAL_SUBMITTED_TO_SUPPLIER"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_AWAITING_SUBMISSION"
            ]),
            target: "DEAL_COMPLIANCE_AWAITING"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_REJECTED_BY_COMPLIANCE"
            ]),
            target: "DEAL_COMPLIANCE_REJECTED"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_CANCELLED_BY_COMPLIANCE"
            ]),
            target: "DEAL_COMPLIANCE_CANCELLED"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_DEAD_BY_COMPLIANCE"
            ]),
            target: "DEAL_COMPLIANCE_DEAD"
          }
        ]
      }
    },
    DEAL_COMPLIANCE_REVIEWING: {
      meta: {
        authorizedRoles: ["COMPLIANCE_OFFICER"],
        description: "CO is actively reviewing the deal."
      },
      on: {
        // CO can keep saving checks without leaving this state
        "complianceOfficer.reviewDeal": {
          guard: "isAuthorised",
          actions: assign({
            creditCheck: ({ event }) => event.payload.creditCheck,
            contractCheck: ({ event }) => event.payload.contractCheck,
            loaCheck: ({ event }) => event.payload.loaCheck,
            isContextDirty: _ => true
          })
        },
        "complianceOfficer.setNextStage": {
          guard: "isAuthorised",
          actions: assign({
            nextStage: ({ event }) => event.payload.nextStage
          })
        },
        "complianceOfficer.submitNextStage": [
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_SUBMITTED_TO_SUPPLIER"
            ]),
            target: "DEAL_SUBMITTED_TO_SUPPLIER"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_AWAITING_SUBMISSION"
            ]),
            target: "DEAL_COMPLIANCE_AWAITING"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_REJECTED_BY_COMPLIANCE"
            ]),
            target: "DEAL_COMPLIANCE_REJECTED"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_CANCELLED_BY_COMPLIANCE"
            ]),
            target: "DEAL_COMPLIANCE_CANCELLED"
          },
          {
            guard: and([
              "isAuthorised",
              ({ context }) =>
                context.nextStage === "NEXT_STAGE_DEAD_BY_COMPLIANCE"
            ]),
            target: "DEAL_COMPLIANCE_DEAD"
          }
        ]
      }
    },

    // This state is effectively "accepted and awaiting final submission"
    DEAL_COMPLIANCE_AWAITING: {
      meta: {
        authorizedRoles: [],
        description: "Awaiting final submission from compliance officer."
      }
    },

    // Rejected, cancelled, or dead
    DEAL_COMPLIANCE_REJECTED: {
      meta: {
        authorizedRoles: [],
        description: "Deal is rejected by compliance."
      }
    },
    DEAL_COMPLIANCE_CANCELLED: {
      meta: {
        authorizedRoles: [],
        description: "Deal is cancelled by compliance."
      }
    },
    DEAL_COMPLIANCE_DEAD: {
      meta: {
        authorizedRoles: [],
        description: "Deal is dead by compliance."
      }
    },

    // Submitted to supplier
    DEAL_SUBMITTED_TO_SUPPLIER: {
      meta: {
        authorizedRoles: [],
        description: "No further actions."
      }
    }
  }
});

async function getMachineStateAndContext(): Promise<{
  context: DealContext;
  state: string;
}> {
  return new Promise(resolve => {
    setTimeout(
      () =>
        resolve({
          context: {
            currentRole: "SALES_AGENT",
            creditCheck: "NOT_REQUIRED",
            contractCheck: "CONTRACT_ACCEPTED",
            loaCheck: "LOA_ACCEPTED",
            isCot: "NO",
            agentCreditCheck: "NOT_REQUIRED",
            agentLoaStatus: "WRITTEN_ATTACH",
            submissionNotes: "",
            // Start out not dirty
            isContextDirty: false
          },
          state: "DEAL_INITIATED"
        }),
      500
    );
  });
}

export function Deal() {
  // Keep the actor in a ref so it doesn't cause re-renders
  const dealActorRef = useRef<ReturnType<typeof createActor> | null>(null);

  // We store just the snapshot in state to ensure UI re-renders on change.
  type DealSnapshot = ReturnType<
    NonNullable<typeof dealActorRef.current>["getSnapshot"]
  >;
  const [dealSnapshot, setDealSnapshot] = useState<DealSnapshot | null>(null);

  // Initialize the machine (and possibly restore from localStorage) on mount
  useEffect(() => {
    let isSubscribed = true;

    async function initMachine() {
      const { context, state } = await getMachineStateAndContext();
      const persistedState = JSON.parse(
        localStorage.getItem("deal-state") || "null"
      );

      dealActorRef.current = createActor(
        dealMachine,
        persistedState
          ? {
              snapshot: persistedState || {
                value: state,
                context
              },
              inspect
            }
          : { inspect }
      );

      const subscription = dealActorRef.current.subscribe(snapshot => {
        if (isSubscribed) {
          setDealSnapshot(snapshot);
          // Always persist machine updates to localStorage
          localStorage.setItem(
            "deal-state",
            JSON.stringify(dealActorRef.current?.getPersistedSnapshot())
          );
        }
      });

      dealActorRef.current.start();

      return () => {
        subscription.unsubscribe();
        isSubscribed = false;
      };
    }

    initMachine();
  }, []);

  const send = useCallback((event: DealEvents) => {
    dealActorRef.current?.send(event);
  }, []);

  // For convenience: helper to update Sales Agent fields
  const updateSalesAgentField = useCallback(
    (partial: Partial<DealContext>) => {
      if (!dealSnapshot) {
        return;
      }
      send({
        type: "salesAgent.saveInitialInfo",
        payload: {
          isCot: partial.isCot ?? dealSnapshot.context.isCot,
          agentCreditCheck:
            partial.agentCreditCheck ?? dealSnapshot.context.agentCreditCheck,
          agentLoaStatus:
            partial.agentLoaStatus ?? dealSnapshot.context.agentLoaStatus,
          submissionNotes:
            partial.submissionNotes ?? dealSnapshot.context.submissionNotes
        }
      });
    },
    [dealSnapshot, send]
  );

  // Helper for updating compliance checks
  const updateComplianceField = useCallback(
    (partial: Partial<DealContext>) => {
      if (!dealSnapshot) {
        return;
      }
      send({
        type: "complianceOfficer.reviewDeal",
        payload: {
          creditCheck: partial.creditCheck ?? dealSnapshot.context.creditCheck,
          contractCheck:
            partial.contractCheck ?? dealSnapshot.context.contractCheck,
          loaCheck: partial.loaCheck ?? dealSnapshot.context.loaCheck
        }
      });
    },
    [dealSnapshot, send]
  );

  // A helper for saving the nextStage in context
  const setNextStage = useCallback(
    (nextStage: NextStage) => {
      send({
        type: "complianceOfficer.setNextStage",
        payload: { nextStage }
      });
    },
    [send]
  );

  if (!dealSnapshot) {
    return <div>Loading deal machine...</div>;
  }

  const { context } = dealSnapshot;
  const { currentRole, nextStage } = context;

  // Determine if the current role can perform actions in this state
  const canAct = (() => {
    const meta = dealSnapshot.getMeta();
    const firstKey = Object.keys(meta)[0];
    if (!firstKey) {
      return false;
    }
    const metaValue = meta[firstKey];
    const { authorizedRoles } = metaValue || { authorizedRoles: [] };
    return authorizedRoles.includes(currentRole);
  })();

  return (
    <div className="grid grid-cols-2 gap-4 p-4">
      {/* ---- Card 1: Restart Deal Machine ---- */}
      <Card>
        <CardHeader>
          <CardTitle>Restart Deal Machine</CardTitle>
          <CardDescription>
            Clear local storage to reset the deal state.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="destructive"
            onClick={() => {
              localStorage.removeItem("deal-state");
              window.location.reload();
            }}
          >
            Clear Local Storage
          </Button>
        </CardContent>
      </Card>

      {/* ---- Card 2: Select User ---- */}
      <Card>
        <CardHeader>
          <CardTitle>Select User</CardTitle>
          <CardDescription>
            Simulate deal flow from different user perspectives.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                {currentRole ? humanize(currentRole) : "Select User"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Users</DropdownMenuLabel>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={() =>
                    send({ type: "setCurrentRole", role: "SALES_AGENT" })
                  }
                >
                  Sales Agent
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    send({ type: "setCurrentRole", role: "COMPLIANCE_OFFICER" })
                  }
                >
                  Compliance Officer
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardContent>
      </Card>

      {/* ---- Card 3: Deal Flow ---- */}
      <Card>
        <CardHeader>
          <CardTitle>Deal Flow</CardTitle>
          <CardDescription>
            Manage the deal process for the {humanize(currentRole)} user.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {canAct ? (
            <>
              {/* ------------------ DEAL_INITIATED UI (Sales Agent) ------------------ */}
              {dealSnapshot.matches("DEAL_INITIATED") && (
                <div className="mt-2 flex flex-col gap-4">
                  {/* Is COT? */}
                  <div className="flex flex-col gap-2">
                    <Label className="font-semibold">
                      Is this deal a change of tenancy (COT)?
                    </Label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="cot"
                          value="YES"
                          checked={context.isCot === "YES"}
                          onChange={() =>
                            updateSalesAgentField({ isCot: "YES" })
                          }
                        />
                        <span>Yes</span>
                      </label>
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="cot"
                          value="NO"
                          checked={context.isCot === "NO"}
                          onChange={() =>
                            updateSalesAgentField({ isCot: "NO" })
                          }
                        />
                        <span>No</span>
                      </label>
                    </div>
                  </div>

                  {/* Agent Credit Check */}
                  <div className="flex flex-col gap-2">
                    <Label className="font-semibold">
                      Is credit check required for this deal?
                    </Label>
                    <div className="flex flex-col space-y-2">
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="creditCheck"
                          value="SALES_PASSED"
                          checked={context.agentCreditCheck === "SALES_PASSED"}
                          onChange={() =>
                            updateSalesAgentField({
                              agentCreditCheck: "SALES_PASSED"
                            })
                          }
                        />
                        <span>
                          Yes, I have conducted the check and customer has
                          passed.
                        </span>
                      </label>
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="creditCheck"
                          value="COMPLIANCE_REQUIRED"
                          checked={
                            context.agentCreditCheck === "COMPLIANCE_REQUIRED"
                          }
                          onChange={() =>
                            updateSalesAgentField({
                              agentCreditCheck: "COMPLIANCE_REQUIRED"
                            })
                          }
                        />
                        <span>
                          Yes, Compliance Officer should conduct the check.
                        </span>
                      </label>
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="creditCheck"
                          value="NOT_REQUIRED"
                          checked={context.agentCreditCheck === "NOT_REQUIRED"}
                          onChange={() =>
                            updateSalesAgentField({
                              agentCreditCheck: "NOT_REQUIRED"
                            })
                          }
                        />
                        <span>
                          No, this deal does not require a credit check.
                        </span>
                      </label>
                    </div>
                  </div>

                  {/* LOA Status */}
                  <div className="flex flex-col gap-2">
                    <Label className="font-semibold">
                      What is the LOA Status?
                    </Label>
                    <div className="flex flex-col space-y-2">
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="loaStatus"
                          value="WRITTEN_ATTACH"
                          checked={context.agentLoaStatus === "WRITTEN_ATTACH"}
                          onChange={() =>
                            updateSalesAgentField({
                              agentLoaStatus: "WRITTEN_ATTACH"
                            })
                          }
                        />
                        <span>
                          Written LOA, I will attach the file in this
                          submission.
                        </span>
                      </label>
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="loaStatus"
                          value="WRITTEN_INBOX"
                          checked={context.agentLoaStatus === "WRITTEN_INBOX"}
                          onChange={() =>
                            updateSalesAgentField({
                              agentLoaStatus: "WRITTEN_INBOX"
                            })
                          }
                        />
                        <span>
                          Written LOA, Compliance can find it in the company
                          inbox.
                        </span>
                      </label>
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="loaStatus"
                          value="VERBAL_RECORDING"
                          checked={
                            context.agentLoaStatus === "VERBAL_RECORDING"
                          }
                          onChange={() =>
                            updateSalesAgentField({
                              agentLoaStatus: "VERBAL_RECORDING"
                            })
                          }
                        />
                        <span>
                          Verbal LOA, Compliance can find recording in the
                          system.
                        </span>
                      </label>
                      <label className="flex items-center space-x-1">
                        <input
                          type="radio"
                          name="loaStatus"
                          value="EXISTING"
                          checked={context.agentLoaStatus === "EXISTING"}
                          onChange={() =>
                            updateSalesAgentField({
                              agentLoaStatus: "EXISTING"
                            })
                          }
                        />
                        <span>
                          Existing LOA, I have recently done a deal for the same
                          company.
                        </span>
                      </label>
                    </div>
                  </div>

                  {/* Deal Submission Notes */}
                  <div className="flex flex-col gap-2">
                    <Label className="font-semibold">
                      Deal Submission Notes
                    </Label>
                    <Textarea
                      placeholder="Enter any additional deal notes here..."
                      value={context.submissionNotes}
                      onChange={e =>
                        updateSalesAgentField({
                          submissionNotes: e.target.value
                        })
                      }
                    />
                  </div>

                  {/* Buttons: Cancel or Submit */}
                  <div className="flex gap-2">
                    {dealSnapshot.matches({
                      DEAL_INITIATED: "DEAL_CREATED"
                    }) ? (
                      <Button
                        variant="destructive"
                        onClick={() => send({ type: "salesAgent.cancelDeal" })}
                      >
                        Cancel Deal
                      </Button>
                    ) : (
                      <Button
                        variant="secondary"
                        onClick={() => send({ type: "salesAgent.cancelDeal" })}
                      >
                        Undo Cancel
                      </Button>
                    )}

                    {dealSnapshot.matches({
                      DEAL_INITIATED: "DEAL_CREATED"
                    }) && (
                      <Button
                        variant="outline"
                        onClick={() =>
                          send({
                            type: "salesAgent.submitDeal",
                            payload: {
                              isCot: context.isCot,
                              agentCreditCheck: context.agentCreditCheck,
                              agentLoaStatus: context.agentLoaStatus,
                              submissionNotes: context.submissionNotes
                            }
                          })
                        }
                      >
                        Submit to Compliance
                      </Button>
                    )}
                  </div>
                </div>
              )}
              {/* ------------------ DEAL_CANCELLED UI (Sales Agent) ------------------ */}
              {dealSnapshot.matches({ DEAL_INITIATED: "DEAL_CANCELLED" }) && (
                <div className="mt-2 flex flex-col gap-4">
                  {/* Single event to resume using history */}
                </div>
              )}
              {dealSnapshot.matches("DEAL_SUBMITTED") && (
                <div className="mt-2 flex flex-col gap-4">
                  {currentRole === "SALES_AGENT" && (
                    <Button
                      variant="destructive"
                      onClick={() => send({ type: "salesAgent.cancelDeal" })}
                    >
                      Cancel Submission
                    </Button>
                  )}
                </div>
              )}
              {/* ------------------ DEAL_SUBMITTED UI (Compliance Officer) ------------------ */}
              {((currentRole === "COMPLIANCE_OFFICER" &&
                dealSnapshot.matches("DEAL_SUBMITTED")) ||
                dealSnapshot.matches("DEAL_COMPLIANCE_REVIEWING")) && (
                <div className="mt-2 flex flex-col gap-4">
                  {/* 1) Credit Check */}
                  <SelectWrapper>
                    <Label>Credit Check</Label>
                    <Select
                      value={context.creditCheck}
                      onValueChange={(value: string) =>
                        updateComplianceField({
                          creditCheck: value as CreditCheck
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Credit Check" />
                      </SelectTrigger>
                      <SelectContent position="popper">
                        <SelectItem value="NOT_REQUIRED">
                          NOT_REQUIRED
                        </SelectItem>
                        <SelectItem value="PASS">PASS</SelectItem>
                        <SelectItem value="FAILED">FAILED</SelectItem>
                      </SelectContent>
                    </Select>
                  </SelectWrapper>

                  {/* 2) Contract Check */}
                  <SelectWrapper>
                    <Label>Contract Check</Label>
                    <Select
                      value={context.contractCheck}
                      onValueChange={(value: string) =>
                        updateComplianceField({
                          contractCheck: value as ContractCheck
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Contract Check" />
                      </SelectTrigger>
                      <SelectContent position="popper">
                        <SelectItem value="CONTRACT_ACCEPTED">
                          CONTRACT_ACCEPTED
                        </SelectItem>
                        <SelectItem value="CONTRACT_ACCEPTED__WITH_CONFIRMATION_CALL">
                          CONTRACT_ACCEPTED__WITH_CONFIRMATION_CALL
                        </SelectItem>
                        <SelectItem value="CONTRACT_REJECTED_WITH_CONFIRMATION_CALL_REQUIRED">
                          CONTRACT_REJECTED_WITH_CONFIRMATION_CALL_REQUIRED
                        </SelectItem>
                        <SelectItem value="CONTRACT_REJECTED">
                          CONTRACT_REJECTED
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </SelectWrapper>

                  {/* 3) LOA Check */}
                  <SelectWrapper>
                    <Label>LOA Check</Label>
                    <Select
                      value={context.loaCheck}
                      onValueChange={(value: string) =>
                        updateComplianceField({ loaCheck: value as LoaCheck })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select LOA Check" />
                      </SelectTrigger>
                      <SelectContent position="popper">
                        <SelectItem value="LOA_ACCEPTED">
                          LOA_ACCEPTED
                        </SelectItem>
                        <SelectItem value="LOA_REJECTED_NOT_FOUND">
                          LOA_REJECTED_NOT_FOUND
                        </SelectItem>
                        <SelectItem value="LOA_REJECTED_INVALID_OR_INCORRECT">
                          LOA_REJECTED_INVALID_OR_INCORRECT
                        </SelectItem>
                        <SelectItem value="LOA_REJECTED_INSUFFICIENT_COVER_PERIOD">
                          LOA_REJECTED_INSUFFICIENT_COVER_PERIOD
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </SelectWrapper>

                  {/* 4) Next Stage */}
                  <SelectWrapper>
                    <Label>Next Stage</Label>
                    <Select
                      value={nextStage || ""}
                      onValueChange={(value: string) =>
                        setNextStage(value as NextStage)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose next stage" />
                      </SelectTrigger>
                      <SelectContent position="popper">
                        <SelectItem value="NEXT_STAGE_SUBMITTED_TO_SUPPLIER">
                          Submitted to Supplier
                        </SelectItem>
                        <SelectItem value="NEXT_STAGE_AWAITING_SUBMISSION">
                          Awaiting Submission
                        </SelectItem>
                        <SelectItem value="NEXT_STAGE_REJECTED_BY_COMPLIANCE">
                          Rejected by Compliance
                        </SelectItem>
                        <SelectItem value="NEXT_STAGE_CANCELLED_BY_COMPLIANCE">
                          Cancelled by Compliance
                        </SelectItem>
                        <SelectItem value="NEXT_STAGE_DEAD_BY_COMPLIANCE">
                          Dead by Compliance
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </SelectWrapper>

                  {/* Buttons: Save or Submit */}
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={async () => {
                        // 1) Send event to update the machine
                        send({
                          type: "complianceOfficer.saveChecks"
                        });

                        // 2) Fake server call
                        await saveMachineStateAndContext();

                        // 3) Show toaster
                        toast({
                          title: "Saved",
                          description:
                            "Fake server call complete. The checks have been saved.",
                          variant: "success"
                        });

                        // 4) Reset dirty flag in the machine
                        send({ type: "resetDirtyFlag" });
                      }}
                    >
                      Save Checks
                    </Button>
                    <Button
                      // disabled={
                      //   !dealSnapshot.can("complianceOfficer.submitNextStage")
                      // }
                      onClick={() =>
                        send({
                          type: "complianceOfficer.submitNextStage"
                        })
                      }
                    >
                      Submit (to Next Stage)
                    </Button>
                  </div>
                </div>
              )}
              {/* ------------------ DEAL_COMPLIANCE_AWAITING ------------------ */}
              {dealSnapshot.matches("DEAL_COMPLIANCE_AWAITING") && (
                <div className="mt-2 text-blue-700">
                  <strong>The deal is awaiting final submission.</strong>
                  <p>No further actions are available.</p>
                </div>
              )}
              {/* ------------------ DEAL_COMPLIANCE_REJECTED ------------------ */}
              {dealSnapshot.matches("DEAL_COMPLIANCE_REJECTED") && (
                <div className="mt-2 text-red-700">
                  <strong>The deal has been rejected by compliance.</strong>
                  <p>No further actions are available.</p>
                </div>
              )}
              {/* ------------------ DEAL_COMPLIANCE_CANCELLED ------------------ */}
              {dealSnapshot.matches("DEAL_COMPLIANCE_CANCELLED") && (
                <div className="mt-2 text-red-700">
                  <strong>The deal has been cancelled by compliance.</strong>
                  <p>No further actions are available.</p>
                </div>
              )}
              {/* ------------------ DEAL_COMPLIANCE_DEAD ------------------ */}
              {dealSnapshot.matches("DEAL_COMPLIANCE_DEAD") && (
                <div className="mt-2 text-red-700">
                  <strong>The deal has been marked dead by compliance.</strong>
                  <p>No further actions are available.</p>
                </div>
              )}
              {/* ------------------ DEAL_SUBMITTED_TO_SUPPLIER ------------------ */}
              {dealSnapshot.matches("DEAL_SUBMITTED_TO_SUPPLIER") && (
                <div className="mt-2 text-green-700">
                  <strong>The deal has been submitted to the supplier.</strong>
                  <p>No further actions are available.</p>
                </div>
              )}
            </>
          ) : (
            <div className="mt-2 mb-2">
              The current deal state is{" "}
              <span className="bg-muted-background font-mono text-muted-foreground italic">
                {String(dealSnapshot.value)}
              </span>{" "}
              which cannot be actioned by the current user role{" "}
              <span className="bg-muted-background font-mono text-muted-foreground italic">
                {humanize(currentRole)}
              </span>
              .
            </div>
          )}
        </CardContent>
      </Card>

      {/* ---- Card 4: Debug / Machine State ---- */}
      <Card>
        <CardHeader>
          <CardTitle>Machine State</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="rounded-md bg-card p-2 text-card-foreground">
            {JSON.stringify(dealSnapshot, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
