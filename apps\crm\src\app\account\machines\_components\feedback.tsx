"use client";

import { createBrowserInspector } from "@statelyai/inspect";
import { Button } from "@watt/crm/components/ui/button";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { useMachine } from "@xstate/react";
import { feedbackMachine } from "../machines/feedback-machine";

const { inspect } = createBrowserInspector({
  // Comment out the line below to start the inspector
  autoStart: false
});

export default function Feedback() {
  const [state, send] = useMachine(feedbackMachine, {
    inspect
  });

  if (state.matches("closed")) {
    return (
      <div>
        <em>Feedback form closed.</em>
        <br />
        <Button
          variant="outline"
          type="button"
          onClick={() => {
            send({ type: "restart" });
          }}
        >
          Provide more feedback
        </Button>
      </div>
    );
  }

  return (
    <div className="absolute top-0 right-0">
      <Button
        variant="outline"
        type="button"
        onClick={() => {
          send({ type: "close" });
        }}
      >
        Close
      </Button>
      {!state.matches("prompt") && (
        <div>
          Not in prompt state.
          <div>
            <Button
              variant="outline"
              type="button"
              onClick={() => send({ type: "feedback.good" })}
            >
              Should not allow this action unless in prompt state.
            </Button>
          </div>
        </div>
      )}
      {state.matches("prompt") && (
        <div className="step">
          <h2>How was your experience?</h2>
          <Button
            variant="outline"
            type="button"
            onClick={() => send({ type: "feedback.good" })}
          >
            Good
          </Button>
          <Button
            variant="outline"
            type="button"
            onClick={() => send({ type: "feedback.bad" })}
          >
            Bad
          </Button>
        </div>
      )}

      {state.matches("thanks") && (
        <div className="step">
          <h2>Thanks for your feedback.</h2>
          {state.context.feedback.length > 0 && (
            <p>"{state.context.feedback}"</p>
          )}
        </div>
      )}

      {state.matches("form") && (
        <form
          className="step"
          onSubmit={ev => {
            ev.preventDefault();
            send({
              type: "submit"
            });
          }}
        >
          <h2>What can we do better?</h2>
          <Textarea
            name="feedback"
            rows={4}
            placeholder="So many things..."
            onChange={ev => {
              send({
                type: "feedback.update",
                value: ev.target.value
              });
            }}
          />
          <Button
            type="submit"
            variant="secondary"
            disabled={!state.can({ type: "submit" })}
          >
            Submit
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              send({ type: "back" });
            }}
          >
            Back
          </Button>
        </form>
      )}
    </div>
  );
}
