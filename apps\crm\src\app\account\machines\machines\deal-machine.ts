import { setup } from "xstate";

const machine = setup({
  types: {
    events: {} as
      | { type: "salesAgent.cancelDeal" }
      | { type: "salesAgent.submittedToCompliance" }
      | { type: "user.resume" }
  }
}).createMachine({
  id: "dealFlow",
  initial: "DEAL_INITIATED",
  states: {
    DEAL_INITIATED: {
      on: {
        "salesAgent.cancelDeal": {
          target: "DEAL_CANCELLED_BY_SALES_AGENT"
        },
        "salesAgent.submittedToCompliance": {
          target: "DEAL_SUBMITTED_TO_COMPLIANCE"
        }
      }
    },
    DEAL_CANCELLED_BY_SALES_AGENT: {
      type: "final"
    },
    DEAL_SUBMITTED_TO_COMPLIANCE: {
      type: "compound",
      initial: "review",
      states: {
        review: {
          on: {
            "user.resume": {
              target: "hist"
            }
          }
        },
        approved: {},
        rejected: {},
        hist: {
          type: "history",
          history: "shallow",
          target: "review" // Default state if no history exists
        }
      }
    }
  }
});

export { machine };
