import { assign, setup } from "xstate";

export const feedbackMachine = setup({
  types: {
    context: {} as { feedback: string },
    events: {} as
      | {
          type: "feedback.good";
        }
      | {
          type: "feedback.bad";
        }
      | {
          type: "feedback.update";
          value: string;
        }
      | { type: "submit" }
      | {
          type: "close";
        }
      | { type: "back" }
      | { type: "restart" }
  },
  guards: {
    feedbackValid: ({ context }) => context.feedback.length > 0
  },
  actions: {
    // onUpdateFeedback2: assign(({ context, event }) => {
    //   return {
    //     feedback: event.value
    //   };
    // }),
    onUpdateFeedback: assign((args, { value }: { value: string }) => {
      return {
        feedback: value
      };
    })
  }
}).createMachine({
  /** @xstate-layout N4IgpgJg5mDOIC5QDMyQEYEMDGBrAxNgDYD2sYA2gAwC6ioADmQJYAuzJAdvSAB6IBGAVQB0AFgCcAJikAOAMxiqVAGyzZEgKwAaEAE9EAWjliRmgOwSVY85okC588-IC+L3agw5cIhgCcSAFsGVnxPCCw8ESgSEghqOiQQJlg2Dm4k-gR5RRFbWRUJcwFNeSoxKQFdAwRDATEBEQF5ZvM2svkVTtk3DzQI718A4NDwyJ8seNoeFLSuHizZUSopeVK7czFS63lqo3rG5tb2qk7u3pAxweQSP0Cw-vGRAFcGCExWSmmk2fZ5zMQ8ik5jyFikRSc8iK5lkOn0ghkIisJWEBSh5kqAguVyiNzu+HGCRmLD+GVAWVWogKWxy5i6mk0Amke1qByaLScJzOKk02Me11u91gz3QgTYRJ+JPSC0BVE0eWcMKoRQkVBhLNkIikjNUQIsAnMKzEYj5XiixDIkHwfjgrEwflYEsYUv+5MB1hECgOsK6lTaLMMZRBUjUWzE6INxV5F04cTgPBxuGJqVJMuyUhEKzWayKNJ2Aahpi2Fk0VA0quKtlNAyi-iCIWTczJfEEcpELR5ykN6i0TIDMkaEg0PLUilLap67ku-NxgsbqYBCFW8i1RSWIdkjkUYhZKgz4PKticcrEKgNKmrT1YAAtMJxcPBJSnpYvOip21CVDygRvbHCaoYbRavUmjAvIsjNOUkiXoMFrkBA84vm6S4hk0qiMvUZ4SBIY79rIpj4ZokhSMqMhiPhbhuEAA */
  id: "feedback",
  initial: "prompt",
  context: {
    feedback: ""
  },
  states: {
    prompt: {
      on: {
        "feedback.good": "thanks",
        "feedback.bad": "form"
      }
    },
    form: {
      on: {
        "feedback.update": {
          actions: assign({
            feedback: ({ event }) => event.value
          })
        },
        back: { target: "prompt" },
        submit: {
          guard: "feedbackValid",
          target: "thanks"
        }
      }
    },
    thanks: {},
    closed: {
      on: {
        restart: {
          target: "prompt",
          actions: assign({
            feedback: ""
          })
        }
      }
    }
  },
  // How do I read this
  on: {
    // I do not understand why a dot is needed here.
    // Short hand notion for on "event" target "state"
    // close: ".closed"
    close: {
      target: ".closed"
    }
  }
});
