"use client";

import type { HeadlessService } from "@novu/headless";
import {
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { useNotifications } from "@watt/crm/hooks/use-notifications";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import {
  type PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";
import {
  type NotificationTableConfigKeys,
  tableConfig
} from "../data-table-config";
type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

type NotificationDataTableProps = PropsWithChildren<{
  tag: NotificationTableConfigKeys;
  novuHeadlessService: HeadlessService | null;
}>;

export function NotificationDataTable({
  tag,
  children,
  novuHeadlessService
}: NotificationDataTableProps) {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [showViewModal, setShowViewModal] = useState(false);

  const { queryParams, removeQueryParams } = useQueryParams<{
    notificationId: string;
  }>();

  useEffect(() => {
    if (queryParams.notificationId) {
      setShowViewModal(true);
    }
  }, [queryParams.notificationId]);

  const {
    notifications,
    markAsUnread,
    markAsRead,
    archive,
    unarchive,
    hasMoreNotifications,
    isLoading,
    fetchNextPage
  } = useNotifications({
    columnFilters,
    tagFilter: tag,
    novuHeadlessService
  });

  const allItems = useMemo(() => notifications ?? [], [notifications]);

  const { columns, Toolbar, ViewModal } = useMemo(() => {
    const config = tableConfig[tag];
    return {
      columns: config.columns({
        markAsUnread,
        markAsRead,
        archive,
        unarchive
      }),
      Toolbar: config.Toolbar,
      ViewModal: config.ViewModal
    };
  }, [tag, markAsUnread, markAsRead, archive, unarchive]);

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const table = useReactTable({
    data: allItems,
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      globalFilter,
      columnPinning: { right: ["actions"] }
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    debugTable: false,
    manualFiltering: true
  });

  const closeViewModal = useCallback(() => {
    setShowViewModal(false);
    removeQueryParams(["notificationId"]);
  }, [removeQueryParams]);

  const renderViewModal = useCallback(() => {
    const notification = allItems.find(
      item => item.id === queryParams.notificationId
    );

    if (!notification?.data) {
      return null;
    }

    return (
      <ViewModal
        open={showViewModal}
        onOpenChange={closeViewModal}
        notification={notification}
      />
    );
  }, [
    allItems,
    showViewModal,
    closeViewModal,
    queryParams.notificationId,
    ViewModal
  ]);

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isLoading}
      totalDBRowCount={allItems.length}
      totalFetched={allItems.length}
      hasNextPage={hasMoreNotifications}
      fetchNextPage={fetchNextPage}
    >
      {children}
      <Toolbar table={table} isFiltered={isFiltered} />
      {renderViewModal()}
    </InfiniteScrollDataTable>
  );
}

export function NotificationDataTableSkeleton({ children }: PropsWithChildren) {
  return (
    <div className="py-4">
      {children}
      <DataTableSkeleton
        columnCount={8}
        searchableColumnCount={0}
        filterableColumnCount={2}
        cellWidths={["12rem", "14rem"]}
        withPagination={false}
        shrinkZero
      />
    </div>
  );
}
