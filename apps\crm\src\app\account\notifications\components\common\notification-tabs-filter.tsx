import { NOTIFICATION_TABS_FILTERS } from "@watt/crm/config/notifications";

import type { NotificationCount } from "@watt/api/src/types/notification";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { NOTIFICATION_TAGS } from "@watt/notifications/src/config";
import { useCallback } from "react";
import { z } from "zod";

import type { NotificationTagKey } from "@watt/crm/config/notifications";
type NotificationTabsFilterProps = {
  unreadCount: NotificationCount[];
};

export const NotificationTabsFilter = ({
  unreadCount
}: NotificationTabsFilterProps) => {
  const queryParamsSchema = z.object({
    filter: z
      .enum(
        Object.values(NOTIFICATION_TAGS) as [
          NotificationTagKey,
          ...NotificationTagKey[]
        ]
      )
      .optional(),
    modal: z.string().optional()
  });

  const { queryParams, setQueryParams } =
    useQueryParams<z.infer<typeof queryParamsSchema>>();

  const renderUnreadCountBadge = useCallback(
    (tag: NotificationTagKey) => {
      if (!unreadCount) {
        return null;
      }

      const count = unreadCount.find(count =>
        count.filter.tags?.includes(tag)
      )?.count;

      return count ? (
        <Badge className="ml-2 size-5 justify-center p-0" variant="secondary">
          {count}
        </Badge>
      ) : null;
    },
    [unreadCount]
  );

  return (
    <div className="flex flex-wrap items-center gap-2">
      <div className="mb-2 inline-flex space-x-4 rounded-md bg-muted-foreground/10 px-2 py-1.5">
        {Object.values(NOTIFICATION_TABS_FILTERS).map(notificationTab => (
          <Button
            key={notificationTab.tag}
            variant={
              queryParams.filter === notificationTab.tag ? "outline" : "ghost"
            }
            onClick={() =>
              setQueryParams({
                filter: notificationTab.tag
              })
            }
            className={cn(
              "h-6 whitespace-nowrap rounded-sm border-none text-muted-foreground/60 hover:bg-transparent hover:text-muted-foreground/60",
              queryParams.filter === notificationTab.tag &&
                "text-selected hover:bg-background hover:text-selected"
            )}
          >
            {notificationTab.label}
            {renderUnreadCountBadge(notificationTab.tag)}
          </Button>
        ))}
      </div>
    </div>
  );
};
