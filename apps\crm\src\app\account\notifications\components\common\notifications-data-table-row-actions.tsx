"use client";

import { Archive, CheckSquare, Eye, MoreHorizontal } from "lucide-react";

import type { Notification } from "@watt/api/src/types/notification";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";

interface NotificationsDataTableRowActionsProps {
  notification: Notification;
  markAsUnread: (messageId: string) => void;
  markAsRead: (messageId: string) => void;
  archive: (messageId: string) => void;
  unarchive: (messageId: string) => void;
  onView: () => void;
}

export function NotificationsDataTableRowActions({
  notification,
  markAsUnread,
  markAsRead,
  archive,
  unarchive,
  onView
}: NotificationsDataTableRowActionsProps) {
  const { read, archived } = notification;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuItem onClick={onView}>
          <Eye className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          View
        </DropdownMenuItem>
        {!read && (
          <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
            <CheckSquare className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Mark as read
          </DropdownMenuItem>
        )}
        {read && (
          <DropdownMenuItem onClick={() => markAsUnread(notification.id)}>
            <CheckSquare className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Mark as unread
          </DropdownMenuItem>
        )}
        {archived && (
          <DropdownMenuItem onClick={() => unarchive(notification.id)}>
            <Archive className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Unarchive
          </DropdownMenuItem>
        )}
        {!archived && (
          <DropdownMenuItem onClick={() => archive(notification.id)}>
            <Archive className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Archive
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
