import { NOTIFICATION_TAGS } from "@watt/notifications/src/config";
import { columns as announcementsColumns } from "./tab-announcements/columns";
import { DataTableToolbar as DataTableToolbarAnnouncements } from "./tab-announcements/data-table-toolbar";
import { ViewAnnouncementNotificationModal } from "./tab-announcements/view-announcement-notification-modal";
import { columns as callbacksColumns } from "./tab-callbacks/columns";
import { DataTableToolbar as DataTableToolbarCallbacks } from "./tab-callbacks/data-table-toolbar";
import { ViewCallbackNotificationModal } from "./tab-callbacks/view-callback-notification-modal";

export const tableConfig = {
  [NOTIFICATION_TAGS.ANNOUNCEMENTS]: {
    columns: announcementsColumns,
    Toolbar: DataTableToolbarAnnouncements,
    ViewModal: ViewAnnouncementNotificationModal
  },
  [NOTIFICATION_TAGS.CALLBACKS]: {
    columns: callbacksColumns,
    Toolbar: DataTableToolbarCallbacks,
    ViewModal: ViewCallbackNotificationModal
  }
};

export type NotificationTableConfigKeys = keyof typeof tableConfig;
