"use client";

import { useQuery } from "@tanstack/react-query";
import { useNovu } from "@watt/crm/hooks/use-novu";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useAppStore } from "@watt/crm/store/app-store";
import { getHeadlessService } from "@watt/crm/utils/notification-headless-instance";
import { NOTIFICATION_TAGS } from "@watt/notifications/src/config";
import { useEffect } from "react";
import {
  NotificationDataTable,
  NotificationDataTableSkeleton
} from "./common/notification-data-table";
import { NotificationTabsFilter } from "./common/notification-tabs-filter";
import type { NotificationTableConfigKeys } from "./data-table-config";

type NotificationsProps = {
  subscriberId: string;
};

export default function Notifications({ subscriberId }: NotificationsProps) {
  const { queryParams, setQueryParams } = useQueryParams<{
    filter: NotificationTableConfigKeys;
  }>();
  const { unreadCount } = useNovu();

  useEffect(() => {
    if (!queryParams.filter) {
      setQueryParams({
        filter: NOTIFICATION_TAGS.ANNOUNCEMENTS
      });
    }
  }, [queryParams, setQueryParams]);

  const {
    data: service,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["initializeHeadlessService", subscriberId],
    queryFn: () => getHeadlessService(subscriberId),
    enabled: false,
    refetchOnWindowFocus: false
  });

  useEffect(() => {
    if (!subscriberId) {
      return;
    }
    refetch();
  }, [subscriberId, refetch]);

  if (isLoading || !service) {
    return (
      <NotificationDataTableSkeleton>
        <h1 className="pb-2 font-bold text-xl tracking-tight">Notifications</h1>
        <NotificationTabsFilter unreadCount={unreadCount} />
      </NotificationDataTableSkeleton>
    );
  }

  const { filter } = queryParams;

  if (!filter || error) {
    return <div>Error loading notifications</div>;
  }

  return (
    <NotificationDataTable
      key={filter}
      tag={filter}
      novuHeadlessService={service}
    >
      <h1 className="font-bold text-xl tracking-tight">Notifications</h1>
      <NotificationTabsFilter unreadCount={unreadCount} />
    </NotificationDataTable>
  );
}
