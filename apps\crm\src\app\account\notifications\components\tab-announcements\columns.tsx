"use client";

import { ArchiveX } from "lucide-react";

import type { ColumnDef } from "@tanstack/react-table";
import type { GetAllPeople } from "@watt/api/src/router/people";
import {
  ANNOUNCEMENT_CATEGORIES,
  type Notification
} from "@watt/api/src/types/notification";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { getAnnouncementsAudience } from "@watt/crm/utils/get-announcements-audience";
import { truncateMessage } from "@watt/crm/utils/truncate-message";
import type { AnnouncementPayload } from "@watt/notifications/src/novu";
import { isValid } from "date-fns";
import { useState } from "react";
import { NotificationsDataTableRowActions } from "../common/notifications-data-table-row-actions";
import { ViewAnnouncementNotificationModal } from "./view-announcement-notification-modal";

interface ColumnsProps {
  markAsUnread: (messageId: string) => void;
  markAsRead: (messageId: string) => void;
  archive: (messageId: string) => void;
  unarchive: (messageId: string) => void;
}

// Update the columns to reflect notifications
export const columns = ({
  markAsUnread,
  markAsRead,
  archive,
  unarchive
}: ColumnsProps): ColumnDef<Notification>[] => [
  {
    id: "status",
    accessorKey: "read",
    header: () => "",
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        {row.original.archived ? (
          <ArchiveX className="size-3" />
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <div
                  className={cn(
                    "size-3 rounded-full ",
                    row.original.read ? "bg-foreground/30" : "bg-secondary"
                  )}
                />
              </TooltipTrigger>
              <TooltipContent side="bottom">
                {row.original.read ? "Read" : "Unread"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    )
  },

  {
    accessorKey: "data.payload.subject",
    header: "Subject",
    cell: ({ row }) => {
      const payload = row.original.data?.payload as AnnouncementPayload;
      return payload?.subject || "";
    },
    meta: {
      dropdownLabel: "Subject"
    }
  },

  {
    id: "category",
    accessorKey: "data.payload.category",
    header: "Category",
    cell: ({ row }) => {
      const payload = row.original.data?.payload as AnnouncementPayload;
      const category =
        payload?.category as keyof typeof ANNOUNCEMENT_CATEGORIES;
      return ANNOUNCEMENT_CATEGORIES[category] || "";
    },
    meta: {
      dropdownLabel: "Category"
    }
  },

  {
    accessorKey: "data.payload.message",
    header: "Content",
    cell: ({ row }) => {
      const payload = row.original.data?.payload as AnnouncementPayload;
      const message = payload?.message || "";
      return (
        <div className="min-w-[240px] max-w-[380px] text-sm">
          {truncateMessage(message || "")}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Content"
    }
  },

  {
    accessorKey: "data.payload.teams",
    header: "Audience",
    cell: ({ row }) => {
      const payload = row.original.data?.payload as AnnouncementPayload;
      const teams = payload?.teams || [];

      return getAnnouncementsAudience(teams);
    },
    meta: {
      dropdownLabel: "Audience"
    }
  },

  {
    accessorKey: "createdAt",
    header: "Received At",
    cell: ({ row }) => {
      const createdAt = row.original.createdAt;
      if (!createdAt || !isValid(new Date(createdAt))) {
        return "";
      }
      const createdAtDate = formatDate(
        new Date(createdAt),
        dateFormats.DD_MM_YYYY
      );
      const createdAtTime = formatDate(new Date(createdAt), dateFormats.HH_MM);
      return (
        <p className="text-sm">
          {createdAtDate}
          <br />
          {createdAtTime}
        </p>
      );
    },
    meta: {
      dropdownLabel: "Received At"
    }
  },

  {
    accessorKey: "data.payload.createdBy",
    header: "Created By",
    cell: ({ row }) => {
      const payload = row.original.data?.payload as AnnouncementPayload;
      return payload?.createdBy || "";
    },
    meta: {
      dropdownLabel: "Created By"
    }
  },

  {
    id: "actions",
    cell: ({ row }) => {
      const [openViewModal, setOpenViewModal] = useState(false);
      const notification = row.original;

      const openViewNotificationModal = () => {
        setOpenViewModal(true);
      };

      const onCloseViewNotificationModal = () => {
        setOpenViewModal(false);
        markAsRead(notification.id);
      };
      return (
        <>
          <NotificationsDataTableRowActions
            notification={notification}
            markAsUnread={markAsUnread}
            markAsRead={markAsRead}
            archive={archive}
            unarchive={unarchive}
            onView={openViewNotificationModal}
          />
          <ViewAnnouncementNotificationModal
            open={openViewModal}
            onOpenChange={onCloseViewNotificationModal}
            notification={notification}
          />
        </>
      );
    }
  }
];

export type PeopleDataTableRow = GetAllPeople;
