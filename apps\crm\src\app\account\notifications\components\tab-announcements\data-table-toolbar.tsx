"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { z } from "zod";

import { ANNOUNCEMENT_CATEGORIES } from "@watt/api/src/types/notification";
import { DataTableSelectFilter } from "@watt/crm/components/data-table/data-table-select-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import type { NotificationTagKey } from "@watt/crm/config/notifications";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import { useAppStore } from "@watt/crm/store/app-store";
import { NOTIFICATION_TAGS } from "@watt/notifications/src/config";
import type { PropsWithChildren } from "react";

interface DataTableToolbarProps<TData> extends PropsWithChildren {
  table: Table<TData>;
  isFiltered: boolean;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const { permissions } = useAppStore(state => state.userData);
  const { isSalesAgent } = permissions;

  const queryParamsSchema = z.object({
    search: z.string().optional()
  });

  const extendedQueryParamsSchema = queryParamsSchema.extend({
    filter: z
      .enum(
        Object.values(NOTIFICATION_TAGS) as [
          NotificationTagKey,
          ...NotificationTagKey[]
        ]
      )
      .optional(),
    modal: z.string().optional()
  });

  const { setQueryParams } =
    useQueryParams<z.infer<typeof extendedQueryParamsSchema>>();

  const { resetFilters } = useSyncTableFilterWithQueryParams(
    table,
    queryParamsSchema
  );

  const showCreateButton = !isSalesAgent;

  return (
    <div className="flex items-center justify-between space-x-2">
      <div className="flex items-center gap-2">
        <DataTableSelectFilter
          column={table.getColumn("category")}
          title="Category"
          options={Object.keys(ANNOUNCEMENT_CATEGORIES).map(key => ({
            label:
              ANNOUNCEMENT_CATEGORIES[
                key as keyof typeof ANNOUNCEMENT_CATEGORIES
              ],
            value: key
          }))}
        />
        <DataTableSelectFilter
          column={table.getColumn("status")}
          title="Status"
          options={[
            {
              label: "Read & Archived",
              value: "read"
            },
            { label: "Unread", value: "unread" }
          ]}
        />
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={resetFilters}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <DataTableViewOptions table={table} />
        {showCreateButton && (
          <Button
            variant="secondary"
            onClick={() => {
              setQueryParams({ modal: "create-notification" });
            }}
            size="sm"
          >
            Create Notification
          </Button>
        )}
      </div>
    </div>
  );
}
