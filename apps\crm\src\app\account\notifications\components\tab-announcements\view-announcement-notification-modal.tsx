import {
  ANNOUNCEMENT_CATEGORIES,
  type Notification
} from "@watt/api/src/types/notification";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { Input } from "@watt/crm/components/ui/input";
import { Label } from "@watt/crm/components/ui/label";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { getAnnouncementsAudience } from "@watt/crm/utils/get-announcements-audience";
import type { AnnouncementPayload } from "@watt/notifications/src/novu";

type ViewAnnouncementNotificationModalProps = {
  notification: Notification;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function ViewAnnouncementNotificationModal({
  notification,
  open,
  onOpenChange
}: ViewAnnouncementNotificationModalProps) {
  const createdAt = notification?.createdAt
    ? formatDate(new Date(notification.createdAt), dateFormats.DD_MM_YYYY_HH_MM)
    : "";
  const payload = notification?.data?.payload as AnnouncementPayload;
  const { subject, category, message, teams, createdBy } = payload;

  return (
    <div>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent onPointerDownOutside={e => e.preventDefault()}>
          <DialogHeader className="space-y-4">
            <DialogTitle>Notification Details</DialogTitle>
            <DialogDescription>
              View the details of this announcement notification.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Subject</Label>
              <Textarea
                className="h-auto max-h-[4.5rem] min-h-[2.5rem] resize-none overflow-y-auto py-2"
                value={subject}
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label className="">Category</Label>
              <Input
                value={
                  ANNOUNCEMENT_CATEGORIES[
                    category as keyof typeof ANNOUNCEMENT_CATEGORIES
                  ]
                }
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label>Content</Label>
              <Textarea
                className="resize-none"
                value={message}
                disabled
                rows={10}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label>Audience</Label>
            <Input value={getAnnouncementsAudience(teams)} disabled />
          </div>
          <div className="space-y-2">
            <Label>Received at</Label>
            <Input value={createdAt} disabled />
          </div>
          <div className="space-y-2">
            <Label>Created by</Label>
            <Input value={createdBy} disabled />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
