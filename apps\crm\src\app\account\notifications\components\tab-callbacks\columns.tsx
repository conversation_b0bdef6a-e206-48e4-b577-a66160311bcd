"use client";

import { ArchiveX } from "lucide-react";

import type { ColumnDef } from "@tanstack/react-table";
import type { GetAllPeople } from "@watt/api/src/router/people";
import type { Notification } from "@watt/api/src/types/notification";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { routes } from "@watt/crm/config/routes";
import {
  getCallbackNotificationCategory,
  isCallbackNotificationPayload,
  isNotificationPayload
} from "@watt/crm/utils/notification-utils";
import { NotificationType } from "@watt/db/src/enums";
import type { InAppNotificationPayload } from "@watt/notifications/src/novu";
import { isValid } from "date-fns";
import Link from "next/link";
import { useState } from "react";
import { NotificationsDataTableRowActions } from "../common/notifications-data-table-row-actions";
import { ViewCallbackNotificationModal } from "./view-callback-notification-modal";

export function getNotificationSubject(notification: Notification) {
  if (!notification.data) {
    return {
      subject: "Unknown Notification"
    };
  }

  const { payload } = notification.data as {
    payload: InAppNotificationPayload;
  };

  const { type } = payload || {};

  switch (type) {
    case NotificationType.UPCOMING_CALLBACK_NOTIFICATION:
      return {
        subject: "Upcoming Callback Alert"
      };

    case NotificationType.TODAY_CALLBACK_NOTIFICATION:
      return {
        subject: "Today's Callback Reminder"
      };

    case NotificationType.VERIFY_CALLBACK_NOTIFICATION:
      return {
        subject: "Verify Callback Completion"
      };

    case NotificationType.OVERDUE_CALLBACK_NOTIFICATION:
      return {
        subject: "Overdue Warning"
      };

    default:
      return {
        subject: "Unknown Notification"
      };
  }
}

interface ColumnsProps {
  markAsUnread: (messageId: string) => void;
  markAsRead: (messageId: string) => void;
  archive: (messageId: string) => void;
  unarchive: (messageId: string) => void;
}

// Update the columns to reflect notifications
export const columns = ({
  markAsUnread,
  markAsRead,
  archive,
  unarchive
}: ColumnsProps): ColumnDef<Notification>[] => [
  {
    id: "status",
    accessorKey: "read",
    header: () => "",
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        {row.original.archived ? (
          <ArchiveX className="size-3" />
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <div
                  className={cn(
                    "size-3 rounded-full ",
                    row.original.read ? "bg-foreground/30" : "bg-secondary"
                  )}
                />
              </TooltipTrigger>
              <TooltipContent side="bottom">
                {row.original.read ? "Read" : "Unread"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    )
  },

  {
    accessorKey: "data.payload.companyName",
    header: "Company",
    cell: ({ row }) => {
      const data = row.original.data;
      const payload = isCallbackNotificationPayload(data?.payload)
        ? data.payload
        : undefined;
      const companyName = payload ? payload.companyName : "";
      return companyName;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company"
    }
  },

  {
    accessorKey: "data.payload.siteRef",
    header: "Site ID",
    cell: ({ row }) => {
      const data = row.original.data;
      const payload = isCallbackNotificationPayload(data?.payload)
        ? data.payload
        : undefined;
      const siteRefId = payload ? payload.siteRefId : 0;
      const siteUrl = `${routes.company.replace("[id]", payload?.companyId || "")}/sites/${siteRefId}`;
      return (
        <Link href={siteUrl} target="_blank">
          <Button variant="link" className="p-2">
            {composeSiteRef(siteRefId)}
          </Button>
        </Link>
      );
    },
    meta: {
      dropdownLabel: "Site ID"
    }
  },

  {
    id: "type",
    accessorKey: "data.payload.type",
    header: "Category",
    cell: ({ row }) => {
      const data = row.original.data;
      const payload = isNotificationPayload(data?.payload)
        ? data.payload
        : undefined;
      if (!payload) {
        return "";
      }
      return getCallbackNotificationCategory(payload.type);
    },
    meta: {
      dropdownLabel: "Category"
    }
  },

  {
    accessorKey: "data.payload.contactName",
    header: "Contact",
    cell: ({ row }) => {
      const data = row.original.data;
      const payload = isCallbackNotificationPayload(data?.payload)
        ? data.payload
        : undefined;
      const contactName = payload ? payload.contactName : "";
      const contactPhone = payload ? payload.contactPhone : "";
      return `${contactName}\n ${formatPhoneNumber(contactPhone)}`;
    },
    meta: {
      dropdownLabel: "Contact"
    }
  },

  {
    accessorKey: "data.payload.subject",
    header: "Subject",
    cell: ({ row }) => {
      const data = row.original.data;
      const payload = isCallbackNotificationPayload(data?.payload)
        ? data.payload
        : undefined;
      const subject = payload?.subject || "";
      return <div>{subject}</div>;
    },
    meta: {
      dropdownLabel: "Subject"
    }
  },

  {
    accessorKey: "data.payload.action",
    header: "Action",
    cell: ({ row }) => {
      const data = row.original.data;
      const payload = isCallbackNotificationPayload(data?.payload)
        ? data.payload
        : undefined;
      const action = payload?.action || "-";
      return <div>{action}</div>;
    },
    meta: {
      dropdownLabel: "Action"
    }
  },

  {
    accessorKey: "createdAt",
    header: "Received At",
    cell: ({ row }) => {
      const createdAt = row.original.createdAt;
      if (!createdAt || !isValid(new Date(createdAt))) {
        return "";
      }
      const createdAtDate = formatDate(
        new Date(createdAt),
        dateFormats.DD_MM_YYYY
      );
      const createdAtTime = formatDate(new Date(createdAt), dateFormats.HH_MM);
      return (
        <p className="text-sm">
          {createdAtDate}
          <br />
          {createdAtTime}
        </p>
      );
    },
    meta: {
      dropdownLabel: "Received At"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const [openViewModal, setOpenViewModal] = useState(false);
      const notification = row.original;

      const openViewNotificationModal = () => {
        setOpenViewModal(true);
      };

      const onCloseViewNotificationModal = () => {
        setOpenViewModal(false);
        markAsRead(notification.id);
      };
      return (
        <>
          <NotificationsDataTableRowActions
            notification={notification}
            markAsUnread={markAsUnread}
            markAsRead={markAsRead}
            archive={archive}
            unarchive={unarchive}
            onView={openViewNotificationModal}
          />

          <ViewCallbackNotificationModal
            open={openViewModal}
            onOpenChange={onCloseViewNotificationModal}
            notification={notification}
          />
        </>
      );
    }
  }
];

export type PeopleDataTableRow = GetAllPeople;
