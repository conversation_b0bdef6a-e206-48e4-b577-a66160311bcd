"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import { z } from "zod";

import { DataTableSelectFilter } from "@watt/crm/components/data-table/data-table-select-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { CALLBACK_NOTIFICATION_CATEGORIES } from "@watt/crm/config/notifications";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import type { PropsWithChildren } from "react";

interface DataTableToolbarProps<TData> extends PropsWithChildren {
  table: Table<TData>;
  isFiltered: boolean;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const queryParamsSchema = z.object({
    search: z.string().optional()
  });

  const { resetFilters } = useSyncTableFilterWithQueryParams(
    table,
    queryParamsSchema
  );

  return (
    <div className="flex items-center justify-between space-x-2">
      <div className="flex items-center space-x-2">
        <DataTableSelectFilter
          column={table.getColumn("type")}
          title="Category"
          options={Object.keys(CALLBACK_NOTIFICATION_CATEGORIES).map(key => ({
            label:
              CALLBACK_NOTIFICATION_CATEGORIES[
                key as keyof typeof CALLBACK_NOTIFICATION_CATEGORIES
              ],
            value: key
          }))}
        />
        <DataTableSelectFilter
          column={table.getColumn("status")}
          title="Status"
          options={[
            {
              label: "Read & Archived",
              value: "read"
            },
            { label: "Unread", value: "unread" }
          ]}
        />
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={resetFilters}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
