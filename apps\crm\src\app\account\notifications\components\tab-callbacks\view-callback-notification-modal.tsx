import type { Notification } from "@watt/api/src/types/notification";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { Input } from "@watt/crm/components/ui/input";
import { Label } from "@watt/crm/components/ui/label";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { getCallbackNotificationCategory } from "@watt/crm/utils/notification-utils";
import { NotificationType } from "@watt/db/src/enums";
import type { InAppNotificationPayload } from "@watt/notifications/src/novu";
import { isValid } from "date-fns";

type ViewCallbackNotificationModalProps = {
  notification: Notification;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function ViewCallbackNotificationModal({
  notification,
  open,
  onOpenChange
}: ViewCallbackNotificationModalProps) {
  const createdAt = notification?.createdAt
    ? formatDate(new Date(notification.createdAt), dateFormats.DD_MM_YYYY_HH_MM)
    : "";

  const payload = notification?.data?.payload as InAppNotificationPayload;

  if (!payload || payload.type === NotificationType.ANNOUNCEMENT_NOTIFICATION) {
    return null;
  }

  const {
    companyName,
    siteRefId,
    type,
    subject,
    contactPhone,
    contactName,
    comment,
    callbackTime
  } = payload;

  const category = getCallbackNotificationCategory(type);

  const formatedCallbackTime =
    callbackTime && isValid(new Date(callbackTime))
      ? formatDate(new Date(callbackTime), dateFormats.DD_MM_YYYY_HH_MM)
      : "";

  return (
    <div>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent onPointerDownOutside={e => e.preventDefault()}>
          <DialogHeader className="space-y-4">
            <DialogTitle>Notification Details</DialogTitle>
            <DialogDescription>
              View the details of this callback notification.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Company Name</Label>
              <Input value={companyName} disabled />
            </div>
            <div className="space-y-2">
              <Label className="">Site ID</Label>
              <Input value={composeSiteRef(siteRefId) || ""} disabled />
            </div>
            <div className="space-y-2">
              <Label>Subject</Label>
              <Textarea
                className="h-auto max-h-[4.5rem] min-h-[2.5rem] resize-none overflow-y-auto py-2"
                value={subject}
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label>Category</Label>
              <Input value={category} disabled />
            </div>
          </div>
          <div className="space-y-2">
            <Label>Contact</Label>
            <Input
              value={`${contactName} ${formatPhoneNumber(contactPhone)}`}
              disabled
            />
          </div>
          <div className="space-y-2">
            <Label>Comments</Label>
            <Textarea
              className="resize-none"
              value={comment}
              disabled
              rows={4}
            />
          </div>
          <div className="space-y-2">
            <Label>Callback Time</Label>
            <Input value={formatedCallbackTime} disabled />
          </div>
          <div className="space-y-2">
            <Label>Received At</Label>
            <Input value={createdAt} disabled />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
