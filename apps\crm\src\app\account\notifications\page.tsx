import type { Metadata } from "next";

import { createServerSupabaseClientCRM } from "@watt/common/src/libs/supabase/supabase";
import { getSupabaseUser } from "@watt/db/src/supabase/get-user";
import { forbidden } from "next/navigation";
import Notifications from "./components/notifications";

export const metadata: Metadata = {
  title: "Notifications",
  description: "List of notifications."
};

export default async function NotificationsPage() {
  const supabase = await createServerSupabaseClientCRM();
  const { user } = await getSupabaseUser(supabase);
  const subscriberId = user?.id;

  if (!subscriberId) {
    return forbidden();
  }

  return (
    <div className="px-4">
      <Notifications subscriberId={subscriberId} />
    </div>
  );
}
