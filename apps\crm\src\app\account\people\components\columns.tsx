"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { GetAllPeople } from "@watt/api/src/router/people";
import { formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import {
  dateFilter,
  textFilter
} from "@watt/crm/components/data-table/data-table-filter-functions";

export const columns: ColumnDef<GetAllPeople>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      const { salutation, forename, surname } = row.original;
      return [salutation, forename, surname].filter(Boolean).join(" ");
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Name"
    }
  },
  {
    accessorKey: "companyName",
    accessorFn: row => row.company.name,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ getValue }) => {
      return <div>{humanize(getValue<string>())}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "position",
    header: "Position",
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Position"
    }
  },
  {
    accessorKey: "dateOfBirth",
    accessorFn: row => formatDate(row.dateOfBirth),
    header: "Date of Birth",
    cell: ({ row }) => formatDate(row.original.dateOfBirth),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Date of Birth"
    }
  },
  {
    accessorKey: "lastContacted",
    accessorFn: row => formatDate(row.lastContacted),
    header: "Last Contacted",
    cell: ({ row }) => formatDate(row.original.lastContacted),
    filterFn: dateFilter,
    meta: {
      dropdownLabel: "Last Contacted"
    }
  }
];

export type PeopleDataTableRow = GetAllPeople;
