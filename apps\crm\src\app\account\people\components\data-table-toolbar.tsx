"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import { getUniqueFilterOptions } from "@watt/crm/utils/get-unique-filter-options";
import { z } from "zod";

import { createZodEnumArray } from "@watt/common/src/utils/zod-literal-union";
import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered: boolean;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const uniqueCompanyNames = getUniqueFilterOptions({
    columnId: "companyName",
    table,
    humanizeValues: true
  });

  const queryParamsSchema = z.object({
    search: z.string().optional(),
    position: z.string().optional(),
    ...(uniqueCompanyNames.length && {
      companyName: createZodEnumArray(uniqueCompanyNames)
    })
  });

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Search people..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        {table.getColumn("companyName") && (
          <DataTableFacetedFilter
            column={table.getColumn("companyName")}
            title="Company Name"
            options={uniqueCompanyNames}
            onFilterChange={handleFilterChange}
          />
        )}
        <DataTableFacetedFilter
          column={table.getColumn("position")}
          title="Position"
          options={getUniqueFilterOptions({ columnId: "position", table })}
          onFilterChange={handleFilterChange}
        />
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
