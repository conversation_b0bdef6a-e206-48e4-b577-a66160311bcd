"use client";

import {
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { trpcClient } from "@watt/crm/utils/api";
import { useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";

import { columns } from "./columns";
import { DataTableToolbar } from "./data-table-toolbar";

type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

export default function PeopleDataTable() {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);

  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = trpcClient.people.getAll.useInfiniteQuery(
    {
      searchFilters: {
        columnFilters: debouncedColumnFilters,
        globalFilter
      }
    },
    {
      getNextPageParam: lastPage => lastPage.nextCursor,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      placeholderData: prev => prev,
      trpc: {
        abortOnUnmount: true
      }
    }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = data?.pages?.[0]?.meta?.totalRowCount ?? 0;
  const totalFetched = allItems.length;

  const table = useReactTable({
    data: allItems,
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      globalFilter
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    debugTable: false,
    manualFiltering: true
  });

  if (isLoading) {
    return (
      <div className="space-y-4 py-4">
        <h1 className="font-bold text-xl tracking-tight">People</h1>
        <DataTableSkeleton
          columnCount={table.getAllColumns().length}
          searchableColumnCount={1}
          filterableColumnCount={5}
          cellWidths={["12rem", "14rem"]}
          withPagination={false}
          shrinkZero
        />
      </div>
    );
  }

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
    >
      <h1 className="font-bold text-xl tracking-tight">People</h1>
      <DataTableToolbar table={table} isFiltered={isFiltered} />
    </InfiniteScrollDataTable>
  );
}
