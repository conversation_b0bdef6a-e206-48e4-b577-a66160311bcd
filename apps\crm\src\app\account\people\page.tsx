import type { Metada<PERSON> } from "next";

import { featureToggles } from "@watt/crm/feature-toggles";
import { notFound } from "next/navigation";
import PeopleDataTable from "./components/data-table";

export const metadata: Metadata = {
  title: "People",
  description: "List of people."
};

export default function PeoplePage() {
  if (!featureToggles.routes.people) {
    notFound();
  }

  return (
    <div className="px-4">
      <PeopleDataTable />
    </div>
  );
}
