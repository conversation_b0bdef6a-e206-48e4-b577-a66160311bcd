"use client";

import { File } from "lucide-react";

import type { ColumnDef } from "@tanstack/react-table";
import type { GetAllPriceList } from "@watt/api/src/router/price-lists";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { IconMap } from "@watt/crm/icons/icon-map";

import { formatBytes } from "@watt/common/src/utils/format-bytes";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { formatDate } from "@watt/common/src/utils/format-date";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Badge } from "@watt/crm/components/ui/badge";
import { DataTableRowActions } from "./data-table-row-actions";
import { mimeTypeToFriendlyName } from "./price-list-form";

export const columns: ColumnDef<GetAllPriceList>[] = [
  {
    accessorKey: "friendlyName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" disableSorting />
    ),
    cell: ({ row }) => (
      <div className="flex items-center space-x-2">
        <File className="size-7 text-muted-foreground" />
        <div className="flex flex-col">
          <span className="font-medium">{row.getValue("friendlyName")}</span>
          <div className="flex items-center space-x-2 text-muted-foreground text-xs">
            <Badge variant="secondary" className="h-5 px-1.5">
              {mimeTypeToFriendlyName(row.original.type)}
            </Badge>
            <span>{formatBytes(row.original.size)}</span>
          </div>
        </div>
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Name"
    }
  },
  {
    accessorKey: "supplier",
    accessorFn: priceList => priceList.provider.udcoreId,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Supplier" disableSorting />
    ),
    cell: ({ getValue, row }) => {
      const supplier = getValue() as string;
      const { logoFileName, displayName } = row.original.provider;

      return (
        <div className="flex flex-col items-center justify-center gap-1">
          <ProviderLogo
            logoFileName={logoFileName}
            displayName={displayName}
            height={10}
          />
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Supplier"
    }
  },
  {
    accessorKey: "utilityTypes",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Utilities" disableSorting />
    ),
    cell: ({ row }) => {
      const utilitiesManaged = row.getValue<string[]>("utilityTypes");
      return (
        <div className="flex w-[180px]">
          {Object.keys(IconMap).map(utility => {
            const isActive = utilitiesManaged.includes(utility);
            return (
              <div key={utility} className="mr-1">
                {isActive && IconMap[utility]}
              </div>
            );
          })}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Utilities"
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => (
      <div>
        {formatDate(row.getValue("createdAt"), dateFormats.DD_MM_YYYY_HH_MM)}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Created At"
    }
  },
  {
    accessorKey: "updatedAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated At" />
    ),
    cell: ({ row }) => (
      <div>
        {formatDate(row.getValue("updatedAt"), dateFormats.DD_MM_YYYY_HH_MM)}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Updated At"
    }
  },
  {
    accessorKey: "createdBy",
    accessorFn: priceList =>
      `${priceList.createdBy.forename} ${priceList.createdBy.surname}`,
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Uploaded By"
        disableSorting
      />
    ),
    cell: ({ row }) => <div>{row.getValue("createdBy")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Uploaded By"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <DataTableRowActions
        priceList={{
          id: row.original.id,
          filename: row.original.filename,
          path: row.original.path,
          type: row.original.type,
          size: row.original.size,
          friendlyName: row.original.friendlyName,
          supplier: row.original.provider.displayName,
          utilityTypes: row.original.utilityTypes
        }}
      />
    ),
    filterFn: textFilter
  }
];
