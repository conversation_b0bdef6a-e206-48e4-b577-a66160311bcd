"use client";

import { Download, <PERSON><PERSON><PERSON><PERSON><PERSON>, Pen, Trash2, X } from "lucide-react";

import { trpcClient } from "@watt/crm/utils/api";
import { useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@watt/crm/components/ui/alert-dialog";
import { Button, buttonVariants } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { toast } from "@watt/crm/components/ui/use-toast";

import type {
  PriceList,
  UpdatePriceListInput
} from "@watt/api/src/types/price-lists";
import { useAppStore } from "@watt/crm/store/app-store";
import { PriceListForm } from "./price-list-form";

interface DataTableRowActionsProps {
  priceList: UpdatePriceListInput;
}

export function DataTableRowActions({ priceList }: DataTableRowActionsProps) {
  const { isAuthorised: isAllowedToEdit } = useAppStore(
    state => state.userData
  ).permissions;
  const [modalIsOpen, setModalOpen] = useState<boolean>(false);
  const [deleteDialogIsOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const updatePriceList = trpcClient.priceLists.update.useMutation();
  const deletePriceList = trpcClient.priceLists.deleteFile.useMutation();

  const signedUrl = trpcClient.priceLists.getSignedUrl.useQuery(
    {
      fileNameWithExtension: priceList.filename
    },
    {
      enabled: false
    }
  );

  const handleUpdatePriceList = async (data: PriceList) => {
    await updatePriceList.mutateAsync({
      filename: data.filename,
      type: data.type,
      size: data.size,
      path: data.path ?? priceList.filename,
      friendlyName: data.friendlyName,
      id: priceList.id,
      supplier: data.supplier,
      utilityTypes: data.utilityTypes,
      sendUpdateNotification: data.sendUpdateNotification,
      notificationContent: data.notificationContent,
      notificationSubject: data.notificationSubject
    });
    setModalOpen(false);
  };

  const handleDeletePriceList = async () => {
    if (!priceList.id) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Price list ID is missing"
      });
      return;
    }

    try {
      await deletePriceList.mutateAsync({ id: priceList.id });
      toast({
        title: "Success",
        description: "Price list deleted successfully",
        variant: "success"
      });
      setDeleteDialogOpen(false);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete price list, please try again"
      });
    }
  };

  const handleDownload = async () => {
    try {
      const signedUrlResponse = await signedUrl.refetch();

      if (!signedUrlResponse.data || !signedUrlResponse.data.signedUrl) {
        toast({
          title: "Error",
          description: "Failed to retrieve the download",
          variant: "destructive"
        });
        return;
      }

      const response = await fetch(signedUrlResponse.data.signedUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", priceList.filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (downloadError) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to download file, please try again"
      });
    }
  };

  return (
    <>
      <DropdownMenu open={dropdownIsOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only fixed">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          {isAllowedToEdit && (
            <>
              <DropdownMenuItem
                onClick={() => {
                  setDropdownOpen(false);
                  setModalOpen(true);
                }}
              >
                <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setDropdownOpen(false);
                  setDeleteDialogOpen(true);
                }}
              >
                <Trash2 className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Delete
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuItem onClick={handleDownload}>
            <Download className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Download
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Drawer
        direction="right"
        onOpenChange={setModalOpen}
        open={modalIsOpen}
        dismissible={false}
      >
        <DrawerContentWithDirection
          variant="right"
          scroll={false}
          className="max-w-[700px]"
          onEscapeKeyDown={() => setModalOpen(false)}
        >
          <Button
            variant="dialog"
            className="top-6 right-6 h-auto p-0"
            onClick={() => setModalOpen(false)}
          >
            <X className="h-4 w-4" />
            <span className="sr-only fixed">Close</span>
          </Button>
          <div className="flex h-screen flex-col space-y-4 overflow-y-scroll p-8">
            <DrawerTitle className="text-xl">Edit Price List</DrawerTitle>
            <DrawerDescription className="italic">
              Please complete all required fields (*) to edit the price list.
            </DrawerDescription>
            <PriceListForm
              priceList={priceList}
              onSubmit={handleUpdatePriceList}
            />
          </div>
        </DrawerContentWithDirection>
      </Drawer>

      <AlertDialog open={deleteDialogIsOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this price list? This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className={buttonVariants({ variant: "destructive" })}
              onClick={handleDeletePriceList}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
