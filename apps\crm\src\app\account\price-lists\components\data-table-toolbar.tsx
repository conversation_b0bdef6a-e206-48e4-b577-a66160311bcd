"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import type { PriceList } from "@watt/api/src/types/price-lists";
import { trpcClient } from "@watt/crm/utils/api";
import { useCallback, useState } from "react";
import { z } from "zod";

import udProvidersList from "@watt/common/src/constants/ud-providers-list.json";
import { DataTableDateRangeFilter } from "@watt/crm/components/data-table/data-table-date-range-filter";
import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle,
  DrawerTrigger
} from "@watt/crm/components/ui/drawer";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import { useAppStore } from "@watt/crm/store/app-store";
import { PriceListForm } from "./price-list-form";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
}

const queryParamsSchema = z.object({
  search: z.string().optional()
});

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const createPriceList = trpcClient.priceLists.create.useMutation();
  const { isAllowedToEditPriceList } = useAppStore(
    state => state.userData
  ).permissions;

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  const handleCreatePriceList = useCallback(
    async (data: PriceList) => {
      if (!data.path) {
        toast({
          title: "Error",
          description: "File is required",
          variant: "destructive"
        });
        return;
      }

      if (!data.notificationContent || !data.notificationSubject) {
        toast({
          title: "Error",
          description: "Notification content and subject are required",
          variant: "destructive"
        });
        return;
      }

      await createPriceList.mutateAsync({
        filename: data.filename,
        type: data.type,
        size: data.size,
        path: data.path,
        friendlyName: data.friendlyName,
        supplier: data.supplier,
        utilityTypes: data.utilityTypes,
        notificationContent: data.notificationContent,
        notificationSubject: data.notificationSubject
      });
      setModalOpen(false);
    },
    [createPriceList]
  );

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        <DataTableFacetedFilter
          column={table.getColumn("supplier")}
          title="Supplier"
          options={udProvidersList.map(supplier => ({
            label: supplier,
            value: supplier
          }))}
          onFilterChange={handleFilterChange}
        />
        {table.getColumn("createdAt") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("createdAt") as any}
            title="Created At"
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center gap-2">
        <DataTableViewOptions table={table} />
        {isAllowedToEditPriceList && (
          <Drawer
            direction="right"
            onOpenChange={setModalOpen}
            open={modalOpen}
            dismissible={false}
          >
            <DrawerTrigger asChild>
              <Button variant="secondary" size="sm">
                New Price List
              </Button>
            </DrawerTrigger>
            <DrawerContentWithDirection
              variant="right"
              scroll={false}
              className="max-w-[700px]"
              onEscapeKeyDown={() => setModalOpen(false)}
            >
              <Button
                variant="dialog"
                className="top-6 right-6 h-auto p-0"
                onClick={() => setModalOpen(false)}
              >
                <X className="h-4 w-4" />
                <span className="sr-only fixed">Close</span>
              </Button>
              <div className="flex h-screen flex-col space-y-4 overflow-y-scroll p-8">
                <DrawerTitle className="text-xl">
                  Upload New Price List
                </DrawerTitle>
                <DrawerDescription className="italic">
                  Please complete all required fields (*) to upload the new
                  price list.
                </DrawerDescription>
                <PriceListForm onSubmit={handleCreatePriceList} />
              </div>
            </DrawerContentWithDirection>
          </Drawer>
        )}
      </div>
    </div>
  );
}
