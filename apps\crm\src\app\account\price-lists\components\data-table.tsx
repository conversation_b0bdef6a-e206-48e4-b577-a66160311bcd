"use client";

import { CircleDollarSign } from "lucide-react";

import {
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";
import { trpcClient } from "@watt/crm/utils/api";
import { useMemo, useState } from "react";
import { useDebounce } from "react-use";
import { columns } from "./columns";
import { DataTableToolbar } from "./data-table-toolbar";

export function DataTable() {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = trpcClient.priceLists.getAll.useInfiniteQuery(
    {
      searchFilters: {
        columnFilters: debouncedColumnFilters,
        globalFilter
      }
    },
    {
      getNextPageParam: lastPage => lastPage.nextCursor,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      placeholderData: prev => prev,
      trpc: {
        abortOnUnmount: true
      }
    }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = useMemo(
    () => data?.pages?.[0]?.meta?.totalRowCount ?? 0,
    [data]
  );
  const totalFetched = useMemo(() => allItems.length, [allItems]);

  const table = useReactTable({
    data: allItems,
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      globalFilter,
      columnPinning: { right: ["actions"] }
    },
    enableRowSelection: false,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualFiltering: true
  });

  const emptyStatePanel = () => (
    <EmptyStatePanel
      title="No price lists found"
      description="There are no price lists. Get started by uploading a new price list."
      Icon={CircleDollarSign}
    />
  );

  if (isLoading) {
    return (
      <div className="space-y-4 py-4">
        <h1 className="font-bold text-xl tracking-tight">Price Lists</h1>
        <DataTableSkeleton
          columnCount={table.getAllColumns().length}
          searchableColumnCount={1}
          filterableColumnCount={3}
          cellWidths={["12rem", "14rem"]}
          withPagination={false}
          shrinkZero
        />
      </div>
    );
  }

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
      emptyStatePanel={emptyStatePanel()}
    >
      <h1 className="font-bold text-xl tracking-tight">Price Lists</h1>{" "}
      <DataTableToolbar table={table} isFiltered={isFiltered} />
    </InfiniteScrollDataTable>
  );
}
