"use client";

import {
  NotificationMessageSchema,
  NotificationSubjectSchema,
  OptionalNotificationMessageSchema,
  OptionalNotificationSubjectSchema
} from "@watt/api/src/types/notification";
import {
  type PriceList,
  PriceListSchema
} from "@watt/api/src/types/price-lists";
import { TEN_MB } from "@watt/common/src/constants/file-sizes";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import udProvidersList from "@watt/common/src/constants/ud-providers-list.json";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from "@watt/crm/components/ui/command";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import { Switch } from "@watt/crm/components/ui/switch";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { revalidatePath } from "@watt/crm/utils/revalidate-path";
import { UtilityType } from "@watt/db/src/enums";
import { createClientComponentClient } from "@watt/db/src/supabase/client";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";

type PriceListFormProps = {
  priceList?: PriceList;
  onSubmit: (data: PriceList) => Promise<void>;
};

export function mimeTypeToFriendlyName(mimeType: string): string {
  const mimeTypeMap: { [key: string]: string } = {
    "application/pdf": "PDF",
    "text/csv": "CSV",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "XLSX",
    "application/vnd.ms-excel.sheet.macroEnabled.12": "XLSM",
    "application/vnd.ms-excel": "XLS"
  };
  return mimeTypeMap[mimeType] || mimeType;
}

export function PriceListForm({ priceList, onSubmit }: PriceListFormProps) {
  const supabase = createClientComponentClient();
  const providerContainerRef = useRef<HTMLDivElement>(null);
  const [isProviderModalOpen, setIsProviderModalOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [fileInputKey] = useState(0);
  const [supplierSearch, setSupplierSearch] = useState("");
  const [isFormSubmitting, setIsFormSubmitting] = useState<boolean>(false);

  const priceListsUtils = trpcClient.useUtils().priceLists;

  const createSignedUploadUrlMutation =
    trpcClient.files.createSignedUploadUrl.useMutation({
      onSuccess: async () => {
        await priceListsUtils.invalidate();
      }
    });

  const form = useZodForm({
    schema: !priceList
      ? PriceListSchema.extend({
          notificationSubject: NotificationSubjectSchema,
          notificationContent: NotificationMessageSchema
        })
      : PriceListSchema.extend({
          notificationSubject: OptionalNotificationSubjectSchema,
          notificationContent: OptionalNotificationMessageSchema
        })
          .refine(
            data => !(data.sendUpdateNotification && !data.notificationSubject),
            {
              message: "Required",
              path: ["notificationSubject"]
            }
          )
          .refine(
            data => !(data.sendUpdateNotification && !data.notificationContent),
            {
              message: "Required",
              path: ["notificationContent"]
            }
          ),
    defaultValues: priceList
      ? {
          filename: priceList.filename,
          type: priceList.type,
          size: priceList.size,
          friendlyName: priceList.friendlyName,
          supplier: priceList.supplier,
          utilityTypes: priceList.utilityTypes
        }
      : undefined
  });

  const supplier = form.watch("supplier");

  useEffect(() => {
    const touchedFields = Object.keys(form.formState.touchedFields);
    if (
      supplier &&
      !touchedFields.includes("notificationSubject") &&
      !touchedFields.includes("notificationContent")
    ) {
      form.setValue(
        "notificationSubject",
        `${supplier} Price Update - ${formatDate(new Date(), dateFormats.DD_MM_YYYY_HH_MM)}`
      );
      form.setValue(
        "notificationContent",
        `New price list for ${supplier} is now available in the CRM. Please review immediately.`
      );
    }
  }, [supplier, form]);

  useEffect(() => {
    if (file) {
      form.setValue("filename", file.name);
      form.setValue("type", file.type);
      form.setValue("size", file.size);
    }
  }, [file, form]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) {
      return;
    }

    const acceptedTypes = event.target.accept.split(",");
    if (!acceptedTypes.includes(selectedFile.type)) {
      event.target.value = "";
      const friendlyTypes = acceptedTypes
        .map(mimeTypeToFriendlyName)
        .join(", ");
      form.setError("filename", {
        type: "fileType",
        message: `Only ${friendlyTypes} files are allowed`
      });
      return;
    }

    if (selectedFile.size > TEN_MB) {
      event.target.value = "";
      form.setError("filename", {
        type: "fileSize",
        message: "File size must be less than 10MB"
      });
      return;
    }
    setFile(selectedFile);
    form.setValue("filename", selectedFile.name);
    form.setValue("type", selectedFile.type);
    form.setValue("size", selectedFile.size);
    form.clearErrors("filename");
  };

  const handleSubmit = async () => {
    const priceListFormData = form.getValues();
    try {
      // We handle form submit state manually to capture all process and disable the button
      setIsFormSubmitting(true);
      if (file) {
        const { data, error } = await createSignedUploadUrlMutation.mutateAsync(
          {
            bucketName: STORAGE_BUCKETS.PRICE_LISTS,
            filename: file.name
          }
        );

        if (!data || error) {
          setIsFormSubmitting(false);
          toast({
            variant: "destructive",
            title: "Error",
            description: error.message ?? "Failed to upload the file"
          });
          return;
        }

        const { token, path } = data;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.PRICE_LISTS)
          .uploadToSignedUrl(file.name, token, file);

        if (!uploadData || uploadError) {
          setIsFormSubmitting(false);
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to upload file"
          });
          return;
        }

        await onSubmit({
          filename: file.name,
          type: file.type,
          size: file.size,
          path: path,
          friendlyName: priceListFormData.friendlyName,
          supplier: priceListFormData.supplier,
          utilityTypes: priceListFormData.utilityTypes,
          sendUpdateNotification: priceListFormData.sendUpdateNotification,
          notificationSubject: priceListFormData.notificationSubject,
          notificationContent: priceListFormData.notificationContent
        });

        setIsFormSubmitting(false);
        toast({
          title: "Success",
          description: priceList
            ? "Price list updated successfully"
            : "Price list created successfully",
          variant: "success"
        });
      } else if (priceList) {
        await onSubmit(priceListFormData);
        setIsFormSubmitting(false);
        toast({
          title: "Success",
          description: "Price list updated successfully",
          variant: "success"
        });
      }
    } catch (e) {
      const error = e as Error;
      setIsFormSubmitting(false);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error.message ||
          "Failed to upload file and create price list, please try again"
      });
    } finally {
      revalidatePath("/account/price-lists");
    }
  };

  const renderNotificationFields = () => {
    return (
      <>
        <FormField
          control={form.control}
          name="notificationSubject"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Subject *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Notification subject"
                  className="placeholder:italic"
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notificationContent"
          render={({ field }) => (
            <FormItem className="flex flex-grow flex-col pb-4">
              <FormLabel>Content*</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  className={cn(
                    !field.value && "text-muted-foreground italic",
                    "flex-grow resize-none"
                  )}
                  placeholder="Notification message"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    );
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleSubmit}
      className="my-4 flex w-full flex-grow flex-col space-y-6"
    >
      <FormField
        control={form.control}
        name="supplier"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Supplier *</FormLabel>
            <FormControl>
              <div ref={providerContainerRef} className="flex w-full flex-col">
                <LookUp
                  open={isProviderModalOpen}
                  onOpenChange={setIsProviderModalOpen}
                >
                  <LookUpTrigger fieldValue={field.value ?? ""}>
                    <span
                      className={cn(
                        "font-normal",
                        !field.value && "text-muted-foreground italic"
                      )}
                    >
                      {field.value || "Select the supplier"}
                    </span>
                  </LookUpTrigger>
                  <LookUpContent
                    placeholder="Search supplier..."
                    searchInput={supplierSearch}
                    onSearchInputChange={supplier => {
                      setSupplierSearch(supplier);
                      if (!supplier) {
                        form.resetField("supplier");
                      }
                    }}
                    shouldFilter={true}
                    container={providerContainerRef.current}
                  >
                    <LookUpGroup>
                      {udProvidersList.map(providerName => (
                        <LookUpItem
                          value={providerName}
                          key={providerName}
                          onSelect={() => {
                            form.setValue("supplier", providerName);
                            setIsProviderModalOpen(false);
                          }}
                        >
                          {providerName}
                        </LookUpItem>
                      ))}
                    </LookUpGroup>
                  </LookUpContent>
                </LookUp>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="friendlyName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Friendly Name *</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Enter the friendly name"
                className="placeholder:italic"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="filename"
        render={() => (
          <FormItem>
            <FormLabel>Upload File *</FormLabel>
            <FormControl>
              <Input
                key={fileInputKey}
                type="file"
                accept="application/pdf,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel.sheet.macroEnabled.12,application/vnd.ms-excel"
                onChange={handleFileChange}
              />
            </FormControl>
            <FormDescription className="text-xs">
              {priceList
                ? `Current file: ${priceList.filename}`
                : "The file size can not exceed 10MB: the accepted formats are xls, xlsx, xlsm, pdf, and csv"}
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="utilityTypes"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Utility Types *</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "justify-between",
                      (!field.value || field.value.length === 0) &&
                        "text-muted-foreground"
                    )}
                  >
                    <span className="max-w-[44ch] overflow-hidden text-ellipsis whitespace-nowrap">
                      {field.value?.length > 0 ? (
                        field.value.map(val => (
                          <Badge key={val} className="mr-1">
                            {humanize(val)}
                          </Badge>
                        ))
                      ) : (
                        <span className="font-normal italic">
                          Select applicable utilities
                        </span>
                      )}
                    </span>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <CommandInput placeholder="Search utility type..." />
                  <CommandEmpty>No utility type found.</CommandEmpty>
                  <CommandGroup>
                    {Object.values(UtilityType).map(type => (
                      <CommandItem
                        key={type}
                        onSelect={() => {
                          const updatedValue = field.value?.includes(type)
                            ? field.value.filter(t => t !== type)
                            : [...(field.value || []), type];
                          form.setValue("utilityTypes", updatedValue);
                        }}
                      >
                        <div
                          className={cn(
                            "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                            field.value?.includes(type)
                              ? "bg-primary text-primary-foreground"
                              : "opacity-50 [&_svg]:invisible"
                          )}
                        >
                          <Check className={cn("h-4 w-4")} />
                        </div>
                        {humanize(type)}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      {priceList && (
        <FormField
          control={form.control}
          name="sendUpdateNotification"
          render={({ field }) => (
            <FormItem className="flex flex-grow flex-col">
              <FormLabel>Send Notification</FormLabel>
              <div className="space-y-4 rounded-md border p-4">
                <div className="flex items-center justify-between gap-2">
                  <div className="space-y-0.5">
                    <FormLabel>Send new notification</FormLabel>
                    <FormDescription className="text-xs">
                      Notify the team about price list changes
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={value => {
                        form.setValue("sendUpdateNotification", value, {
                          shouldDirty: true
                        });
                      }}
                      className="data-[state=checked]:bg-secondary"
                    />
                  </FormControl>
                  <FormMessage />
                </div>
                {!!field.value && renderNotificationFields()}
              </div>
            </FormItem>
          )}
        />
      )}

      {!priceList && renderNotificationFields()}

      <Button
        type="submit"
        variant="secondary"
        disabled={isFormSubmitting}
        className="w-full"
      >
        {isFormSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        Confirm
      </Button>
    </FormWrapper>
  );
}
