"use client";

import type { ColumnDef, Row } from "@tanstack/react-table";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatNumber } from "@watt/common/src/utils/format-number";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";

import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { MpxnInputField } from "@watt/crm/components/mpxn/mpxn-input-field";
import { QuoteStatusBadge } from "@watt/crm/components/quick-actions/quote/status/quote-status-badge";

import type { MyQuoteListEntityAddress } from "@watt/api/src/router/quote";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { buttonVariants } from "@watt/crm/components/ui/button";
import { routes } from "@watt/crm/config/routes";
import type { QuoteStatus } from "@watt/db/src/enums";
import Link from "next/link";
import { DataTableRowActions, type MyQuote } from "./data-table-row-actions";

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
function textFilter(rows: Row<any>, id: string, filterValue: string): boolean {
  return filterValue.includes(rows.getValue(id));
}

// TODO switch to using 'columnHelper.accessor' as it is properly typed
// https://tanstack.com/table/v8/docs/guide/column-defs#column-helpers
// const columnHelper = createColumnHelper<MyQuote>();

export const columns: ColumnDef<MyQuote>[] = [
  {
    accessorKey: "companyName",
    accessorFn: row => row.siteMeter.companySite.company.name,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ row, getValue }) => {
      const companyId = row.original.siteMeter.companySite.company.id;
      const siteRefId = row.original.siteMeter.companySite.siteRefId;
      const companyName = humanize(getValue<string>());

      return (
        <div className="flex flex-wrap items-center gap-2">
          <Link
            href={`${routes.company.replace("[id]", "")}${companyId}/activity`}
            className={cn(
              buttonVariants({ variant: "link", size: "sm" }),
              "h-auto px-0"
            )}
          >
            {companyName}
          </Link>
          {siteRefId && companyId && (
            <Link
              href={`${routes.company.replace("[id]", "")}${companyId}/sites/${siteRefId}`}
              className={cn(
                buttonVariants({ variant: "link", size: "sm" }),
                "h-auto px-0"
              )}
            >
              {composeSiteRef(siteRefId)}
            </Link>
          )}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "entityAddress",
    accessorFn: row => row.siteMeter.companySite.entityAddress,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site" />
    ),
    cell: ({ getValue }) => {
      const entityAddress = getValue<MyQuoteListEntityAddress>();
      return <div>{getAddressDisplayName(entityAddress)}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Site"
    }
  },
  {
    accessorKey: "meterIdentifier",
    accessorFn: row => row.meterIdentifier,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Meter Identifier" />
    ),
    cell: ({ row, getValue }) => {
      const meterIdentifier = getValue<string | null>();
      const utilityType = row.original.utilityType;

      if (!meterIdentifier) {
        return <div>N/A</div>;
      }

      return (
        <MpxnInputField
          meterIdentifier={meterIdentifier}
          utilityType={utilityType}
          className="px-0"
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Meter Identifier"
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ getValue }) => {
      const createdAt = getValue<Date | null>();
      if (createdAt) {
        const formattedDate = formatDate(createdAt, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(createdAt, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Created At"
    }
  },
  {
    accessorKey: "totalUsage",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Consumption"
        description="(kWh/year)"
      />
    ),
    cell: ({ row }) => {
      const totalUsage =
        row.original.electricityUsage?.totalUsage ??
        row.original.gasUsage?.totalUsage;
      return <div>{totalUsage ? formatNumber(totalUsage) : "N/A"}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Consumption"
    }
  },
  {
    accessorKey: "endDate",
    // add 24 hours to createdAt to get the end date
    accessorFn: row => {
      const createdAt = row.createdAt;
      if (createdAt) {
        const endDate = new Date(createdAt);
        endDate.setDate(endDate.getDate() + 1);
        return endDate;
      }

      return null;
    },
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valid Until" />
    ),
    cell: ({ getValue }) => {
      const validUntil = getValue<Date | null>();
      if (validUntil) {
        const formattedDate = formatDate(validUntil, dateFormats.DD_MM_YYYY);
        const formattedTime = formatDate(validUntil, dateFormats.HH_MM);
        return (
          <div className="flex flex-col">
            <span>{formattedDate}</span>
            <span>{formattedTime}</span>
          </div>
        );
      }
      return <div>N/A</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Valid Until"
    }
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue<QuoteStatus>("status");

      return <QuoteStatusBadge status={status} />;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions quote={row.original} />,
    filterFn: textFilter
  }
];
