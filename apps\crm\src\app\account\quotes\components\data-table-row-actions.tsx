import type { ElectricityUsage, GasUsage } from "@prisma/client";
import type { My_Quotes_List } from "@watt/api/src/router";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import { Eye, ListPlus, MoreHorizontal } from "lucide-react";
import { useState } from "react";

import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { useQuoteModal } from "@watt/crm/hooks/use-quote-modal";

interface DataTableRowActionsProps {
  quote: Omit<
    MyQuote,
    | "meterIdentifier"
    | "contractStartDate"
    | "siteMeterId"
    | "currentProviderId"
    | "electricityUsageId"
    | "gasUsageId"
    | "electricitySupplierId"
    | "gasSupplierId"
    | "createdById"
  >;
}

export type MyQuote = ExtractElementType<My_Quotes_List["items"]>;

export function DataTableRowActions({ quote }: DataTableRowActionsProps) {
  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const { openQuoteModal } = useQuoteModal();

  const handleNewQuotes = () => {
    const utilityUsage = (quote.electricityUsage ?? quote.gasUsage) as
      | ElectricityUsage
      | GasUsage
      | null;

    if (!utilityUsage) {
      return null;
    }

    openQuoteModal("get-quotes", {
      businessSearch: quote.siteMeter.companySite.company.name,
      selectedBusinessRef:
        quote.siteMeter.companySite.company.registrationNumber,
      addressSearch: quote.siteMeter.companySite.entityAddress.postcode,
      selectedAddressId: quote.siteMeter.companySite.entityAddress.id,
      businessType: quote.siteMeter.companySite.company.businessType,
      utilityType: quote.utilityType,
      meterIdentifier:
        quote.siteMeter.electricSiteMeter?.mpan.value ||
        quote.siteMeter.gasSiteMeter?.mprnValue,
      creditScore: quote.creditScore ? quote.creditScore.toString() : "",
      upliftRate: quote.upliftRate ? quote.upliftRate.toString() : "",
      currentSupplier: quote.currentProvider.udcoreId || "",
      manualConsumptionEntry: utilityUsage.manualConsumptionEntry
        ? "true"
        : "false",
      dayUsage: utilityUsage.dayUsage.toString(),
      ...("nightUsage" in utilityUsage && {
        nightUsage: utilityUsage.nightUsage.toString()
      }),
      ...("weekendUsage" in utilityUsage && {
        weekendUsage: utilityUsage.weekendUsage.toString()
      }),
      totalUsage: utilityUsage.totalUsage.toString(),
      isSmartMeter: quote.siteMeter.isSmartMeter ? "true" : "false",
      isCustomQuotesOnly: quote.isCustomQuotesOnly ? "true" : "false"
    });
  };

  return (
    <DropdownMenu open={dropdownIsOpen} onOpenChange={setDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only fixed">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuItem
          onClick={() => {
            openQuoteModal("view-quotes", { quoteListId: quote.id });
          }}
        >
          <Eye className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          View Quotes
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleNewQuotes}>
          <ListPlus className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          New Quotes
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
