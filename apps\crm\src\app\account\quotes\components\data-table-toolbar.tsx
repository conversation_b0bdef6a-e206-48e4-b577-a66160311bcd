"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import { statuses } from "@watt/crm/common-data/quotes/data";
import {
  getUniqueFilterOptions,
  getUniqueMpxnFilterOptions
} from "@watt/crm/utils/get-unique-filter-options";
import { z } from "zod";

import {
  createDateObjectSchema,
  createZodEnumArray
} from "@watt/common/src/utils/zod-literal-union";
import { DataTableDateRangeFilter } from "@watt/crm/components/data-table/data-table-date-range-filter";
import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered,
  children
}: DataTableToolbarProps<TData>) {
  const uniqueCompanyNames = getUniqueFilterOptions({
    columnId: "companyName",
    table,
    humanizeValues: true
  });
  const uniqueMeterIdentifiers = getUniqueMpxnFilterOptions(
    "meterIdentifier",
    table
  );
  const queryParamsSchema = z.object({
    status: createZodEnumArray(statuses),
    ...(uniqueCompanyNames.length && {
      companyName: createZodEnumArray(uniqueCompanyNames)
    }),
    ...(uniqueMeterIdentifiers.length && {
      meterIdentifier: createZodEnumArray(uniqueMeterIdentifiers)
    }),
    createdAt: createDateObjectSchema(),
    search: z.string().optional()
  });

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        {table.getColumn("companyName") && (
          <DataTableFacetedFilter
            column={table.getColumn("companyName")}
            title="Company Name"
            options={uniqueCompanyNames}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("meterIdentifier") && (
          <DataTableFacetedFilter
            column={table.getColumn("meterIdentifier")}
            title="Meter Identifier"
            options={uniqueMeterIdentifiers}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("createdAt") && (
          <DataTableDateRangeFilter
            // biome-ignore lint/suspicious/noExplicitAny: <fix later>
            column={table.getColumn("createdAt") as any}
            title="Created At"
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("status") && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={statuses}
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <DataTableViewOptions table={table} />
        {children}
      </div>
    </div>
  );
}
