"use client";

import {
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { trpcClient } from "@watt/crm/utils/api";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { InfiniteScrollDataTable } from "@watt/crm/components/data-table/data-table-infinite-scroll";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { Button } from "@watt/crm/components/ui/button";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useQuoteModal } from "@watt/crm/hooks/use-quote-modal";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";

import { columns } from "./columns";
import { DataTableToolbar } from "./data-table-toolbar";
import { EmptyStatePanel } from "./empty-state";

type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

type QueryParams = {
  verbalContract: boolean;
};

export function QuotesDataTable() {
  const { queryParams } = useQueryParams<Partial<QueryParams>>();
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const { openQuoteModal } = useQuoteModal();

  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = trpcClient.quote.myQuotesList.useInfiniteQuery(
    {
      searchFilters: {
        columnFilters: debouncedColumnFilters,
        globalFilter
      }
    },
    {
      getNextPageParam: lastPage => lastPage.nextCursor,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      placeholderData: prev => prev,
      trpc: {
        abortOnUnmount: true
      }
    }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = useMemo(
    () => data?.pages?.[0]?.meta?.totalRowCount ?? 0,
    [data]
  );
  const totalFetched = useMemo(() => allItems.length, [allItems]);

  const handleOpenQuoteModal = useCallback(
    (quoteListId: string) => {
      openQuoteModal("view-quotes", { quoteListId });
    },
    [openQuoteModal]
  );

  useEffect(() => {
    if (queryParams.verbalContract && totalFetched === 1) {
      const firstItem = allItems[0];
      if (firstItem) {
        handleOpenQuoteModal(firstItem.id);
      }
    }
  }, [
    queryParams.verbalContract,
    totalFetched,
    allItems,
    handleOpenQuoteModal
  ]);

  const table = useReactTable({
    data: allItems,
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      globalFilter,
      columnPinning: { right: ["actions"] }
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    debugTable: false,
    manualFiltering: true
  });

  const getQuotes = () => {
    openQuoteModal("get-quotes");
  };

  const emptyStatePanel = () => (
    <EmptyStatePanel
      title="No quotes found"
      description="It looks like you haven't created any quotes yet. Get started by creating your first quote."
    >
      <Button variant="outline" className="font-medium" onClick={getQuotes}>
        Get Quotes
      </Button>
    </EmptyStatePanel>
  );

  if (isLoading) {
    return (
      <div className="space-y-4 py-4">
        <h1 className="font-bold text-xl tracking-tight">Quotes</h1>
        <DataTableSkeleton
          columnCount={table.getAllColumns().length}
          searchableColumnCount={1}
          filterableColumnCount={3}
          cellWidths={["12rem", "14rem"]}
          withPagination={false}
          shrinkZero
        />
      </div>
    );
  }

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
      emptyStatePanel={emptyStatePanel()}
    >
      <h1 className="font-bold text-xl tracking-tight">Quotes</h1>
      <DataTableToolbar table={table} isFiltered={isFiltered}>
        <Button variant="secondary" onClick={getQuotes} size="sm">
          Get Quotes
        </Button>
      </DataTableToolbar>
    </InfiniteScrollDataTable>
  );
}
