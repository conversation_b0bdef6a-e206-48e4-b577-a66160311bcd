"use client";

import { useSupabaseSessionUser } from "@watt/crm/hooks/use-session-user";

export function SomeComponent() {
  const x = useSupabaseSessionUser();
  const { data: userData, isLoading, isFetching, isRefetching } = x;
  const { permissions } = userData ?? {};
  console.log("permissions: ", permissions);

  return (
    <div>
      <div>isLoading: {isLoading ? "true" : "false"}</div>
      <div>isFetching: {isFetching ? "true" : "false"}</div>
      <div>isRefetching: {isRefetching ? "true" : "false"}</div>
      {JSON.stringify(permissions, null, 2)}
    </div>
  );
}
