import { supabaseSessionUserQueryOptions } from "@watt/crm/utils/supabase/get-session-user";
import type { Metadata } from "next";
import { getQueryClient } from "../../../trpc/get-query-client";
import { QuotesDataTable } from "./components/quotes-data-table";

export const metadata: Metadata = {
  title: "Quotes",
  description: "List of quotes."
};

export default async function QuotesPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery(supabaseSessionUserQueryOptions);

  return (
    <div className="px-4">
      <QuotesDataTable />
    </div>
  );
}
