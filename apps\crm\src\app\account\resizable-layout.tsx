"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type PropsWithChildren, useCallback, useRef, useState } from "react";
import type { ImperativePanelHandle } from "react-resizable-panels";

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup
} from "@watt/crm/components/ui/resizable";
import { TooltipProvider } from "@watt/crm/components/ui/tooltip";
import { Sidebar } from "./components/sidebar";

type ResizableLayoutProps = PropsWithChildren & {
  defaultCollapsed: boolean;
  defaultLayout?: number[];
  isAuthorised: boolean;
};

export function ResizableLayout({
  children,
  defaultLayout = [15, 85],
  defaultCollapsed,
  isAuthorised
}: ResizableLayoutProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const leftPanelRef = useRef<ImperativePanelHandle>(null);

  const onToggle = useCallback(() => {
    const panel = leftPanelRef.current;
    const collapsed = panel?.isCollapsed() ?? defaultCollapsed;

    setIsCollapsed(collapsed);
    document.cookie = `react-resizable-panels-layout:collapsed=${JSON.stringify(collapsed)}; path=/`;
  }, [defaultCollapsed]);

  return (
    <TooltipProvider delayDuration={0}>
      <ResizablePanelGroup
        direction="horizontal"
        onLayout={(sizes: number[]) => {
          document.cookie = `react-resizable-panels-layout:layout=${JSON.stringify(sizes)}; path=/`;
        }}
        className="h-full items-stretch"
      >
        <ResizablePanel
          ref={leftPanelRef}
          defaultSize={isCollapsed ? 4 : defaultLayout[0]}
          collapsedSize={4}
          collapsible={true}
          minSize={15}
          maxSize={15}
          onCollapse={onToggle}
          onExpand={onToggle}
          className={cn(
            "transition-all duration-300 ease-in-out",
            isCollapsed ? "min-w-[65px] max-w-[65px]" : "min-w-[220px]"
          )}
        >
          <Sidebar isCollapsed={isCollapsed} isAuthorised={isAuthorised} />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={defaultLayout[1]}>
          {children}
        </ResizablePanel>
      </ResizablePanelGroup>
    </TooltipProvider>
  );
}
