"use client";

import { UtilityType } from "@prisma/client";
import type { ColumnDef } from "@tanstack/react-table";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { IconMap } from "@watt/crm/icons/icon-map";
import { type JSX, cloneElement } from "react";

import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { Button } from "@watt/crm/components/ui/button";

import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import type { SiteRow } from "./data-table";
import { ToggleCompanyGrouping } from "./toggle-company-grouping";

// Remove the local textFilter function

export const columns: ColumnDef<SiteRow>[] = [
  {
    accessorKey: "company",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="flex max-w-[500px] truncate">
            {/* {propsCasted.row.getValue<Company>("company").name}
            <ToggleCompanyGrouping row={propsCasted.row} /> */}

            <Button variant="none" className="gap-2">
              {row.depth === 0 && (
                <div
                // onClick={() => propsCasted.openCompanyProfile()}
                >
                  {humanize(row.original.company.name)}
                </div>
              )}

              {row.getCanExpand() ? (
                <Button
                  variant="none"
                  onClick={row.getToggleExpandedHandler()}
                  className="group flex items-center gap-2"
                >
                  <ToggleCompanyGrouping row={row} />
                </Button>
              ) : null}
            </Button>
          </span>
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Company Name"
    }
  },
  {
    accessorKey: "address",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Site Address" />
    ),
    cell: ({ row }) => {
      return (
        <div className="w-[120px]">
          {getAddressDisplayName(row.original.entityAddress)}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Site Address"
    }
  },
  {
    accessorKey: "utilitiesManaged",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Utilities Managed" />
    ),
    cell: ({ row }) => {
      const utilitiesManaged = row.original.siteMeters.map(meter =>
        meter.utilityType.toString()
      );
      return (
        <div className="flex w-[180px]">
          {Object.keys(IconMap).map(utility => {
            const isActive = utilitiesManaged.includes(utility);
            return (
              <div key={utility} className="mr-1">
                {isActive
                  ? IconMap[utility]
                  : cloneElement(IconMap[utility] as JSX.Element, {
                      color: "grey"
                    })}
                {}
              </div>
            );
          })}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Utilities Managed"
    }
  },
  {
    accessorKey: "mpan",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="MPAN" />
    ),
    cell: ({ row }) => {
      const electricMeterCounts = row.original.siteMeters.filter(
        meter => meter.utilityType === UtilityType.ELECTRICITY
      ).length;
      return <p>{electricMeterCounts}</p>;
    },
    meta: {
      dropdownLabel: "MPAN"
    }
  },
  {
    accessorKey: "mprn",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="MPRN" />
    ),
    cell: ({ row }) => {
      const gasMeterCounts = row.original.siteMeters.filter(
        meter => meter.utilityType === UtilityType.GAS
      ).length;
      return <p>{gasMeterCounts}</p>;
    },
    meta: {
      dropdownLabel: "MPRN"
    }
  }
];
