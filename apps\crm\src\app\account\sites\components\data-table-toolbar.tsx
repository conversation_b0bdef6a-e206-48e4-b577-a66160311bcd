"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import React from "react";
import { useDebounce } from "react-use";

import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  children
}: DataTableToolbarProps<TData>) {
  const [debouncedSearchValue, setDebouncedSearchValue] = React.useState("");
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  useDebounce(
    () => {
      table.setGlobalFilter(debouncedSearchValue);
    },
    500, // You can adjust the debounce time as needed
    [debouncedSearchValue]
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearchValue(event.target.value);
  };

  const resetFilters = () => {
    table.resetColumnFilters();
    table.resetGlobalFilter();
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
    setDebouncedSearchValue("");
  };

  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          ref={searchInputRef}
        />
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <DataTableViewOptions table={table} />
        {children}
      </div>
    </div>
  );
}
