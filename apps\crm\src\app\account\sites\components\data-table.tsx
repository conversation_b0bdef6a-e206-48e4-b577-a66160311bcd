"use client";

import {
  type ColumnDef,
  type ColumnFiltersState,
  type ExpandedState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { SitesWith_Add_Com } from "@watt/api/src/router/site";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { trpcClient } from "@watt/crm/utils/api";
import {
  type BaseTableRow,
  groupedDataRestructure
} from "@watt/crm/utils/dropdown-restructure";
import type { ExtractElementType } from "@watt/crm/utils/extract-element-type";
import { useEffect, useMemo, useState } from "react";

import { DataTablePagination } from "@watt/crm/components/data-table/data-table-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";
import { routes } from "@watt/crm/config/routes";
import { useNavigateToRoute } from "@watt/crm/hooks/use-navigate-to-route";

import { DataTableToolbar } from "../components/data-table-toolbar";

export type SiteRow = ExtractElementType<SitesWith_Add_Com>;
export type SiteRowWith_SubRow = SiteRow extends BaseTableRow<SiteRow>
  ? SiteRow
  : never;

interface DataTableProps {
  columns: ColumnDef<SiteRow>[];
}

export function DataTable({ columns }: DataTableProps) {
  const navigateToRoute = useNavigateToRoute();
  const [rowSelection, setRowSelection] = useState({});
  const [originalPageSize, setOriginalPageSize] = useState<number>(10);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const { data, isLoading } = trpcClient.site.all.useQuery();

  const groupedData: SiteRowWith_SubRow[] = useMemo(() => {
    return groupedDataRestructure<SiteRowWith_SubRow>(
      data,
      (item, currentValue) => item.company.name === currentValue.company.name
    );
  }, [data]);

  const table = useReactTable({
    data: groupedData,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      expanded
    },
    enableRowSelection: false,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    getSubRows: (row: SiteRowWith_SubRow) => row.subRows,
    getExpandedRowModel: getExpandedRowModel(),

    onExpandedChange: setExpanded,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });
  const activeSubRowsCount = useMemo(() => {
    return table.getRowModel().rows.filter(row => row.depth !== 0).length;
  }, [table]);

  useEffect(() => {
    table.setPageSize(originalPageSize + activeSubRowsCount);
  }, [table, activeSubRowsCount, originalPageSize]);

  return (
    <div className="space-y-4 py-4">
      <h1 className="font-bold text-xl tracking-tight">Sites</h1>
      <DataTableToolbar table={table} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  className={cn(
                    "transition-all duration-300 hover:bg-muted-foreground/30",
                    rowIndex % 2 === 0 && "bg-muted"
                  )}
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, {
                        ...cell.getContext(),
                        openSiteProfile: () =>
                          navigateToRoute(routes.company, {
                            dynamicPaths: {
                              id: row.original.company.id
                            },
                            queryParams: {
                              siteId: row.original.id,
                              tab: "sites"
                            }
                          }),
                        openCompanyProfile: () =>
                          navigateToRoute(routes.company, {
                            dynamicPaths: {
                              id: row.original.company.id
                            }
                          })
                      })}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {isLoading ? "Loading..." : "No results."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination
        table={table}
        setOriginalPageSize={setOriginalPageSize}
      />
    </div>
  );
}
