"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import type { Profile } from "@watt/db/src/types/profile";
import { getRoleDisplayName } from "@watt/db/src/utils/get-role-display-name";

import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { Badge } from "@watt/crm/components/ui/badge";

import { DataTableRowActions } from "./data-table-row-actions";

export const columns: ColumnDef<Profile>[] = [
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => (
      <div className="max-w-[20ch] overflow-hidden text-ellipsis whitespace-nowrap">
        {row.getValue("email")}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Email"
    }
  },
  {
    accessorKey: "directDial",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Phone" />
    ),
    cell: ({ row }) => (
      <div className="max-w-[20ch] overflow-hidden text-ellipsis whitespace-nowrap">
        {row.getValue("directDial")}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Phone"
    }
  },
  {
    accessorKey: "role",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Role" />
    ),
    cell: ({ row }) => (
      <div>
        <Badge>{getRoleDisplayName(row.getValue("role"))}</Badge>
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Role"
    }
  },
  {
    accessorKey: "fullName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Full Name" />
    ),
    cell: ({ row }) => (
      <div className="max-w-[16ch] overflow-hidden text-ellipsis whitespace-nowrap">
        {row.getValue("fullName")}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Full Name"
    }
  },
  {
    accessorKey: "disabled",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Disabled" />
    ),
    cell: ({ row }) => {
      const disabled = row.getValue("disabled") as boolean;
      return (
        <div>
          <Badge variant={disabled ? "destructive" : "default"}>
            {row.getValue("disabled") ? "Yes" : "No"}
          </Badge>
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Disabled"
    }
  },
  {
    accessorKey: "huntGroups",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Hunt Groups" />
    ),
    cell: ({ row }) => {
      const huntGroups = row.getValue("huntGroups") as string[];
      return (
        <div className="w-44">
          {huntGroups.map(huntGroup => (
            <Badge key={huntGroup} className="mr-2 mb-1">
              {humanize(huntGroup)}
            </Badge>
          ))}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Hunt Groups"
    }
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => (
      <div>
        {formatDate(row.getValue("createdAt"), dateFormats.DD_MM_YYYY_HH_MM)}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Created At"
    }
  },
  {
    accessorKey: "updatedAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated At" />
    ),
    cell: ({ row }) => (
      <div>
        {formatDate(row.getValue("updatedAt"), dateFormats.DD_MM_YYYY_HH_MM)}
      </div>
    ),
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Updated At"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions userProfile={row.original} />,
    filterFn: textFilter
  }
];
