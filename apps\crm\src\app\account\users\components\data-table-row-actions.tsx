"use client";

import {
  Ban,
  CheckCircle2,
  ExternalLink,
  MoreHorizontal,
  Pen,
  Phone,
  User
} from "lucide-react";

import { useAppStore } from "@watt/crm/store/app-store";
import { trpcClient } from "@watt/crm/utils/api";
import type { Profile } from "@watt/db/src/types/profile";
import Link from "next/link";
import { useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@watt/crm/components/ui/alert-dialog";
import { Button, buttonVariants } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { toast } from "@watt/crm/components/ui/use-toast";

import { UserProfileForm } from "./user-profile-form";

interface DataTableRowActionsProps {
  userProfile: Profile;
}

export function DataTableRowActions({ userProfile }: DataTableRowActionsProps) {
  const [modalIsOpen, setModalOpen] = useState<boolean>();
  const [disableDialogIsOpen, setDisableDialog] = useState<boolean>();
  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const updateUserProfileMutation =
    trpcClient.userProfile.updateUserProfile.useMutation();
  const { isAdmin } = useAppStore(state => state.userData).permissions;

  // const { user, setUser } = useAppStore(state => ({
  //   user: state.userData.user,
  //   setUser: state.setUser
  // }));

  // TODO: Setup a way to update the currently logged in user profile after updating the user.
  // Otherwise they need to logout / log backin
  // const updateCurrentUserProfile = async (profile: UpdateUserProfile) => {
  //   console.log("new name is", profile.fullName);
  //   console.log("user id", user?.id);
  //   console.log("profile id", profile.id);

  //   if (!user) {
  //     return;
  //   }

  //   if (user.id !== profile.id) {
  //     return;
  //   }

  //   const supabaseUser = await getSupabaseSessionUser();
  //   setUser(supabaseUser);
  // };

  const handleUpdateUser = async (data: Partial<Profile>) => {
    try {
      const profile = await updateUserProfileMutation.mutateAsync({
        ...data,
        id: userProfile.id
      });

      // updateCurrentUserProfile(profile);

      toast({
        title: "Success",
        description: "User updated successfully",
        variant: "success"
      });
      setModalOpen(false);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update user, please try again"
      });
    }
  };

  return (
    <>
      <DropdownMenu open={dropdownIsOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only fixed">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem
            disabled={!isAdmin}
            onClick={async () => {
              if (!isAdmin) {
                return;
              }

              setDropdownOpen(false);
              setModalOpen(true);
            }}
          >
            <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Edit
          </DropdownMenuItem>
          {isAdmin && (
            <DropdownMenuItem>
              <Link
                className="flex"
                href={`/account/admin-calls/${userProfile.email}`}
                target="_blank"
                rel="noreferrer"
              >
                <Phone className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Call history
                <ExternalLink className="ml-2 h-3.5 w-3.5 text-muted-foreground/70" />
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            disabled={!isAdmin}
            onClick={() => {
              if (!isAdmin) {
                return;
              }

              setDropdownOpen(false);
              setDisableDialog(true);
            }}
          >
            {userProfile.disabled ? (
              <>
                <CheckCircle2 className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Enable
              </>
            ) : (
              <>
                <Ban className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                Disable
              </>
            )}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Dialog onOpenChange={setModalOpen} open={modalIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex flex-row items-center">
              <User className="mr-2 h-4 w-4" /> Update Profile
            </DialogTitle>
          </DialogHeader>
          <UserProfileForm
            submitText="Update"
            userProfile={userProfile}
            key={userProfile.id}
            onSubmit={handleUpdateUser}
            isLoading={updateUserProfileMutation.isPending}
          />
        </DialogContent>
      </Dialog>
      <AlertDialog open={disableDialogIsOpen} onOpenChange={setDisableDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm</AlertDialogTitle>
            <AlertDialogDescription>
              {`Are you sure you want to ${userProfile.disabled ? "enable" : "disable"} this user`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className={buttonVariants({ variant: "destructive" })}
              onClick={() =>
                handleUpdateUser({ disabled: !userProfile.disabled })
              }
            >
              {userProfile.disabled ? "Enable" : "Disable"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
