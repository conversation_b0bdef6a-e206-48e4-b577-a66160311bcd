"use client";

import { UserPlus, X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import { useAppStore } from "@watt/crm/store/app-store";
import { trpcClient } from "@watt/crm/utils/api";
import { roles } from "@watt/db/src/enumerable-types";
import type { ProfileWithPassword } from "@watt/db/src/types/profile";
import { useCallback, useState } from "react";
import { z } from "zod";

import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@watt/crm/components/ui/dialog";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";

import { log } from "@watt/common/src/utils/axiom-logger";
import { createZodEnumArray } from "@watt/common/src/utils/zod-literal-union";
import { UserProfileForm } from "./user-profile-form";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
}

const queryParamsSchema = z.object({
  role: createZodEnumArray(roles),
  search: z.string().optional()
});

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const [modalOpen, setModalOpen] = useState<boolean>();
  const createUserAndProfileMutation =
    trpcClient.userProfile.createUserAndProfile.useMutation();
  const { isAdmin: isAllowedToEdit } = useAppStore(
    state => state.userData
  ).permissions;

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  const handleInviteUser = useCallback(
    async (data: ProfileWithPassword) => {
      try {
        await createUserAndProfileMutation.mutateAsync(data);
        toast({
          title: "Success",
          description: !data.password
            ? "An invite has been sent to the user"
            : "User created with password",
          variant: "success"
        });
        setModalOpen(false);
      } catch (e) {
        const error = e as Error;
        log.error("hooks/use-authentication.handleSignIn", { error });

        toast({
          title: "Error",
          description: error.message ?? "Failed to create user",
          variant: "destructive"
        });
      }
    },
    [createUserAndProfileMutation]
  );

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        {table.getColumn("role") && (
          <DataTableFacetedFilter
            column={table.getColumn("role")}
            title="Role"
            options={roles}
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <DataTableViewOptions table={table} />
        {isAllowedToEdit && (
          <Dialog onOpenChange={setModalOpen} open={modalOpen}>
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="font-semibold text-xs"
              >
                <UserPlus className="mr-2 h-4 w-4" />
                New
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex flex-row items-center">
                  <UserPlus className="mr-2 h-4 w-4" /> Add New User
                </DialogTitle>
                <DialogDescription>
                  Create a new user account and send them an invitation to join.
                </DialogDescription>
              </DialogHeader>
              <UserProfileForm
                submitText="Confirm"
                onSubmit={handleInviteUser}
                isLoading={createUserAndProfileMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
