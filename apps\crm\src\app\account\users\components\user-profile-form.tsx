import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Badge } from "@watt/crm/components/ui/badge";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList
} from "@watt/crm/components/ui/command";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { huntGroups, roles } from "@watt/db/src/enumerable-types";
import { type HuntGroup, UserRole } from "@watt/db/src/enums";
import {
  ProfileModelWithPassword,
  ProfileModelWithRequiredPassword,
  type ProfileWithPassword
} from "@watt/db/src/types/profile";
import { Check, ChevronsUpDown } from "lucide-react";

type UserProfileFormProps = {
  submitText?: string;
  userProfile?: ProfileWithPassword;
  onSubmit: (data: ProfileWithPassword) => void;
  isLoading: boolean;
};

export function UserProfileForm({
  submitText = "Submit",
  userProfile,
  onSubmit,
  isLoading
}: UserProfileFormProps) {
  const form = useZodForm({
    schema: !userProfile
      ? ProfileModelWithRequiredPassword
      : ProfileModelWithPassword,
    defaultValues: {
      email: userProfile?.email || "",
      password: "",
      directDial: userProfile?.directDial || "",
      forename: userProfile?.forename || "",
      surname: userProfile?.surname || "",
      role: userProfile?.role || UserRole.SALES_AGENT,
      huntGroups: userProfile?.huntGroups || []
    }
  });

  return (
    <FormWrapper
      form={form}
      handleSubmit={data => {
        onSubmit({
          ...data,
          email: data.email.toLowerCase(),
          directDial: data.directDial?.replace("+44", "0")
        });
      }}
      className="my-4 space-y-4"
    >
      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Email *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className="col-span-3"
                autoComplete="off"
                placeholder="Enter email"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {!userProfile && (
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Password *</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter password"
                  className="col-span-3"
                  type="password"
                  autoCapitalize="none"
                  autoComplete="new-password"
                  autoCorrect="off"
                  disabled={!!userProfile}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      <FormField
        control={form.control}
        name="directDial"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Phone number</FormLabel>
            <FormControl>
              <Input
                {...field}
                type="tel"
                className="col-span-3"
                placeholder="Enter phone number"
                value={!field.value ? "" : field.value}
                onChange={e => {
                  const { value } = e.target;
                  if (!/^0\d{0,10}$/.test(value) && value !== "") {
                    return;
                  }
                  field.onChange(e);
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="role"
        render={() => (
          <FormItem className="flex flex-col">
            <FormLabel>Role *</FormLabel>
            <FormControl>
              <Select
                onValueChange={(selectedIndex: string) => {
                  const selectedRole = roles[Number.parseInt(selectedIndex)];
                  if (selectedRole) {
                    form.setValue("role", selectedRole.value as UserRole);
                  }
                }}
                defaultValue={
                  userProfile
                    ? roles
                        .findIndex(role => role.value === userProfile.role)
                        .toString()
                    : undefined
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role, i) => (
                    <SelectItem value={i.toString()} key={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="forename"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Forename *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className="col-span-3"
                placeholder="Enter forename"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="surname"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Surname *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className="col-span-3"
                placeholder="Enter surname"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="huntGroups"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Hunt groups</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "justify-between",
                      (!field.value || field.value.length === 0) &&
                        "text-muted-foreground"
                    )}
                  >
                    {/* TODO (Stephen): This max-w-44ch is a hack so that text-ellipsis works */}
                    <span className="max-w-[44ch] overflow-hidden text-ellipsis whitespace-nowrap">
                      {!field.value || field.value.length === 0
                        ? "Select hunt groups"
                        : field.value.map(val => (
                            <Badge
                              className="mr-2 bg-purple-200 text-purple-700 hover:bg-purple-300"
                              key={val}
                            >
                              {
                                huntGroups.find(group => group.value === val)
                                  ?.label
                              }
                            </Badge>
                          ))}
                    </span>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[46ch] max-w-[46ch] p-0">
                <Command>
                  <CommandInput placeholder="Search hunt groups..." />
                  <CommandEmpty>No hunt groups found.</CommandEmpty>
                  <CommandList className="max-h-[400px] overflow-y-scroll">
                    {huntGroups.map(group => (
                      <CommandItem
                        value={group.label}
                        key={group.value}
                        onSelect={() => {
                          const currentHuntGroups =
                            form.getValues("huntGroups") ?? [];
                          const isAlreadySelected = currentHuntGroups.includes(
                            group.value as HuntGroup
                          );
                          if (isAlreadySelected) {
                            form.setValue(
                              "huntGroups",
                              currentHuntGroups.filter(
                                lang => lang !== group.value
                              )
                            );
                          } else {
                            form.setValue("huntGroups", [
                              ...currentHuntGroups,
                              group.value as HuntGroup
                            ]);
                          }
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            field.value?.includes(group.value as HuntGroup)
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />

                        {group.label}
                      </CommandItem>
                    ))}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormDescription className="text-muted-foreground text-xs italic">
              The user will receive calls from all hunt groups you select here.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
      <Button
        type="submit"
        variant="secondary"
        disabled={isLoading}
        className="button-click-animation w-full"
      >
        {submitText}
      </Button>
    </FormWrapper>
  );
}
