import {
  formatAddress,
  getAddressById
} from "@watt/api/src/service/get-address";
import { AddressLookupError } from "@watt/api/src/service/search-addresses";
import { log } from "@watt/common/src/utils/axiom-logger";
import { parseRequestRouteParams } from "@watt/common/src/utils/parse-request-route-params";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  id: z.string()
});

type Params = z.infer<typeof ParamsSchema>;

export async function GET(
  _request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  try {
    const { id } = parseRequestRouteParams(params, ParamsSchema);

    const address = await getAddressById({
      id,
      hideAddressWithCreatedById: true
    });

    if (!address) {
      return NextResponse.json(
        { message: `No address found for ID: ${id}` },
        { status: 404 }
      );
    }

    const formattedAddress = formatAddress(address);
    return NextResponse.json(formattedAddress);
  } catch (error) {
    if (error === AddressLookupError.NONE_FOUND) {
      return NextResponse.json(
        { message: "No address found" },
        { status: 404 }
      );
    }

    log.error("address/[id]/route.GET: ", { error });
    return new NextResponse("Internal server error", { status: 500 });
  }
}
