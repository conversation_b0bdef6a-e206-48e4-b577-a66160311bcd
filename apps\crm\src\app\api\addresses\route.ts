import {
  AddressLookupError,
  searchAddressesWhere
} from "@watt/api/src/service/search-addresses";
import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestQueryParamsToResponse } from "@watt/common/src/utils/parse-request-query-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import type { NextRequest } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const { data, error } = parseRequestQueryParamsToResponse(
    request,
    z.object({
      postcode: z.string(),
      take: z.number().optional()
    })
  );

  if (error) {
    log.error("addresses/route.GET", { error });
    return ResponseHelper.badRequest({
      message: "Invalid query parameters"
    });
  }

  try {
    const results = await searchAddressesWhere({
      input: {
        postcode: data.postcode
      },
      take: data.take,
      hideAddressWithCreatedById: true
    });
    return ResponseHelper.ok(results);
  } catch (err) {
    if (err === AddressLookupError.BAD_QUERY) {
      log.warn("addressRouter.findMany: Bad query input", {
        postcode: data.postcode
      });
      return ResponseHelper.badRequest(
        ErrorResponseSchema.parse({ message: "Invalid search parameters" })
      );
    }

    if (err === AddressLookupError.BAD_POSTCODE) {
      return ResponseHelper.badRequest(
        ErrorResponseSchema.parse({ message: "Invalid postcode" })
      );
    }

    if (err === AddressLookupError.BAD_MPAN) {
      return ResponseHelper.badRequest(
        ErrorResponseSchema.parse({ message: "Invalid MPAN format" })
      );
    }

    if (err === AddressLookupError.BAD_MPRN) {
      return ResponseHelper.badRequest(
        ErrorResponseSchema.parse({ message: "Invalid MPRN format" })
      );
    }

    if (err === AddressLookupError.NONE_FOUND) {
      return ResponseHelper.notFound(
        ErrorResponseSchema.parse({
          message: `No addresses found for ${data.postcode}`
        })
      );
    }

    log.error("addresses/route.GET", { err });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error",
        description: err instanceof Error ? err.message : undefined
      })
    );
  }
}
