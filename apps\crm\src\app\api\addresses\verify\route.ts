import { AddressLookupError } from "@watt/api/src/service/search-addresses";
import {
  type VerifyAddressPayload,
  VerifyAddressPayloadSchema,
  verifyAddress
} from "@watt/api/src/service/verify-address";
import { log } from "@watt/common/src/utils/axiom-logger";
import { parseRequestBody } from "@watt/common/src/utils/parse-next-request-body";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import type { NextRequest } from "next/server";
import { ZodError } from "zod";

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const addressData = await parseRequestBody<VerifyAddressPayload>(
      request,
      VerifyAddressPayloadSchema
    );

    const verificationResult = await verifyAddress(addressData);

    return ResponseHelper.ok(verificationResult);
  } catch (err: unknown) {
    if (err === AddressLookupError.TOO_MANY_MATCHES) {
      log.warn(
        "addresses/verify/route.POST: Handled: Address search returned too many matches.",
        { detail: err }
      );
      return ResponseHelper.badRequest({
        message: "Address search returned too many matches",
        description:
          "Please provide more specific address details to narrow down the search results"
      });
    }

    if (err instanceof ZodError) {
      log.warn(
        "addresses/verify/route.POST: Validation failed for request body.",
        { errors: err.flatten().fieldErrors }
      );
      return ResponseHelper.badRequest({
        message: "Invalid request body: Validation failed",
        errors: err.flatten().fieldErrors
      });
    }

    // Fallthrough for any other type of error
    log.error(
      "addresses/verify/route.POST: Unhandled error processing request.",
      { error: err }
    );
    const errorMessage =
      err instanceof Error
        ? err.message
        : typeof err === "string"
          ? err
          : "An unknown error occurred";
    return ResponseHelper.badRequest({
      message: "Failed to process address verification request",
      description: errorMessage
    });
  }
}
