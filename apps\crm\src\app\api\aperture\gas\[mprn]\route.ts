import { MPRNSchema } from "@watt/common/src/mprn/mprn";
import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestRouteParams } from "@watt/common/src/utils/parse-request-route-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { getGasMeterData } from "@watt/external-apis/src/libs/aperture/lookup-gas-meter";
import type { NextRequest } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  mprn: MPRNSchema
});

type Params = z.infer<typeof ParamsSchema>;

export async function GET(
  request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  try {
    const { mprn } = parseRequestRouteParams(params, ParamsSchema);

    const gasMeter = await getGasMeterData({
      mprn: mprn
    });

    if (!gasMeter || !gasMeter.data) {
      if (gasMeter.error) {
        return ResponseHelper.internalServerError(
          ErrorResponseSchema.parse({
            message: "Internal server error",
            description: JSON.stringify(gasMeter.error)
          })
        );
      }
      return ResponseHelper.notFound({
        message: `No gas data found for mprn: ${mprn}`
      });
    }

    return ResponseHelper.ok(gasMeter.data);
  } catch (error) {
    log.error("aperture/gas/[mprn]/route.GET: ", { error });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error"
      })
    );
  }
}
