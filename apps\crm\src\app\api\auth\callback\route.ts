import { createServerSupabaseClientCRM } from "@watt/common/src/libs/supabase/supabase";
import { log } from "@watt/common/src/utils/axiom-logger";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// This is used to handle the callback from the OAuth provider after the user has signed in.
export async function GET(request: Request) {
  try {
    // The `/auth/callback` route is required for the server-side auth flow implemented
    // by the Auth Helpers package. It exchanges an auth code for the user's session.
    // https://supabase.com/docs/guides/auth/auth-helpers/nextjs#managing-sign-in-with-code-exchange
    const requestUrl = new URL(request.url);
    const code = requestUrl.searchParams.get("code");

    log.info("crm/auth/callback.route.ts: ", { code });
    if (code) {
      const supabase = await createServerSupabaseClientCRM();
      await supabase.auth.exchangeCodeForSession(code);
    }

    // URL to redirect to after sign in process completes
    return NextResponse.redirect(requestUrl.origin);
  } catch (error) {
    log.error("crm/auth/callback.route.ts: ", { error });
    return new NextResponse("Internal Server Error on auth callback", {
      status: 500
    });
  }
}
