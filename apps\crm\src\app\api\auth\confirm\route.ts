import type { EmailOtpType } from "@supabase/supabase-js";
import { createServerSupabaseClientCRM } from "@watt/common/src/libs/supabase/supabase";
import { log } from "@watt/common/src/utils/axiom-logger";
import { type NextRequest, NextResponse } from "next/server";

// https://supabase.com/docs/guides/auth/server-side/email-based-auth-with-pkce-flow-for-ssr
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get("token_hash");
  const type = searchParams.get("type") as EmailOtpType | null;
  const next = searchParams.get("next") ?? "/";
  const redirectTo = request.nextUrl.clone();
  redirectTo.pathname = next;

  if (token_hash && type) {
    const supabase = await createServerSupabaseClientCRM();
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash
    });

    if (!error) {
      console.log("redirecting to: ", redirectTo);
      return NextResponse.redirect(redirectTo);
    }

    log.error("auth/confirm/route.GET: Failed to handle auth verification ", {
      error
    });
  }

  // return the user to an error page with some instructions
  redirectTo.pathname = "/authentication/auth-code-error";
  return NextResponse.redirect(redirectTo);
}
