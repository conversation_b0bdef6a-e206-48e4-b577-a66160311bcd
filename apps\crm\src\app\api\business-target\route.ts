import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestQueryParams } from "@watt/common/src/utils/parse-request-query-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import {
  BusinessTargeterParamsSchema,
  getBusinessTarget
} from "@watt/external-apis/src/libs/experian/business-targeter";
import { type NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const queryParams = parseRequestQueryParams(
      request,
      BusinessTargeterParamsSchema
    );

    const businessTarget = await getBusinessTarget(queryParams);

    if (!businessTarget || !businessTarget.data) {
      if (businessTarget.error) {
        return ResponseHelper.internalServerError(
          ErrorResponseSchema.parse({
            message: "Internal server error",
            description: JSON.stringify(businessTarget.error)
          })
        );
      }
      return new NextResponse(
        `No company found for queryParams: ${JSON.stringify(queryParams)}`,
        {
          status: 404
        }
      );
    }

    return new NextResponse(JSON.stringify(businessTarget.data), {
      headers: {
        "content-type": "application/json"
      }
    });
  } catch (error) {
    log.error("business-target/[id]/route.GET: ", { error });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error",
        description: JSON.stringify(error)
      })
    );
  }
}
