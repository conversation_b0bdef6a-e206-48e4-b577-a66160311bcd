import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestRouteParams } from "@watt/common/src/utils/parse-request-route-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { lookUpCompany } from "@watt/external-apis/src/libs/companies-house/look-up-company";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  company_number: z.string()
});

type Params = z.infer<typeof ParamsSchema>;

export async function GET(
  request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  try {
    const { company_number } = parseRequestRouteParams(params, ParamsSchema);

    const companyHouse = await lookUpCompany({
      companyNumber: company_number
    });

    if (!companyHouse || !companyHouse.data) {
      if (companyHouse.error) {
        return ResponseHelper.internalServerError(
          ErrorResponseSchema.parse({
            message: "Internal server error",
            description: JSON.stringify(companyHouse.error)
          })
        );
      }
      return new NextResponse(
        `No company found for company_number: ${company_number}`,
        {
          status: 404
        }
      );
    }

    return new NextResponse(JSON.stringify(companyHouse.data), {
      headers: {
        "content-type": "application/json"
      }
    });
  } catch (error) {
    log.error("companies-house/[id]/route.GET: ", { error });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error"
      })
    );
  }
}
