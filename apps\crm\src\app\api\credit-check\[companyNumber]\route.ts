import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestQueryParams } from "@watt/common/src/utils/parse-request-query-params";
import { parseRequestRouteParams } from "@watt/common/src/utils/parse-request-route-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { getCommercialDelphiScore } from "@watt/external-apis/src/libs/experian/commercial-delphi-score";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  companyNumber: z.string()
});

type Params = z.infer<typeof ParamsSchema>;

export async function GET(
  request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  const queryParams = parseRequestQueryParams(
    request,
    z.object({
      companyType: z
        .string()
        .transform(value => Number.parseInt(value, 10))
        .refine(value => !Number.isNaN(value), {
          message: "companyType must be a string representing a number"
        })
    })
  );

  const { companyType } = queryParams;

  try {
    const { companyNumber } = parseRequestRouteParams(params, ParamsSchema);

    const delphiScore = await getCommercialDelphiScore({
      companyNumber,
      companyType
    });

    if (!delphiScore || !delphiScore.data) {
      if (delphiScore.error) {
        return ResponseHelper.internalServerError(delphiScore.error);
      }
      return new NextResponse(
        `No company found for companyNumber: ${companyNumber}`,
        {
          status: 404
        }
      );
    }

    return new NextResponse(JSON.stringify(delphiScore.data), {
      headers: {
        "content-type": "application/json"
      }
    });
  } catch (error) {
    log.error("credit-check/[id]/route.GET: ", { error });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error"
      })
    );
  }
}
