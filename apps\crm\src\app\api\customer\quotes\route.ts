import { generatePdf } from "@watt/api/src/service/email";
import { log } from "@watt/common/src/utils/axiom-logger";
import { parseRequestQueryParamsToResponse } from "@watt/common/src/utils/parse-request-query-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import type { NextRequest } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const QuotesPdfParamsSchema = z.object({
  emailquoteid: z.string()
});

export async function GET(request: NextRequest) {
  try {
    const { data, error } = parseRequestQueryParamsToResponse(
      request,
      QuotesPdfParamsSchema
    );

    if (error) {
      log.error("Failed to parse query parameters", {
        error
      });

      return ResponseHelper.badRequest({
        message: "Invalid query parameters"
      });
    }

    if (!data.emailquoteid) {
      return ResponseHelper.badRequest({
        message: "emailquoteid is required"
      });
    }

    const pdf = await generatePdf(data.emailquoteid, request.nextUrl.origin);

    // TODO (Stephen): Use 'ErrorResponseSchema' for the error type so
    // we can have an error code instead of using if name === x
    if (pdf.error && pdf.error.name === "PromiseTimeoutError") {
      log.error("Failed to create PDF timeout", {
        error: pdf.error
      });
      return ResponseHelper.internalServerError({
        message: "Failed to create PDF timeout"
      });
    }

    if (pdf.error) {
      log.error("Failed to create PDF", {
        error: pdf.error
      });
      return ResponseHelper.internalServerError({
        message: "Failed to create PDF"
      });
    }

    return ResponseHelper.ok(pdf.data, "pdf");
  } catch (e) {
    const error = e as Error;
    log.error("Failed to create PDF", { error });
  }
}
