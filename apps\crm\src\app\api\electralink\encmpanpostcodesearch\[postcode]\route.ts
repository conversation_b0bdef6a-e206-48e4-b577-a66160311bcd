import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { postcodeSchema } from "@watt/common/src/utils/format-postcode";
import { parseRequestRouteParams } from "@watt/common/src/utils/parse-request-route-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { getEncryptedMpanByPostcode } from "@watt/external-apis/src/libs/electralink/encrypted-mpan-by-postcode";
import type { NextRequest } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  postcode: postcodeSchema
});

type Params = z.infer<typeof ParamsSchema>;

export async function GET(
  request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  try {
    const { postcode } = parseRequestRouteParams(params, ParamsSchema);

    const encryptedMpan = await getEncryptedMpanByPostcode({
      postcode
    });

    if (!encryptedMpan || !encryptedMpan.data) {
      if (encryptedMpan.error) {
        return ResponseHelper.internalServerError(
          ErrorResponseSchema.parse({
            message: "Internal server error",
            description: JSON.stringify(encryptedMpan.error)
          })
        );
      }
      return ResponseHelper.notFound({
        message: `No encrypted mpan data found for postcode: ${postcode}`
      });
    }

    return ResponseHelper.ok(encryptedMpan.data);
  } catch (error) {
    log.error("electralink/encmpanpostcodesearch/[postcode]/route.GET: ", {
      error
    });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error"
      })
    );
  }
}
