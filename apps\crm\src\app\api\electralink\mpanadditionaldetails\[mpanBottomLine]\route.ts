import { MPANSchema } from "@watt/common/src/mpan/mpan";
import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestRouteParams } from "@watt/common/src/utils/parse-request-route-params";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { getMpanAdditionalDetailsByMpanBottomLine } from "@watt/external-apis/src/libs/electralink/mpan-additional-details";
import type { NextRequest } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  mpanBottomLine: MPANSchema
});

type Params = z.infer<typeof ParamsSchema>;

export async function GET(
  request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  try {
    const { mpanBottomLine } = parseRequestRouteParams(params, ParamsSchema);

    const mpanAdditionalDetails =
      await getMpanAdditionalDetailsByMpanBottomLine({
        mpan: mpanBottomLine
      });

    if (!mpanAdditionalDetails || !mpanAdditionalDetails.data) {
      if (mpanAdditionalDetails.error) {
        return ResponseHelper.internalServerError(
          ErrorResponseSchema.parse({
            message: "Internal server error",
            description: JSON.stringify(mpanAdditionalDetails.error)
          })
        );
      }
      return ResponseHelper.notFound({
        message: `No additional details found for mpanBottomLine: ${mpanBottomLine}`
      });
    }

    return ResponseHelper.ok(mpanAdditionalDetails.data);
  } catch (error) {
    log.error(
      "electralink/mpanadditionaldetails/[mpanBottomLine]/route.GET: ",
      { error }
    );
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error"
      })
    );
  }
}
