import { updateEmailEventStatus } from "@watt/api/src/service/email-event";
import { parseRequestBody } from "@watt/common/src/utils/parse-next-request-body";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { stringToPrismaEnum } from "@watt/common/src/utils/string-to-prisma-enum";
import { zodSeparatePassthroughFields } from "@watt/common/src/utils/zod-separate-passthrough-fields";
import { EmailEventModel } from "@watt/db/prisma/zod";
import { EmailTransactionEvents } from "@watt/db/src/enums";
import type { NextRequest } from "next/server";
import { z } from "zod";

const EmailEventsExtendedModel = EmailEventModel.extend({
  urlOffset: z
    .object({
      index: z.number(),
      type: z.string()
    })
    .optional(),
  messageId: z.string().optional(),
  event: z.string().transform(stringToPrismaEnum(EmailTransactionEvents)),
  sendAt: z.coerce.string(),
  customArgs: z.record(z.unknown()).optional()
}).passthrough();

// Function to separate known fields and passthrough fields
const separateFields = (fields: z.infer<typeof EmailEventsExtendedModel>) => {
  const [knownFields, passthroughFields] = zodSeparatePassthroughFields(
    EmailEventsExtendedModel,
    fields
  );
  return { knownFields, passthroughFields };
};

const extractUrlOffset = (urlOffset?: { index: number; type: string }) => {
  return (
    urlOffset && {
      urlOffsetIndex: urlOffset.index,
      urlOffsetType: urlOffset.type
    }
  );
};

const extractMessageId = (sgMessageId: string) => {
  const messageId = sgMessageId.split(".")[0];
  if (!messageId) {
    throw new Error("Invalid sgMessageId - missing messageId component");
  }
  return { messageId };
};

const EmailEventsTransformedModel = z.array(
  EmailEventsExtendedModel.transform(fields => {
    const { knownFields, passthroughFields } = separateFields(fields);
    const { urlOffset, ...rest } = knownFields;

    return {
      ...rest,
      ...extractUrlOffset(urlOffset),
      ...extractMessageId(rest.sgMessageId),
      customArgs: passthroughFields
    };
  })
);

export async function POST(req: NextRequest) {
  try {
    const events = await parseRequestBody(req, EmailEventsTransformedModel);

    await updateEmailEventStatus(events);
  } catch (e) {
    const error = e as Error;
    if (error.stack?.includes("createdById")) {
      // If the email was sent via SendGrid UI it will not have a createdById
      return ResponseHelper.noContent();
    }
    throw error;
  }

  return ResponseHelper.noContent();
}
