import type { NextApiResponse } from "next";
import type { NextRequest } from "next/server";

// Tell Next.js not to prebuild this route during build time
export const dynamic = "force-dynamic";
export const dynamicParams = true;
export const revalidate = 0;

// Create a function that returns an object with all the handlers
function createHandlers() {
  // Shared function to get the handlers
  async function getHandlersInternal() {
    const { serve } = await import("@novu/framework/next");
    const { workflows } = await import("@watt/notifications/src/workflows");
    return serve({ workflows });
  }

  // Define the handler methods
  return {
    async GET(req: NextRequest, context: NextApiResponse) {
      const handlers = await getHandlersInternal();
      return handlers.GET(req, context);
    },

    async POST(req: NextRequest, context: NextApiResponse) {
      const handlers = await getHandlersInternal();
      return handlers.POST(req, context);
    },

    async OPTIONS(req: NextRequest, context: NextApiResponse) {
      const handlers = await getHandlersInternal();
      return handlers.OPTIONS(req, context);
    }
  };
}

// Export the handlers using destructuring
export const { GET, POST, OPTIONS } = createHandlers();
