import { env } from "@watt/common/src/config/env";
import { log } from "@watt/common/src/utils/axiom-logger";
import { ErrorResponseSchema } from "@watt/common/src/utils/error-response-schema";
import { parseRequestQueryParamsToResponse } from "@watt/common/src/utils/parse-request-query-params";
import { buildSafeUrl } from "@watt/common/src/utils/safe-url-builder";
import { ResponseHelper } from "@watt/common/src/utils/server/response-helper";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const StorageProxyParamsSchema = z.object({
  token: z.string()
});

/**
 * Allows us to proxy requests to the Supabase storage service.
 * changes urls of the form;
 * http://localhost:54321/storage/v1/object/sign/email-attachments/hDO_Qs-9ToGssXNXND2sMw/quote.pdf?token={token}
 * to
 * http://localhost:3000/api/storage/v1/object/sign/email-attachments/hDO_Qs-9ToGssXNXND2sMw/quote.pdf?token={token}
 * @param request
 * @param param1
 * @returns
 */
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ path: string[] }> }
) {
  const params = await props.params;
  const { data, error } = parseRequestQueryParamsToResponse(
    request,
    StorageProxyParamsSchema
  );

  if (error) {
    log.error("Failed to parse query parameters", { error });
    return ResponseHelper.badRequest(
      ErrorResponseSchema.parse({
        message: "Invalid query parameters",
        description: JSON.stringify(error)
      })
    );
  }

  const destinationUrl = buildSafeUrl({
    baseUrl: env.NEXT_PUBLIC_SUPABASE_URL,
    path: `/storage/${params.path.join("/")}`,
    queryParams: {
      token: data.token
    }
  });

  try {
    const response = await fetch(destinationUrl);

    if (!response.ok) {
      log.error("Failed to fetch from storage proxy", {
        destinationUrl,
        status: response.status,
        data,
        params
      });
      return ResponseHelper.internalServerError(
        ErrorResponseSchema.parse({
          message: "Internal Server Error",
          description: response.statusText
        })
      );
    }

    const responseBody = await response.blob();

    return new NextResponse(responseBody, {
      status: response.status,
      headers: {
        "Content-Type":
          response.headers.get("Content-Type") || "application/octet-stream",
        "Content-Disposition":
          response.headers.get("Content-Disposition") || "inline"
      }
    });
  } catch (e) {
    const error = e as Error;
    log.error("Error in storage proxy route:", {
      error,
      destinationUrl,
      data,
      params
    });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal Server Error",
        description: error instanceof Error ? error.message : undefined
      })
    );
  }
}
