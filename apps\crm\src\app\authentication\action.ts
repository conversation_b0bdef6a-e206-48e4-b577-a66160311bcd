"use server";

import { createServerSupabaseClientCRM } from "@watt/common/src/libs/supabase/supabase";
import { routes } from "@watt/crm/config/routes";
import { actionClient } from "@watt/crm/lib/safe-action";
import { returnValidationErrors } from "next-safe-action";
import {
  MagicLinkSchema,
  PasswordResetSchema,
  PasswordUpdateSchema,
  UserAuthSchema
} from "./schema";

export const signIn = actionClient
  .schema(UserAuthSchema)
  .action(async ({ parsedInput: { email, password } }) => {
    const supabase = await createServerSupabaseClientCRM();
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      returnValidationErrors(UserAuthSchema, {
        _errors: [error.message ?? "Failed to sign in. Please try again."]
      });
    }

    // IMPORTANT: Server-side redirects from Server Actions can cause issues with POST requests
    // being preserved through the redirect, leading to the destination page receiving a POST
    // instead of a GET request. This manifests as tabs/windows not loading properly after login.
    // To fix this, we return a success response and handle the redirect on the client side.
    return { success: true, redirectTo: routes.HOME };
  });

export const resetPassword = actionClient
  .schema(PasswordResetSchema)
  .action(async ({ parsedInput: { email } }) => {
    const supabase = await createServerSupabaseClientCRM();
    const { error } = await supabase.auth.resetPasswordForEmail(email);

    if (error) {
      returnValidationErrors(PasswordResetSchema, {
        _errors: [
          error.message ?? "Failed to reset password. Please try again."
        ]
      });
    }

    // Return success response to avoid POST redirect issues
    return { success: true, redirectTo: routes.login };
  });

export const updatePassword = actionClient
  .schema(PasswordUpdateSchema)
  .action(async ({ parsedInput: { password } }) => {
    const supabase = await createServerSupabaseClientCRM();
    const { error } = await supabase.auth.updateUser({ password });

    if (error) {
      returnValidationErrors(PasswordUpdateSchema, {
        _errors: [
          error.message ?? "Failed to update password. Please try again."
        ]
      });
    }

    // Return success response to avoid POST redirect issues
    return { success: true, redirectTo: routes.login };
  });

export const sendMagicLink = actionClient
  .schema(MagicLinkSchema)
  .action(async ({ parsedInput: { email } }) => {
    const supabase = await createServerSupabaseClientCRM();
    const { error } = await supabase.auth.signInWithOtp({ email });

    if (error) {
      returnValidationErrors(MagicLinkSchema, {
        _errors: [
          error.message ?? "Failed to send magic link. Please try again."
        ]
      });
    }

    // Return success response to avoid POST redirect issues
    return { success: true, redirectTo: routes.login };
  });
