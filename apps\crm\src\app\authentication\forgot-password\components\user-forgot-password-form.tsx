"use client";

import AutoForm, { AutoFormButton } from "@watt/crm/components/ui/auto-form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import { resetPassword } from "../../action";
import { PasswordResetSchema } from "../../schema";

export function UserForgotPasswordForm() {
  const router = useRouter();
  const formAction = useAction(resetPassword, {
    onSuccess: result => {
      toast({
        variant: "success",
        title: "Password reset email sent",
        description: "Check your email for further instructions."
      });
      if (result?.data?.success && result?.data?.redirectTo) {
        router.push(result.data.redirectTo);
      }
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to send password reset email. Please try again.",
        variant: "destructive"
      });
    }
  });

  return (
    <AutoForm
      formSchema={PasswordResetSchema}
      formAction={formAction}
      fieldConfig={{
        email: {
          inputProps: {
            placeholder: "<EMAIL>",
            autoCapitalize: "none",
            autoComplete: "email",
            autoCorrect: "off"
          }
        }
      }}
    >
      {({ isSubmitting }) => (
        <AutoFormButton
          disabled={isSubmitting}
          isSubmitting={isSubmitting}
          className="w-full"
        >
          Reset Password
        </AutoFormButton>
      )}
    </AutoForm>
  );
}
