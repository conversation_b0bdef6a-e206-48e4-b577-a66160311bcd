import { buttonVariants } from "@watt/crm/components/ui/button";
import Link from "next/link";
import { UserForgotPasswordForm } from "./components/user-forgot-password-form";

export default function ForgotPasswordPage() {
  return (
    <>
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="font-semibold text-2xl tracking-tight">
          Forgot Password
        </h1>
        <p className="text-muted-foreground text-sm">
          Enter your email to reset your password.
        </p>
      </div>
      <UserForgotPasswordForm />
      <div className="flex flex-col space-y-2">
        <Link
          className={buttonVariants({ variant: "link" })}
          href="/authentication/login"
        >
          Back to login
        </Link>
      </div>
    </>
  );
}
