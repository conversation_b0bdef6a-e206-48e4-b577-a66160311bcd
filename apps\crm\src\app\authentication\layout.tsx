import { WattLogo } from "@watt/common/src/components/svgs/watt-logo.svg";
import { env } from "@watt/common/src/config/env";
import { capitalize } from "@watt/common/src/utils/capitalise";
import { isProductionEnvironment } from "@watt/common/src/utils/is-production-environment";
import { routes } from "@watt/crm/config/routes";
import Link from "next/link";
import type { PropsWithChildren } from "react";

/**
 * Gets the environment display prefix if needed
 */
function getEnvironmentPrefix(): string {
  // Don't show environment prefix in production
  if (isProductionEnvironment()) {
    return "";
  }

  const environment = env.NEXT_PUBLIC_ENVIRONMENT;

  // If no environment is set, return empty string
  if (!environment) {
    return "";
  }

  // Format the environment name for display (sentence case)
  const environmentDisplay =
    environment === "develop" ? "Dev" : capitalize(environment.toLowerCase());

  return `${environmentDisplay} `;
}

export default function AuthenticationLayout({ children }: PropsWithChildren) {
  const environmentPrefix = getEnvironmentPrefix();

  return (
    <div className="container relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-black lg:flex dark:border-r">
        <div
          className="absolute inset-0 bg-auto bg-bottom bg-sky-500/10 bg-no-repeat"
          style={{
            backgroundImage: "url(/skyline.svg)"
          }}
        />
        <div className="relative z-20 flex items-center font-medium text-lg">
          <Link
            href={routes.login}
            className="flex items-center gap-2 transition duration-500 hover:scale-110"
          >
            <div
              className={`inline-flex items-center rounded p-2 ${environmentPrefix ? "environment-indicator-stripe" : ""}`}
            >
              <WattLogo className="h-9 w-9 text-primary" />
              <span className="ml-2 font-bold text-2xl text-primary">
                {environmentPrefix}Watt.co.uk
              </span>
            </div>
          </Link>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          {children}
        </div>
      </div>
    </div>
  );
}
