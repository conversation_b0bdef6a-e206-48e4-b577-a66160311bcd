"use client";

import AutoForm, { AutoFormButton } from "@watt/crm/components/ui/auto-form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAppStore } from "@watt/crm/store/app-store";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { signIn } from "../../action";
import { UserAuthSchema } from "../../schema";

export function UserLoginForm() {
  const router = useRouter();
  const { getSessionUser } = useAppStore(state => ({
    getSessionUser: state.getSessionUser
  }));

  const formAction = useAction(signIn, {
    onError: ({ error }) => {
      toast({
        title: "Sign-in failed",
        description:
          error.validationErrors?._errors?.[0] ??
          "An error occurred during sign-in.",
        variant: "destructive"
      });
    },
    onSuccess: result => {
      if (result?.data?.success && result?.data?.redirectTo) {
        // getSessionUser triggers an async update to the store but doesn't return a promise
        getSessionUser();
        // Use router.push for client-side navigation to ensure proper GET request
        router.push(result.data.redirectTo);
      }
    }
  });

  useEffect(() => {
    if (
      Notification.permission !== "granted" &&
      Notification.permission !== "denied"
    ) {
      Notification.requestPermission().then(permission => {
        if (permission === "granted") {
          console.info("Notification permission granted.");
        }
      });
    }
  }, []);

  return (
    <AutoForm
      formSchema={UserAuthSchema}
      formAction={formAction}
      fieldConfig={{
        email: {
          inputProps: {
            placeholder: "<EMAIL>",
            autoCapitalize: "none",
            autoComplete: "email",
            autoCorrect: "off"
          }
        },
        password: {
          inputProps: {
            type: "password",
            placeholder: "••••••••",
            autoComplete: "current-password",
            autoCapitalize: "none",
            autoCorrect: "off"
          }
        }
      }}
    >
      {({ isSubmitting }) => (
        <AutoFormButton
          disabled={isSubmitting}
          isSubmitting={isSubmitting}
          className="w-full"
        >
          Login
        </AutoFormButton>
      )}
    </AutoForm>
  );
}
