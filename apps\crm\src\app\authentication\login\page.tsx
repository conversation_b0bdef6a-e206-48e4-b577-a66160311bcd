"use client";

import { Alert, AlertDescription } from "@watt/crm/components/ui/alert";
import { buttonVariants } from "@watt/crm/components/ui/button";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { UserLoginForm } from "./components/user-login-form";

export default function LoginPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const errorMessages: Record<string, string> = {
    unauthorized_access:
      "You do not have permission to access this application."
  };
  return (
    <>
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="font-semibold text-2xl tracking-tight">Welcome!</h1>
        <p className="text-muted-foreground text-sm">
          Enter your email and password to continue.
        </p>
      </div>
      {error && errorMessages[error] && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{errorMessages[error]}</AlertDescription>
        </Alert>
      )}
      <UserLoginForm />
      <div className="flex flex-col space-y-2">
        <Link
          className={buttonVariants({ variant: "link" })}
          href="/authentication/forgot-password"
        >
          Forgot password?
        </Link>
      </div>
    </>
  );
}
