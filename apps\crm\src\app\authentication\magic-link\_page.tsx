import { buttonVariants } from "@watt/crm/components/ui/button";
import Link from "next/link";
import { UserMagicLinkForm } from "./components/user-magic-link-form";

export default function MagicLinkPage() {
  return (
    <>
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="font-semibold text-2xl tracking-tight">Magic Link</h1>
        <p className="text-muted-foreground text-sm">
          Enter your email to receive a magic link for login.
        </p>
      </div>
      <UserMagicLinkForm />
      <div className="flex flex-col space-y-2">
        <Link
          className={buttonVariants({ variant: "link" })}
          href="/authentication/login"
        >
          Back to login
        </Link>
      </div>
    </>
  );
}
