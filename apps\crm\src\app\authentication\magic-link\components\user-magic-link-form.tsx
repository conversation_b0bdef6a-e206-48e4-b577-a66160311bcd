"use client";

import AutoForm, { AutoFormButton } from "@watt/crm/components/ui/auto-form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAction } from "next-safe-action/hooks";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { sendMagicLink } from "../../action";
import { MagicLinkSchema } from "../../schema";

export function UserMagicLinkForm() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const formAction = useAction(sendMagicLink, {
    onSuccess: result => {
      toast({
        title: "Magic link sent",
        description: "Check your email for the login link.",
        variant: "success"
      });
      if (result?.data?.success && result?.data?.redirectTo) {
        router.push(result.data.redirectTo);
      }
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to send magic link. Please try again.",
        variant: "destructive"
      });
    }
  });

  useEffect(() => {
    if (searchParams.get("expired") === "true") {
      toast({
        title: "Link has expired",
        description: "Please try another link.",
        variant: "destructive"
      });
    }
  }, [searchParams]);

  return (
    <AutoForm
      formSchema={MagicLinkSchema}
      formAction={formAction}
      fieldConfig={{
        email: {
          inputProps: {
            placeholder: "<EMAIL>",
            autoCapitalize: "none",
            autoComplete: "email",
            autoCorrect: "off"
          }
        }
      }}
    >
      {({ isSubmitting }) => (
        <AutoFormButton
          disabled={isSubmitting}
          isSubmitting={isSubmitting}
          className="w-full"
          variant="outline"
        >
          Send Magic Link
        </AutoFormButton>
      )}
    </AutoForm>
  );
}
