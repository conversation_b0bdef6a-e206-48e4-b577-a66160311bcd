"use client";

import AutoForm, { AutoFormButton } from "@watt/crm/components/ui/auto-form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import { updatePassword } from "../../action";
import { PasswordUpdateSchema } from "../../schema";

export function UserUpdatePasswordForm() {
  const router = useRouter();
  const formAction = useAction(updatePassword, {
    onSuccess: result => {
      toast({
        variant: "success",
        title: "Password updated",
        description: "Your password has been updated."
      });
      if (result?.data?.success && result?.data?.redirectTo) {
        router.push(result.data.redirectTo);
      }
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update password. Please try again.",
        variant: "destructive"
      });
    }
  });

  return (
    <AutoForm
      formSchema={PasswordUpdateSchema}
      formAction={formAction}
      fieldConfig={{
        password: {
          inputProps: {
            placeholder: "********",
            autoCapitalize: "none",
            autoComplete: "password",
            autoCorrect: "off"
          }
        }
      }}
    >
      {({ isSubmitting }) => (
        <AutoFormButton
          disabled={isSubmitting}
          isSubmitting={isSubmitting}
          className="w-full"
        >
          Update Password
        </AutoFormButton>
      )}
    </AutoForm>
  );
}
