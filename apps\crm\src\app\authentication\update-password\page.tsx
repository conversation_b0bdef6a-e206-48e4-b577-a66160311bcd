import { buttonVariants } from "@watt/crm/components/ui/button";
import type { Metadata } from "next";
import Link from "next/link";
import { UserUpdatePasswordForm } from "./components/user-forgot-password-form";

export const metadata: Metadata = {
  title: "Update Password",
  description: "Update your password."
};

// Users can only access this page if they have a temp session that is emailed to them.
// The direct logic for this is in the middleware.ts file for the supabase session auth.
export default function ForgotPasswordPage() {
  return (
    <>
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="font-semibold text-2xl tracking-tight">
          Forgot Password
        </h1>
        <p className="text-muted-foreground text-sm">
          Enter your new password.
        </p>
      </div>
      <UserUpdatePasswordForm />
      <div className="flex flex-col space-y-2">
        <Link
          className={buttonVariants({ variant: "link" })}
          href="/authentication/login"
        >
          Back to login
        </Link>
      </div>
    </>
  );
}
