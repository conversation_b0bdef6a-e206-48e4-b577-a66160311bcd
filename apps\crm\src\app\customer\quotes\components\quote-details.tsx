"use client";
import { formatStringToDateShort } from "@watt/common/src/utils/format-date";
import { formatkWhConsumption } from "@watt/common/src/utils/format-kwh-consumption";
import { getFullMeterIdentifier } from "@watt/common/src/utils/get-full-meter-identifier";
import { UtilityType } from "@watt/db/src/enums";

import Image from "next/image";

import { MpxnInputField } from "@watt/crm/components/mpxn/mpxn-input-field";

import { trpcClient } from "@watt/crm/utils/api";
import { QuoteResultsDataTable } from "./quote-results-data-table";

type QuoteDetailsProps = {
  emailQuoteId: string;
};

export function QuoteDetails({ emailQuoteId }: QuoteDetailsProps) {
  const [emailQuote] =
    trpcClient.quote.getPublicQuotesByEmailQuoteId.useSuspenseQuery({
      emailQuoteId: emailQuoteId ?? ""
    });

  const { quoteList, quotes, subject } = emailQuote;

  const {
    contractStartDate,
    siteMeter,
    createdAt,
    utilityType,
    electricityUsage,
    gasUsage
  } = quoteList ?? {};

  const { companySite, electricSiteMeter, gasSiteMeter } = siteMeter ?? {};

  const companyName = companySite?.company.name;

  const totalUsage = electricityUsage?.totalUsage ?? gasUsage?.totalUsage;

  const fullMeterIdentifier = getFullMeterIdentifier(
    utilityType === UtilityType.ELECTRICITY
      ? electricSiteMeter?.mpan
      : gasSiteMeter?.mprnValue
  );

  if (!emailQuoteId) {
    return <div>Missing query param `emailQuoteId`</div>;
  }

  if (!emailQuoteId || !emailQuote || !quoteList || !utilityType) {
    return <div>Error fetching quote</div>;
  }

  if (!fullMeterIdentifier) {
    return <div>Missing meter identifier</div>;
  }

  return (
    <div className="flex flex-col p-8">
      <div className="mb-8 flex justify-between bg-primary">
        <div>
          <Image
            className="py-5 pl-5"
            src={"/static/watt-logo-white.png"}
            width="214"
            height="52"
            alt="Watto.co.uk Logo"
          />
        </div>

        <div>
          <Image
            className="px-5"
            src={"/static/skyline.png"}
            width="400"
            height="1164"
            alt="Watt.co.uk header"
          />
        </div>
      </div>

      <div className="px-4 pb-6">
        <h1 className="pb-8 text-center font-bold text-3xl">{subject}</h1>
        <p className="mb-6">Dear {companyName},</p>
        <p className="text-primary">
          {quotes.length > 1 ? (
            <>
              Thank you for your time on the phone earlier. As discussed,
              we&apos;ve carefully selected several{" "}
              {utilityType.toLocaleLowerCase()} quotes for you.
            </>
          ) : (
            <>
              Following our recent phone conversation, I&apos;m pleased to
              present you with a competitive {utilityType.toLocaleLowerCase()}{" "}
              energy quote tailored to your needs:
            </>
          )}
        </p>
      </div>

      <h2 className="pb-4 text-center font-bold text-xl">Quote Details</h2>
      <div className="mb-6 text-center text-sm">
        <div className="m-auto mb-4 w-[225px] text-xs">
          <MpxnInputField
            meterIdentifier={fullMeterIdentifier}
            utilityType={utilityType}
            censored
          />
        </div>
        <div className="mb-2">
          <span className="text-primary">
            Quote Date:{" "}
            {formatStringToDateShort(createdAt?.toDateString() ?? "")}
          </span>
        </div>
        <div className="mb-2">
          <span className="text-primary">Business Name: {companyName}</span>
        </div>
        <div className="mb-2">
          <span>
            Contract Start Date:{" "}
            {formatStringToDateShort(contractStartDate?.toDateString() ?? "")}
          </span>
        </div>
        <div>
          <span className="text-primary">
            Estimated Usage per annum: {formatkWhConsumption(totalUsage)}
          </span>
        </div>
      </div>
      <div className="flex">
        {!!emailQuote?.quoteList && !!emailQuote.quotes && (
          <QuoteResultsDataTable
            data={emailQuote}
            fullMeterIdentifier={fullMeterIdentifier}
          />
        )}
      </div>
      <div className="mt-2 justify-center px-4">
        <p className="break-inside-avoid pt-[3ch] pb-[3ch] text-center text-muted-foreground text-xs">
          Prices are valid at the time of quotation but may be withdrawn at the
          supplier&apos;s discretion due to fluctuations in the energy market
          and are subject to a satisfactory credit check. All Prices quoted are
          base unit rate + commission and standing charge for energy, full
          details are enclosed in the contract. These prices may include FIT, RO
          and other pass-through charges and government charges or taxes unless
          otherwise stated. In order for you to move to another supplier,
          contract Termination has to be sent to your current supplier. If you
          subsequently fail to agree a new contract before the end of the
          contract, this current supplier will charge you out-of-contract rates
          until such time as a new contract is agreed. For more information
          please see your contract terms and conditions or contact the sender.
          The sender does not accept responsibility for any errors or omissions.
        </p>
      </div>
      <div className="flex break-inside-avoid flex-col items-center justify-around pt-4">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={"/static/ups.png"}
          width="200"
          height="48"
          alt="Utility Preference Services Ltd Logo"
        />
        <p className="text-muted-foreground text-xs">
          Watt Utilities are working in partnership with Utility Preference
          Services Ltd.{" "}
        </p>
      </div>
    </div>
  );
}
