"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { calculatePriceDifference } from "@watt/common/src/utils/calculate-price-difference";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatAndPadCurrency } from "@watt/common/src/utils/format-currency";

import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { HUNDRED_THOUSAND_LENGTH_WITH_SYMBOLS } from "@watt/crm/config/currency";

type UtilityQuote =
  | FindUniqueQuoteListSelectQuotesGetPayload["electricQuote"]
  | FindUniqueQuoteListSelectQuotesGetPayload["gasQuote"];

function extractQuoteValue<T>(
  quote: FindUniqueQuoteListSelectQuotesGetPayload,
  extractor: (utilityQuote: UtilityQuote) => T | undefined
): T | undefined {
  return quote.electricQuote
    ? extractor(quote.electricQuote)
    : quote.gasQuote
      ? extractor(quote.gasQuote)
      : undefined;
}

export const quoteResultsColumns = (
  maxDecimalPlaces: number,
  currentPrice: number
): ColumnDef<FindUniqueQuoteListSelectQuotesGetPayload>[] => [
  {
    accessorKey: "supplier",
    accessorFn: quote => quote.provider.udcoreId,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Supplier / Product" />
    ),
    cell: ({ getValue, row }) => {
      const sticky = row.original.sticky;
      const supplier = getValue() as string;
      const { logoFileName } = row.original.provider;
      const contractType =
        row.original.electricQuote?.contractType ??
        row.original.gasQuote?.contractType;
      return (
        <div className="flex flex-col items-center justify-center">
          {/* Using nextJS Image causes it to not render some images when too many are loaded at once. */}
          {logoFileName ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={`/static/providers/${logoFileName}.png`}
              className="h-auto w-[60px] object-scale-down"
              alt={supplier}
              width="60"
              height="20"
            />
          ) : (
            <span className={cn(sticky && "text-primary-foreground")}>
              {supplier}
            </span>
          )}
          {!sticky && (
            <div className="flex flex-row gap-2 pt-[2px] pr-1">
              <span className="text-xs">{contractType}</span>
            </div>
          )}
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "duration",
    accessorFn: quote => quote.duration,
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Term"
        description="(month)"
      />
    ),
    cell: ({ getValue }) => {
      const duration = getValue() as number;
      return <div className="text-md">{duration}</div>;
    },
    enableSorting: false,
    enableHiding: false
  },
  // {
  //   accessorKey: "contractType",
  //   accessorFn: quote => extractQuoteValue(quote, q => q?.contractType) ?? "Default",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Product" description="(plan type)" />
  //   ),
  //   cell: ({ getValue }) => {
  //     const contractType = getValue() as string;
  //     // Split "word | word2"
  //     const contractTypes = contractType.split(" | ");

  //     return (
  //       <div className="flex flex-row gap-1 pr-1">
  //         <span className="text-xs">{contractType}</span>
  //       </div>
  //     );
  //   },
  //   enableSorting: false,
  //   enableHiding: false
  // },
  {
    accessorKey: "unitRate",
    accessorFn: quote => {
      const rateValue = extractQuoteValue(quote, q => q?.unitRate);
      return typeof rateValue === "number"
        ? rateValue.toFixed(maxDecimalPlaces)
        : undefined;
    },
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Unit Rate"
        description="(pence/kWh)"
      />
    ),
    cell: ({ getValue }) => {
      const unitRate = getValue<string | undefined>();
      return (
        <div className="flex rounded-tl-md rounded-bl-md bg-muted py-2 font-medium">
          <span className={cn("text-right", !unitRate && "italic")}>
            {unitRate ?? "N/A"}
          </span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "nightUnitRate",
    accessorFn: quote => {
      const rateValue = extractQuoteValue(quote, q =>
        q && "nightUnitRate" in q ? q.nightUnitRate : undefined
      );
      return typeof rateValue === "number" && rateValue > 0
        ? rateValue.toFixed(maxDecimalPlaces)
        : undefined;
    },
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Night Rate"
        description="(pence/kWh)"
      />
    ),
    cell: ({ getValue }) => {
      const nightUnitRate = getValue<string | undefined>();
      return (
        <div className="flex bg-muted py-2 font-medium">
          <span className={cn("text-right", !nightUnitRate && "italic")}>
            {nightUnitRate ?? "N/A"}
          </span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "weekendUnitRate",
    accessorFn: quote => {
      const rateValue = extractQuoteValue(quote, q =>
        q && "weekendUnitRate" in q ? q.weekendUnitRate : undefined
      );
      return typeof rateValue === "number" && rateValue > 0
        ? rateValue.toFixed(maxDecimalPlaces)
        : undefined;
    },
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Off-peak Rate"
        description="(pence/kWh)"
      />
    ),
    cell: ({ getValue }) => {
      const weekendUnitRate = getValue<string | undefined>();
      return (
        <div className="flex bg-muted py-2 font-medium">
          <span className={cn("text-right", !weekendUnitRate && "italic")}>
            {weekendUnitRate ?? "N/A"}
          </span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "standingCharge",
    accessorFn: quote =>
      extractQuoteValue(quote, q =>
        q?.standingCharge?.toFixed(maxDecimalPlaces)
      ),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Standing Charge"
        description="(pence/day)"
      />
    ),
    cell: ({ getValue }) => {
      const standingCharge = getValue<string | undefined>();
      return (
        <div className="flex bg-muted py-2 font-medium">
          <span className={cn("text-right", !standingCharge && "italic")}>
            {standingCharge ?? "N/A"}
          </span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "capacityChargeKva",
    accessorFn: quote =>
      quote.electricQuote?.capacityChargeKva
        ? Number.parseFloat(quote.electricQuote.capacityChargeKva).toFixed(
            maxDecimalPlaces
          )
        : undefined,
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Capacity Charge"
        description="(pence/day)"
      />
    ),
    cell: ({ getValue }) => {
      const capacityCharge = getValue() as string | undefined;
      return (
        <div className="flex bg-muted py-2 font-medium">
          <span className={cn("text-right", !capacityCharge && "italic")}>
            {capacityCharge ?? "N/A"}
          </span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "annualCost",
    accessorFn: quote => extractQuoteValue(quote, q => q?.annualPrice),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Annual Cost"
        description="(excluding VAT and CCL)"
      />
    ),
    cell: ({ getValue }) => {
      const annualCost = getValue<number | undefined>();
      return (
        <div className="flex rounded-tr-md rounded-br-md bg-muted py-2 font-medium">
          <span
            className={cn("whitespace-pre text-right", !annualCost && "italic")}
          >
            {annualCost
              ? formatAndPadCurrency(
                  annualCost,
                  HUNDRED_THOUSAND_LENGTH_WITH_SYMBOLS
                )
              : "N/A"}
          </span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "priceDifference",
    accessorFn: quote => extractQuoteValue(quote, q => q?.priceDifference),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Price Difference"
        description="(£ and %)"
      />
    ),
    cell: ({ getValue, row }) => {
      if (row.original.sticky) {
        return null;
      }

      const difference = getValue<number | undefined>();

      if (difference === undefined || !currentPrice) {
        return <span className="text-muted-foreground italic">N/A</span>;
      }

      const { colorClass, formattedDifference, formattedPercentage } =
        calculatePriceDifference(currentPrice, difference);

      return (
        <div className="flex flex-col justify-between gap-2 whitespace-nowrap font-semibold">
          <span className={cn(colorClass, "")}>{formattedDifference}</span>
          <span className={cn(colorClass, "")}>{formattedPercentage}</span>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false
  }
];
