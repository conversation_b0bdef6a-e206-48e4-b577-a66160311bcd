"use client";

import {
  type Row,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { Public_Quotes_By_Email_Quote_Id } from "@watt/api/src/router";
import { parseLongMpan } from "@watt/common/src/mpan/mpan";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";

import { useMemo, useState } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";

import { quoteResultsColumns } from "./quote-results-columns";

interface DataTableProps {
  data: Public_Quotes_By_Email_Quote_Id;
  fullMeterIdentifier: string;
}

export function QuoteResultsDataTable({
  data,
  fullMeterIdentifier
}: DataTableProps) {
  const { maxDecimalPlaces, tariffRates } = data;
  const quotes = useMemo(() => {
    return data.quoteList?.quotes ?? [];
  }, [data.quoteList?.quotes]);

  const currentSupplier = quotes.find(quote => quote.sticky);
  const utilityQuote =
    currentSupplier?.electricQuote ?? currentSupplier?.gasQuote;
  const currentPrice = utilityQuote?.annualPrice ?? 0;

  const showCapacityCharge = useMemo(() => {
    return (
      parseLongMpan(fullMeterIdentifier).data?.profileClass === "00" &&
      quotes.some(quote => {
        const charge = Number.parseFloat(
          quote.electricQuote?.capacityChargeKva ?? "0"
        );
        return charge > 0;
      })
    );
  }, [fullMeterIdentifier, quotes]);

  const defaultColumnVisibility = {
    unitRate:
      tariffRates.day ||
      quotes.some(
        quote => quote.electricQuote?.unitRate || quote.gasQuote?.unitRate
      ),
    nightUnitRate:
      tariffRates.night ||
      quotes.some(quote => quote.electricQuote?.nightUnitRate),
    weekendUnitRate:
      tariffRates.weekend ||
      quotes.some(quote => quote.electricQuote?.weekendUnitRate),
    priceDifference:
      currentPrice > 0 &&
      quotes.some(
        quote => quote.electricQuote?.annualPrice || quote.gasQuote?.annualPrice
      ),
    capacityChargeKva: showCapacityCharge
  } satisfies VisibilityState;

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    defaultColumnVisibility
  );

  const columns = quoteResultsColumns(maxDecimalPlaces, currentPrice);

  const table = useReactTable({
    data: quotes,
    columns,
    state: {
      columnVisibility
    },

    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel()
  });

  return (
    <div className="min-w-full space-y-4">
      <Table>
        <TableHeader className="sticky top-0 z-10 h-16 bg-background [&_tr]:border-b-0">
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id} className="hover:bg-transparent">
              {headerGroup.headers.map(header => (
                <TableHead
                  key={header.id}
                  className="py-4 text-center *:justify-center"
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
          <TableStickyHeadRow
            heading="Current Supply"
            totalColSpan={
              table.getAllColumns().filter(column => column.getIsVisible())
                .length
            }
            rows={table.getRowModel().rows.filter(row => row.original.sticky)}
          />
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table
              .getRowModel()
              .rows.filter(row => !row.original.sticky)
              .map((row, index) => {
                const visibleColumns = row.getVisibleCells();
                const lastVisibleColumnId = visibleColumns.at(-1)?.column.id;

                return (
                  <TableRow
                    key={row.id}
                    className={cn(
                      "break-inside-avoid hover:bg-transparent",
                      index === 0 && "border-t-2 border-dashed"
                    )}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell
                        key={cell.id}
                        className={cn(
                          "p-0 py-2 text-center *:justify-center",
                          cell.column.id === lastVisibleColumnId && "pr-2"
                        )}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })
          ) : (
            <TableRow className="hover:bg-transparent">
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No quotes found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

type TableStickyHeadRowProps<R> = {
  heading: string;
  totalColSpan: number;
  rows: Row<R>[];
};

function TableStickyHeadRow<R>({
  heading,
  totalColSpan,
  rows
}: TableStickyHeadRowProps<R>) {
  if (!rows.length) {
    return null;
  }

  return (
    <>
      <TableRow>
        <TableHead colSpan={totalColSpan} className="h-auto text-primary">
          <div className="flex flex-row items-center justify-center gap-2">
            <span className="font-semibold text-base italic">{heading}</span>
          </div>
        </TableHead>
      </TableRow>
      {rows.map(row => {
        const visibleColumns = row.getVisibleCells();
        const lastVisibleColumnId = visibleColumns.at(-1)?.column.id;

        return (
          <TableRow
            key={row.id}
            className={
              "sticky top-0 z-10 h-12 border-0 bg-primary/50 text-primary-foreground hover:bg-primary/50"
            }
          >
            {row.getVisibleCells().map(cell => (
              <TableCell
                key={cell.id}
                className={cn(
                  "justify-center p-0 text-center text-foreground *:justify-center",
                  cell.column.id === lastVisibleColumnId && "pr-2"
                )}
              >
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableCell>
            ))}
          </TableRow>
        );
      })}
    </>
  );
}
