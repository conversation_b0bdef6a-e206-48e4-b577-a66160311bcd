import { parseSearchParamsToResponse } from "@watt/common/src/utils/parse-search-params";
import type { Metadata } from "next";
import { z } from "zod";

import { HydrateClient, tRPCServerApi } from "@watt/crm/trpc/server";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { QuoteDetails } from "./components/quote-details";
export const metadata: Metadata = {
  title: "Your Quotes",
  description: "Explore Your Energy Options"
};

type PageProps = {
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  params: { [key: string]: any };
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  searchParams: { [key: string]: any };
};

const QuotesPageParamsSchema = z.object({
  emailquoteid: z.string()
});

export default async function QuotesPage(props: PageProps) {
  const { data, error } = parseSearchParamsToResponse(
    await props.searchParams,
    QuotesPageParamsSchema
  );

  if (!data) {
    return notFound();
  }

  await tRPCServerApi.quote.getPublicQuotesByEmailQuoteId.prefetch({
    emailQuoteId: data.emailquoteid
  });

  if (error) {
    return <div>Invalid search parameters</div>;
  }

  return (
    <HydrateClient>
      <Suspense fallback={<div>Loading...</div>}>
        <QuoteDetails emailQuoteId={data.emailquoteid} />
      </Suspense>
    </HydrateClient>
  );
}
