"use client";

import { log } from "@watt/common/src/utils/axiom-logger";

import { toast } from "@watt/crm/components/ui/use-toast";
import { useAuthentication } from "@watt/crm/hooks/use-authentication";
import { useIdle } from "@watt/crm/hooks/use-idle";

type IdleCheckerProps = {
  isSystemUser: boolean;
};

export function IdleChecker({ isSystemUser }: IdleCheckerProps) {
  const { handleSignOut } = useAuthentication();

  const handleIdle = async () => {
    await onLogout();
  };

  const handleIdleWarning = () => {
    toast({
      title: "You are about to be logged out",
      description:
        "You have been idle for 59 minutes and will be logged out in 60 seconds",
      variant: "destructive"
    });
  };

  const onLogout = async () => {
    log.info("app/idle-checker/onLogout: Logging out user due to inactivity");
    handleSignOut();
  };

  useIdle({
    enabled: !isSystemUser,
    onIdle: handleIdle,
    onWarning: handleIdleWarning
  });

  return null;
}
