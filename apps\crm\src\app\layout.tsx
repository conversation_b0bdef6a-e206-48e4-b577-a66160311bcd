import "@watt/crm/styles/globals.css";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Analytics } from "@watt/crm/components/analytics";
import { StyleSwitcher } from "@watt/crm/components/style-switcher";
import { TailwindIndicator } from "@watt/crm/components/tailwind-indicator";
import { ThemeProvider } from "@watt/crm/components/theme-provider";
import { Toaster } from "@watt/crm/components/ui/toaster";
import { TooltipProvider } from "@watt/crm/components/ui/tooltip";
import { siteConfig } from "@watt/crm/config/site";
import { fontSans } from "@watt/crm/lib/fonts";
import type { Metadata, Viewport } from "next";
import { AxiomWebVitals } from "next-axiom";
import { headers } from "next/headers";

import { TRPCReactProvider } from "../trpc/trpc-react-provider";
import { FavIcon } from "./fav-icon";

import NextTopLoader from "nextjs-toploader";
import { type PropsWithChildren, Suspense } from "react";
import PostHogPageView from "./posthog-page-view";
import { PHProvider } from "./providers";
import { ReactScan } from "./react-scan";
import { StagewiseToolbarWrapper } from "./stagewise-toolbar";

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" }
  ]
};

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`
  },
  description: siteConfig.description,
  keywords: ["Watt.co.uk", "CRM"],
  authors: [
    {
      name: "Watt.co.uk",
      url: "https://watt.co.uk"
    }
  ],
  creator: "Stephen Rayner",
  manifest: "/site.webmanifest",
  robots: {
    index: false,
    follow: false,
    noimageindex: true,
    noarchive: true,
    nocache: true,
    nosnippet: true
  }
};

export default async function RootLayout({ children }: PropsWithChildren) {
  const header = await headers();
  const renderPdf = header.get("x-render-pdf") === "true";
  const isDevelopment = process.env.NODE_ENV === "development";

  const stagewiseConfig = {
    plugins: []
  };

  return (
    <html lang="en" suppressHydrationWarning>
      <FavIcon />
      <AxiomWebVitals />
      <PHProvider>
        <body
          className={cn(
            "min-h-screen bg-background font-sans antialiased",
            fontSans.variable
          )}
        >
          <Suspense fallback={null}>
            <ReactScan options={{ enabled: false, log: true }} />
          </Suspense>
          <Suspense fallback={null}>
            <PostHogPageView />
          </Suspense>
          <TRPCReactProvider readonlyHeaders={header} showDevTools={!renderPdf}>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <NextTopLoader color="#92bc20" showSpinner={false} />
              <TooltipProvider delayDuration={0}>
                <div className="relative flex h-screen flex-col">
                  {children}
                </div>
              </TooltipProvider>
              {!renderPdf && <TailwindIndicator />}
            </ThemeProvider>
            <StyleSwitcher />
            <Analytics />
            <Toaster />
            {isDevelopment && (
              <StagewiseToolbarWrapper config={stagewiseConfig} />
            )}
          </TRPCReactProvider>
        </body>
      </PHProvider>
    </html>
  );
}
