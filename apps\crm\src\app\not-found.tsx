import Image from "next/image";
import Link from "next/link";

import { buttonVariants } from "@watt/crm/components/ui/button";

export default function NotFound() {
  return (
    <div className="m-16 flex h-full flex-col items-center justify-center space-y-6 text-center">
      <h1 className="font-semibold text-5xl">404 - Page not found</h1>
      <Image
        src="/img/404.png"
        alt="404 error"
        width={200}
        height={284}
        priority
      />

      <p>The page you are looking for does not exist or has been moved.</p>
      <Link className={buttonVariants()} href="/">
        Return Home
      </Link>
    </div>
  );
}

export const runtime = "edge";
