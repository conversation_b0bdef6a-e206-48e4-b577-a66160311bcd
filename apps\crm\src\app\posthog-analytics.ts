// analytics.ts
import { env } from "@watt/common/src/config/env";
import posthog from "posthog-js";

export function initPostHog() {
  if (posthog.__loaded) {
    return;
  }

  posthog.init(env.NEXT_PUBLIC_POSTHOG_KEY, {
    name: "crm",
    api_host: "/ingest",
    person_profiles: "identified_only", // or 'always' to create profiles for anonymous users as well
    capture_pageview: false, // Disable automatic pageview capture, as we capture manually
    capture_pageleave: true, // Enable pageleave capture
    loaded: posthog => {
      const consent = "yes";
      posthog.set_config({
        persistence: consent === "yes" ? "localStorage+cookie" : "memory"
      });

      // tag all events with environment
      posthog.register({ environment: env.NEXT_PUBLIC_ENVIRONMENT });
      if (env.NODE_ENV === "development") {
        posthog.debug(); // debug mode in development
      }
    }
  });
}

export function identifyUser(
  id: string,
  userProperties?: Record<string, unknown>
) {
  if (posthog.__loaded) {
    posthog.register({ environment: env.NEXT_PUBLIC_ENVIRONMENT });
    posthog.identify(id, userProperties);
  }
}

export function trackEvent(event: string, props?: Record<string, unknown>) {
  if (posthog.__loaded) {
    posthog.capture(event, props);
  }
}

export function reset() {
  if (posthog.__loaded) {
    posthog.reset();
  }
}
