"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { usePostHog } from "posthog-js/react";
import { useEffect } from "react";

/**
 * Since it uses useSearchParams, it must be a client component and its usage must be wrapped in a Suspense.
 * This is to prevent the page from being deopted to client-side rendering.
 * https://nextjs.org/docs/messages/deopted-into-client-rendering
 *
 * @returns null
 */
export default function PostHogPageView(): null {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const posthog = usePostHog();
  useEffect(() => {
    // Track pageviews
    if (pathname && posthog) {
      let url = window.origin + pathname;
      if (searchParams.toString()) {
        url = `${url}?${searchParams.toString()}`;
      }
      posthog.capture("$pageview", {
        $current_url: url
      });
    }
  }, [pathname, searchParams, posthog]);

  return null;
}
