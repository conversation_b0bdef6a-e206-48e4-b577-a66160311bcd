import { env } from "@watt/common/src/config/env";
import { PostHog } from "posthog-node";

/**
 * PostHog client for server-side analytics
 * @returns PostHog client
 * @example
 * ```ts
 * const posthog = PostHogClient();
 * posthog.capture({
 *   distinctId: '<EMAIL>',
 *   event: 'Page was loaded'
 * });
 * ```
 */
export default function postHogClient() {
  if (env.NEXT_PUBLIC_DISABLE_POSTHOG === "true") {
    return {
      capture: () => {},
      identify: () => {},
      alias: () => {},
      setPersonProperties: () => {},
      groupIdentify: () => {},
      flush: () => Promise.resolve(),
      shutdown: () => Promise.resolve()
    } as unknown as PostHog;
  }
  const client = new PostHog(env.NEXT_PUBLIC_POSTHOG_KEY, {
    host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    flushAt: 1,
    flushInterval: 0
  });

  // Wrap capture() to inject `environment` into every event
  const originalCapture = client.capture.bind(client);
  client.capture = args =>
    originalCapture({
      ...args,
      properties: {
        environment: process.env.NEXT_PUBLIC_ENVIRONMENT,
        ...(args.properties || {})
      }
    });
  return client;
}
