"use client";

import { env } from "@watt/common/src/config/env";
import posthog from "posthog-js";
import { PostHogProvider } from "posthog-js/react";
import { useEffect } from "react";
import { initPostHog } from "./posthog-analytics";

/**
 * PostHog provider, tracks pageviews and captures events by default.
 * If you need to capture a custom event, you can do so by calling posthog.capture() in a react component.
 * `posthog.capture('my event', { property: 'value' })`
 */
export function PHProvider({
  children
}: {
  children: React.ReactNode;
}) {
  useEffect(() => {
    if (env.NEXT_PUBLIC_DISABLE_POSTHOG === "true") {
      return;
    }

    initPostHog();
  }, []);

  if (env.NEXT_PUBLIC_DISABLE_POSTHOG === "true") {
    return <>{children}</>;
  }

  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
