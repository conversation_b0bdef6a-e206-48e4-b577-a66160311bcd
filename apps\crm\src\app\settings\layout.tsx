import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import type { Metadata } from "next";
import Link from "next/link";
import type { PropsWithChildren } from "react";

import { SidebarNav } from "@watt/crm/components/sidebar-nav";
import { Separator } from "@watt/crm/components/ui/separator";
import { routes } from "@watt/crm/config/routes";

export const metadata: Metadata = {
  title: "Forms",
  description: "Advanced form example using react-hook-form and Zod."
};

const sidebarNavItems = [
  {
    title: "Account",
    href: "/settings"
  }
];

export default function SettingsLayout({ children }: PropsWithChildren) {
  return (
    <div className="space-y-6 p-10 pb-16">
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Link
            href={routes.calls}
            className="rounded-md border border-transparent p-2 hover:border-gray-300"
          >
            <ArrowLeft className="h-4 w-4" />
          </Link>
          <h2 className="font-bold text-2xl tracking-tight">Settings</h2>
        </div>
        <p className="text-muted-foreground">Manage your account settings.</p>
      </div>
      <Separator className="my-6" />
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
        <aside className="-mx-4 lg:w-1/5">
          <SidebarNav items={sidebarNavItems} />
        </aside>
        <div className="flex-1 lg:max-w-2xl">{children}</div>
      </div>
    </div>
  );
}
