"use client";

import type { ToolbarConfig } from "@stagewise/toolbar-next";
import dynamic from "next/dynamic";

// Client-side-only import of StagewiseToolbar
const StagewiseToolbar = dynamic(
  () => import("@stagewise/toolbar-next").then(mod => mod.StagewiseToolbar),
  { ssr: false }
);

// Wrapper component that can be used in the Server Component layout
export function StagewiseToolbarWrapper({ config }: { config: ToolbarConfig }) {
  return <StagewiseToolbar config={config} />;
}
