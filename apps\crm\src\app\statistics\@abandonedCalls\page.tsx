import type { Metada<PERSON> } from "next";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";

import { AbandonedCallsTable } from "../components/abandoned-calls-table";

export const metadata: Metadata = {
  title: "Statistics",
  description: "Statistics"
};

export default function AbandonedCallsPage() {
  return (
    <div className="flex min-h-screen w-full flex-col gap-2">
      <div className=" mx-2 h-full gap-4 ">
        <Card className="xl:col-span-4">
          <div>
            <CardHeader className="py-2">
              <CardTitle className="text-2xl text-foreground">
                Abandoned Calls
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AbandonedCallsTable />
            </CardContent>
          </div>
        </Card>
      </div>
    </div>
  );
}
