import type { Metadata } from "next";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle
} from "@watt/crm/components/ui/card";

import { ActivityTable } from "../components/activity-table";
import { AgentActivityBarChart } from "../components/agent-activity-bar-chart";
import { KpiCards } from "../components/kpi-cards";
import { TotalTable } from "../components/total-table";

export const metadata: Metadata = {
  title: "Statistics",
  description: "Statistics"
};

export default function StatisticsPage() {
  return (
    <div className="flex min-h-screen w-full flex-col gap-2">
      <KpiCards />
      <div className="mx-2 grid h-full grid-cols-auto gap-4 lg:grid-cols-2 xl:grid-cols-11">
        <Card className="xl:col-span-3">
          <div>
            <CardHeader className="py-2">
              <CardTitle className="text-2xl text-foreground">
                Activity Chart
              </CardTitle>
            </CardHeader>
            <CardContent className="h-full">
              <AgentActivityBarChart />
            </CardContent>
          </div>
        </Card>

        <Card className="xl:col-span-4">
          <div>
            <CardHeader className="py-2">
              <CardTitle className="text-2xl text-foreground">Total</CardTitle>
            </CardHeader>
            <CardContent>
              <TotalTable />
            </CardContent>
          </div>
        </Card>

        <Card className="xl:col-span-4">
          <div>
            <CardHeader className="py-2">
              <CardTitle className="text-2xl text-foreground">
                Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ActivityTable />
            </CardContent>
          </div>
        </Card>
      </div>
    </div>
  );
}
