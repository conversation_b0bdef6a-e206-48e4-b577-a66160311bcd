"use client";

import type { AbandonCallInstance } from "@prisma/client";
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { formatDate } from "@watt/common/src/utils/format-date";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { isOfficeOpen } from "@watt/common/src/utils/office-open";
import {
  abandonCallStatuses,
  directions,
  queueResults
} from "@watt/crm/common-data/calls/data";
import { useStatisticsStore } from "@watt/crm/store/statistics";
import { trpcClient } from "@watt/crm/utils/api";
import { formatDurationMinuteSeconds } from "@watt/crm/utils/format-time-duration";
import React, { useMemo } from "react";

import { DAY_START } from "@watt/crm/app/utils/day-start";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import {
  dateFilter,
  textFilter
} from "@watt/crm/components/data-table/data-table-filter-functions";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";

export const columns: ColumnDef<AbandonCallInstance>[] = [
  {
    accessorKey: "callSid",
    header: "Call ID",
    cell: ({ row }) => (
      <div className="line-clamp-1">{row.getValue("callSid")}</div>
    ),
    filterFn: textFilter
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    cell: ({ row }) => <div>{formatDate(row.getValue("createdAt"))}</div>,
    filterFn: dateFilter
  },
  {
    accessorKey: "to",
    header: "To",
    cell: ({ row }) => <div>{row.getValue("to")}</div>
  },
  {
    accessorKey: "from",
    header: "From",
    cell: ({ row }) => (
      <div>{row.getValue<string>("from").replace("client:", "")}</div>
    )
  },
  {
    accessorKey: "caller",
    header: "Caller",
    cell: ({ row }) => (
      <div>
        {formatPhoneNumber(
          row.getValue<string | null>("caller")?.replace("client:", "")
        ) ?? "-"}
      </div>
    )
  },
  {
    accessorKey: "called",
    header: "Called Number",
    cell: ({ row }) => (
      <div>{formatPhoneNumber(row.getValue("called")) ?? "-"}</div>
    )
  },
  // {
  //   accessorKey: "from",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="From" disableSorting />,
  //   cell: ({ row }) => {
  //     const directDialE164 = useAppStore.getState().user?.profileData?.directDialE164;
  //
  //     const from = row.original.caller;
  //     const fromPhoneNumber = row.getValue("from") as string;

  //     return (
  //       <div className="flex max-w-[25ch] flex-wrap items-center gap-1 overflow-hidden text-ellipsis">
  //         <span className="whitespace-nowrap">{formatPhoneNumber(fromPhoneNumber)}</span>
  //         {(fromPhoneNumber === directDialE164 || from) && (
  //           <Badge>{fromPhoneNumber === directDialE164 ? "You" : from}</Badge>
  //         )}
  //       </div>
  //     );
  //   },
  //   filterFn: textFilter,
  // },
  // {
  //   accessorKey: "to",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="To" disableSorting />,
  //   cell: ({ row }) => {
  //     const directDialE164 = useAppStore.getState().user?.profileData?.directDialE164;
  //     const to = row.original.to;
  //     const toPhoneNumber = row.getValue("to") as string;

  //     return (
  //       <div className="flex max-w-[25ch] flex-wrap items-center gap-1 overflow-hidden text-ellipsis">
  //         <span className="whitespace-nowrap">{formatPhoneNumber(toPhoneNumber)}</span>
  //         {(toPhoneNumber === directDialE164 || to) && (
  //           <Badge>{toPhoneNumber === directDialE164 ? "You" : to}</Badge>
  //         )}
  //       </div>
  //     );
  //   },
  //   filterFn: textFilter,
  // },
  {
    accessorKey: "direction",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Direction" disableSorting />
    ),
    cell: ({ row }) => {
      const direction = directions.find(
        direction => direction.value === row.getValue("direction")
      );

      if (!direction) {
        return null;
      }

      return (
        <div className="flex items-center">
          {direction.icon && (
            <direction.icon className="mr-2 h-4 w-4 text-muted-foreground" />
          )}
          <span>{direction.label}</span>
        </div>
      );
    },
    filterFn: textFilter
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" disableSorting />
    ),
    cell: ({ row }) => {
      const status = abandonCallStatuses.find(
        status => status.value === row.getValue("status")
      );

      if (!status) {
        return null;
      }

      return (
        <div className="flex items-center">
          {status.icon && (
            <status.icon className="mr-2 h-4 w-4 text-muted-foreground" />
          )}
          <span>{status.label}</span>
        </div>
      );
    },
    filterFn: textFilter
  },

  {
    accessorKey: "queueResult",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Queue Result"
        disableSorting
      />
    ),
    cell: ({ row }) => {
      const queueResult = queueResults.find(
        status => status.value === row.getValue("queueResult")
      );

      if (!queueResult) {
        return null;
      }

      return (
        <div className="flex items-center">
          {queueResult.icon && (
            <queueResult.icon className="mr-2 h-4 w-4 text-muted-foreground" />
          )}
          <span>{queueResult.label}</span>
        </div>
      );
    },
    filterFn: textFilter
  },
  {
    accessorKey: "queueTime",
    header: "Queue Time",
    cell: ({ row }) => {
      const queueTime = row.original.queueTime;
      if (!queueTime) {
        return null;
      }

      return (
        <div>{formatDurationMinuteSeconds(Number.parseInt(queueTime))} sec</div>
      );
    },
    filterFn: textFilter
  }
];

export function AbandonedCallsTable() {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({
      callSid: false
    });
  const [rowSelection, setRowSelection] = React.useState({});

  const shouldRefetch = isOfficeOpen();

  const { statisticsDateFilter } = useStatisticsStore(state => ({
    statisticsDateFilter: state.statisticsDateFilter
  }));

  const abandonedCalls = trpcClient.activity.getAbandonedCalls.useQuery(
    {
      createdAfter:
        statisticsDateFilter?.toDateString() ?? DAY_START.toDateString()
    },
    {
      placeholderData: prev => prev,
      refetchInterval: shouldRefetch ? 10_000 : false // 10 seconds or no refetch
    }
  );

  const callData = useMemo(
    () => abandonedCalls.data ?? [],
    [abandonedCalls.data]
  );

  const table = useReactTable({
    data: callData,
    columns,
    enableRowSelection: false,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection
    },
    initialState: {
      pagination: {
        pageSize: 100
      }
    }
  });

  if (!abandonedCalls.data) {
    return <>Loading...</>;
  }

  return (
    <div className="h-full w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
