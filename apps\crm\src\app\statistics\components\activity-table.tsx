"use client";

import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { AgentCurrentActivity } from "@watt/api/src/router/activity";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { isOfficeOpen } from "@watt/common/src/utils/office-open";
import { useStatisticsStore } from "@watt/crm/store/statistics";
import { trpcClient } from "@watt/crm/utils/api";
import { formatDurationHourMinuteSeconds } from "@watt/crm/utils/format-time-duration";
import type { AgentStatus } from "@watt/db/src/enums";
import React, { useMemo } from "react";

import { DAY_START } from "@watt/crm/app/utils/day-start";
import { Badge } from "@watt/crm/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";

export const columns: ColumnDef<AgentCurrentActivity>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div className="line-clamp-1 capitalize">{row.getValue("name")}</div>
    )
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <div className="capitalize">
        <Badge
          className={cn(row.getValue("status") === "IDLE" && "bg-red-400")}
        >
          {row.getValue("status")}
        </Badge>
      </div>
    )
  },
  {
    accessorKey: "duration",
    header: "Duration",
    cell: ({ row }) => (
      <div className="capitalize">
        {formatDurationHourMinuteSeconds(row.getValue("duration"))}
      </div>
    )
  },
  {
    accessorKey: "phone",
    header: "Phone",
    cell: ({ row }) => (
      <div className="capitalize">
        {formatPhoneNumber(row.getValue("phone")) ?? "-"}
      </div>
    )
  }
];

const statusOrder = {
  OUTGOING: 1,
  INCOMING: 2,
  RINGING: 3,
  ON_HOLD: 4,
  IDLE: 5,
  OFFLINE: 6
} as const;

export function ActivityTable() {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const shouldRefetch = isOfficeOpen();

  const { statisticsDateFilter } = useStatisticsStore(state => ({
    statisticsDateFilter: state.statisticsDateFilter
  }));

  const currentActivities =
    trpcClient.activity.getAgentsCurrentActivity.useQuery(
      {
        createdAfter:
          statisticsDateFilter?.toDateString() ?? DAY_START.toDateString()
      },
      {
        placeholderData: prev => prev,
        refetchInterval: shouldRefetch ? 10_000 : false // 10 seconds or no refetch
      }
    );

  // const secondsElapsed = useIntervalCounter(currentActivities.data, 1000);

  const activityData = useMemo(
    () =>
      currentActivities.data
        ?.map(
          (activity): AgentCurrentActivity => ({
            ...activity,
            duration: activity.duration
          })
        )
        .sort((a, b) => {
          return (
            statusOrder[a.status as AgentStatus] -
            statusOrder[b.status as AgentStatus]
          );
        }) ?? [],
    [currentActivities.data]
  );

  const table = useReactTable({
    data: activityData,
    columns,
    enableRowSelection: false,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection
    },
    initialState: {
      pagination: {
        pageSize: 100
      }
    }
  });

  if (!currentActivities.data) {
    return <>Loading...</>;
  }

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  key={row.id}
                  className={cn(
                    getColorForStatus(row.getValue("status"), rowIndex)
                  )}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

function getColorForStatus(status: AgentStatus, rowIndex: number) {
  switch (status) {
    case "IDLE":
      return "";
    case "ON_HOLD":
      return rowIndex % 2 === 0 ? "bg-gray-400" : "bg-gray-300";
    case "RINGING":
      return rowIndex % 2 === 0 ? "bg-red-400" : "bg-red-300";
    case "OUTGOING":
      return rowIndex % 2 === 0 ? "bg-yellow-400" : "bg-yellow-300";
    case "INCOMING":
      return rowIndex % 2 === 0 ? "bg-green-400" : "bg-green-300";
  }
}
