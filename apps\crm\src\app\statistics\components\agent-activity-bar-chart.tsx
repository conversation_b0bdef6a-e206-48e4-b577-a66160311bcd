"use client";

import { isOfficeOpen } from "@watt/common/src/utils/office-open";
import { useStatisticsStore } from "@watt/crm/store/statistics";
import { trpcClient } from "@watt/crm/utils/api";
import { useMemo } from "react";
import {
  Bar,
  Bar<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from "recharts";

import { DAY_START } from "@watt/crm/app/utils/day-start";

export function AgentActivityBarChart() {
  const shouldRefetch = isOfficeOpen();

  const { statisticsDateFilter } = useStatisticsStore(state => ({
    statisticsDateFilter: state.statisticsDateFilter
  }));

  const totalActivity = trpcClient.activity.getAgentsTotalActivity.useQuery(
    {
      createdAfter:
        statisticsDateFilter?.toDateString() ?? DAY_START.toDateString()
    },
    {
      placeholderData: prev => prev,
      refetchInterval: shouldRefetch ? 10_000 : false // 10 seconds or no refetch
    }
  );

  const filteredData = useMemo(() => {
    return totalActivity.data
      ?.filter(
        agent =>
          agent.outgoing !== 0 || agent.answered !== 0 || agent.abandoned !== 0
      )
      .sort((a, b) => b.outgoing - a.outgoing);
  }, [totalActivity.data]);

  if (filteredData?.length === 0) {
    return (
      <div className="flex h-full flex-col items-center justify-center">
        <div className="text-4xl text-gray-400">No data</div>
        <div className="text-gray-400 text-sm">None worth showing at least</div>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" aspect={1.1}>
      <BarChart layout="vertical" data={filteredData}>
        <XAxis type="number" />
        <YAxis type="category" dataKey="name" stroke="#888888" fontSize={12} />
        <Tooltip />
        <Bar
          dataKey="answered"
          fill="rgb(74, 222, 128)"
          name="Answered Calls"
        />
        <Bar
          dataKey="outgoing"
          fill="rgb(96, 165, 250)"
          name="Outgoing Calls"
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
