"use client";

import { RELEASE_DATE_OF_NEW_ABANDON_CALL } from "@watt/common/src/config/new-abandon-call-release-date";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { isOfficeOpen } from "@watt/common/src/utils/office-open";
import { useStatisticsStore } from "@watt/crm/store/statistics";
import { trpcClient } from "@watt/crm/utils/api";
import { FileBarChart2 } from "lucide-react";
import { useMemo } from "react";

import { DAY_START } from "@watt/crm/app/utils/day-start";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";

import { cardsData } from "../config";

export function KpiCards() {
  const shouldRefetch = isOfficeOpen();

  const { statisticsDateFilter } = useStatisticsStore(state => ({
    statisticsDateFilter: state.statisticsDateFilter
  }));

  const kpiData = trpcClient.activity.getKPISummary.useQuery(
    {
      createdAfter:
        statisticsDateFilter?.toDateString() ?? DAY_START.toDateString()
    },
    {
      placeholderData: prev => prev,
      refetchInterval: shouldRefetch ? 10_000 : false // 10 seconds or no refetch
    }
  );

  const {
    incomingCalls,
    outgoingCalls,
    abandonedCalls,
    unansweredCalls,
    queueDropoutCalls,
    missedCalls,
    redirectedToQueueCalls
  } = kpiData.data ?? {};
  const cardValues: Record<string, string | number | undefined> = {
    incomingCalls,
    outgoingCalls,
    abandonedCalls,
    unansweredCalls,
    queueDropoutCalls,
    missedCalls,
    redirectedToQueueCalls,
    serviceLevel: "100"
  };

  const filteredCardsData = useMemo(() => {
    const isBeforeNewAbandonCallReleaseDate =
      statisticsDateFilter &&
      statisticsDateFilter < RELEASE_DATE_OF_NEW_ABANDON_CALL;

    const visibleCards = isBeforeNewAbandonCallReleaseDate
      ? ["incomingCalls", "outgoingCalls", "abandonedCalls"]
      : ([
          "incomingCalls",
          "outgoingCalls",
          "unansweredCalls",
          "queueDropoutCalls",
          "missedCalls",
          "redirectedToQueueCalls"
        ] as const);

    // biome-ignore lint/suspicious/noExplicitAny: <fix later>
    return cardsData.filter(card => visibleCards.includes(card.type as any));
  }, [statisticsDateFilter]);

  return (
    <div className="mx-auto grid max-w-fit grid-cols-auto gap-4 px-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {filteredCardsData.map(({ style, title, description, type }) => (
        <Tooltip key={title}>
          <TooltipTrigger asChild>
            <Card className={cn(style, "w-full hover:cursor-pointer")}>
              <div className="space-between flex h-full flex-col items-center">
                <CardHeader className="flex-1 items-center p-4">
                  <CardTitle className="font-medium text-white text-xl">
                    {title}
                  </CardTitle>
                  <span className="text-white text-xs">{description}</span>
                </CardHeader>
                <CardContent className="pb-4">
                  <span className="font-bold text-5xl text-white">
                    {cardValues[type] ?? "..."}
                  </span>
                </CardContent>
              </div>
            </Card>
          </TooltipTrigger>
          <TooltipContent className="flex flex-row items-center gap-2">
            {description} <FileBarChart2 className="h-4 w-4" />
          </TooltipContent>
        </Tooltip>
      ))}
    </div>
  );
}
