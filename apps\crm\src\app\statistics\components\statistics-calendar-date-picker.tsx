"use client";

import { useStatisticsStore } from "@watt/crm/store/statistics";
import dynamic from "next/dynamic";

const CalendarDatePicker = dynamic(() =>
  import("@watt/crm/components/date-picker").then(mod => ({
    default: mod.CalendarDatePicker
  }))
);

export function StatisticsCalendarDatePicker() {
  const { statisticsDateFilter, setStatisticsDateFilter } = useStatisticsStore(
    state => ({
      statisticsDateFilter: state.statisticsDateFilter,
      setStatisticsDateFilter: state.setStatisticsDateFilter
    })
  );

  return (
    <>
      <CalendarDatePicker
        setDate={setStatisticsDateFilter}
        date={statisticsDateFilter}
      />
    </>
  );
}
