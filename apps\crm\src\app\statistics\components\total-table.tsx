"use client";

import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { AgentTotalActivity } from "@watt/api/src/router/activity";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { isOfficeOpen } from "@watt/common/src/utils/office-open";
import { useStatisticsStore } from "@watt/crm/store/statistics";
import { trpcClient } from "@watt/crm/utils/api";
import { formatDurationHourMinuteSeconds } from "@watt/crm/utils/format-time-duration";
import React from "react";

import { DAY_START } from "@watt/crm/app/utils/day-start";
import { Badge } from "@watt/crm/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";

export const columns: ColumnDef<AgentTotalActivity>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div className="line-clamp-1 capitalize">{row.getValue("name")}</div>
    )
  },
  {
    accessorKey: "answered",
    header: "Answered",
    cell: ({ row }) => (
      <div className="capitalize">
        <Badge className="bg-green-400">{row.getValue("answered")}</Badge>
      </div>
    )
  },
  {
    accessorKey: "outgoing",
    header: "Outgoing",
    cell: ({ row }) => (
      <div className="capitalize">
        <Badge className="bg-blue-400">{row.getValue("outgoing")}</Badge>
      </div>
    )
  },
  {
    accessorKey: "duration",
    header: "Duration",
    cell: ({ row }) => (
      <div className="capitalize">
        {formatDurationHourMinuteSeconds(row.getValue("duration"))}
      </div>
    )
  }
];

export function TotalTable() {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const shouldRefetch = isOfficeOpen();

  const { statisticsDateFilter } = useStatisticsStore(state => ({
    statisticsDateFilter: state.statisticsDateFilter
  }));

  const totalActivity = trpcClient.activity.getAgentsTotalActivity.useQuery(
    {
      createdAfter:
        statisticsDateFilter?.toDateString() ?? DAY_START.toDateString()
    },
    {
      placeholderData: prev => prev,
      refetchInterval: shouldRefetch ? 10_000 : false // 10 seconds or no refetch
    }
  );

  const table = useReactTable({
    data: totalActivity.data || [],
    columns,
    enableRowSelection: false,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection
    },
    initialState: {
      pagination: {
        pageSize: 100
      }
    }
  });

  if (!totalActivity.data) {
    return <>Loading...</>;
  }

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  className={cn(
                    "transition-all duration-300 hover:bg-muted-foreground/30",
                    rowIndex % 2 === 0 && "bg-muted"
                  )}
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
