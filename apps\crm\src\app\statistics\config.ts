type CardsData = {
  type:
    | "incomingCalls"
    | "outgoingCalls"
    | "abandonedCalls"
    | "serviceLevel"
    | "unansweredCalls"
    | "queueDropoutCalls"
    | "missedCalls"
    | "redirectedToQueueCalls";
  title: string;
  description: string;
  style: string;
};

export const cardsData: CardsData[] = [
  {
    type: "incomingCalls",
    title: "Incoming Calls",
    description: "All inbound calls",
    style: "bg-green-400"
  },
  {
    type: "outgoingCalls",
    title: "Outgoing Calls",
    description: "All outbound calls",
    style: "bg-[#0586CC]"
  },
  {
    type: "queueDropoutCalls",
    title: "Queue Dropout Calls",
    description: "Customer hungup whilst in queue",
    style: "bg-[#D13125]"
  },
  {
    type: "abandonedCalls",
    title: "Abandoned Calls",
    description: "Customer hungup before agent pickup",
    style: "bg-[#D13125]"
  },
  {
    type: "unansweredCalls",
    title: "Unanswered Calls",
    description: "Customer didn't pickup",
    style: "bg-[#13BFB2]"
  },
  {
    type: "missedCalls",
    title: "Missed Calls",
    description: "Agent missed direct call",
    style: "bg-[#737373]"
  },
  {
    type: "redirectedToQueueCalls",
    title: "Redirected Direct Calls ",
    description: "Redirected to the queue",
    style: "bg-[#FF914D]"
  }
];
