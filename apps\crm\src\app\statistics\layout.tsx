import type { PropsWithChildren, ReactNode } from "react";

import { Separator } from "@watt/crm/components/ui/separator";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";

import RefreshBanner from "./components/refresh-banner";
import { StatisticsCalendarDatePicker } from "./components/statistics-calendar-date-picker";

type LayoutProps = PropsWithChildren<{
  overview: ReactNode;
  abandonedCalls: ReactNode;
}>;

export default function Layout({ overview, abandonedCalls }: LayoutProps) {
  const tabs = [
    {
      type: "overview",
      title: "Overview",
      children: overview
    },
    {
      type: "queueDropoutCalls",
      title: "Queue Dropout Calls",
      children: abandonedCalls
    }
  ];

  return (
    <Tabs defaultValue="overview" className="gap-2">
      <div>
        <RefreshBanner />
      </div>
      <div className="flex flex-row items-center justify-between gap-2 p-2">
        <h1 className="font-bold text-xl">Statistics</h1>
        <div className="flex flex-row gap-2">
          <TabsList>
            {tabs.map(({ title, type }) => (
              <TabsTrigger key={title} value={type} className="flex gap-1">
                {title}
              </TabsTrigger>
            ))}
          </TabsList>
          <div>
            <StatisticsCalendarDatePicker />
          </div>
        </div>
      </div>
      <Separator />
      {tabs.map(({ title, type, children }) => (
        <TabsContent key={title} value={type}>
          {children}
        </TabsContent>
      ))}
    </Tabs>
  );
}
