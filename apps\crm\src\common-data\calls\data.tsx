import {
  <PERSON><PERSON><PERSON>riangle,
  ArrowLeftCircle,
  ArrowRightCircle,
  CalendarX,
  CassetteTape,
  CheckCircle,
  CheckCircle2,
  CircleDotDashed,
  CircleOff,
  Frown,
  Phone,
  PhoneCall,
  PhoneIncoming,
  PhoneMissed,
  PhoneOff,
  PhoneOutgoing,
  Repeat,
  Server,
  UserX,
  Users,
  VolumeX,
  XCircle
} from "lucide-react";

export const statuses = [
  {
    value: "COMPLETED",
    label: "Completed",
    icon: CheckCircle2
  },
  {
    value: "RINGING",
    label: "Ringing",
    icon: PhoneCall
  },
  {
    value: "IN_PROGRESS",
    label: "In Progress",
    icon: CircleDotDashed
  },
  {
    value: "NO_ANSWER",
    label: "No Answer",
    icon: XCircle
  },
  {
    value: "BUSY",
    label: "Busy",
    icon: CalendarX
  },
  {
    value: "FAILED",
    label: "Failed",
    icon: CircleOff
  },
  {
    value: "CANCELED",
    label: "Canceled",
    icon: XCircle
  }
];

export const durations = [
  {
    value: "0-5",
    min: 0,
    max: 5,
    label: "0 - 5 sec"
  },
  {
    value: "5-10",
    min: 5,
    max: 10,
    label: "5 - 10 sec"
  },
  {
    value: "10-20",
    min: 10,
    max: 20,
    label: "10 - 20 sec"
  },
  {
    value: "20-60",
    min: 20,
    max: 60,
    label: "20 - 60 sec"
  },
  {
    value: "60-120",
    min: 60,
    max: 120,
    label: "1 - 2 min"
  },
  {
    value: "120-300",
    min: 120,
    max: 300,
    label: "2 - 5 min"
  },
  {
    value: "300-600",
    min: 300,
    max: 600,
    label: "5 - 10 min"
  },
  {
    value: "600-900",
    min: 600,
    max: 900,
    label: "10 - 15 min"
  },
  {
    value: "900-1800",
    min: 900,
    max: 1800,
    label: "15 - 30 min"
  },
  {
    value: "1800-3600",
    min: 1800,
    max: 3600,
    label: "30 - 60 min"
  },
  {
    value: "3600+",
    min: 3600,
    max: Number.POSITIVE_INFINITY,
    label: "60+ min"
  }
];

export const directions = [
  {
    value: "outbound-dial",
    label: "Out",
    icon: PhoneOutgoing
  },
  {
    value: "outbound-api",
    label: "Out Transfer",
    icon: PhoneOutgoing
  },
  {
    value: "inbound-api",
    label: "In Transfer",
    icon: PhoneIncoming
  },
  {
    value: "inbound",
    label: "In",
    icon: PhoneIncoming
  }
];

export const sources = [
  {
    label: "Record",
    value: "RecordVerb",
    icon: CassetteTape
  },
  {
    label: "Conference",
    value: "Conference",
    icon: Users
  },
  {
    label: "Conference",
    value: "OutboundAPI",
    icon: Users
  },
  {
    label: "Trunking",
    value: "Trunking",
    icon: Users
  },
  {
    label: "Automatic",
    value: "DialVerb",
    icon: Phone
  },
  {
    label: "Manual",
    value: "StartCallRecordingAPI",
    icon: CassetteTape
  },
  {
    label: "Manual Conference",
    value: "StartConferenceRecordingAPI",
    icon: CassetteTape
  }
];

export const queueResults = [
  {
    value: "bridged",
    label: "Bridged",
    icon: CheckCircle // Replace with actual icon component
  },
  {
    value: "bridging-in-process",
    label: "Bridging in Process",
    icon: PhoneCall // Replace with actual icon component
  },
  {
    value: "error",
    label: "Error",
    icon: AlertTriangle // Replace with actual icon component
  },
  {
    value: "hangup",
    label: "Hangup",
    icon: PhoneOff // Replace with actual icon component
  },
  {
    value: "leave",
    label: "Leave",
    icon: ArrowRightCircle // Replace with actual icon component
  },
  {
    value: "redirected",
    label: "Redirected",
    icon: ArrowLeftCircle // Replace with actual icon component
  },
  {
    value: "redirected-from-bridged",
    label: "Redirected from Bridged",
    icon: VolumeX // Replace with actual icon component
  },
  {
    value: "queue-full",
    label: "Queue Full",
    icon: Server // Replace with actual icon component
  },
  {
    value: "system-error",
    label: "System Error",
    icon: Frown // Replace with actual icon component
  }
];

export const abandonCallStatuses = [
  {
    value: "UNANSWERED",
    label: "Unanswered",
    icon: PhoneMissed // Represents a missed call
  },
  {
    value: "QUEUE_DROPOUT",
    label: "Queue Dropout",
    icon: UserX // Suggests a user dropping out or leaving
  },
  {
    value: "MISSED_CALL",
    label: "Missed Call",
    icon: PhoneOff // Indicates a call that was not answered
  },
  {
    value: "REDIRECTED_TO_QUEUE",
    label: "Redirected to Queue",
    icon: Repeat // Symbolizes redirection or looping back
  }
];
