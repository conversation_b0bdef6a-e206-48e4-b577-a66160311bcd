import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { randomColourByString } from "@watt/common/src/utils/random-colour-by-string";
import { generateAvatarFallback } from "@watt/crm/utils/generate-avatar-fallback";
import { Avatar, AvatarFallback } from "../ui/avatar";

type CompanyAvatarProps = {
  displayName?: string;
  className?: string;
};

export function CompanyAvatar({ displayName, className }: CompanyAvatarProps) {
  const initials = generateAvatarFallback(displayName);
  const { hslString, isDark } = randomColourByString(initials);

  return (
    <Avatar className={cn("size-7", className)}>
      <AvatarFallback
        className={cn(isDark ? "text-white" : "text-black")}
        style={{
          backgroundColor: `hsl(${hslString})`
        }}
      >
        {initials}
      </AvatarFallback>
    </Avatar>
  );
}
