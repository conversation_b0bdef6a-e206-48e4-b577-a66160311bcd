import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { randomColourByString } from "@watt/common/src/utils/random-colour-by-string";
import { generateAvatarFallback } from "@watt/crm/utils/generate-avatar-fallback";
import { Avatar, AvatarFallback } from "../ui/avatar";

type UserAvatarProps = {
  fullName?: string;
  className?: string;
  fallbackClassName?: string;
};

export function UserAvatar({
  fullName,
  className,
  fallbackClassName
}: UserAvatarProps) {
  const initials = generateAvatarFallback(fullName);
  const { hslString, isDark } = randomColourByString(initials);

  return (
    <Avatar className={cn("size-7", className)}>
      <AvatarFallback
        className={cn(fallbackClassName, isDark ? "text-white" : "text-black")}
        style={{
          backgroundColor: `hsl(${hslString})`
        }}
      >
        {initials}
      </AvatarFallback>
    </Avatar>
  );
}
