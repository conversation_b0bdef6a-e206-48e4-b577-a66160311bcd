"use client";

import { Loader2 } from "lucide-react";

import { TRPCClientError } from "@trpc/client";
import type { Address_Find_Many } from "@watt/api/src/router";
import { trpcClient } from "@watt/crm/utils/api";
import { useCallback, useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { log } from "@watt/common/src/utils/axiom-logger";
import { AddressInput } from "@watt/crm/components/input/address-input";
import { Button } from "@watt/crm/components/ui/button";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAddressSearch } from "@watt/crm/hooks/use-address-search";

export type BillingEntityAddress = {
  id: string;
  postcode: string;
  displayName: string | null;
};

export type BillingAddressFormProps = {
  modalOpen: boolean;
  setModalOpen: (open: boolean) => void;
  onSubmit?: (
    newBillingAddressId: string,
    newBillingEntityAddress: BillingEntityAddress
  ) => void;
  companyId: string;
  siteId: string;
  billingAddressId: string | null;
  billingEntityAddress: BillingEntityAddress | undefined;
};

type AddressFormState = {
  addressSearch: string;
  selectedAddressId: string;
};

export function BillingAddressForm({
  modalOpen,
  setModalOpen,
  siteId,
  companyId,
  billingAddressId,
  billingEntityAddress,
  onSubmit
}: BillingAddressFormProps) {
  const [formState, setFormState] = useState<AddressFormState>({
    addressSearch: "",
    selectedAddressId: ""
  });
  const [addressData, setAddressData] = useState<
    Address_Find_Many | undefined
  >();
  const { fetchAddress, isFetchingAddress } = useAddressSearch();
  const billingAddressMutation =
    trpcClient.billingAddress.upsertBillingAddress.useMutation();

  const selectedAddress = useMemo(() => {
    if (addressData) {
      return addressData.find(
        address => address?.id === formState.selectedAddressId
      );
    }
    return billingEntityAddress && billingEntityAddress;
  }, [formState.selectedAddressId, addressData, billingEntityAddress]);

  const handleAddressSearchInputChange = (addressSearch: string) => {
    setFormState(prev => ({ ...prev, addressSearch, selectedAddressId: "" }));
  };

  const onAddressSubmit = (id: string) => {
    setFormState({ addressSearch: id, selectedAddressId: id });
  };

  const handleSubmit = async () => {
    if (!formState.selectedAddressId) {
      toast({
        title: "Unable to save billing address",
        description: "Please select an address",
        variant: "destructive",
        duration: 10000
      });
      return;
    }
    try {
      const payload = {
        companyId: companyId,
        companySiteId: siteId,
        entityAddressId: formState.selectedAddressId,
        id: billingAddressId
      };
      const newBillingAddress =
        await billingAddressMutation.mutateAsync(payload);
      toast({
        title: "Billing address updated",
        description: "Billing address has been updated successfully.",
        variant: "success"
      });
      fullResetAddressFormData();
      setModalOpen(false);
      onSubmit?.(newBillingAddress.id, newBillingAddress.entityAddress);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while adding the new billing address. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to save billing address",
        description,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  const handleAddressSelect = useCallback(
    (addressId: string) => {
      const selectedAddressObj = addressData?.find(
        address => address?.id === addressId
      );
      if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
        toast({
          title: "Invalid address",
          description:
            "Selected address does not have a valid address or postcode",
          variant: "destructive"
        });
        return;
      }
      setFormState(prev => ({ ...prev, selectedAddressId: addressId }));
    },
    [addressData]
  );

  const fullResetAddressFormData = () => {
    setFormState({ addressSearch: "", selectedAddressId: "" });
    setAddressData(undefined);
  };

  useDebounce(
    async function lookupAddressOnSearchInput() {
      try {
        // Only run if this modal is open
        if (!modalOpen) {
          return;
        }

        if (!formState.addressSearch) {
          fullResetAddressFormData();
          return;
        }

        const result = await fetchAddress(formState.addressSearch);
        if (result) {
          setAddressData(result);

          if (formState.selectedAddressId) {
            const selectedAddressObj = result?.find(
              address => address.id === formState.selectedAddressId
            );

            if (
              !selectedAddressObj?.displayName ||
              !selectedAddressObj.postcode
            ) {
              fullResetAddressFormData();

              toast({
                title: "Invalid address",
                description:
                  "Selected address does not have a valid address or postcode",
                variant: "destructive"
              });
              return;
            }
          }
        }
      } catch (e) {
        const error = e as Error;
        const description =
          error instanceof TRPCClientError && !!error.data.zodError
            ? "Error fetching address. Please check the input and try again."
            : error.message;
        toast({
          title: "Unable to get address",
          description,
          variant: "destructive"
        });
      }
    },
    1000,
    [formState.addressSearch, modalOpen]
  );

  const onDialogClose = () => {
    fullResetAddressFormData();
    setModalOpen(false);
  };

  return (
    <DialogContent
      onDialogClose={onDialogClose}
      onPointerDownOutside={e => e.preventDefault()}
    >
      <DialogHeader className="space-y-4">
        <DialogTitle>
          {billingEntityAddress
            ? "Update Billing Address"
            : "Add Billing Address"}
        </DialogTitle>
        <DialogDescription className="!mb-4 italic">
          Search by postcode or add new address manually
        </DialogDescription>
      </DialogHeader>
      <AddressInput
        value={billingEntityAddress?.id || ""}
        isLoading={isFetchingAddress}
        addressData={addressData}
        selectedAddress={selectedAddress as Address_Find_Many[0]}
        handleAddressSelect={handleAddressSelect}
        handleAddressSearchInputChange={handleAddressSearchInputChange}
        onSubmit={onAddressSubmit}
        addressSearchInput={formState.addressSearch}
        hideMetersInfo
      />
      <Button
        type="submit"
        variant="secondary"
        className="button-click-animation mt-2 mb-4 w-full"
        disabled={
          billingAddressMutation.isPending || !formState.selectedAddressId
        }
        onClick={handleSubmit}
      >
        {billingAddressMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        {billingEntityAddress ? "Update" : "Confirm"}
      </Button>
    </DialogContent>
  );
}
