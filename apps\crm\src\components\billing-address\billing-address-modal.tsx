"use client";

import { Pencil } from "lucide-react";

import { useState } from "react";

import {
  BillingAddressForm,
  type BillingAddressFormProps,
  type BillingEntityAddress
} from "@watt/crm/components/billing-address/billing-address-form";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import { Dialog, DialogTrigger } from "@watt/crm/components/ui/dialog";

type BillingAddressModalProps = Omit<
  BillingAddressFormProps,
  "modalOpen" | "setModalOpen"
> & {
  onSubmit?: (
    newBillingAddressId: string,
    newBillingEntityAddress: BillingEntityAddress
  ) => void;
  children?: React.ReactNode;
};

export function BillingAddressModal(props: BillingAddressModalProps) {
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <Dialog onOpenChange={setModalOpen} open={modalOpen}>
      <DialogTrigger asChild>
        {props.children || (
          <Button
            variant="ghost"
            size="sm"
            className="!ml-4 !mt-0 invisible text-muted-foreground/70 group-hover:visible"
          >
            <Pencil className="size-3.5" />
            <span className="sr-only fixed">Edit</span>
          </Button>
        )}
      </DialogTrigger>
      <BillingAddressForm
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onSubmit={props.onSubmit}
        {...props}
      />
    </Dialog>
  );
}
