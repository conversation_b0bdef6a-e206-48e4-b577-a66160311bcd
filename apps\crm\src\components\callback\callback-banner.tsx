import { CallbackStatus } from "@prisma/client";
import type {
  CallbackCustomStatusFilterOptionKey,
  CallbackWithUserFlagOutput
} from "@watt/api/src/types/callback";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { isDateOverdue } from "@watt/common/src/utils/format-date";
import { routes } from "@watt/crm/config/routes";
import { isBefore, isSameDay, startOfDay } from "date-fns";
import Link from "next/link";

export type ScheduledCallbackStatus = {
  hasCallbackOnAnotherSite: boolean;
  activeCount: number;
  overdueCount: number;
};

type CallbackBannerProps = {
  callback:
    | Pick<
        CallbackWithUserFlagOutput,
        "callbackTime" | "createdBy" | "status" | "isCreatedByCurrentUser"
      >
    | undefined;
  scheduledCallbackStatus: ScheduledCallbackStatus;
};

const getMessage = (
  callback: Pick<
    CallbackWithUserFlagOutput,
    "callbackTime" | "createdBy" | "status" | "isCreatedByCurrentUser"
  >,
  scheduledCallbackStatus: ScheduledCallbackStatus
) => {
  const {
    createdBy: { fullName },
    callbackTime
  } = callback;
  const { hasCallbackOnAnotherSite, activeCount, overdueCount } =
    scheduledCallbackStatus;

  const formattedCallbackTime = callbackTime
    ? formatDate(callbackTime, dateFormats.HH_mm_dd_MM_YYYY)
    : undefined;

  if (hasCallbackOnAnotherSite) {
    const totalCount = activeCount + overdueCount;

    if (totalCount === 1) {
      const statusMessage =
        activeCount > 0
          ? "active callback scheduled"
          : "overdue callback awaiting completion";

      return `This lead has an ${statusMessage} on another site by ${fullName}.`;
    }

    if (activeCount > 0 && overdueCount === 0) {
      return `This lead has ${activeCount} active callbacks scheduled on other sites by ${fullName}.`;
    }

    if (overdueCount > 0 && activeCount === 0) {
      return `This lead has ${overdueCount} overdue callbacks awaiting completion on other sites by ${fullName}.`;
    }

    if (activeCount > 0 && overdueCount > 0) {
      return `This lead has ${activeCount} active and ${overdueCount} overdue callbacks scheduled on other sites.`;
    }
  }

  const isCallbackOverdue = isDateOverdue(callbackTime);
  return isCallbackOverdue
    ? `This lead has an overdue callback scheduled on this site at ${formattedCallbackTime} by ${fullName}.`
    : `This lead has an active callback scheduled on this site at ${formattedCallbackTime} by ${fullName}.`;
};

export function CallbackBanner({
  callback,
  scheduledCallbackStatus
}: CallbackBannerProps) {
  if (!callback || callback.status !== CallbackStatus.SCHEDULED) {
    return null;
  }

  const getCallbackPageUrl = (): string => {
    let filterOption: CallbackCustomStatusFilterOptionKey;

    const { callbackTime } = callback;
    const now = new Date();

    if (isBefore(callbackTime, startOfDay(now))) {
      filterOption = "Overdue";
    } else if (isSameDay(callbackTime, now)) {
      filterOption = "DueToday";
    } else {
      filterOption = "Future";
    }

    return `${routes.callbacks}?filter=${filterOption}`;
  };

  const message = getMessage(callback, scheduledCallbackStatus);

  const baseClassNames =
    "-mb-4 -mx-4 flex items-center justify-center bg-red-500 py-2 text-sm text-white shadow-md";

  if (callback.isCreatedByCurrentUser) {
    return (
      <Link href={getCallbackPageUrl()} className={baseClassNames}>
        <span>{message}</span>
      </Link>
    );
  }
  return (
    <div className={cn(baseClassNames, "cursor-not-allowed")}>
      <span>{message}</span>
    </div>
  );
}
