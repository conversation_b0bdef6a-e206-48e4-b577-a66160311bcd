import { capitalize } from "lodash";
import { AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";

type CallbackDeleteAlertProps = {
  count: number;
  companyName: string | null;
  createdByUser: string | null;
};

export function CallbackDeleteAlert({
  count,
  companyName,
  createdByUser
}: CallbackDeleteAlertProps) {
  if (!count || !companyName || !createdByUser) {
    return null;
  }

  return (
    <Alert
      variant="warn-secondary"
      className="rounded-none border-0 border-yellow-500 border-l-4 text-black"
    >
      <AlertTitle className="flex items-center gap-2">
        <AlertTriangle className="size-4 stroke-black" /> Heads up!
      </AlertTitle>
      <AlertDescription className="pl-0">
        <div className="mt-2 pl-0">
          <p>
            This will delete <span className="font-bold">{count}</span>{" "}
            {count <= 1 ? "callback" : "callbacks"} for the company:{" "}
            <span className="font-bold">{capitalize(companyName)}</span> created
            by the user: <span className="font-bold">{createdByUser}</span>
          </p>
        </div>
      </AlertDescription>
    </Alert>
  );
}
