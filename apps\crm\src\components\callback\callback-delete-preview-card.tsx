import type { ScheduledCallback } from "@watt/api/src/types/callback";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { format } from "date-fns";
import { Badge } from "../ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Textarea } from "../ui/textarea";

export function CallbackDeletePreviewCard({
  subject,
  companyContact,
  callbackTime,
  comment,
  siteRefId
}: ScheduledCallback) {
  return (
    <Card>
      <CardHeader className="py-4">
        <CardTitle className="flex justify-end">
          <Badge
            variant="accent"
            className="border-transparent bg-muted-foreground text-background hover:bg-muted-foreground/80 hover:text-background"
          >
            {composeSiteRef(siteRefId)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <p>
          <span className="text-muted-foreground">Subject: </span>
          {subject}
        </p>
        <p>
          <span className="text-muted-foreground">Contact Name: </span>
          {companyContact}
        </p>
        <p>
          <span className="text-muted-foreground">Time: </span>
          {format(callbackTime, dateFormats.DD_MM_YYYY_HH_MM)}
        </p>
        <p>
          <span className="text-muted-foreground">Comment: </span>
          {comment && (
            <Textarea
              className="mt-2 w-full resize-none overflow-y-hidden rounded-md border border-input p-2 text-sm hover:overflow-y-auto"
              disabled
              defaultValue={comment}
            />
          )}
        </p>
      </CardContent>
    </Card>
  );
}
