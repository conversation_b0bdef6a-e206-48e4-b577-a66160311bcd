"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>en,
  Eye,
  PhoneCall,
  Trash2
} from "lucide-react";

import { useMemo, useState } from "react";

import type { CallbackWithUserFlagOutput } from "@watt/api/src/types/callback";
import type { Contact } from "@watt/api/src/types/people";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { isDateOverdue } from "@watt/common/src/utils/format-date";
import { isCreatedByMe } from "@watt/crm/app/utils/access-permissions";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { featureToggles } from "@watt/crm/feature-toggles";
import { useAppStore } from "@watt/crm/store/app-store";
import { Separator } from "../ui/separator";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "../ui/tooltip";
import { CALLBACK_ACTIONS, type CallbackActionType } from "./callback-form";
import { CallbackModal } from "./callback-modal";
import { CallbackViewModal } from "./callback-view-modal";

type CallbackDropdownMenuProps = {
  siteId: string;
  companyId: string;
  callback: CallbackWithUserFlagOutput | undefined;
  latestCallback: CallbackWithUserFlagOutput | undefined;
  contacts: Contact[];
  hasOtherUsersCallbacks: boolean;
  hasCallbackOnAnotherSite?: boolean;
};

export function CallbackDropdownMenu({
  siteId,
  companyId,
  callback,
  latestCallback,
  contacts,
  hasOtherUsersCallbacks,
  hasCallbackOnAnotherSite
}: CallbackDropdownMenuProps) {
  // admins, and managers roles can see the callback details
  const { isManager, isAdmin } = useAppStore(
    state => state.userData
  ).permissions;

  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [action, setAction] = useState<CallbackActionType>();

  const handleCloseDropdown = () => {
    setOpenEditModal(false);
  };

  const handleMenuItemClick = (e: Event, action: CallbackActionType) => {
    e.preventDefault();
    setAction(action);
    setOpenEditModal(true);
  };

  const isCallbackBtnDisabled =
    callback && !(isAdmin || isManager || callback.isCreatedByCurrentUser);
  const isManagerViewMode = isManager && !callback?.isCreatedByCurrentUser;
  const isAdminViewMode = isAdmin && !callback?.isCreatedByCurrentUser;
  const isCallbackIdSet = !!callback?.id;
  const isCallbackOverdue = callback && isDateOverdue(callback.callbackTime);

  // define disabled states for menu items
  const isAddDisabled = isCallbackIdSet || hasOtherUsersCallbacks;
  const isViewDisabled = !isCallbackIdSet || hasCallbackOnAnotherSite;
  const isCompleteDisabled =
    !isCallbackIdSet ||
    !isCallbackOverdue ||
    isAdminViewMode ||
    isManagerViewMode; // we don't need to mark as completed if the callback is not overdue
  const isEditDisabled =
    !isCallbackIdSet ||
    isAdminViewMode ||
    !isCreatedByMe(callback?.createdById);

  const isDeleteDisabled = useMemo(() => {
    if (!isCallbackIdSet) {
      return !(isManager && hasCallbackOnAnotherSite);
    }

    if (isAdminViewMode) {
      return true;
    }

    return !isManager ? isCallbackOverdue : false;
  }, [
    isCallbackIdSet,
    isManager,
    hasCallbackOnAnotherSite,
    isAdminViewMode,
    isCallbackOverdue
  ]);

  const showCompleteCallbackTooltip =
    isCompleteDisabled && isCallbackIdSet && !isAdminViewMode;

  const setDropdownModalOpen = (open: boolean) => {
    if (isCallbackBtnDisabled) {
      return;
    }
    setDropdownOpen(open);
  };

  const handleViewCallback = (e: Event) => {
    e.preventDefault();
    setOpenViewModal(true);
  };

  const deleteAction =
    isManager && !isCreatedByMe(callback?.createdById)
      ? CALLBACK_ACTIONS.DELETE_BY_MANAGER
      : CALLBACK_ACTIONS.DELETE;

  const callbackToDelete = useMemo(() => {
    if (
      action === CALLBACK_ACTIONS.DELETE_BY_MANAGER &&
      hasCallbackOnAnotherSite
    ) {
      return latestCallback;
    }
    return callback;
  }, [action, hasCallbackOnAnotherSite, latestCallback, callback]);

  return (
    <TooltipProvider>
      <DropdownMenu open={dropdownIsOpen} onOpenChange={setDropdownModalOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "flex data-[state=open]:bg-muted",
                  !featureToggles.features.callbackActions && "hidden"
                )}
                disabled={isCallbackBtnDisabled}
              >
                Callback
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent hidden={!isCallbackBtnDisabled}>
            Restricted view: Limited to admin roles and callback creator
          </TooltipContent>
        </Tooltip>

        <DropdownMenuContent align="end" className="w-[240px]">
          <p className="m-2 font-semibold text-sm">Callback Actions</p>
          <Separator />
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <DropdownMenuItem
                  disabled={isAddDisabled}
                  onSelect={e => handleMenuItemClick(e, CALLBACK_ACTIONS.ADD)}
                >
                  <PhoneCall className="mr-2 size-3.5" />
                  Add Callback
                </DropdownMenuItem>
              </div>
            </TooltipTrigger>
            <TooltipContent hidden={isCallbackIdSet || !hasOtherUsersCallbacks}>
              Another user already has a callback scheduled on another site for
              this company group.
            </TooltipContent>
          </Tooltip>

          <DropdownMenuItem
            disabled={isViewDisabled}
            onSelect={handleViewCallback}
          >
            <Eye className="mr-2 size-3.5" />
            View Callback
          </DropdownMenuItem>
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <DropdownMenuItem
                  disabled={isCompleteDisabled}
                  onSelect={e =>
                    handleMenuItemClick(e, CALLBACK_ACTIONS.COMPLETE)
                  }
                >
                  <CheckCircle className="mr-2 size-3.5" />
                  Complete Callback
                </DropdownMenuItem>
              </div>
            </TooltipTrigger>
            <TooltipContent hidden={!showCompleteCallbackTooltip}>
              Cannot complete future callback. Update scheduled time if needed.
            </TooltipContent>
          </Tooltip>
          <DropdownMenuItem
            disabled={isEditDisabled}
            onSelect={e => handleMenuItemClick(e, CALLBACK_ACTIONS.EDIT)}
          >
            <ClipboardPen className="mr-2 size-3.5" />
            Edit Callback
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled={isDeleteDisabled}
            onSelect={e => handleMenuItemClick(e, deleteAction)}
          >
            <Trash2 className="mr-2 size-3.5" />
            Delete Callback
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <CallbackModal
        open={openEditModal}
        onOpenChange={setOpenEditModal}
        action={action}
        siteId={siteId}
        callback={callbackToDelete}
        contacts={contacts}
        onSubmitForm={handleCloseDropdown}
        companyId={companyId}
      />
      <CallbackViewModal
        open={openViewModal}
        onOpenChange={setOpenViewModal}
        callback={callback}
        contact={contacts.find(c => c.id === callback?.companyContactId)}
      />
    </TooltipProvider>
  );
}
