import { TRPCClientError } from "@trpc/client";
import type { CallbackOutput } from "@watt/api/src/types/callback";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  dateFormats,
  formatDate,
  isDateOverdue
} from "@watt/common/src/utils/format-date";
import { trpcClient } from "@watt/crm/utils/api";
import { isBefore, parse, startOfDay } from "date-fns";
import { useMemo, useRef, useState } from "react";

import { CallbackDeleteReason, CallbackStatus } from "@prisma/client";
import type { Contact } from "@watt/api/src/types/people";
import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { z } from "zod";
import { DatePickerInlineInput } from "../date-picker-inline-input";
import { AddNewContactTrigger } from "../quick-actions/contact/add-new-contact-trigger";
import { CompanyContactInfo } from "../quick-actions/contact/company-contact-info";
import TimePicker from "../time-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "../ui/select";
import { Textarea } from "../ui/textarea";

export const CALLBACK_ACTIONS = {
  ADD: "add",
  EDIT: "edit",
  DELETE: "delete",
  DELETE_BY_MANAGER: "deleteByManager",
  COMPLETE: "complete"
} as const;

export type CallbackActionType =
  (typeof CALLBACK_ACTIONS)[keyof typeof CALLBACK_ACTIONS];

type CallbackFormProps = {
  siteId: string;
  companyId: string;
  contacts: Pick<
    Contact,
    "id" | "forename" | "surname" | "emails" | "phoneNumbers" | "siteId"
  >[];
  action: CallbackActionType | undefined;
  callback?: CallbackOutput;
  onSubmitForm: () => void;
  totalCallbacksCount?: number; // Atm used only for manager delete callbacks
};

const deletionReasonLabels = {
  URGENT_CUSTOMER_REQUEST: "Urgent Customer Request",
  RESOURCE_MANAGEMENT: "Resource Management",
  CUSTOMER_PREFERENCE: "Customer Preference",
  OTHER: "Other (specify in comments)"
} as const;

const CallbackFormSchema = (action: CallbackActionType | undefined) =>
  z
    .object({
      id: z.string().optional(),
      subject: z
        .string()
        .min(1, "Subject is required")
        .max(75, "Input too long. Please limit to 75 characters."),
      companyContactId: z.string().min(1, "Contact is required"),
      date: z.string().min(1, "Date is required"),
      time: z.string().min(1, "Time is required"),
      comment: z
        .string()
        .max(300, "Input too long. Please limit to 300 characters.")
        .optional(),
      deletionReason: z.nativeEnum(CallbackDeleteReason).optional(),
      deletionComment: z.string().optional()
    })
    .superRefine((data, ctx) => {
      // Validate `deletionReason` for DELETE_BY_MANAGER
      if (
        action === CALLBACK_ACTIONS.DELETE_BY_MANAGER &&
        !data.deletionReason
      ) {
        ctx.addIssue({
          path: ["deletionReason"],
          code: z.ZodIssueCode.custom,
          message: "Deletion Reason is required."
        });
      }
      // Validate `deletionComment` when deletionReason is 'OTHER'
      if (
        data.deletionReason === CallbackDeleteReason.OTHER &&
        !data.deletionComment
      ) {
        ctx.addIssue({
          path: ["deletionComment"],
          code: z.ZodIssueCode.custom,
          message: "Please specify the reason"
        });
      }
    });

export const callbackActionState = {
  [CALLBACK_ACTIONS.ADD]: {
    title: "Add Callback",
    description: "Enter details to schedule a new callback for this site.",
    toastTitle: "New callback added",
    toastDescription: "The callback has been added successfully.",
    errorToastTitle: "Unable to add a new callback",
    errorDescription:
      "Error occurred while adding the new callback. Please check the form and try again.",
    buttonText: "Confirm"
  },
  [CALLBACK_ACTIONS.EDIT]: {
    title: "Edit Callback",
    description:
      "Review and update the callback details for this site. All fields can be modified as needed.",
    overdueDescription:
      "Only comments can be modified for past callbacks. Other fields are locked.",
    toastTitle: "Callback updated",
    toastDescription: "The callback has been updated successfully.",
    errorToastTitle: "Unable to update callback",
    errorDescription:
      "Error occurred while updating the callback. Please check the form and try again.",
    buttonText: "Confirm"
  },
  [CALLBACK_ACTIONS.DELETE]: {
    title: "Delete Callback",
    description:
      "Are you sure you want to delete this callback? This action cannot be undone.",
    toastTitle: "Callback deleted",
    toastDescription: "The callback has been deleted successfully.",
    errorToastTitle: "Unable to delete callback",
    errorDescription:
      "Error occurred while deleting the callback. Please try again.",
    buttonText: "Delete"
  },
  [CALLBACK_ACTIONS.DELETE_BY_MANAGER]: {
    title: "Delete Company Callbacks",
    description:
      "Are you sure you want to remove all callbacks for this company? This action cannot be undone.",
    toastTitle: "Callbacks deleted",
    toastDescription: "The callback has been successfully deleted.",
    errorToastTitle: "Unable to delete callbacks",
    errorDescription:
      "Error occurred while deleting the callbacks. Please try again.",
    buttonText: "Delete"
  },
  [CALLBACK_ACTIONS.COMPLETE]: {
    title: "Complete Callback",
    description:
      "Confirm that you have completed the callback for this site. This will remove the callback marking and update the status.",
    toastTitle: "Callback completed",
    toastDescription: "The callback has been completed successfully.",
    errorToastTitle: "Unable to complete callback",
    errorDescription:
      "Error occurred while completing the callback. Please try again.",
    buttonText: "Confirm"
  }
};

export function CallbackForm({
  siteId,
  companyId,
  callback,
  contacts,
  action,
  onSubmitForm,
  totalCallbacksCount
}: CallbackFormProps) {
  const upsertCallbackMutation = trpcClient.callback.upsert.useMutation();
  const completeCallbackMutation = trpcClient.callback.complete.useMutation();
  const deleteCallbackMutation = trpcClient.callback.delete.useMutation();
  const managerDeleteAllCallbacksMutation =
    trpcClient.callback.managerDeleteAll.useMutation();
  const contactFormContainerRef = useRef<HTMLDivElement>(null);
  const [openContactFormModal, setOpenContactFormModal] = useState(false);
  const [contactSearch, setContactSearch] = useState("");

  // determine if the new contact will be set as the primary contact for the site
  const setAsPrimaryContact = useMemo(() => {
    return !contacts?.find(contact => contact.siteId === siteId);
  }, [contacts, siteId]);

  const DELETION_REASON_COMMENT = {
    rows: 4,
    maxLength: 255
  };

  const defaultValues = useMemo(
    () => ({
      companyContactId: callback?.companyContactId || undefined,
      subject: callback?.subject || undefined,
      comment: callback?.comment || undefined,
      date: callback?.callbackTime
        ? formatDate(callback.callbackTime, dateFormats.DD_MM_YYYY)
        : undefined,
      time: callback?.callbackTime
        ? formatDate(callback.callbackTime, dateFormats.HH_MM)
        : undefined,
      // Include these fields with explicit undefined values when needed
      ...(action === CALLBACK_ACTIONS.DELETE_BY_MANAGER
        ? {
            deletionReason: undefined,
            deletionComment: undefined
          }
        : {})
    }),
    [callback, action]
  );

  const {
    toastTitle = "",
    toastDescription = "",
    buttonText = "",
    errorDescription = "",
    errorToastTitle = ""
  } = action ? callbackActionState[action] : {};

  const deleteAction =
    action === CALLBACK_ACTIONS.DELETE_BY_MANAGER ||
    action === CALLBACK_ACTIONS.DELETE;

  const formInputsDisabled = action === CALLBACK_ACTIONS.DELETE;
  const hideFormInputs = action === CALLBACK_ACTIONS.DELETE_BY_MANAGER;
  const isCallbackOverdue = callback && isDateOverdue(callback.callbackTime);

  const form = useZodForm({
    schema: CallbackFormSchema(action),
    defaultValues
  });

  const deletionReason = form.watch("deletionReason");

  const handleFormSubmit = async () => {
    const { date, time, companyContactId, subject, comment, deletionComment } =
      form.getValues();
    const callbackTime = `${date} ${time}`;
    const status = (() => {
      switch (action) {
        case CALLBACK_ACTIONS.COMPLETE:
          return CallbackStatus.COMPLETE;
        case CALLBACK_ACTIONS.DELETE:
        case CALLBACK_ACTIONS.DELETE_BY_MANAGER:
          return CallbackStatus.CANCELLED;
        default:
          return callback?.status ?? CallbackStatus.SCHEDULED;
      }
    })();

    const parsedDate = parse(date, dateFormats.DD_MM_YYYY, new Date());
    const parsedDateAndTime = parse(
      callbackTime,
      dateFormats.DD_MM_YYYY_HH_MM,
      new Date()
    );

    const formatedDateAndTime = formatDate(
      parsedDateAndTime,
      dateFormats.YYYY_MM_DD_HYPHEN_HH_MM
    );

    // for scheduled callbacks, check if date and time are in the past,
    // for overdue callbacks we don't need this check as we only need to update the comments.
    if (!isCallbackOverdue && status === CallbackStatus.SCHEDULED) {
      // check if date is in the past
      if (isBefore(parsedDate, startOfDay(new Date()))) {
        form.setError("date", {
          message: "Select a time in the future for the callback"
        });
        return;
      }
      // check if date and time are in the past
      if (isBefore(parsedDateAndTime, new Date())) {
        form.setError("time", {
          message: "Select a time in the future for the callback"
        });
        return;
      }
    }

    try {
      let result: CallbackOutput | undefined;

      // Handle existing callback updates
      if (callback?.id) {
        result = await handleExistingCallback({
          id: callback.id,
          formatedDateAndTime,
          companyContactId,
          status,
          subject,
          comment,
          deletionReason,
          deletionComment
        });
      } else {
        // Handle new callback creation
        result = await createNewCallback({
          formatedDateAndTime,
          companyContactId,
          status,
          subject,
          comment
        });
      }

      if (result) {
        resetForm(result);
      }

      onSubmitForm();
    } catch (e) {
      const error = e as Error;
      log.error("Failed to perform callback mutation", error);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? errorDescription
          : error.message;
      toast({
        title: errorToastTitle,
        description,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  const handleExistingCallback = async ({
    id,
    formatedDateAndTime,
    companyContactId,
    status,
    subject,
    comment,
    deletionReason,
    deletionComment
  }: {
    id: string;
    formatedDateAndTime: string;
    companyContactId: string;
    status: CallbackStatus;
    subject: string;
    comment?: string;
    deletionReason?: CallbackDeleteReason;
    deletionComment?: string;
  }): Promise<CallbackOutput | undefined> => {
    if (action === CALLBACK_ACTIONS.COMPLETE) {
      await completeCallbackMutation.mutateAsync({
        id,
        comment
      });
      toast({
        title: toastTitle,
        description: toastDescription,
        variant: "success"
      });
      return;
    }

    if (action === CALLBACK_ACTIONS.DELETE_BY_MANAGER) {
      await managerDeleteAllCallbacksMutation.mutateAsync({
        id,
        companyId,
        deletionReason,
        deletionComment
      });
      toast({
        title: toastTitle,
        description:
          totalCallbacksCount && totalCallbacksCount > 1
            ? `${totalCallbacksCount} callbacks have been successfully deleted.`
            : toastDescription,
        variant: "success"
      });
      return;
    }

    if (action === CALLBACK_ACTIONS.DELETE) {
      await deleteCallbackMutation.mutateAsync({
        id
      });
      toast({
        title: toastTitle,
        description: toastDescription,
        variant: "success"
      });
      return;
    }

    const updatedCallback = await upsertCallbackMutation.mutateAsync({
      id,
      companySiteId: siteId,
      companyContactId,
      callbackTime: formatedDateAndTime,
      status,
      subject,
      comment,
      notified: false
    });

    toast({
      title: toastTitle,
      description: toastDescription,
      variant: "success"
    });

    return updatedCallback;
  };

  const createNewCallback = async ({
    formatedDateAndTime,
    companyContactId,
    status,
    subject,
    comment
  }: {
    formatedDateAndTime: string;
    companyContactId: string;
    status: CallbackStatus;
    subject: string;
    comment?: string;
  }): Promise<CallbackOutput> => {
    const newCallback = await upsertCallbackMutation.mutateAsync({
      companySiteId: siteId,
      companyContactId,
      callbackTime: formatedDateAndTime,
      status,
      subject,
      comment,
      notified: false
    });

    toast({
      title: toastTitle,
      description: toastDescription,
      variant: "success"
    });

    return newCallback;
  };

  const resetForm = (result: CallbackOutput) => {
    const isDeleteOrComplete =
      action === CALLBACK_ACTIONS.DELETE ||
      action === CALLBACK_ACTIONS.COMPLETE;

    const resetValues = isDeleteOrComplete
      ? {
          companyContactId: "",
          subject: "",
          comment: "",
          date: "",
          time: ""
        }
      : {
          companyContactId: result.companyContactId || undefined,
          subject: result.subject || undefined,
          comment: result.comment || undefined,
          date: result.callbackTime
            ? formatDate(result.callbackTime, dateFormats.DD_MM_YYYY)
            : undefined,
          time: result.callbackTime
            ? formatDate(result.callbackTime, dateFormats.HH_MM)
            : undefined
        };

    form.reset(resetValues);
  };

  const submitButtonDisabled = useMemo(() => {
    return (
      upsertCallbackMutation.isPending ||
      completeCallbackMutation.isPending ||
      managerDeleteAllCallbacksMutation.isPending ||
      deleteCallbackMutation.isPending ||
      (!form.formState.isDirty &&
        !(
          action === CALLBACK_ACTIONS.DELETE ||
          action === CALLBACK_ACTIONS.COMPLETE
        )) // we don't need to disable the submit button to delete or complete a callback
    );
  }, [
    completeCallbackMutation.isPending,
    deleteCallbackMutation.isPending,
    form.formState.isDirty,
    managerDeleteAllCallbacksMutation.isPending,
    upsertCallbackMutation.isPending,
    action
  ]);

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleFormSubmit}
      className="my-4 space-y-4 overflow-x-hidden px-2"
    >
      {!hideFormInputs && (
        <>
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Subject *</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    disabled={formInputsDisabled || isCallbackOverdue}
                    className={cn(
                      !field.value && "text-muted-foreground italic"
                    )}
                    placeholder="Reason for callback"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companyContactId"
            render={({ field }) => {
              const selectedContact = contacts.find(
                contact => contact.id === field.value
              );
              return (
                <FormItem className="flex flex-col">
                  <FormLabel>Select a Contact *</FormLabel>
                  <FormControl>
                    <div
                      ref={contactFormContainerRef}
                      className="flex w-full max-w-[600px] flex-col"
                    >
                      <LookUp
                        open={openContactFormModal}
                        onOpenChange={setOpenContactFormModal}
                      >
                        <LookUpTrigger
                          fieldValue={field.value}
                          disabled={formInputsDisabled || isCallbackOverdue}
                        >
                          {selectedContact ? (
                            <CompanyContactInfo {...selectedContact} />
                          ) : (
                            <span className="font-normal italic">
                              Select an existing contact or add a new one
                            </span>
                          )}
                        </LookUpTrigger>
                        <LookUpContent
                          placeholder="Type contact name or email address"
                          searchInput={contactSearch}
                          onSearchInputChange={setContactSearch}
                          container={contactFormContainerRef.current}
                          className="pb-10"
                          shouldFilter
                        >
                          <LookUpGroup className="p-0">
                            {contacts.map(contact => {
                              const primaryEmail = contact.emails?.find(
                                email => email.isPrimary
                              )?.email;

                              if (!primaryEmail) {
                                // There will always be a primary email
                                return null;
                              }

                              const lookupValue = `${primaryEmail} ${contact.forename} ${contact.surname}`;
                              return (
                                <LookUpItem
                                  key={contact.id}
                                  value={lookupValue}
                                  onSelect={() => {
                                    form.setValue(
                                      "companyContactId",
                                      contact.id,
                                      {
                                        shouldDirty: true
                                      }
                                    );
                                  }}
                                >
                                  <CompanyContactInfo {...contact} />
                                </LookUpItem>
                              );
                            })}
                          </LookUpGroup>
                          <div className="absolute bottom-0 flex w-full justify-center border-t bg-background">
                            <AddNewContactTrigger
                              companyId={companyId}
                              siteId={siteId}
                              setAsPrimaryContact={setAsPrimaryContact} // TODO (Bidur): Copied from QuoteEmailForm, the logic around this will change with the new contact realationships
                              onSubmit={(_, contactId) => {
                                form.setValue("companyContactId", contactId);
                              }}
                            />
                          </div>
                        </LookUpContent>
                      </LookUp>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Select the Date *</FormLabel>
                <FormControl>
                  <DatePickerInlineInput
                    {...field}
                    placeholder="Select the callback date (DD/MM/YYYY)"
                    calendarProps={{
                      captionLayout: "dropdown-buttons",
                      disabled: {
                        before: new Date()
                      }
                    }}
                    disabled={formInputsDisabled || isCallbackOverdue}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="time"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Select the Time *</FormLabel>
                <FormControl>
                  <TimePicker
                    {...field}
                    placeholder="Select the callback time (HH:MM)"
                    value={field.value}
                    onChange={field.onChange}
                    disabled={formInputsDisabled || isCallbackOverdue}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="comment"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Comment</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    className={cn(
                      !field.value && "text-muted-foreground italic",
                      "resize-none"
                    )}
                    placeholder="Add optional comment (300 character limit)"
                    rows={4}
                    disabled={formInputsDisabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      {action === CALLBACK_ACTIONS.DELETE_BY_MANAGER && (
        <>
          <FormField
            control={form.control}
            name="deletionReason"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Deletion Reason *</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <SelectTrigger
                      className={cn(
                        !field.value && "text-muted-foreground italic"
                      )}
                    >
                      <SelectValue placeholder="Select a deletion reason" />
                    </SelectTrigger>

                    <SelectContent position="popper">
                      {Object.entries(deletionReasonLabels)?.map(
                        ([key, label]) => {
                          return (
                            <SelectItem key={key} value={key}>
                              {label}
                            </SelectItem>
                          );
                        }
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="deletionComment"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  Comment {deletionReason === CallbackDeleteReason.OTHER && "*"}
                </FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    className={cn(
                      !field.value && "text-muted-foreground italic",
                      "resize-none"
                    )}
                    placeholder={
                      deletionReason === CallbackDeleteReason.OTHER
                        ? "Please explain why manager deletion is required (300 character limit)"
                        : "Add optional comment (300 character limit)"
                    }
                    rows={DELETION_REASON_COMMENT.rows}
                    maxLength={DELETION_REASON_COMMENT.maxLength}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      {deleteAction && (
        <FormMessage className="italic">
          {`Click 'Delete' to proceed with the deletion or 'X' to cancel and keep the ${action === CALLBACK_ACTIONS.DELETE ? "callback for this site." : "callbacks"}`}
        </FormMessage>
      )}

      <Button
        disabled={submitButtonDisabled}
        type="submit"
        variant={deleteAction ? "destructive" : "secondary"}
        className="button-click-animation w-full"
      >
        {buttonText || "Confirm"}
      </Button>
    </FormWrapper>
  );
}
