import type { CallbackOutput } from "@watt/api/src/types/callback";
import type { Contact } from "@watt/api/src/types/people";
import { isDateOverdue } from "@watt/common/src/utils/format-date";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAppStore } from "@watt/crm/store/app-store";
import { trpcClient } from "@watt/crm/utils/api";
import type { ErrorInfo } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { ScrollArea } from "../ui/scroll-area";
import { CallbackDeleteAlert } from "./callback-delete-alert";
import { CallbackDeletePreviewCard } from "./callback-delete-preview-card";
import type { CallbackActionType } from "./callback-form";
import {
  CALLBACK_ACTIONS,
  CallbackForm,
  callbackActionState
} from "./callback-form";

type CallbackModalProps = {
  siteId: string;
  companyId: string;
  contacts: Pick<
    Contact,
    "id" | "forename" | "surname" | "emails" | "phoneNumbers" | "siteId"
  >[];
  action: CallbackActionType | undefined;
  callback?: CallbackOutput;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmitForm: () => void;
};

function handleErrorForBoundary(
  error: Error,
  _errorInfo: ErrorInfo,
  onOpenChange: (open: boolean) => void
) {
  onOpenChange(false);

  const { errorToastTitle } =
    callbackActionState[CALLBACK_ACTIONS.DELETE_BY_MANAGER];

  toast({
    title: errorToastTitle,
    description: error.message,
    variant: "destructive",
    duration: 10000
  });
}

export function CallbackModal({
  siteId,
  companyId,
  contacts,
  action,
  callback,
  open,
  onOpenChange,
  onSubmitForm
}: CallbackModalProps) {
  const { isManager } = useAppStore(state => state.userData).permissions;
  const { data } = trpcClient.callback.scheduledCallbacks.useQuery(
    {
      companyId
    },
    {
      enabled: isManager && action === CALLBACK_ACTIONS.DELETE_BY_MANAGER
    }
  );
  const { title = "", description = "" } = action
    ? callbackActionState[action]
    : {};

  const isCallbackOverdue = callback && isDateOverdue(callback.callbackTime);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader className="space-y-4">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription className="flex flex-col gap-y-6" asChild>
            <span>
              {data && action === CALLBACK_ACTIONS.DELETE_BY_MANAGER && (
                <ErrorBoundary
                  onError={(error, errorInfo) =>
                    handleErrorForBoundary(error, errorInfo, onOpenChange)
                  }
                  fallbackRender={({ error }) => (
                    <div className="bg-red-100 p-4">
                      <h2 className="font-bold">Error</h2>
                      <p>{error.message}</p>
                    </div>
                  )}
                >
                  <CallbackDeleteAlert {...data} />
                </ErrorBoundary>
              )}
              <span className="italic">
                {action === CALLBACK_ACTIONS.EDIT && isCallbackOverdue
                  ? callbackActionState[CALLBACK_ACTIONS.EDIT]
                      .overdueDescription
                  : description}
              </span>
              {data && action === CALLBACK_ACTIONS.DELETE_BY_MANAGER && (
                <ScrollArea className="max-h-[420px] pr-4">
                  <div className="flex flex-col gap-y-4">
                    {data.callbacks.map(callback => (
                      <CallbackDeletePreviewCard
                        key={callback.id}
                        {...callback}
                      />
                    ))}
                  </div>
                </ScrollArea>
              )}
            </span>
          </DialogDescription>
        </DialogHeader>

        <CallbackForm
          siteId={siteId}
          companyId={companyId}
          contacts={contacts}
          action={action}
          callback={callback}
          onSubmitForm={onSubmitForm}
          totalCallbacksCount={data?.count}
        />
      </DialogContent>
    </Dialog>
  );
}
