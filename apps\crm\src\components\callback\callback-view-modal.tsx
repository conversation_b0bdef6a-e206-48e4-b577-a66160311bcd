import type { CallbackOutput } from "@watt/api/src/types/callback";
import type { Contact } from "@watt/api/src/types/people";
import { useCallback } from "react";

import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "../ui/dialog";

type CallbackViewModalProps = {
  callback: CallbackOutput | undefined;
  open: boolean;
  contact: Pick<Contact, "id" | "forename" | "surname"> | undefined;
  onOpenChange: (open: boolean) => void;
};

export function CallbackViewModal({
  callback,
  open,
  contact,
  onOpenChange
}: CallbackViewModalProps) {
  const { subject, callbackTime, comment } = callback || {};

  const renderCallbackRow = useCallback(
    (heading: string, content: string | null | undefined) => (
      <div className="flex gap-2">
        <p className="min-w-20 text-muted-foreground">{heading}</p>
        <p className="overflow-x-hidden break-words text-foreground">
          {content}
        </p>
      </div>
    ),
    []
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader className="space-y-4">
          <DialogTitle>Callback Details</DialogTitle>
        </DialogHeader>
        <DialogDescription className="space-y-4 overflow-x-hidden">
          {renderCallbackRow("Subject:", subject)}
          {renderCallbackRow(
            "Contact:",
            `${contact?.forename} ${contact?.surname}`
          )}
          {renderCallbackRow(
            "Date:",
            formatDate(callbackTime, dateFormats.DD_MM_YYYY)
          )}
          {renderCallbackRow(
            "Time:",
            formatDate(callbackTime, dateFormats.HH_MM)
          )}
          {renderCallbackRow("Comments:", comment)}
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
}
