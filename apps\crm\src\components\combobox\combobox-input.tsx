import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { ChevronsUpDown, Plus } from "lucide-react";
import { type ReactNode, useState } from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@watt/crm/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";

import { Badge } from "../ui/badge";
import { Button } from "../ui/button";

type ComboboxTriggerProps = {
  placeholder: string;
  fieldValue: string[];
} & React.ComponentProps<"button">;

type ComboboxContentProps = {
  className?: string;
  placeholder?: string;
  allowAddItem?: boolean;
  allowSearch?: boolean;
  onAddItem?: (value: string) => void;
  children: ReactNode;
  container?: HTMLElement | null;
} & React.ComponentProps<"div">;

const ComboboxTrigger: React.FC<ComboboxTriggerProps> = ({
  ref,
  placeholder,
  fieldValue
}) => (
  <PopoverTrigger asChild>
    <Button
      variant="outline"
      role="combobox"
      className={cn(
        "justify-between",
        (!fieldValue || fieldValue.length === 0) &&
          "font-normal text-muted-foreground italic"
      )}
      ref={ref}
    >
      {/* TODO (Stephen): This max-w-44ch is a hack so that text-ellipsis works */}
      <span className="max-w-[44ch] overflow-hidden text-ellipsis whitespace-nowrap">
        {!fieldValue || fieldValue.length === 0
          ? placeholder
          : fieldValue.map(val => (
              <Badge className="mr-2" key={val}>
                {val}
              </Badge>
            ))}
      </span>
      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
    </Button>
  </PopoverTrigger>
);
ComboboxTrigger.displayName = "ComboboxTrigger";

const ComboboxContent: React.FC<ComboboxContentProps> = ({
  ref,
  className,
  placeholder,
  allowAddItem = false,
  allowSearch = true,
  onAddItem,
  children,
  container
}) => {
  const [searchValue, setSearchValue] = useState<string>("");
  const handleAddItem = () => {
    if (onAddItem) {
      onAddItem(searchValue);
      setSearchValue("");
    }
  };

  return (
    <PopoverContent
      ref={ref}
      className={cn("w-[var(--radix-popover-trigger-width)] p-0", className)}
      container={container}
    >
      <Command>
        {allowSearch && (
          <>
            <CommandInput
              value={searchValue}
              onValueChange={setSearchValue}
              placeholder={placeholder}
            />
            <CommandEmpty>No results found.</CommandEmpty>
          </>
        )}
        <CommandList className="max-h-[400px] overflow-y-auto">
          {children}
        </CommandList>
        {allowAddItem && searchValue && (
          <Button
            variant="link"
            className="gap-1 rounded-none border-t bg-background hover:no-underline"
            onClick={handleAddItem}
          >
            <Plus className="h-4 w-4" />
            Add {searchValue}
          </Button>
        )}
      </Command>
    </PopoverContent>
  );
};
ComboboxContent.displayName = "ComboboxContent";

const Combobox = Popover;
const ComboboxGroup = CommandGroup;
const ComboboxItem = CommandItem;

export {
  Combobox,
  ComboboxContent,
  ComboboxGroup,
  ComboboxItem,
  ComboboxTrigger
};
