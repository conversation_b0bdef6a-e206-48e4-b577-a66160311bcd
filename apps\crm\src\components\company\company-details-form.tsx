import { TRPCClientError } from "@trpc/client";
import type { Address_Find_Many } from "@watt/api/src/router/address";
import type { CompanyWith_Add_Con_Sit } from "@watt/api/src/router/company";
import { CompanyDetailsSchema } from "@watt/api/src/types/company";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2 } from "lucide-react";
import { useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { useAddressSearch } from "@watt/crm/hooks/use-address-search";
import { BusinessType } from "@watt/db/src/enums";
import { getRegistrationNumberLabel } from "@watt/db/src/maps/business-type-map";
import { DatePickerInlineInput } from "../date-picker-inline-input";
import { AddressInput } from "../input/address-input";
import { toast } from "../ui/use-toast";

type QueryParams = {
  addressSearch: string;
  selectedAddressId: string;
};

type CompanyDetailsFormProps = {
  companyData: NonNullable<CompanyWith_Add_Con_Sit>;
  onSubmitForm: () => void;
};

export function CompanyDetailsForm({
  onSubmitForm,
  companyData
}: CompanyDetailsFormProps) {
  const {
    name: companyName,
    id: companyId,
    registrationNumber,
    entityAddressId,
    companyDetails,
    entityAddress,
    businessType
  } = companyData;
  const { queryParams, setQueryParams, removeQueryParams } =
    useQueryParams<Partial<QueryParams>>();
  const [addressData, setAddressData] = useState<
    Address_Find_Many | undefined
  >();
  const { fetchAddress, isFetchingAddress } = useAddressSearch();
  const companyMutation = trpcClient.company.update.useMutation();

  const defaultValues = useMemo(() => {
    const {
      dateIncorporated,
      dateMovedIn,
      website,
      natureOfBusiness,
      officeNumber,
      officeNumber2
    } = companyDetails || {};
    return {
      entityAddressId,
      dateIncorporated: dateIncorporated
        ? formatDate(dateIncorporated, dateFormats.DD_MM_YYYY)
        : undefined,
      dateMovedIn: dateMovedIn
        ? formatDate(dateMovedIn, dateFormats.DD_MM_YYYY)
        : undefined,
      website: website || "",
      natureOfBusiness: natureOfBusiness || "",
      officeNumber: officeNumber || "",
      officeNumber2: officeNumber2 || ""
    };
  }, [companyDetails, entityAddressId]);

  const form = useZodForm({
    schema: CompanyDetailsSchema,
    defaultValues
  });

  const handleSubmit = async () => {
    try {
      await companyMutation.mutateAsync({
        companyId,
        ...form.getValues()
      });
      onSubmitForm();
      removeQueryParams([], { newParams: true });
      toast({
        title: "Success",
        description: "Company details have been successfully updated.",
        variant: "success"
      });
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while updating company details. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to update company details",
        description,
        variant: "destructive"
      });
    }
  };

  const selectedAddress = useMemo(() => {
    if (addressData) {
      return addressData.find(
        address => address.id === queryParams.selectedAddressId
      );
    }
    return entityAddress && entityAddress;
  }, [queryParams.selectedAddressId, addressData, entityAddress]);

  const onAddressSubmit = (id: string) => {
    setQueryParams({ addressSearch: id, selectedAddressId: id });
  };

  const handleAddressSearchInputChange = (addressSearch: string) => {
    setQueryParams({ addressSearch, selectedAddressId: "" });
    form.setValue("entityAddressId", "");
  };

  const handleAddressSelect = (addressId: string) => {
    const selectedAddressObj = addressData?.find(
      address => address.id === addressId
    );
    if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
      toast({
        title: "Invalid address",
        description:
          "Selected address does not have a valid address or postcode",
        variant: "destructive"
      });
      return;
    }
    setQueryParams({ selectedAddressId: addressId });
    form.setValue("entityAddressId", addressId);
  };

  const fullResetAddressFormData = () => {
    form.resetField("entityAddressId");
    setQueryParams({ addressSearch: "", selectedAddressId: "" });
    setAddressData(undefined);
  };

  useDebounce(
    async function lookupAddressOnSearchInput() {
      try {
        if (!queryParams.addressSearch) {
          fullResetAddressFormData();
          return;
        }

        const result = await fetchAddress(queryParams.addressSearch);

        setAddressData(result);

        if (queryParams.selectedAddressId) {
          const selectedAddressObj = result?.find(
            address => address.id === queryParams.selectedAddressId
          );

          if (
            !selectedAddressObj?.displayName ||
            !selectedAddressObj.postcode
          ) {
            fullResetAddressFormData();

            toast({
              title: "Invalid address in the URL",
              description:
                "Selected address does not have a valid address or postcode",
              variant: "destructive"
            });
            return;
          }
          form.setValue("entityAddressId", queryParams.selectedAddressId);
        }
      } catch (e) {
        const error = e as Error;
        const description =
          error instanceof TRPCClientError && !!error.data.zodError
            ? "Error fetching address. Please check the input and try again."
            : error.message;
        toast({
          title: "Unable to get address",
          description,
          variant: "destructive"
        });
      }
    },
    1000,
    [queryParams.addressSearch]
  );

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleSubmit}
      className="my-4 space-y-4 overflow-x-hidden px-1"
    >
      <FormField
        name="companyName"
        render={() => (
          <FormItem className="flex flex-col">
            <FormLabel>Company Name</FormLabel>
            <FormControl>
              <Input value={humanize(companyName)} disabled />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {registrationNumber &&
        ([BusinessType.LTD, BusinessType.CHARITY] as BusinessType[]).includes(
          businessType
        ) && (
          <FormField
            name="registrationNumber"
            render={() => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  {getRegistrationNumberLabel(businessType)}
                </FormLabel>
                <FormControl>
                  <Input value={registrationNumber} disabled />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

      <FormField
        control={form.control}
        name="entityAddressId"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Registered Address</FormLabel>
            <FormControl>
              <AddressInput
                value={field.value || ""}
                isLoading={isFetchingAddress}
                addressData={addressData}
                selectedAddress={selectedAddress as Address_Find_Many[0]}
                handleAddressSelect={handleAddressSelect}
                handleAddressSearchInputChange={handleAddressSearchInputChange}
                onSubmit={onAddressSubmit}
                addressSearchInput={queryParams.addressSearch}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="officeNumber"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Phone *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="Primary contact number"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="officeNumber2"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Phone Secondary</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="Secondary contact number (optional)"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="dateIncorporated"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Date Incorporated</FormLabel>
            <DatePickerInlineInput
              {...field}
              placeholder="Select incorporation date (DD/MM/YYYY)"
              calendarProps={{
                toDate: new Date(),
                captionLayout: "dropdown-buttons",
                fromYear: 1844,
                toYear: new Date().getFullYear(),
                disabled: { after: new Date() }
              }}
            />
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="dateMovedIn"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Date Moved In</FormLabel>
            <DatePickerInlineInput
              {...field}
              placeholder="Select move-in date (DD/MM/YYYY)"
              calendarProps={{
                toDate: new Date(),
                captionLayout: "dropdown-buttons",
                fromYear: 1844,
                toYear: new Date().getFullYear(),
                disabled: { after: new Date() }
              }}
            />
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="natureOfBusiness"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Nature of the Business</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="Please describe the nature of your business"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="website"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Company Website</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="https://"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <Button
        type="submit"
        variant="secondary"
        className="button-click-animation w-full"
        disabled={companyMutation.isPending}
      >
        {companyMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        Update
      </Button>
    </FormWrapper>
  );
}
