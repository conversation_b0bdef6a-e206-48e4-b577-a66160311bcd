import type { CompanyWith_Add_Con_Sit } from "@watt/api/src/router/company";
import { revalidatePath } from "@watt/crm/utils/revalidate-path";
import { Pencil } from "lucide-react";
import { useState } from "react";

import { But<PERSON> } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "../ui/dialog";
import { CompanyDetailsForm } from "./company-details-form";

interface CompanyDetailsUpdateModalProps {
  companyData: NonNullable<CompanyWith_Add_Con_Sit>;
}

export function CompanyDetailsUpdateModal({
  companyData
}: CompanyDetailsUpdateModalProps) {
  const [companyEditModalOpen, setCompanyEditModalOpen] = useState(false);
  const handleSubmitForm = () => {
    setCompanyEditModalOpen(false);
    revalidatePath(`/account/companies/${companyData.id}`);
  };
  return (
    <Dialog onOpenChange={setCompanyEditModalOpen} open={companyEditModalOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="invisible text-muted-foreground/70 group-hover:visible"
        >
          <Pencil className="h-3.5 w-3.5" />
          <span className="sr-only fixed">Edit</span>
        </Button>
      </DialogTrigger>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="mb-4">Update Company Information</DialogTitle>
          <DialogDescription>
            Edit the company details and information below.
          </DialogDescription>
        </DialogHeader>
        <CompanyDetailsForm
          onSubmitForm={handleSubmitForm}
          companyData={companyData}
        />
      </DialogContent>
    </Dialog>
  );
}
