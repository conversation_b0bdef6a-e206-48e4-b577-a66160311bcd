"use client";

import { Plus } from "lucide-react";

import { type PropsWithChildren, useState } from "react";

import type { PhoneNumberFormData } from "@watt/crm/components/contact/contact-form";
import { Button } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@watt/crm/components/ui/dialog";
import { PhoneNumberForm } from "./phone-number-form";

type AddNewPhoneNumberTriggerProps = PropsWithChildren<{
  title?: string;
  existingPhoneNumber?: PhoneNumberFormData;
  onSubmit: (newPhoneNumber: PhoneNumberFormData) => void;
  initialPhoneNumber?: string;
}>;

export function AddNewPhoneNumberTrigger({
  title,
  existingPhoneNumber,
  onSubmit,
  initialPhoneNumber,
  children
}: AddNewPhoneNumberTriggerProps) {
  const [phoneNumberModalOpen, setPhoneNumberModalOpen] = useState(false);

  const handleSubmitForm = (data: PhoneNumberFormData) => {
    onSubmit(data);
    setPhoneNumberModalOpen(false);
  };

  // Decide the title for the dialog
  const dialogTitle = existingPhoneNumber
    ? "Edit Phone Number"
    : "Add New Phone Number";

  return (
    <Dialog onOpenChange={setPhoneNumberModalOpen} open={phoneNumberModalOpen}>
      <DialogTrigger asChild>
        {children ? (
          children
        ) : (
          <Button
            variant="link"
            className="w-full bg-background hover:no-underline"
          >
            <Plus className="mr-1 h-4 w-4" />
            {title || "Add New Phone Number"}
          </Button>
        )}
      </DialogTrigger>

      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogDescription>
            {existingPhoneNumber
              ? "Update the phone number details below."
              : "Enter a new phone number for this contact."}
          </DialogDescription>
        </DialogHeader>
        <PhoneNumberForm
          onSubmitForm={handleSubmitForm}
          existingPhoneNumber={existingPhoneNumber}
          initialPhoneNumber={initialPhoneNumber}
        />
      </DialogContent>
    </Dialog>
  );
}
