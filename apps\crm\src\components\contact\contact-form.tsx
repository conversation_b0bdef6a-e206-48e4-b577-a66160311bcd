import { TRPCClientError } from "@trpc/client";
import {
  type Contact,
  type UpsertContactFormData,
  UpsertContactSchema
} from "@watt/api/src/types/people";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2, Pen, Star, Trash2 } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import {
  type ContactAddressFormData,
  createUkPhoneNumberSchema
} from "@watt/api/src/types/people";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { DynamicAddressFormList } from "@watt/crm/components/dynamic-address/dynamic-address-form-list";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { Salutation } from "@watt/db/src/enums";
import type { PhoneNumberType } from "@watt/db/src/enums";
import { v4 as uuidv4 } from "uuid";
import {
  ComboboxLookup,
  ComboboxLookupContent,
  ComboboxLookupGroup,
  ComboboxLookupItem,
  ComboboxLookupTrigger
} from "../combobox-lookup";
import {
  Combobox,
  ComboboxContent,
  ComboboxGroup,
  ComboboxItem,
  ComboboxTrigger
} from "../combobox/combobox-input";
import { DatePickerInlineInput } from "../date-picker-inline-input";
import { Switch } from "../ui/switch";
import { AddNewPhoneNumberTrigger } from "./add-new-phone-number-trigger";

type ContactFormProps = {
  companyId?: string;
  siteId?: string;
  contact?: Contact;
  setAsPrimaryContact?: boolean;
  onSubmitForm: (contact: Contact) => void;
};

type EmailFormData = {
  id?: string;
  email: string;
  isPrimary: boolean;
};

export type PhoneNumberFormData = {
  id?: string;
  phoneNumber: string;
  type: PhoneNumberType;
  isPrimary?: boolean;
};

export function ContactForm({
  companyId,
  siteId,
  setAsPrimaryContact,
  contact,
  onSubmitForm
}: ContactFormProps) {
  const upsertContactMutation = trpcClient.people.upsertContact.useMutation();

  const isPrimarySiteContact =
    !!contact?.isPrimarySiteContact || setAsPrimaryContact;

  const defaultValues = {
    companyId: companyId || contact?.companyId || "",
    siteId: siteId || contact?.siteId || undefined,
    salutation: contact?.salutation || undefined,
    forename: contact?.forename || "",
    surname: contact?.surname || "",
    position: contact?.position || "",
    isPrimarySiteContact,
    dateOfBirth: contact?.dateOfBirth
      ? formatDate(contact.dateOfBirth, dateFormats.DD_MM_YYYY)
      : undefined,
    contactEmailsData:
      contact?.emails?.map(email => ({
        id: email.id,
        email: email.email,
        isPrimary: email.isPrimary
      })) || [],
    phoneNumbers:
      contact?.phoneNumbers?.map(phone => ({
        id: phone.id,
        phoneNumber: phone.phoneNumber,
        type: phone.type,
        isPrimary: phone.isPrimary
      })) || [],
    addresses:
      contact?.addresses?.map(contactAddress => ({
        id: contactAddress.id,
        address: {
          id: contactAddress.address.id,
          postcode: contactAddress.address.postcode,
          displayName: getAddressDisplayName(contactAddress.address)
        },
        movedInDate: contactAddress.movedInDate
          ? formatDate(contactAddress.movedInDate, dateFormats.DD_MM_YYYY)
          : "",
        movedOutDate: contactAddress.movedOutDate
          ? formatDate(contactAddress.movedOutDate, dateFormats.DD_MM_YYYY)
          : "",
        isCurrent: contactAddress.isCurrent ?? false,
        localId: contactAddress.id || uuidv4()
      })) || []
  } satisfies UpsertContactFormData;

  const isEditMode = !!contact;

  const form = useZodForm({
    schema: UpsertContactSchema,
    defaultValues
  });

  const [emailList, setEmailList] = useState<EmailFormData[]>(
    defaultValues.contactEmailsData
  );
  const emailContainerRef = useRef<HTMLDivElement>(null);

  const [phoneNumberList, setPhoneNumberList] = useState<PhoneNumberFormData[]>(
    defaultValues.phoneNumbers
  );
  const phoneNumberContainerRef = useRef<HTMLDivElement>(null);
  const [isPhoneNumberModalOpen, setIsPhoneNumberModalOpen] = useState(false);
  const [initialPhoneNumberValue, setInitialPhoneNumberValue] =
    useState<string>("");

  // We use contactAddressList to track the addresses for the contact
  // Using the form values directly is not stable and causes re-renders
  const [contactAddressList, setContactAddressList] = useState<
    ContactAddressFormData[]
  >(defaultValues.addresses);

  const handleAddEmail = (newEmail: string) => {
    if (!newEmail) {
      return;
    }

    const emailValidation = z.string().email().safeParse(newEmail);
    if (!emailValidation.success) {
      toast({
        title: "Invalid email address",
        description: "Please enter a valid email.",
        variant: "destructive"
      });
      return;
    }

    if (emailList.some(email => email.email === newEmail)) {
      toast({
        title: "Email already added",
        description: "This email is already in the list.",
        variant: "destructive"
      });
      return;
    }

    const isFirstEmail = emailList.length === 0;
    const newEmailData: EmailFormData = {
      email: newEmail,
      isPrimary: isFirstEmail
    };
    const updatedList = [...emailList, newEmailData];
    setEmailList(updatedList);
    form.setValue("contactEmailsData", updatedList, { shouldDirty: true });
  };

  const setPrimaryEmail = ({ id, email }: { id?: string; email?: string }) => {
    const updatedList = emailList.map(e => ({
      ...e,
      isPrimary: (e.id && e.id === id) || (!e.id && e.email === email)
    }));
    setEmailList(updatedList);
    form.setValue("contactEmailsData", updatedList, { shouldDirty: true });
  };

  const removeEmail = ({ id, email }: { id?: string; email?: string }) => {
    const updatedList = emailList.filter(
      e => !(id && e.id === id) && !(email && e.email === email)
    );
    if (!updatedList.some(e => e.isPrimary) && updatedList.length > 0) {
      const firstEmail = updatedList[0];
      if (firstEmail) {
        updatedList[0] = { ...firstEmail, isPrimary: true };
      }
    }
    setEmailList(updatedList);
    form.setValue("contactEmailsData", updatedList, { shouldDirty: true });
  };

  const phoneNumberSchema = createUkPhoneNumberSchema("Invalid phone number.");
  const handleAddPhoneNumber = (newPhoneNumber: PhoneNumberFormData) => {
    // Validate the phone number
    const phoneValidation = phoneNumberSchema.safeParse(
      newPhoneNumber.phoneNumber
    );
    if (!phoneValidation.success) {
      toast({
        title: "Invalid phone number",
        description: "Enter a valid UK phone number.",
        variant: "destructive"
      });
      return;
    }

    // Check if already exists
    if (
      phoneNumberList.some(p => p.phoneNumber === newPhoneNumber.phoneNumber)
    ) {
      toast({ title: "Phone number already added", variant: "destructive" });
      return;
    }

    // If it's the first phone number, make it primary by default
    const isFirstPhoneNumber = phoneNumberList.length === 0;
    const updatedList = [
      ...phoneNumberList,
      { ...newPhoneNumber, isPrimary: isFirstPhoneNumber }
    ];
    setPhoneNumberList(updatedList);
    form.setValue("phoneNumbers", updatedList, { shouldDirty: true });
  };

  const setPrimaryPhoneNumber = ({
    id,
    phoneNumber
  }: { id?: string; phoneNumber?: string }) => {
    const updatedList = phoneNumberList.map(p => ({
      ...p,
      isPrimary:
        (p.id && p.id === id) || (!p.id && p.phoneNumber === phoneNumber)
    }));
    setPhoneNumberList(updatedList);
    form.setValue("phoneNumbers", updatedList, { shouldDirty: true });
  };

  const removePhoneNumber = ({
    id,
    phoneNumber
  }: { id?: string; phoneNumber?: string }) => {
    const updatedList = phoneNumberList.filter(
      p =>
        !(id && p.id === id) && !(phoneNumber && p.phoneNumber === phoneNumber)
    );
    if (!updatedList.some(p => p.isPrimary) && updatedList.length > 0) {
      const firstPhone = updatedList[0];
      if (firstPhone) {
        updatedList[0] = { ...firstPhone, isPrimary: true };
      }
    }
    setPhoneNumberList(updatedList);
    form.setValue("phoneNumbers", updatedList, { shouldDirty: true });
  };

  const editPhoneNumber = (updatedPhone: PhoneNumberFormData) => {
    // We can find the phone by its ID if available, otherwise match by phoneNumber
    const updatedList = phoneNumberList.map(p => {
      const matchId = updatedPhone.id && p.id === updatedPhone.id;
      const matchNumber =
        !updatedPhone.id && p.phoneNumber === updatedPhone.phoneNumber;
      if (matchId || matchNumber) {
        return { ...updatedPhone };
      }
      return p;
    });

    setPhoneNumberList(updatedList);
    form.setValue("phoneNumbers", updatedList, { shouldDirty: true });
  };

  const handleFormSubmit = async () => {
    try {
      const response = await upsertContactMutation.mutateAsync({
        id: contact?.id,
        ...form.getValues(),
        contactEmailsData: emailList,
        phoneNumbers: phoneNumberList
      });

      if (!response) {
        toast({
          title: "Unable to add contact",
          description: "An error occurred while adding the contact.",
          variant: "destructive"
        });
        return;
      }

      onSubmitForm(response);
      form.reset(defaultValues);

      toast({
        title: isEditMode ? "Contact updated" : "New contact added",
        description: isEditMode
          ? "The contact has been updated successfully."
          : "The contact has been added successfully.",
        variant: "success"
      });
    } catch (e) {
      const error = e as Error;
      log.error("Failed to perform contact mutation", error);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? isEditMode
            ? "Error updating contact. Check the form and try again."
            : "Error adding new contact. Check the form and try again."
          : error.message;
      toast({
        title: isEditMode
          ? "Unable to update contact"
          : "Unable to add new contact",
        description,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleFormSubmit}
      className="my-4 space-y-4 overflow-x-hidden px-1"
    >
      <FormField
        control={form.control}
        name="salutation"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                value={field.value}
              >
                <SelectTrigger
                  className={cn(!field.value && "text-muted-foreground italic")}
                >
                  <SelectValue placeholder="Select Title" />
                </SelectTrigger>

                <SelectContent position="popper">
                  {Object.keys(Salutation).map(key => (
                    <SelectItem key={key} value={key}>
                      {humanize(key)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="forename"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Forename *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="Enter Forename"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="surname"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Surname *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="Enter Surname"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="position"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Position *</FormLabel>
            <FormControl>
              <Input
                {...field}
                className={cn(!field.value && "text-muted-foreground italic")}
                placeholder="Enter Position"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="dateOfBirth"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Date of Birth</FormLabel>
            <DatePickerInlineInput
              {...field}
              placeholder="Enter Date of Birth (DD/MM/YYYY)"
              calendarProps={{
                toDate: new Date(),
                captionLayout: "dropdown-buttons",
                fromYear: 1920,
                toYear: new Date().getFullYear(),
                disabled: { after: new Date() }
              }}
            />
            <FormMessage />
          </FormItem>
        )}
      />

      {!isPrimarySiteContact && (
        <FormField
          control={form.control}
          name="isPrimarySiteContact"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Key Contact</FormLabel>
              <FormControl>
                <div className="space-y-4 rounded-md border p-4">
                  <div className="flex items-center justify-between gap-2">
                    <div className="space-y-0.5">
                      <FormLabel className="text-xs">
                        Mark this Contact as Key Contact
                      </FormLabel>
                      <FormDescription className="text-xs">
                        Turn on the toggle if this person is the primary contact
                        for this site.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={value => {
                          form.setValue("isPrimarySiteContact", value, {
                            shouldDirty: true
                          });
                        }}
                        className="data-[state=checked]:bg-secondary"
                      />
                    </FormControl>
                    <FormMessage />
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="contactEmailsData"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Emails *</FormLabel>
            <FormControl>
              <div ref={emailContainerRef} className="flex w-full flex-col">
                <Combobox>
                  <ComboboxTrigger
                    fieldValue={field.value.map(email => email.email) ?? []}
                    placeholder="Add one or more email addresses"
                  />
                  <ComboboxContent
                    placeholder="Type to add email address"
                    container={emailContainerRef.current}
                    allowAddItem={true}
                    onAddItem={handleAddEmail}
                  >
                    <ComboboxGroup>
                      {emailList.map(item => (
                        <ComboboxItem key={item.id || item.email}>
                          <div className="flex w-full items-center justify-between">
                            <div className="flex items-center">
                              {emailList.length > 1 && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <button
                                        type="button"
                                        onClick={() =>
                                          setPrimaryEmail({
                                            id: item.id,
                                            email: item.email
                                          })
                                        }
                                        className="mr-2"
                                      >
                                        <Star
                                          className={cn(
                                            "h-4 w-4",
                                            item.isPrimary
                                              ? "fill-yellow-500 text-yellow-500"
                                              : "text-gray-400"
                                          )}
                                        />
                                      </button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      Primary email for correspondences
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                              <span>{item.email}</span>
                            </div>
                            <Button
                              type="button"
                              variant="link"
                              className="h-4 p-2"
                              onClick={() =>
                                removeEmail({
                                  id: item.id,
                                  email: item.email
                                })
                              }
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </ComboboxItem>
                      ))}
                    </ComboboxGroup>
                  </ComboboxContent>
                </Combobox>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="phoneNumbers"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Phone Numbers</FormLabel>
            <FormControl>
              <div
                ref={phoneNumberContainerRef}
                className="flex w-full flex-col"
              >
                <ComboboxLookup
                  open={isPhoneNumberModalOpen}
                  onOpenChange={setIsPhoneNumberModalOpen}
                >
                  <ComboboxLookupTrigger
                    fieldValue={
                      phoneNumberList.map(
                        phone =>
                          `${formatPhoneNumber(phone.phoneNumber)} - ${humanize(
                            phone.type
                          )}`
                      ) ?? []
                    }
                    placeholder="Add one or more phone numbers"
                  />
                  <ComboboxLookupContent
                    placeholder="Type to add phone number"
                    container={phoneNumberContainerRef.current}
                    onSearchInputChange={setInitialPhoneNumberValue}
                  >
                    <ComboboxLookupGroup>
                      {phoneNumberList.map(item => (
                        <ComboboxLookupItem
                          key={item.id || item.phoneNumber}
                          onSelect={() => {}}
                        >
                          <div className="flex w-full items-center justify-between">
                            <div className="flex items-center space-x-2">
                              {phoneNumberList.length > 1 && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <button
                                        type="button"
                                        onClick={() =>
                                          setPrimaryPhoneNumber({
                                            id: item.id,
                                            phoneNumber: item.phoneNumber
                                          })
                                        }
                                        className="mr-2"
                                      >
                                        <Star
                                          className={cn(
                                            "h-4 w-4",
                                            item.isPrimary
                                              ? "fill-yellow-500 text-yellow-500"
                                              : "text-gray-400"
                                          )}
                                        />
                                      </button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      Primary phone number
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}

                              <span>{formatPhoneNumber(item.phoneNumber)}</span>
                              <Badge>{humanize(item.type)}</Badge>
                            </div>
                            <div className="flex items-center">
                              {/* EDIT BUTTON */}
                              <AddNewPhoneNumberTrigger
                                existingPhoneNumber={item}
                                onSubmit={updatedPhoneNumber =>
                                  editPhoneNumber(updatedPhoneNumber)
                                }
                              >
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="text-foreground hover:bg-muted"
                                >
                                  <Pen className="h-4 w-4" />
                                </Button>
                              </AddNewPhoneNumberTrigger>

                              {/* DELETE BUTTON */}
                              <Button
                                type="button"
                                variant="link"
                                className="h-4 p-2"
                                onClick={() =>
                                  removePhoneNumber({
                                    id: item.id,
                                    phoneNumber: item.phoneNumber
                                  })
                                }
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>
                          </div>
                        </ComboboxLookupItem>
                      ))}
                    </ComboboxLookupGroup>

                    {/* This is the "Add" scenario */}
                    <AddNewPhoneNumberTrigger
                      onSubmit={(newPhoneNumber: PhoneNumberFormData) => {
                        handleAddPhoneNumber(newPhoneNumber);
                        setIsPhoneNumberModalOpen(false);
                      }}
                      initialPhoneNumber={initialPhoneNumberValue}
                    />
                  </ComboboxLookupContent>
                </ComboboxLookup>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="addresses"
        render={() => (
          <FormItem className="flex flex-col">
            <FormLabel>Addresses</FormLabel>
            <FormControl>
              <DynamicAddressFormList
                contactAddresses={contactAddressList}
                updateContactAddresses={setContactAddressList}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <Button
        disabled={upsertContactMutation.isPending || !form.formState.isDirty}
        type="submit"
        variant="secondary"
        className="button-click-animation w-full"
      >
        {upsertContactMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        Confirm
      </Button>
    </FormWrapper>
  );
}
