import type {
  CompanyContactEmail,
  CompanyContactPhoneNumber
} from "@prisma/client";
import { getPrimaryEmail } from "@watt/api/src/utils/get-primary-email";
import { getPrimaryPhoneNumber } from "@watt/api/src/utils/get-primary-phone-number";
import { Mail, PhoneCall, UserCheck } from "lucide-react";

interface ContactInformationProps {
  contact?: {
    forename: string;
    surname: string;
    emails: CompanyContactEmail[];
    phoneNumbers: CompanyContactPhoneNumber[];
  };
}

export function ContactInformation({ contact }: ContactInformationProps) {
  if (!contact) {
    return null;
  }

  const primaryEmail = getPrimaryEmail(contact.emails);
  const primaryPhone = getPrimaryPhoneNumber(contact.phoneNumbers);

  return (
    <div className="my-2 mb-4 flex flex-col space-y-2 lg:my-2 lg:flex-row lg:space-x-6 lg:space-y-0">
      <div className="flex items-center">
        <UserCheck className="mr-2 h-4 w-4 sm:mr-4" />
        <span className="whitespace-nowrap">
          {contact.forename} {contact.surname}
        </span>
      </div>

      {primaryPhone && (
        <div className="flex items-center">
          <PhoneCall className="mr-2 h-4 w-4 sm:mr-4" />
          <span className="whitespace-nowrap">{primaryPhone}</span>
        </div>
      )}

      {primaryEmail && (
        <div className="flex items-center">
          <Mail className="mr-2 h-4 w-4 sm:mr-4" />
          <span className="whitespace-nowrap">{primaryEmail}</span>
        </div>
      )}
    </div>
  );
}
