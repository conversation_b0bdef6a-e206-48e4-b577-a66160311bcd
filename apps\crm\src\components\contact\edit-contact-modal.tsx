"use client";

import { useState } from "react";

import type { Contact } from "@watt/api/src/types/people";
import { ContactForm } from "@watt/crm/components/contact/contact-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@watt/crm/components/ui/dialog";

type EditContactModalProps = {
  children: React.ReactNode;
  contact: Contact;
  className?: string;
  onSubmit?: (contact: Contact) => void;
};

export function EditContactModal(props: EditContactModalProps) {
  const [openEditModal, setOpenEditModal] = useState(false);
  const handleSubmitContact = (contact: Contact) => {
    setOpenEditModal(false);
    props.onSubmit?.(contact);
  };

  return (
    <Dialog open={openEditModal} onOpenChange={setOpenEditModal}>
      <DialogTrigger asChild>{props.children}</DialogTrigger>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader className="space-y-4">
          <DialogTitle>Edit Contact Person</DialogTitle>
          <DialogDescription className="italic">
            Please update the contact details when the new information is
            provided by the customer.
          </DialogDescription>
        </DialogHeader>
        <ContactForm
          onSubmitForm={handleSubmitContact}
          contact={props.contact}
        />
      </DialogContent>
    </Dialog>
  );
}
