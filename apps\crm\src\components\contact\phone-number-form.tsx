import { createUkPhoneNumberSchema } from "@watt/api/src/types/people";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { PhoneNumberType } from "@watt/db/src/enums";
import { useEffect } from "react";
import { z } from "zod";

type PhoneNumberFormProps = {
  onSubmitForm: (data: {
    id?: string;
    phoneNumber: string;
    type: PhoneNumberType;
    isPrimary?: boolean;
  }) => void;
  existingPhoneNumber?: {
    id?: string;
    phoneNumber: string;
    type: PhoneNumberType;
    isPrimary?: boolean;
  };
  initialPhoneNumber?: string;
};

const PhoneNumberSchema = z.object({
  phoneNumber: createUkPhoneNumberSchema("Invalid phone number").pipe(
    z.string().min(1, "Required")
  ),
  type: z.nativeEnum(PhoneNumberType)
});

export function PhoneNumberForm({
  onSubmitForm,
  existingPhoneNumber,
  initialPhoneNumber
}: PhoneNumberFormProps) {
  const form = useZodForm({
    schema: PhoneNumberSchema,
    defaultValues: {
      phoneNumber: existingPhoneNumber?.phoneNumber || "",
      type: existingPhoneNumber?.type || PhoneNumberType.OTHER
    }
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: We only want to set this value once on mount
  useEffect(() => {
    if (initialPhoneNumber) {
      form.setValue("phoneNumber", initialPhoneNumber, {
        shouldDirty: true
      });
    }
  }, []);

  const { isDirty } = form.formState;

  const handleFormSubmit = (data: z.infer<typeof PhoneNumberSchema>) => {
    onSubmitForm({
      ...data,
      id: existingPhoneNumber?.id
    });
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleFormSubmit}
      className="space-y-4"
    >
      <FormField
        control={form.control}
        name="phoneNumber"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Phone Number *</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder="Enter phone number"
                className={cn(!field.value && "text-muted-foreground italic")}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="type"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Type *</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(PhoneNumberType).map(type => (
                    <SelectItem key={type} value={type}>
                      {humanize(type)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <Button
        type="submit"
        variant="secondary"
        disabled={!isDirty}
        className="w-full"
      >
        Confirm
      </Button>
    </FormWrapper>
  );
}
