"use client";

import { Loader2 } from "lucide-react";

import { TRPCClientError } from "@trpc/client";
import { ContractPeriodCustomQuoteFormSchema } from "@watt/api/src/types/custom-quote";
import { trpcClient } from "@watt/crm/utils/api";
import { useEffect, useMemo, useRef, useState } from "react";

import { QuoteType, UtilityType } from "@prisma/client";
import type {
  GetCustomQuoteDataById,
  UpdateContractPeriodCustomQuote
} from "@watt/api/src/router";
import udProvidersList from "@watt/common/src/constants/ud-providers-list.json";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  convertLocalDateToUTCString,
  convertUTCStringToLocalDate,
  dateFormats,
  formatDate
} from "@watt/common/src/utils/format-date";
import type { TariffRates } from "@watt/common/src/utils/split-usage-by-rate";
import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { SuffixInput } from "@watt/crm/components/suffix-input";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { Switch } from "@watt/crm/components/ui/switch";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { addDays, addYears } from "date-fns";
import { DatePickerInput } from "../date-picker-input";

const ONE_YEAR_IN_MONTHS = 12;
const CONTRACT_END_DATE_OFFSET = -1;

export type ContractPeriodCustomQuoteFormProps = {
  tariffRates: TariffRates;
  showCapacityCharge: boolean;
  customQuoteData: GetCustomQuoteDataById["customQuote"];
  onSubmitForm: (updatedContractData: UpdateContractPeriodCustomQuote) => void;
};

export function ContractPeriodCustomQuoteForm({
  tariffRates,
  showCapacityCharge,
  customQuoteData,
  onSubmitForm
}: ContractPeriodCustomQuoteFormProps) {
  const supplierContainerRef = useRef<HTMLDivElement>(null);
  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);
  const [supplierSearch, setSupplierSearch] = useState("");
  const customQuoteMutation =
    trpcClient.customQuote.updateContractPeriodCustomQuote.useMutation();
  const isEditCustomQuote = useMemo(() => !!customQuoteData, [customQuoteData]);

  const form = useZodForm({
    schema: ContractPeriodCustomQuoteFormSchema,
    defaultValues: {
      quoteListId: customQuoteData.quoteList.id,
      contractId: customQuoteData.contract?.id ?? "",
      customQuoteId: customQuoteData.id ?? "",
      utilityType:
        customQuoteData.utilityType === UtilityType.ELECTRICITY
          ? UtilityType.ELECTRICITY
          : UtilityType.GAS,
      providerName: customQuoteData.provider.displayName ?? "",
      contractType:
        customQuoteData.electricQuote?.contractType ??
        customQuoteData.gasQuote?.contractType ??
        "",
      durationInYears: customQuoteData.duration / ONE_YEAR_IN_MONTHS,
      contractStartDate: formatDate(
        customQuoteData.quoteList.contractStartDate,
        dateFormats.YYYY_MM_DD_HYPHEN
      ),
      contractEndDate: formatDate(
        customQuoteData.endDate,
        dateFormats.YYYY_MM_DD_HYPHEN
      ),
      unitRate:
        customQuoteData.electricQuote?.unitRate ??
        customQuoteData.gasQuote?.unitRate ??
        (!tariffRates.day ? 0 : undefined),
      nightUnitRate:
        customQuoteData.electricQuote?.nightUnitRate ??
        (!tariffRates.night ? 0 : undefined),
      weekendUnitRate:
        customQuoteData.electricQuote?.weekendUnitRate ??
        (!tariffRates.weekend ? 0 : undefined),
      standingCharge:
        customQuoteData.electricQuote?.standingCharge ??
        customQuoteData.gasQuote?.standingCharge ??
        undefined,
      upliftRate:
        customQuoteData.electricQuote?.unitRateUplift ??
        customQuoteData.gasQuote?.unitRateUplift ??
        undefined,
      ratesIncludeUplift: !!customQuoteData, // All custom quotes after the form is submitted have rates with uplift included so we set this value to true
      capacityChargeKva: !showCapacityCharge
        ? 0
        : customQuoteData.electricQuote?.capacityChargeKva
          ? Number.parseInt(customQuoteData.electricQuote?.capacityChargeKva)
          : undefined,
      standingChargeUplift:
        customQuoteData?.electricQuote?.standingChargeUplift ??
        customQuoteData?.gasQuote?.standingChargeUplift ??
        undefined,
      isBespokeQuote: customQuoteData?.type === QuoteType.BESPOKE
    }
  });

  const rateIncludeUpliftWatch = form.watch("ratesIncludeUplift");
  const contractStartDateWatch = form.watch("contractStartDate");
  const durationInYearsWatch = form.watch("durationInYears");

  // Update contract end date when contract start date or durationInYears changes
  useEffect(() => {
    if (contractStartDateWatch && durationInYearsWatch) {
      form.setValue(
        "contractEndDate",
        formatDate(
          addDays(
            addYears(new Date(contractStartDateWatch), durationInYearsWatch),
            CONTRACT_END_DATE_OFFSET // Contract ends 1 day before the end of the last year
          ),
          dateFormats.YYYY_MM_DD_HYPHEN
        ),
        {
          shouldDirty: true
        }
      );
    }
  }, [contractStartDateWatch, durationInYearsWatch, form]);

  const ratesHelperText = useMemo(() => {
    return rateIncludeUpliftWatch ? "(includes uplift)" : "(base rate)";
  }, [rateIncludeUpliftWatch]);

  const toggleHelperText = useMemo(() => {
    return rateIncludeUpliftWatch
      ? "If set to true, rates already include uplift."
      : "If set to false, uplift will be added to base rate upon confirmation.";
  }, [rateIncludeUpliftWatch]);

  const isBespokeQuoteWatch = form.watch("isBespokeQuote");
  const bespokeDescriptionText = useMemo(() => {
    return isBespokeQuoteWatch
      ? "This quote is a bespoke quote and not a normal flat file quote."
      : "This quote is a normal quote generated by a flat file and is not a bespoke quote.";
  }, [isBespokeQuoteWatch]);

  const handleFormSubmit = async () => {
    try {
      const upliftRate = form.getValues().upliftRate;
      const standingChargeUplift = form.getValues().standingChargeUplift;
      const hasValidUpliftRate = upliftRate && !Number.isNaN(upliftRate);
      const hasValidStandingChargeUplift =
        standingChargeUplift && !Number.isNaN(standingChargeUplift);

      // Checks at least one uplift rate or standing charge uplift rate is provided
      if (!hasValidUpliftRate && !hasValidStandingChargeUplift) {
        toast({
          title: "Missing Uplift Input",
          description:
            "Enter a value for Unit Rate Uplift or Standing Charge Uplift to proceed.",
          variant: "destructive",
          duration: 10000
        });
        return;
      }

      const result = await customQuoteMutation.mutateAsync(form.getValues());
      toast({
        title: "Custom quote updated successfully",
        description: `Custom quote for ${form.getValues().providerName} has been updated successfully.`,
        variant: "success"
      });
      onSubmitForm(result);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while updating a custom quote. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to update custom quote",
        description,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  return (
    <div className="flex flex-col space-y-4">
      <FormWrapper
        form={form}
        handleSubmit={handleFormSubmit}
        className="space-y-4"
      >
        <div className="my-4 max-h-[600px] space-y-4 overflow-auto px-4 py-4">
          <FormField
            control={form.control}
            name="providerName"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Supplier Name *</FormLabel>
                <FormControl>
                  <div
                    ref={supplierContainerRef}
                    className="flex w-full flex-col"
                  >
                    <LookUp
                      open={isSupplierModalOpen}
                      onOpenChange={setIsSupplierModalOpen}
                    >
                      <LookUpTrigger fieldValue={field.value}>
                        <span className="font-normal">{field.value}</span>
                      </LookUpTrigger>
                      <LookUpContent
                        placeholder="Search provider..."
                        searchInput={supplierSearch}
                        onSearchInputChange={currentSupplier => {
                          setSupplierSearch(currentSupplier);
                          if (!currentSupplier) {
                            form.resetField("providerName");
                          }
                        }}
                        shouldFilter={true}
                        container={supplierContainerRef.current}
                      >
                        <LookUpGroup>
                          {udProvidersList.map(providerName => (
                            <LookUpItem
                              value={providerName}
                              key={providerName}
                              onSelect={() => {
                                form.setValue("providerName", providerName);
                                setIsSupplierModalOpen(false);
                              }}
                            >
                              {providerName}
                            </LookUpItem>
                          ))}
                        </LookUpGroup>
                      </LookUpContent>
                    </LookUp>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="contractType"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Product Type</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="durationInYears"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Term *</FormLabel>
                <FormControl>
                  <SuffixInput
                    {...field}
                    value={field.value === undefined ? "" : field.value}
                    suffix="year"
                    type="number"
                    onWheel={e => e.currentTarget.blur()}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contractStartDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Contract Start Date *</FormLabel>
                <DatePickerInput
                  date={
                    field.value
                      ? convertUTCStringToLocalDate(field.value)
                      : undefined
                  }
                  placeholder="Select the start date"
                  setDate={date => {
                    if (!date) {
                      return;
                    }
                    form.setValue(
                      "contractStartDate",
                      convertLocalDateToUTCString(date),
                      {
                        shouldDirty: true
                      }
                    );
                  }}
                  calendarProps={{
                    fromDate: new Date()
                  }}
                />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="contractEndDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Contract End Date</FormLabel>
                <DatePickerInput
                  date={
                    field.value
                      ? convertUTCStringToLocalDate(field.value)
                      : undefined
                  }
                  placeholder="Select the end date"
                  setDate={() => null}
                  calendarProps={{
                    fromDate: new Date()
                  }}
                  disabled
                />
              </FormItem>
            )}
          />
          {!!tariffRates.day && (
            <FormField
              control={form.control}
              name="unitRate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>
                    Day Rate *{" "}
                    <span className="text-muted-foreground">
                      {ratesHelperText}
                    </span>
                  </FormLabel>
                  <FormControl>
                    <SuffixInput
                      {...field}
                      value={field.value === undefined ? "" : field.value}
                      suffix="pence/kWh"
                      type="number"
                      onWheel={e => e.currentTarget.blur()}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {!!tariffRates.night && (
            <FormField
              control={form.control}
              name="nightUnitRate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>
                    Night Rate *{" "}
                    <span className="text-muted-foreground">
                      {ratesHelperText}
                    </span>
                  </FormLabel>
                  <FormControl>
                    <SuffixInput
                      {...field}
                      value={field.value === undefined ? "" : field.value}
                      suffix="pence/kWh"
                      type="number"
                      onWheel={e => e.currentTarget.blur()}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {!!tariffRates.weekend && (
            <FormField
              control={form.control}
              name="weekendUnitRate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>
                    Evening/Weekend Rate *{" "}
                    <span className="text-muted-foreground">
                      {ratesHelperText}
                    </span>
                  </FormLabel>
                  <FormControl>
                    <SuffixInput
                      {...field}
                      value={field.value === undefined ? "" : field.value}
                      suffix="pence/kWh"
                      type="number"
                      onWheel={e => e.currentTarget.blur()}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {showCapacityCharge && (
            <FormField
              control={form.control}
              name="capacityChargeKva"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Capacity Charge *</FormLabel>
                  <FormControl>
                    <SuffixInput
                      {...field}
                      value={field.value === undefined ? "" : field.value}
                      type="number"
                      suffix="pence/day"
                      onWheel={e => e.currentTarget.blur()}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          <FormField
            control={form.control}
            name="upliftRate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Unit Rate Uplift</FormLabel>
                <FormControl>
                  <SuffixInput
                    {...field}
                    value={field.value === undefined ? "" : field.value}
                    suffix="pence/kWh"
                    type="number"
                    step="0.01"
                    onWheel={e => e.currentTarget.blur()}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="standingCharge"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Standing Charge *</FormLabel>
                <FormControl>
                  <SuffixInput
                    {...field}
                    value={field.value === undefined ? "" : field.value}
                    suffix="pence/day"
                    type="number"
                    onWheel={e => e.currentTarget.blur()}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="standingChargeUplift"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Uplift Rate *</FormLabel>
                <FormControl>
                  <SuffixInput
                    {...field}
                    value={field.value === undefined ? "" : field.value}
                    suffix="pence/day"
                    type="number"
                    step="0.01"
                    onWheel={e => e.currentTarget.blur()}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="ratesIncludeUplift"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Rates already include uplift *</FormLabel>
                <div className="flex items-center justify-between gap-2 rounded-md border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-xs">
                      Turn on Include Uplift Toggle
                    </FormLabel>
                    <FormDescription className="text-xs">
                      {toggleHelperText}
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={value => {
                        form.setValue("ratesIncludeUplift", value);
                      }}
                      className="data-[state=checked]:bg-secondary"
                    />
                  </FormControl>
                  <FormMessage />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          {!isEditCustomQuote && (
            <FormField
              control={form.control}
              name="isBespokeQuote"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Bespoke *</FormLabel>
                  <div className="flex items-center justify-between gap-2 rounded-md border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-xs">
                        Turn on if the quote is bespoke
                      </FormLabel>
                      <FormDescription className="text-xs">
                        {bespokeDescriptionText}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={value => {
                          form.setValue("isBespokeQuote", value);
                        }}
                        className="data-[state=checked]:bg-secondary"
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <Button
          type="submit"
          variant="secondary"
          className="button-click-animation w-full"
          disabled={customQuoteMutation.isPending}
        >
          {customQuoteMutation.isPending && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          Confirm
        </Button>
      </FormWrapper>
    </div>
  );
}
