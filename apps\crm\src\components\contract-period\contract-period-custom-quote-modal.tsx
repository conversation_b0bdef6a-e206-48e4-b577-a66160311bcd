import { PencilLine } from "lucide-react";
import { useMemo, useState } from "react";

import { Button } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@watt/crm/components/ui/dialog";

import type { UpdateContractPeriodCustomQuote } from "@watt/api/src/router";
import { trpcClient } from "@watt/crm/utils/api";
import { Skeleton } from "../ui/skeleton";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { ContractPeriodCustomQuoteForm } from "./contract-period-custom-quote-form";

export type ContractPeriodCustomQuoteModalProps = {
  customQuoteId: string;
  onSubmitForm: (updatedContractData: UpdateContractPeriodCustomQuote) => void;
  isDisabled: boolean;
};

export function ContractPeriodCustomQuoteModal({
  customQuoteId,
  onSubmitForm,
  isDisabled
}: ContractPeriodCustomQuoteModalProps) {
  const [customQuoteModalOpen, setCustomQuoteModalOpen] = useState(false);
  // Only show the tooltip if the quote is a UD quote
  const showToolTip = isDisabled ? undefined : false;

  const handleSubmitForm = (
    updatedContractData: UpdateContractPeriodCustomQuote
  ) => {
    onSubmitForm(updatedContractData);
    setCustomQuoteModalOpen(false);
  };
  return (
    <Dialog onOpenChange={setCustomQuoteModalOpen} open={customQuoteModalOpen}>
      <Tooltip open={showToolTip}>
        <TooltipTrigger asChild>
          <DialogTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              disabled={isDisabled}
            >
              <PencilLine className="size-4" />
              <span className="sr-only fixed">Edit Contract Period</span>
            </Button>
          </DialogTrigger>
        </TooltipTrigger>
        <TooltipContent className="max-w-80">
          UD quotes cannot be edited as they are system-generated. If the quote
          details are incorrect, please use the Requote button to cancel and
          select a new quote.
        </TooltipContent>
      </Tooltip>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        className="flex max-w-2xl flex-col px-2"
      >
        <DialogHeader className="space-y-4 px-4">
          <DialogTitle>Edit Custom Quote</DialogTitle>
          <DialogDescription className="space-y-1 italic">
            Edit the custom quote for this quote list.
          </DialogDescription>
        </DialogHeader>
        <ContractPeriodCustomQuoteContainer
          customQuoteId={customQuoteId}
          onSubmitForm={handleSubmitForm}
        />
      </DialogContent>
    </Dialog>
  );
}

function ContractPeriodCustomQuoteContainer({
  customQuoteId,
  onSubmitForm
}: Omit<ContractPeriodCustomQuoteModalProps, "isDisabled">) {
  const { data, isLoading } =
    trpcClient.customQuote.getCustomQuoteById.useQuery(
      {
        customQuoteId: customQuoteId
      },
      {
        refetchOnWindowFocus: false
      }
    );

  const showCapacityCharge = useMemo(
    () =>
      data?.additionalMeterData?.profileClass === "00" &&
      data.additionalMeterData?.measurementClass.toUpperCase() !== "G",
    [data?.additionalMeterData]
  );

  if (isLoading) {
    return <Skeleton className="h-80 w-full" />;
  }

  if (!data) {
    return <div className="text-center">Error fetching custom quote data</div>;
  }

  return (
    <ContractPeriodCustomQuoteForm
      customQuoteData={data.customQuote}
      tariffRates={data.tariffRates}
      showCapacityCharge={showCapacityCharge}
      onSubmitForm={onSubmitForm}
    />
  );
}
