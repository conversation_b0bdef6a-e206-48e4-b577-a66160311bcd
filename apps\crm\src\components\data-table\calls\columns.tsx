"use client";

import { Info } from "lucide-react";

import type { ColumnDef } from "@tanstack/react-table";
import {
  directions,
  sources,
  statuses
} from "@watt/crm/common-data/calls/data";
import { useAppStore } from "@watt/crm/store/app-store";
import { copyToClipboard } from "@watt/crm/utils/copy";
import { formatDurationMinuteSeconds } from "@watt/crm/utils/format-time-duration";
import type { CompleteCallInstance } from "@watt/db/prisma/zod";
import type { CallStatus } from "@watt/db/src/enums";

import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";

import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { formatPhoneNumber } from "@watt/common/src/utils/format-phone-number";
import { humanize } from "@watt/common/src/utils/humanize-string";
import {
  dateFilter,
  durationFilter,
  textFilter
} from "@watt/crm/components/data-table/data-table-filter-functions";
import { DataTableRowActions } from "./data-table-row-actions";

const RecordingStatusMessages: Record<CallStatus, string> = {
  QUEUED: "Waiting...",
  RINGING: "Ringing...",
  IN_PROGRESS: "Recording...",
  COMPLETED: "No recordings",
  BUSY: "N/A",
  FAILED: "N/A",
  NO_ANSWER: "N/A",
  CANCELED: "N/A"
};

export const columns: ColumnDef<CompleteCallInstance>[] = [
  {
    accessorKey: "sid",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Call ID" disableSorting />
    ),
    cell: ({ row }) => <div>{row.getValue("sid")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Call ID"
    }
  },
  {
    accessorKey: "parentCallSid",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Parent Call ID"
        disableSorting
      />
    ),
    cell: ({ row }) => <div>{row.getValue("parentCallSid")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Parent Call ID"
    }
  },
  {
    accessorKey: "dateCreated",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Date & Duration"
        disableSorting
      />
    ),
    cell: ({ row }) => {
      const duration = row.original.duration;
      const dateCreated = formatDate(
        row.original.dateCreated,
        dateFormats.DD_MM_YYYY_HH_MM
      );

      return (
        <div>
          {dateCreated} <br />
          <span className="text-muted-foreground">
            {formatDurationMinuteSeconds(Number.parseInt(duration))} sec
          </span>
        </div>
      );
    },
    filterFn: dateFilter,
    meta: {
      dropdownLabel: "Date & Duration"
    }
  },
  {
    accessorKey: "from",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="From" disableSorting />
    ),
    cell: ({ row }) => {
      const { profileData } = useAppStore(state => state.userData);
      const directDialE164 = profileData?.directDialE164;
      const fromIdentity = row.original.fromIdentity;
      const fromPhoneNumber = row.getValue("from") as string;

      return (
        <div className="flex max-w-[25ch] flex-wrap items-center gap-1 overflow-hidden text-ellipsis">
          <span className="whitespace-nowrap">
            {formatPhoneNumber(fromPhoneNumber)}
          </span>
          {(fromPhoneNumber === directDialE164 || fromIdentity) && (
            <Badge>
              {fromPhoneNumber === directDialE164 ? "You" : fromIdentity}
            </Badge>
          )}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "From"
    }
  },
  {
    accessorKey: "to",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="To" disableSorting />
    ),
    cell: ({ row }) => {
      const { profileData } = useAppStore(state => state.userData);
      const directDialE164 = profileData?.directDialE164;
      const toIdentity = row.original.toIdentity;
      const toPhoneNumber = row.getValue("to") as string;

      return (
        <div className="flex max-w-[25ch] flex-wrap items-center gap-1 overflow-hidden text-ellipsis">
          <span className="whitespace-nowrap">
            {formatPhoneNumber(toPhoneNumber)}
          </span>
          {(toPhoneNumber === directDialE164 || toIdentity) && (
            <Badge>
              {toPhoneNumber === directDialE164 ? "You" : toIdentity}
            </Badge>
          )}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "To"
    }
  },
  {
    accessorKey: "direction",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Direction" disableSorting />
    ),
    cell: ({ row }) => {
      const direction = directions.find(
        direction => direction.value === row.getValue("direction")
      );

      if (!direction) {
        return null;
      }

      return (
        <div className="flex items-center">
          {direction.icon && (
            <direction.icon className="mr-2 h-4 w-4 text-muted-foreground" />
          )}
          <span>{direction.label}</span>
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Direction"
    }
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" disableSorting />
    ),
    cell: ({ row }) => {
      const status = statuses.find(
        status => status.value === row.getValue("status")
      );

      if (!status) {
        return null;
      }

      return (
        <div className="flex items-center">
          {status.icon && (
            <status.icon className="mr-2 h-4 w-4 text-muted-foreground" />
          )}
          <span>{status.label}</span>
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status"
    }
  },
  {
    accessorKey: "duration",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Duration" disableSorting />
    ),
    cell: ({ row }) => (
      <div>{formatDurationMinuteSeconds(row.getValue("duration"))} sec</div>
    ),
    filterFn: durationFilter,
    meta: {
      dropdownLabel: "Duration"
    }
  },
  {
    accessorKey: "recording.mediaUrl",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Recording" disableSorting />
    ),
    cell: ({ row }) => {
      const recordings = row.original.recordings;
      const status = row.original.status;

      if (!recordings.length) {
        return RecordingStatusMessages[status];
      }

      return (
        <div className="gap-4">
          {recordings
            // sort the recordings by dateCreated
            .sort((a, b) => {
              return (
                new Date(b.dateCreated).getTime() -
                new Date(a.dateCreated).getTime()
              );
            })
            .map(recording => {
              const mediaUrl = recording.mediaUrl;
              // const mediaUrl = recording.localMediaUrl ?? recording.mediaUrl;
              const status = statuses.find(
                status => status.value === recording.status
              );

              if (status && status?.value !== "COMPLETED") {
                return (
                  <div key={recording.sid} className="flex items-center">
                    {status.icon && (
                      <status.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span>{status.label}</span>
                  </div>
                );
              }

              if (!mediaUrl) {
                return null;
              }

              // Ensure the URL uses wav extension instead of mp3
              const wavMediaUrl = mediaUrl.includes(".mp3")
                ? mediaUrl.replace(".mp3", "")
                : mediaUrl;

              const source = sources.find(
                source => source.value === recording.source
              );

              return (
                <div
                  key={recording.sid}
                  className="mb-2 flex items-center gap-2"
                >
                  <audio controls controlsList="nodownload noplaybackrate">
                    <source src={wavMediaUrl} type="audio/mpeg" />
                    Your browser does not support the audio element.
                  </audio>
                  <div className="flex flex-col items-center justify-center gap-1 align-middle">
                    <div className="flex flex-row items-center gap-1">
                      <Badge variant="outline" className="flex items-center">
                        {source?.icon && (
                          <source.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                        )}
                        <span>
                          {source?.label ?? humanize(recording.source)}
                        </span>
                      </Badge>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="none"
                              className="h-0 p-0"
                              onClick={() => {
                                copyToClipboard(recording.sid, "recording sid");
                              }}
                            >
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>SID: {recording.sid}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <span className="text-muted-foreground text-xs">
                      {formatDurationMinuteSeconds(
                        Number.parseInt(recording.duration)
                      )}{" "}
                      sec
                    </span>
                  </div>
                </div>
              );
            })}
        </div>
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Recording"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />
  }
];
