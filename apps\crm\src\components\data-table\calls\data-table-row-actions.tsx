"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ClipboardCopy,
  Download,
  FolderArchive,
  MoreHorizontal,
  ScrollText
} from "lucide-react";

import type { RecordingInstance } from "@prisma/client";
import type { Row } from "@tanstack/react-table";
import { log } from "@watt/common/src/utils/axiom-logger";
import { copyToClipboard } from "@watt/crm/utils/copy";
import type { CompleteCallInstance } from "@watt/db/prisma/zod";
import { saveAs } from "file-saver";
import JSZip from "jszip";

import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}

export function formatCallForCompliance(call: CompleteCallInstance): string {
  // Start with call details
  let result = "--- Call Details ---\n";
  result += `SID: ${call.sid}\n`;
  result += `Parent Call SID: ${call.parentCallSid || "N/A"}\n`;
  result += `Date Created: ${call.dateCreated.toISOString()}\n`;
  result += `From: ${call.from} (${call.fromFormatted})\n`;
  result += `To: ${call.to} (${call.toFormatted})\n`;
  result += `Status: ${call.status}\n`;
  result += `Duration: ${call.duration} seconds\n`;
  result += `Direction: ${call.direction}\n`;

  // Add recordings details
  if (call.recordings.length > 0) {
    result += "\n--- Recordings ---\n";
    call.recordings.forEach((recording, index) => {
      result += `Recording ${index + 1}:\n`;
      result += `  SID: ${recording.sid}\n`;
      result += `  Date Created: ${recording.dateCreated.toISOString()}\n`;
      result += `  Duration: ${recording.duration} seconds\n`;
      result += `  Media URL: ${recording.mediaUrl}.mp3\n`;
      // result += `  Local Media URL: ${recording.localMediaUrl}\n`;
      result += "---\n";
    });
  } else {
    result += "\nNo recordings for this call.\n";
  }

  return result;
}

const downloadSingleFile = async (url: string) => {
  const response = await fetch(url);
  const blob = await response.blob();
  saveAs(blob, url.split("/").pop() || "download.mp3"); // Use the filename from the URL or default to 'download.mp3'
};

const downloadAllFiles = async (recordings: RecordingInstance[]) => {
  try {
    if (recordings.length === 1) {
      const firstRecording = recordings[0];
      if (!firstRecording) {
        return;
      }
      downloadSingleFile(
        firstRecording.mediaUrl +
          (firstRecording.mediaUrl.includes(".mp3") ? "" : ".mp3")
      );
      return;
    }

    const zip = new JSZip();

    const fetchPromises = recordings.map(
      (recording: RecordingInstance, index: number) => {
        return fetch(recording.mediaUrl)
          .then(response => response.blob())
          .then(blob => {
            zip.file(`recording-${index + 1}.mp3`, blob);
          });
      }
    );

    await Promise.all(fetchPromises);
    const content = await zip.generateAsync({ type: "blob" });
    saveAs(content, "all-recordings.zip");
  } catch (err) {
    log.error("calls/data-table-row-actions.downloadAllFiles", { err });
    toast({
      title: "Error downloading recordings!",
      description: "There was an error downloading the recordings.",
      variant: "destructive"
    });
  }
};

export function DataTableRowActions<TData>({
  row
}: DataTableRowActionsProps<TData>) {
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  const { recordings } = row.original as any;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  const transcripts: any[] = [];

  // This function will create the submenu for downloading individual recordings
  const renderDownloadSubmenu = (recordings: RecordingInstance[]) => {
    return (
      <DropdownMenuSub>
        <DropdownMenuSubTrigger>
          <Download className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          <span>Download</span>
        </DropdownMenuSubTrigger>
        <DropdownMenuPortal>
          <DropdownMenuSubContent>
            {recordings.reverse().map((recording, index) => {
              const mediaUrlWithExtension =
                recording.mediaUrl +
                (recording.mediaUrl.includes(".mp3") ? "" : ".mp3");
              return (
                <DropdownMenuItem
                  key={recording.sid}
                  onClick={() => downloadSingleFile(mediaUrlWithExtension)}
                >
                  <CassetteTape className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                  <span>{`Recording ${index + 1}`}</span>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuSubContent>
        </DropdownMenuPortal>
      </DropdownMenuSub>
    );
  };

  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  const renderTranscribeSubmenu = (transcripts: any[]) => {
    return (
      <DropdownMenuSub>
        <DropdownMenuSubTrigger>
          <Download className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          <span>Transcripts</span>
        </DropdownMenuSubTrigger>
        <DropdownMenuPortal>
          <DropdownMenuSubContent>
            {transcripts.reverse().map((transcript, index) => {
              return (
                <DropdownMenuItem
                  key={transcript.sid}
                  onClick={() => {
                    toast({
                      title: "Coming soon!",
                      description: "Transcripts are coming soon!"
                    });
                  }}
                >
                  <ScrollText className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                  <span>{`Transcript   ${index + 1}`}</span>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuSubContent>
        </DropdownMenuPortal>
      </DropdownMenuSub>
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only fixed">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuGroup>
          {recordings.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  {renderDownloadSubmenu(recordings)}
                </TooltipTrigger>
                <TooltipContent>
                  <p>Download individual recordings</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {transcripts.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  {renderTranscribeSubmenu(transcripts)}
                </TooltipTrigger>
                <TooltipContent>
                  <p>Download individual transcripts</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {recordings.length > 0 && (
            <DropdownMenuItem onClick={() => downloadAllFiles(recordings)}>
              <FolderArchive className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
              <span>Download all</span>
            </DropdownMenuItem>
          )}

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => {
                    const callData = row.original as CompleteCallInstance;
                    const formattedData = formatCallForCompliance(callData);
                    copyToClipboard(formattedData, "call data");
                  }}
                >
                  <ClipboardCopy className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                  Copy call data
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  Puts all of the call details in your paste bin. Makes it
                  easier to add this to the CRM.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
