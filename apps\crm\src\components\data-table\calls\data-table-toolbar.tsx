"use client";

import type { Column, Table } from "@tanstack/react-table";
import {
  directions,
  durations,
  statuses
} from "@watt/crm/common-data/calls/data";
import type { DateRange } from "@watt/crm/types/date-types"; // Adjust the path as needed
import { X } from "lucide-react";
import { z } from "zod";

import {
  createDateObjectSchema,
  createZodEnumArray
} from "@watt/common/src/utils/zod-literal-union";
import { DataTableDateRangeFilter } from "@watt/crm/components/data-table/data-table-date-range-filter";
import {
  DataTableFacetedFilter,
  type DataTableOption
} from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import { trpcClient } from "@watt/crm/utils/api";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered
}: DataTableToolbarProps<TData>) {
  const { data: allDirectDials } =
    trpcClient.userProfile.getAllDirectDialsAndFullNames.useQuery();

  // As the agents in async data createZodEnumArray throws a zod literal error as the data is not available
  // therefore we insert 2 empty objects in the array to suppress the error for now
  const agentLists = allDirectDials?.length
    ? allDirectDials
    : ([{}, {}] as DataTableOption[]);

  const queryParamsSchema = z.object({
    status: createZodEnumArray(statuses),
    duration: createZodEnumArray(durations),
    direction: createZodEnumArray(directions),
    from: createZodEnumArray(agentLists),
    to: createZodEnumArray(agentLists),
    dateCreated: createDateObjectSchema(),
    search: z.string().optional()
  });

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="ml-auto flex items-center justify-between gap-2">
      <div className="flex items-center space-x-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
        />
        {table.getColumn("dateCreated") && (
          <DataTableDateRangeFilter
            // TODO switch to using 'columnHelper.accessor' as it is properly typed
            // https://tanstack.com/table/v8/docs/guide/column-defs#column-helpers
            // const columnHelper = createColumnHelper<MyQuote>();
            column={
              table.getColumn("dateCreated") as Column<
                TData,
                DateRange | undefined
              >
            }
            title="Date created"
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("duration") && (
          <DataTableFacetedFilter
            column={table.getColumn("duration")}
            title="Duration"
            options={durations}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("from") && (
          <DataTableFacetedFilter
            column={table.getColumn("from")}
            title="From"
            options={agentLists}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("to") && (
          <DataTableFacetedFilter
            column={table.getColumn("to")}
            title="To"
            options={agentLists}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("direction") && (
          <DataTableFacetedFilter
            column={table.getColumn("direction")}
            title="Direction"
            options={directions}
            onFilterChange={handleFilterChange}
          />
        )}
        {table.getColumn("status") && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={statuses}
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
