"use client";

import {
  type ColumnDef,
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { trpcClient } from "@watt/crm/utils/api";
import { useMemo, useState } from "react";
import { useDebounce } from "react-use";

import { DataTableToolbar } from "@watt/crm/components/data-table/calls/data-table-toolbar";
import { useFetchErrorToast } from "@watt/crm/hooks/use-fetch-error-toast";
import { useSlowResponseToast } from "@watt/crm/hooks/use-slow-response-toast";

import { InfiniteScrollDataTable } from "../data-table-infinite-scroll";
import { DataTableSkeleton } from "../data-table-skeleton";

enum CallsListProcedures {
  MyCallsList = "myCallsList",
  AllCallsList = "allCallsList",
  UsersCallsList = "usersCallsList"
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  isAdminCall?: boolean;
  usersEmail?: string;
}

// Bidur: Temp suppression of value type error as it causes further errors on correction
type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

export function DataTable<TData, TValue>({
  columns,
  isAdminCall,
  usersEmail = ""
}: DataTableProps<TData, TValue>) {
  const endpoint = isAdminCall
    ? CallsListProcedures.AllCallsList
    : CallsListProcedures.MyCallsList;
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    sid: false,
    parentCallSid: false,
    duration: false
  });
  const [globalFilter, setGlobalFilter] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);

  // TODO this conditional useIfiniteQuery hitting 3 different tRPC routes is ugly and causing type issues with tRPC 11.
  // Fixed it with a low effort hack but this needs more work.
  const {
    data,
    isLoading,
    fetchNextPage,
    isFetching,
    hasNextPage,
    error,
    isError
  } = usersEmail
    ? trpcClient.calls.usersCallsList.useInfiniteQuery(
        {
          email: usersEmail,
          searchFilters: {
            columnFilters: debouncedColumnFilters,
            globalFilter
          }
        },
        {
          getNextPageParam: lastPage => lastPage.nextCursor,
          refetchOnWindowFocus: false,
          refetchOnMount: false,
          placeholderData: prev => prev,
          trpc: {
            abortOnUnmount: true
          }
        }
      )
    : trpcClient.calls[endpoint].useInfiniteQuery(
        {
          searchFilters: {
            columnFilters: debouncedColumnFilters,
            globalFilter
          }
        },
        {
          getNextPageParam: lastPage => lastPage.nextCursor,
          refetchOnWindowFocus: false,
          refetchOnMount: false,
          placeholderData: prev => prev,
          trpc: {
            abortOnUnmount: true
          }
        }
      );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  useSlowResponseToast({
    isLoading,
    isFetching
  });

  useFetchErrorToast({
    isError,
    error
  });

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = data?.pages?.[0]?.meta?.totalRowCount ?? 0;
  const totalFetched = allItems.length;

  const table = useReactTable({
    // TODO fix possible undefined, and type this properly via backend type not 'callDetailsSchema'
    data: allItems as unknown as TData[],
    columns,
    state: {
      sorting,
      columnVisibility,
      columnFilters,
      globalFilter
    },
    enableRowSelection: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    debugTable: false,
    manualFiltering: true
  });

  if (isLoading) {
    return (
      <div className="space-y-4 py-4">
        <h1 className="font-bold text-xl tracking-tight">Calls History</h1>
        <DataTableSkeleton
          columnCount={table.getAllColumns().length}
          searchableColumnCount={1}
          filterableColumnCount={1}
          cellWidths={["12rem", "14rem"]}
          withPagination={false}
          shrinkZero
        />
      </div>
    );
  }

  return (
    <InfiniteScrollDataTable
      table={table}
      isFetching={isFetching}
      totalDBRowCount={totalDBRowCount}
      totalFetched={totalFetched}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
    >
      <h1 className="font-bold text-xl tracking-tight">Calls History</h1>

      <DataTableToolbar table={table} isFiltered={isFiltered} />
    </InfiniteScrollDataTable>
  );
}
