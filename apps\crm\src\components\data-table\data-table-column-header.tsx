import type { Column } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, ChevronsUpDown, EyeOff } from "lucide-react";

import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";

interface DataTableColumnHeaderProps<TData, TValue>
  extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>;
  title: string;
  description?: string;
  disableSorting?: boolean;
}

interface ColumnHeaderContentProps {
  title: string;
  isSortable?: boolean;
  isSorted?: "asc" | "desc" | false;
}

function ColumnHeaderContent({
  title,
  isSortable,
  isSorted
}: ColumnHeaderContentProps) {
  return (
    <div className="inline-flex flex-col">
      <div className="flex items-center font-semibold text-primary">
        <span className="text-left">{title}</span>
        {isSortable &&
          (isSorted === "desc" ? (
            <ArrowDown className="ml-2 size-4 min-w-4" />
          ) : isSorted === "asc" ? (
            <ArrowUp className="ml-2 size-4 min-w-4" />
          ) : (
            <ChevronsUpDown className="ml-2 size-4 min-w-4" />
          ))}
      </div>
    </div>
  );
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  description,
  disableSorting
}: DataTableColumnHeaderProps<TData, TValue>) {
  const canSort = column.getCanSort() && !disableSorting;
  const isSorted = canSort ? column.getIsSorted() : false;

  if (!canSort) {
    return <ColumnHeaderContent title={title} />;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex flex-col items-center justify-start align-middle">
          <Button
            variant="ghost"
            size="sm"
            className="-ml-3 h-auto shrink flex-col items-center font-semibold text-primary data-[state=open]:bg-accent"
          >
            <ColumnHeaderContent title={title} isSortable isSorted={isSorted} />
          </Button>
          {description && (
            <span className="font-normal text-muted-foreground text-xs">
              {description}
            </span>
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
          <ArrowUp className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          Asc
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
          <ArrowDown className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          Desc
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>
          <EyeOff className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          Hide
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
