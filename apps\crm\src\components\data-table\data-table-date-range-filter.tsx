"use client";

import type { Column } from "@tanstack/react-table";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { PlusCircle } from "lucide-react";
import type { DateRange } from "react-day-picker";

import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import { Calendar } from "@watt/crm/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import { Separator } from "@watt/crm/components/ui/separator";

interface DataTableDateRangeFilterProps<
  TData,
  TValue extends DateRange | undefined
> {
  column: Column<TData, TValue> | undefined;
  title: string;
  allowFutureDates?: boolean;
  onFilterChange?: (columnId: string, value: DateRange | undefined) => void;
}

export function DataTableDateRangeFilter<
  TData,
  TValue extends DateRange | undefined
>({
  column,
  title,
  allowFutureDates,
  onFilterChange
}: DataTableDateRangeFilterProps<TData, TValue>) {
  const selectedDateRange = column?.getFilterValue() as DateRange | undefined;

  const formattedDateOrRangeSelection =
    selectedDateRange?.from && selectedDateRange?.to
      ? `${formatDate(selectedDateRange.from, dateFormats.MMMM_dd_yyyy)} - ${formatDate(
          selectedDateRange.to,
          dateFormats.MMMM_dd_yyyy
        )}`
      : selectedDateRange?.from
        ? formatDate(selectedDateRange.from, dateFormats.MMMM_dd_yyyy)
        : "";

  const setSelectedDateRange = (dateRange: DateRange | undefined) => {
    // We convert dateRage to yyyy-MM-dd format and back to Date format to avoid timezone issues.
    // TODO (Bidur): Review this implementation.
    const formattedDateRange = {
      from: dateRange?.from
        ? new Date(formatDate(dateRange.from, dateFormats.YYYY_MM_DD_HYPHEN))
        : undefined,
      to: dateRange?.to
        ? new Date(formatDate(dateRange.to, dateFormats.YYYY_MM_DD_HYPHEN))
        : undefined
    };

    if (onFilterChange && column?.id) {
      onFilterChange(column.id, formattedDateRange);
    } else {
      column?.setFilterValue(formattedDateRange);
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="border-dashed">
          <PlusCircle className="mr-2 h-4 w-4" />
          {title}
          {formattedDateOrRangeSelection && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge
                variant="secondary"
                className="rounded-sm px-1 font-normal"
              >
                {formattedDateOrRangeSelection}
              </Badge>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="end">
        <Calendar
          initialFocus
          mode="range"
          defaultMonth={selectedDateRange?.from}
          selected={selectedDateRange}
          onSelect={setSelectedDateRange}
          numberOfMonths={2}
          disabled={day => !allowFutureDates && day > new Date()}
        />
        {!selectedDateRange?.from && !selectedDateRange?.to && (
          <p className="px-2 pb-2 text-muted-foreground text-sm">
            Select a date or a range by choosing start and end dates.
          </p>
        )}
        {selectedDateRange?.from && !selectedDateRange?.to && (
          <p className="px-2 pb-2 text-muted-foreground text-sm">
            Single date selected. Choose an end date for a range.
          </p>
        )}
      </PopoverContent>
    </Popover>
  );
}
