import type { Row } from "@tanstack/react-table";
import { endOfDay } from "date-fns";
import type { DateRange } from "react-day-picker";

export function textFilter<R>(
  rows: Row<R>,
  id: string,
  filterValue: string
): boolean {
  return filterValue.includes(rows.getValue(id));
}

export function dateFilter<R>(
  row: Row<R>,
  id: string,
  filterValue: DateRange
): boolean {
  if (!filterValue || !filterValue.from || !filterValue.to) {
    return true;
  }

  const rowDateValue = row.getValue(id);
  const toAtEndOfDay = endOfDay(filterValue.to);
  const rowDate = rowDateValue as Date;
  return rowDate >= filterValue.from && rowDate <= toAtEndOfDay;
}

export function durationFilter<R>(
  rows: Row<R>,
  id: string,
  filterValue: string[]
): boolean {
  const durationValue = Number.parseInt(rows.getValue(id));

  const selectedDurations = filterValue.map(value => {
    const [min, max] = value.split("-").map(Number);
    return { min, max };
  });

  for (const selectedDuration of selectedDurations) {
    const { min, max } = selectedDuration;
    if (
      min !== undefined &&
      max !== undefined &&
      durationValue >= min &&
      durationValue <= max
    ) {
      return true;
    }
  }

  return false;
}

export function countFilter<R>(
  rows: Row<R>,
  id: string,
  filterValue: number
): boolean {
  const count = rows.getValue(id) as number;
  return count >= filterValue;
}
