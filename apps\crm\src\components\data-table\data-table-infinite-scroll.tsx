import { type Table as TanstackTable, flexRender } from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type ReactElement, type UIEvent, useCallback, useRef } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableLoading,
  TableRow
} from "@watt/crm/components/ui/table";
import { getCommonPinningStyles } from "@watt/crm/utils/common-pinning-styles";

const ITEM_HEIGHT = 57;

interface DataTableProps<TData> extends React.HTMLAttributes<HTMLDivElement> {
  table: TanstackTable<TData>;
  isFetching: boolean;
  hasNextPage: boolean | undefined;
  totalDBRowCount: number;
  totalFetched: number;
  fetchNextPage: () => void;
  emptyStatePanel?: ReactElement;
}

export function InfiniteScrollDataTable<TData>({
  children,
  table,
  isFetching,
  hasNextPage,
  totalDBRowCount,
  totalFetched,
  emptyStatePanel,
  fetchNextPage
}: DataTableProps<TData>) {
  const tableContainerRef = useRef<HTMLDivElement>(null);

  const fetchMoreOnBottomReached = useCallback(
    (event: UIEvent<HTMLDivElement>) => {
      const containerRefElement = event.currentTarget;
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        if (
          scrollHeight - scrollTop - clientHeight < 10 &&
          !isFetching &&
          (totalFetched < totalDBRowCount || hasNextPage)
        ) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, isFetching, totalFetched, totalDBRowCount, hasNextPage]
  );

  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => ITEM_HEIGHT,
    //measure dynamic row height, except in firefox because it measures table border height incorrectly
    measureElement:
      typeof window !== "undefined" &&
      navigator.userAgent.indexOf("Firefox") === -1
        ? element => element?.getBoundingClientRect().height
        : undefined,
    count: rows.length,
    overscan: 10
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  const paddingTop = virtualRows.length > 0 ? virtualRows?.[0]?.start || 0 : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? totalSize - (virtualRows?.[virtualRows.length - 1]?.end || 0)
      : 0;

  const columnsLength = table.getAllColumns().length;

  return (
    <div className="flex h-screen flex-col gap-4 py-4">
      <div className="sticky top-0 z-20 space-y-2">{children}</div>
      <div
        className="flex-1 overflow-y-auto rounded-md border"
        onScroll={e => fetchMoreOnBottomReached(e)}
        ref={tableContainerRef}
      >
        <Table outerClassName="overflow-visible">
          <TableHeader className="sticky top-0 z-10 bg-background">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
            {isFetching && (
              <TableLoading colSpan={table.getAllLeafColumns().length} />
            )}
          </TableHeader>
          {table.getRowModel().rows?.length ? (
            <TableBody>
              {paddingTop > 0 && (
                <TableRow>
                  <td style={{ height: `${paddingTop}px` }} />
                </TableRow>
              )}
              {virtualRows.map(virtualRow => {
                const row = rows[virtualRow.index];

                if (!row) {
                  return null;
                }

                return (
                  <TableRow
                    key={row.id}
                    data-index={virtualRow.index}
                    ref={node => rowVirtualizer.measureElement(node)}
                    className={cn(
                      "transition-all duration-300 hover:bg-muted-foreground/30",
                      virtualRow.index % 2 === 0 && "bg-muted"
                    )}
                    data-state={row.getIsSelected() && "selected"}
                    style={{
                      height: `${virtualRow.size}px`
                    }}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell
                        key={cell.id}
                        style={{
                          ...getCommonPinningStyles({
                            column: cell.column,
                            backgroundColor:
                              virtualRow.index % 2 === 0
                                ? "hsl(var(--muted))"
                                : "hsl(var(--background))"
                          })
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })}
              {!hasNextPage && (
                <TableRow>
                  <TableCell
                    colSpan={columnsLength}
                    className="h-24 text-center"
                  >
                    {isFetching ? "" : "No more results."}
                  </TableCell>
                </TableRow>
              )}
              {paddingBottom > 0 && (
                <TableRow>
                  <td style={{ height: `${paddingBottom}px` }} />
                </TableRow>
              )}
            </TableBody>
          ) : (
            <TableBody>
              <TableRow>
                <TableCell colSpan={columnsLength} className="h-24 text-center">
                  {isFetching ? "" : emptyStatePanel || "No results"}
                </TableCell>
              </TableRow>
            </TableBody>
          )}
        </Table>
      </div>
    </div>
  );
}
