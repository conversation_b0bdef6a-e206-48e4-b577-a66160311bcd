"use client";

import type { Column } from "@tanstack/react-table";
import { Badge } from "@watt/crm/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  SelectWrapper
} from "@watt/crm/components/ui/select";
import { Separator } from "@watt/crm/components/ui/separator";
import { ChevronDown, type LucideIcon, PlusCircle } from "lucide-react";

export type DataTableOption = {
  label: string;
  value: string;
  icon?: LucideIcon;
};

interface DataTableSelectFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title: string;
  options: DataTableOption[];
  className?: string;
}

export function DataTableSelectFilter<TData, TValue>({
  column,
  title,
  options,
  className
}: DataTableSelectFilterProps<TData, TValue>) {
  const selectedValue = (column?.getFilterValue() as string[] | undefined)?.[0];

  const handleValueChange = (value: string) => {
    if (value === "__CLEAR__") {
      column?.setFilterValue(undefined);
    } else {
      column?.setFilterValue(value ? [value] : undefined);
    }
  };

  return (
    <SelectWrapper>
      <Select value={selectedValue ?? ""} onValueChange={handleValueChange}>
        <SelectTrigger
          className="h-8 border-dashed px-3 text-sm"
          icon={<ChevronDown className="h-4 w-4 pl-2 opacity-50" />}
        >
          <div className="flex items-center space-x-2">
            <PlusCircle className="h-4 w-4" />
            <span>{title}</span>
            {selectedValue && (
              <>
                <Separator orientation="vertical" className="mx-2 h-4" />
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {
                    options.find(option => option.value === selectedValue)
                      ?.label
                  }
                </Badge>
              </>
            )}
          </div>
        </SelectTrigger>
        <SelectContent position="popper">
          {options.map(option => {
            if (!option.value) {
              return null;
            }
            return (
              <SelectItem key={option.value} value={option.value}>
                {option.icon && (
                  <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                )}
                {option.label}
              </SelectItem>
            );
          })}
          {selectedValue && (
            <>
              <SelectSeparator />
              <SelectItem value="__CLEAR__">Clear filters</SelectItem>
            </>
          )}
        </SelectContent>
      </Select>
    </SelectWrapper>
  );
}
