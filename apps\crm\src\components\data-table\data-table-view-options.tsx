"use client";

import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import type { Table } from "@tanstack/react-table";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { SlidersHorizontal } from "lucide-react";

import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@watt/crm/components/ui/dropdown-menu";
import { useMemo } from "react";

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>;
}

type CustomColumnMeta = {
  dropdownLabel: string;
};

export function DataTableViewOptions<TData>({
  table
}: DataTableViewOptionsProps<TData>) {
  const columns = useMemo(() => {
    return table
      .getAllColumns()
      .filter(
        column =>
          typeof column.accessorFn !== "undefined" && column.getCanHide()
      );
  }, [table]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <SlidersHorizontal className="mr-2 h-4 w-4" />
          View
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="max-h-[var(--radix-dropdown-menu-content-available-height)] w-[150px] overflow-auto"
      >
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {columns.map(column => {
          return (
            <DropdownMenuCheckboxItem
              key={column.id}
              checked={column.getIsVisible()}
              onCheckedChange={value => column.toggleVisibility(!!value)}
            >
              {(column.columnDef.meta as CustomColumnMeta)?.dropdownLabel ||
                humanize(column.id)}
            </DropdownMenuCheckboxItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
