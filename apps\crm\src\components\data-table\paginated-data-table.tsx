"use client";

import { type Table, flexRender } from "@tanstack/react-table";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { ReactNode } from "react";

import {
  DataTablePagination,
  type RowData
} from "@watt/crm/components/data-table/data-table-pagination";
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Table as UiTable
} from "@watt/crm/components/ui/table";

type PaginatedDataTableProps<TData> = {
  table: Table<RowData<TData>>;
  isFetching?: boolean;
  emptyStatePanel?: ReactNode;
  children?: ReactNode;
};

export function PaginatedDataTable<TData>({
  table,
  isFetching,
  emptyStatePanel,
  children
}: PaginatedDataTableProps<TData>) {
  const hasRows = table.getRowModel().rows.length > 0;

  return (
    <div className="space-y-4 p-2">
      {children}

      <div className="rounded-md border bg-background">
        <UiTable>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {hasRows ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  key={row.id}
                  className={cn(
                    "transition-all duration-300 hover:bg-muted-foreground/30",
                    rowIndex % 2 === 0 && "bg-muted"
                  )}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} className="h-12">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={table.getAllColumns().length}
                  className="h-24 text-center"
                >
                  {isFetching ? "Loading..." : emptyStatePanel || "No results."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </UiTable>
      </div>

      <DataTablePagination table={table} />
    </div>
  );
}
