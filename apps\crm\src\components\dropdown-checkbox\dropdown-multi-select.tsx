"use client";

import { Ch<PERSON>ronDown, SquarePlus } from "lucide-react";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList
} from "@watt/crm/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import type React from "react";

type DropdownMultiSelectTriggerProps = React.ComponentProps<typeof Button> & {
  placeholder: string;
  fieldValue: string[];
  childClassName?: string;
};

const DropdownMultiSelectTrigger: React.FC<DropdownMultiSelectTriggerProps> = ({
  ref,
  placeholder,
  fieldValue,
  className,
  childClassName,
  ...props
}) => (
  <PopoverTrigger asChild>
    <Button
      variant="outline"
      role="combobox"
      className={cn(
        "w-full justify-between px-3",
        (!fieldValue || fieldValue.length === 0) &&
          "font-normal text-muted-foreground italic",
        className
      )}
      ref={ref}
      {...props}
    >
      <span
        className={cn(
          !!fieldValue.length &&
            "max-w-[44ch] overflow-hidden text-ellipsis whitespace-nowrap",
          childClassName
        )}
      >
        {!fieldValue || fieldValue.length === 0
          ? placeholder
          : fieldValue.map(val => (
              <Badge key={val} className="mr-2">
                {val}
              </Badge>
            ))}
      </span>
      <ChevronDown className="ml-2 size-4 shrink-0 opacity-50" />
    </Button>
  </PopoverTrigger>
);
DropdownMultiSelectTrigger.displayName = "DropdownMultiSelectTrigger";

const MAX_SHOW_ITEMS = 2;

const DropdownMultiSelectLinkTrigger: React.FC<
  DropdownMultiSelectTriggerProps
> = ({ ref, placeholder, fieldValue, className, ...props }) => (
  <PopoverTrigger asChild>
    <Button
      variant="none"
      role="combobox"
      className={cn(
        "h-auto w-max items-center justify-start p-0 text-xs",
        (!fieldValue || fieldValue.length === 0) &&
          "font-normal text-muted-foreground",
        className
      )}
      ref={ref}
      {...props}
    >
      <SquarePlus className="mr-1 size-3 shrink-0" />
      <span className="flex">
        {/* Max 3 items and truncate */}
        {!fieldValue || fieldValue.length === 0
          ? placeholder
          : fieldValue.slice(0, MAX_SHOW_ITEMS).map(val => (
              <div key={val} className="mr-2">
                {val}
              </div>
            ))}
        {fieldValue.length > MAX_SHOW_ITEMS &&
          `+${fieldValue.length - MAX_SHOW_ITEMS}`}
      </span>
    </Button>
  </PopoverTrigger>
);
DropdownMultiSelectLinkTrigger.displayName = "DropdownMultiSelectLinkTrigger";

type DropdownMultiSelectContentProps = React.ComponentProps<
  typeof PopoverContent
> & {
  container?: HTMLElement | null;
};

const DropdownMultiSelectContent: React.FC<DropdownMultiSelectContentProps> = ({
  ref,
  className,
  children,
  container,
  ...props
}) => (
  <PopoverContent
    ref={ref}
    className={cn("p-0", className)}
    container={container}
    {...props}
  >
    <Command>
      <CommandList className="max-h-[400px] overflow-y-scroll">
        {children}
      </CommandList>
    </Command>
  </PopoverContent>
);
DropdownMultiSelectContent.displayName = "DropdownMultiSelectContent";

const DropdownMultiSelect = Popover;
const DropdownMultiSelectGroup = CommandGroup;
const DropdownMultiSelectItem = CommandItem;
const DropdownMultiSelectEmpty = CommandEmpty;

export {
  DropdownMultiSelect,
  DropdownMultiSelectContent,
  DropdownMultiSelectEmpty,
  DropdownMultiSelectGroup,
  DropdownMultiSelectItem,
  DropdownMultiSelectLinkTrigger,
  DropdownMultiSelectTrigger
};
