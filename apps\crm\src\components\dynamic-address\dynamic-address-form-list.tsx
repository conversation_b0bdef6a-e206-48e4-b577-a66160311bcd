import type {
  ContactAddressFormData,
  UpsertContactFormData
} from "@watt/api/src/types/people";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { parse } from "date-fns";
import { Pen, Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import { ContactAddressForm } from "../quick-actions/address/contact-address-form";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { DeleteAddressAlert } from "./delete-address-alert";

type DynamicAddressFormListProps = {
  contactAddresses?: ContactAddressFormData[];
  updateContactAddresses: (addresses: ContactAddressFormData[]) => void;
};

type AddressCardProps = {
  address: ContactAddressFormData;
  onUpdate: (data: ContactAddressFormData) => void;
  onRemove: (localId: string) => void;
  isFormActive: boolean;
  onFormStateChange: (isActive: boolean) => void;
};

function AddressCard({
  address,
  onUpdate,
  onRemove,
  isFormActive,
  onFormStateChange
}: AddressCardProps) {
  const { getValues, setValue } = useFormContext<UpsertContactFormData>();
  const [isEditing, setIsEditing] = useState(false);

  const currentFormIndex = useMemo(() => {
    const addresses = getValues("addresses") || [];
    return addresses.findIndex(addr => addr.localId === address.localId);
  }, [getValues, address.localId]);

  const handleOnUpdate = (data: ContactAddressFormData) => {
    onUpdate(data);
    handleStopEditing();
  };

  const handleRemoveAddress = () => {
    onRemove(address.localId);
    const addresses = getValues("addresses");
    setValue(
      "addresses",
      addresses.filter(addr => addr.localId !== address.localId),
      { shouldDirty: true }
    );
  };

  const handleStartEditing = () => {
    setIsEditing(true);
    onFormStateChange(true);
  };

  const handleStopEditing = () => {
    setIsEditing(false);
    onFormStateChange(false);
  };

  if (isEditing) {
    return (
      <Card>
        <CardContent className="pt-6">
          <ContactAddressForm
            index={currentFormIndex}
            existingAddress={address}
            onCancel={handleStopEditing}
            onSubmitForm={handleOnUpdate}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative h-[150px]">
      <CardHeader className="block">
        <CardTitle className="font-medium text-sm">
          {getAddressDisplayName(address.address)}
        </CardTitle>
        <div className="flex flex-wrap gap-2 text-muted-foreground text-xs">
          <div className="flex items-center gap-1">
            <span className="font-medium">Moved in:</span>
            <span>{address.movedInDate}</span>
          </div>
          {address.movedOutDate && (
            <div className="flex items-center gap-1">
              <span className="font-medium">Moved out:</span>
              <span>{address.movedOutDate}</span>
            </div>
          )}
        </div>
        {address.isCurrent && (
          <Badge variant="secondary">Current Address</Badge>
        )}
        <div className="absolute right-3 bottom-2 flex gap-1">
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="text-muted-foreground/70"
            onClick={handleStartEditing}
            disabled={isFormActive && !isEditing}
          >
            <Pen className="h-4 w-4" />
          </Button>
          <DeleteAddressAlert onSubmit={handleRemoveAddress} />
        </div>
      </CardHeader>
    </Card>
  );
}

export function DynamicAddressFormList({
  contactAddresses = [],
  updateContactAddresses
}: DynamicAddressFormListProps) {
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [isFormActive, setIsFormActive] = useState(false);

  const handleAddAddress = (data: ContactAddressFormData) => {
    updateContactAddresses([...contactAddresses, data]);
    setIsAddingNew(false);
    setIsFormActive(false);
  };

  const handleStartAddingNew = () => {
    setIsAddingNew(true);
    setIsFormActive(true);
  };

  const handleCancelAddingNew = () => {
    setIsAddingNew(false);
    setIsFormActive(false);
  };

  const handleUpdateAddress = (data: ContactAddressFormData) => {
    const newAddresses = contactAddresses.map(addr =>
      addr.localId === data.localId ? data : addr
    );
    updateContactAddresses(newAddresses);
  };

  const handleRemoveAddress = (localId: string) => {
    const newAddresses = contactAddresses.filter(
      addr => addr.localId !== localId
    );
    updateContactAddresses(newAddresses);
  };

  // Sort addresses by movedInDate, with current address first
  const sortedAddressesMostRecentFirst = useMemo(() => {
    return [...contactAddresses].sort((a, b) => {
      if (a.isCurrent !== b.isCurrent) {
        return a.isCurrent ? -1 : 1;
      }

      const dateA = parse(a.movedInDate, dateFormats.DD_MM_YYYY, new Date());
      const dateB = parse(b.movedInDate, dateFormats.DD_MM_YYYY, new Date());
      return dateB.getTime() - dateA.getTime();
    });
  }, [contactAddresses]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2">
        <Button
          type="button"
          variant="outline"
          className="w-full justify-start font-normal text-muted-foreground italic"
          onClick={handleStartAddingNew}
          disabled={isFormActive}
        >
          Add contact address
          <Plus className="ml-auto size-4 opacity-50" />
        </Button>

        {isAddingNew && (
          <Card>
            <CardContent className="pt-6">
              <ContactAddressForm
                onSubmitForm={handleAddAddress}
                onCancel={handleCancelAddingNew}
                index={contactAddresses.length}
              />
            </CardContent>
          </Card>
        )}
      </div>

      {sortedAddressesMostRecentFirst.map(address => (
        <AddressCard
          key={address.localId}
          address={address}
          onUpdate={handleUpdateAddress}
          onRemove={handleRemoveAddress}
          isFormActive={isFormActive}
          onFormStateChange={setIsFormActive}
        />
      ))}
    </div>
  );
}
