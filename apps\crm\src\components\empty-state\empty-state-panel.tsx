import type { LucideIcon } from "lucide-react";
import { AudioLines } from "lucide-react";
import { EmptyState } from "./empty-state";

type EmptyStatePanelProps = {
  title: string;
  Icon?: LucideIcon;
  description: string;
  children?: React.ReactNode;
};

export function EmptyStatePanel({
  title,
  description,
  Icon,
  children
}: EmptyStatePanelProps) {
  return (
    <EmptyState.Root
      title={title}
      description={description}
      icon={
        <EmptyState.Icon>
          {Icon ? (
            <Icon className="h-8 w-8 text-muted-foreground" />
          ) : (
            <AudioLines className="h-8 w-8 text-muted-foreground" />
          )}
        </EmptyState.Icon>
      }
    >
      {children}
    </EmptyState.Root>
  );
}
