import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { PropsWithChildren } from "react";
import React from "react";

type EmptyStateProps = PropsWithChildren<{
  title: string;
  description: string;
  icon?: React.ReactNode;
}> &
  React.HTMLAttributes<HTMLDivElement>;

const EmptyStateRoot = ({
  title,
  description,
  icon,
  children,
  className,
  ...props
}: EmptyStateProps) => (
  <div
    className={cn(
      "flex flex-col items-center justify-center px-4 md:px-6",
      className
    )}
    {...props}
  >
    <div className="max-w-md space-y-6 p-16 text-center">
      <div className="flex flex-shrink flex-row justify-center">
        {icon && icon}
      </div>
      <EmptyStateTitle>{title}</EmptyStateTitle>
      <EmptyStateDescription>{description}</EmptyStateDescription>
      <div className="flex flex-col gap-4">
        {React.Children.map(children, child => (
          <div className="flex-shrink-0">{child}</div>
        ))}{" "}
      </div>
    </div>
  </div>
);

const EmptyStateIcon = ({
  className,
  children
}: {
  className?: string;
  children: React.ReactNode;
}) => (
  <div className={cn("flex flex-shrink rounded-md border p-2", className)}>
    {children}
  </div>
);

const EmptyStateTitle = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) => (
  <h2
    className={cn("font-bold text-base tracking-tight", className)}
    {...props}
  />
);

const EmptyStateDescription = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) => (
  <p
    className={cn("text-gray-500 text-sm dark:text-gray-400", className)}
    {...props}
  />
);

export const EmptyState = {
  Root: EmptyStateRoot,
  Icon: EmptyStateIcon,
  Title: EmptyStateTitle,
  Description: EmptyStateDescription
};
