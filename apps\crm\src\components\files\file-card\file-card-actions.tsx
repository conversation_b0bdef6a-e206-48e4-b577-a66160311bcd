"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { type LucideIcon, MoreVertical } from "lucide-react";

export type FileCardAction = {
  icon: LucideIcon;
  label: string;
  action: () => void | Promise<void>;
};

type FileCardActionsProps = {
  actions: FileCardAction[];
  className?: string;
};

export function FileCardActions({ actions, className }: FileCardActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "flex size-8 items-center justify-center border-0 bg-transparent p-0 hover:bg-background",
            className
          )}
        >
          <MoreVertical className="size-3.5 rotate-90" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" onClick={e => e.stopPropagation()}>
        {actions.map(action => (
          <DropdownMenuItem key={action.label} onClick={action.action}>
            <action.icon className="mr-2 size-4" />
            {action.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
