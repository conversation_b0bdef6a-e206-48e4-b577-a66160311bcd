"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { getInitials } from "@watt/common/src/utils/get-initials";
import { randomColourByString } from "@watt/common/src/utils/random-colour-by-string";
import { Avatar, AvatarFallback } from "@watt/crm/components/ui/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import type { FileCardProps } from ".";

type FileCardAvatarProps = {
  createdBy: FileCardProps["companyFile"]["createdBy"];
  variant: FileCardProps["variant"];
};

export function FileCardAvatar({ createdBy, variant }: FileCardAvatarProps) {
  const initials = getInitials(createdBy.forename, createdBy.surname);

  const { hslString, isDark } = randomColourByString(initials);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Avatar
          className={cn(
            "text-muted-foreground",
            variant === "grid" ? "size-6 text-xs" : "size-7 text-sm"
          )}
        >
          <AvatarFallback
            className={cn(isDark ? "text-white" : "text-black")}
            style={{
              backgroundColor: `hsl(${hslString})`
            }}
          >
            {initials}
          </AvatarFallback>
        </Avatar>
      </TooltipTrigger>
      <TooltipContent>
        {createdBy.forename} {createdBy.surname}
      </TooltipContent>
    </Tooltip>
  );
}
