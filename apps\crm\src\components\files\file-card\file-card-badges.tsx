"use client";

import { UtilityTypeIcon } from "@watt/crm/app/account/companies/[id]/components/site/profile";
import { Badge } from "@watt/crm/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import type { SiteRefData } from "@watt/crm/utils/files";
import { useMemo } from "react";

type FileCardBadgesProps = {
  siteRefData: SiteRefData[];
};

export function FileCardBadges({ siteRefData }: FileCardBadgesProps) {
  if (!siteRefData || siteRefData.length === 0) {
    return null;
  }

  const {
    firstSite,
    otherSites,
    firstMeter,
    remainingFirstSiteMeters,
    totalAdditionalItems
  } = useMemo(() => {
    const [firstSite, ...otherSites] = siteRefData;
    if (!firstSite) {
      return {
        firstSite: null,
        otherSites: [],
        firstMeter: null,
        remainingFirstSiteMeters: [],
        totalAdditionalItems: 0
      };
    }
    const firstSiteMeters = firstSite.meters ?? [];
    const [firstMeter, ...remainingFirstSiteMeters] = firstSiteMeters;

    const totalAdditionalItems =
      otherSites.length > 0
        ? otherSites.flatMap(site => site.meters ?? []).length - 1
        : remainingFirstSiteMeters.length;

    return {
      firstSite,
      otherSites,
      firstMeter,
      remainingFirstSiteMeters,
      totalAdditionalItems
    };
  }, [siteRefData]);

  if (!firstSite) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <Badge variant="outline">
        <div className="flex flex-row items-center gap-2">
          {firstSite.siteRefId}
          {firstMeter && (
            <>
              <UtilityTypeIcon
                utilityType={firstMeter.utilityType}
                className="size-2.5"
              />
              {firstMeter.meterNumber}
            </>
          )}
        </div>
      </Badge>
      {totalAdditionalItems > 0 && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline">+{totalAdditionalItems}</Badge>
          </TooltipTrigger>
          <TooltipContent>
            {otherSites.length > 0 ? (
              otherSites.map(site => (
                <div key={site.siteRefId}>
                  {site.meters?.slice(1).map(meter => (
                    <div
                      key={meter.meterNumber}
                      className="flex flex-row items-center gap-2"
                    >
                      {site.siteRefId}
                      <UtilityTypeIcon
                        utilityType={meter.utilityType}
                        className="size-2.5"
                      />
                      {meter.meterNumber}
                    </div>
                  ))}
                </div>
              ))
            ) : (
              <div className="flex flex-col gap-1">
                {remainingFirstSiteMeters.map(meter => (
                  <div
                    key={meter.meterNumber}
                    className="flex flex-row items-center gap-2"
                  >
                    {firstSite.siteRefId}
                    <UtilityTypeIcon
                      utilityType={meter.utilityType}
                      className="size-2.5"
                    />
                    {meter.meterNumber}
                  </div>
                ))}
              </div>
            )}
          </TooltipContent>
        </Tooltip>
      )}
    </div>
  );
}
