"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import type { LucideIcon } from "lucide-react";
import type { FileCardProps } from ".";

type FileCardCategoryProps = {
  Icon: LucideIcon;
  category: string;
  variant: FileCardProps["variant"];
};

export function FileCardCategory({
  Icon,
  category,
  variant
}: FileCardCategoryProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Icon
          className={cn(
            "shrink-0 text-primary",
            variant === "grid" ? "size-8" : "size-6"
          )}
        />
      </TooltipTrigger>
      <TooltipContent>{category}</TooltipContent>
    </Tooltip>
  );
}
