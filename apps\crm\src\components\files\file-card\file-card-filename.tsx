"use client";

import {
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Content,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { truncateFilename } from "@watt/crm/utils/files";
import type { FileCardProps } from ".";

type FileCardFilenameProps = {
  filename: string;
  variant: FileCardProps["variant"];
};

export function FileCardFilename({ filename, variant }: FileCardFilenameProps) {
  if (variant === "list" && filename.length > 30) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <p className="break-all text-md">{truncateFilename(filename)}</p>
        </TooltipTrigger>
        <TooltipContent className="max-w-[90ch] text-justify">
          {filename}
        </TooltipContent>
      </Tooltip>
    );
  }

  return <p className="break-all text-md">{filename}</p>;
}
