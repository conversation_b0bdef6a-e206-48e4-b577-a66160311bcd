"use client";

import type { GetCompanyFiles } from "@watt/api/src/router";
import { DOCUMENT_MIME_TYPES } from "@watt/common/src/constants/mime-types";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { log } from "@watt/common/src/utils/axiom-logger";
import { formatBytes } from "@watt/common/src/utils/format-bytes";
import { formatRelativeOrFullDate } from "@watt/common/src/utils/format-date";
import { ConfirmationDialog } from "@watt/crm/components/confirmation-dialog";
import type { QueryParams } from "@watt/crm/components/quick-actions/file/edit-file-modal/types-and-data";
import { FileModalTypes } from "@watt/crm/components/quick-actions/file/file-provider";
import { getMimeTypeIcon } from "@watt/crm/components/quick-actions/file/file-uploader";
import { UPLOAD_CONFIGS } from "@watt/crm/components/quick-actions/file/upload-files-modal/types-and-data";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { trpcClient } from "@watt/crm/utils/api";
import { mapSiteRefData } from "@watt/crm/utils/files";
import { DownloadIcon, PencilIcon, TrashIcon } from "lucide-react";
import { memo, useMemo, useState } from "react";
import type { FileCardVariant } from "../files-grid/files-grid";
import { type FileCardAction, FileCardActions } from "./file-card-actions";
import { FileCardAvatar } from "./file-card-avatar";
import { FileCardBadges } from "./file-card-badges";
import { FileCardCategory } from "./file-card-category";
import { FileCardFilename } from "./file-card-filename";

export type FileCardProps = {
  companyFile: GetCompanyFiles[number];
  variant: FileCardVariant;
};

function FileCardComponent({ companyFile, variant }: FileCardProps) {
  const { id, path, filename, mimeType, createdBy } = companyFile;

  const trpc = trpcClient.useUtils();

  const [isDeleteFileDialogOpen, setIsDeleteFileDialogOpen] = useState(false);

  const { setQueryParams } = useQueryParams<QueryParams>();

  const handleEditFile = () => {
    setQueryParams({
      modal: FileModalTypes.editFile,
      companyFileId: id
    });
  };

  async function handleDownloadFile() {
    try {
      const { data, error } =
        await trpc.client.files.getSignedDownloadUrl.query({
          bucketName: STORAGE_BUCKETS.COMPANY_FILES,
          filename: path
        });

      if (error) {
        throw error;
      }

      const response = await fetch(data.signedUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      const description =
        e instanceof Error ? e.message : "Something went wrong";
      log.error(description);
      toast({
        title: "Unable to download file",
        description,
        variant: "destructive"
      });
    }
  }

  async function handlePreviewFile() {
    try {
      if (companyFile.mimeType !== DOCUMENT_MIME_TYPES.PDF) {
        toast({
          title: "No preview",
          description:
            "Preview unavailable for this file format. Only PDF files can be previewed online. Download to access content."
        });
      } else {
        const { data, error } =
          await trpc.client.files.getSignedDownloadUrl.query({
            bucketName: STORAGE_BUCKETS.COMPANY_FILES,
            filename: path
          });

        if (error) {
          throw error;
        }

        window.open(data.signedUrl, "_blank");
      }
    } catch (e) {
      const description =
        e instanceof Error ? e.message : "Something went wrong";
      log.error(description);
      toast({
        title: "Unable to preview file",
        description,
        variant: "destructive"
      });
    }
  }

  async function handleDeleteFile() {
    try {
      await trpc.client.companyFiles.deleteFile.mutate({
        fileId: id
      });

      await trpc.companyFiles.getCompanyFiles.invalidate();

      toast({
        title: "File deleted successfully",
        description: `You have deleted ${filename}`,
        variant: "success"
      });
    } catch (e) {
      const description =
        e instanceof Error ? e.message : "Something went wrong";
      log.error(description);
      toast({
        title: "Unable to delete file",
        description,
        variant: "destructive"
      });
    }
  }

  const actions: FileCardAction[] = [
    {
      icon: PencilIcon,
      label: "Edit",
      action: handleEditFile
    },
    {
      icon: DownloadIcon,
      label: "Download",
      action: handleDownloadFile
    },
    {
      icon: TrashIcon,
      label: "Delete",
      action: () => setIsDeleteFileDialogOpen(true)
    }
  ];

  const siteRefData = useMemo(() => mapSiteRefData(companyFile), [companyFile]);

  const fileCategory = useMemo(
    () => UPLOAD_CONFIGS[companyFile.type].label,
    [companyFile.type]
  );

  const Icon = getMimeTypeIcon(mimeType);

  return (
    <>
      <div className="group h-full w-full">
        <span className="sr-only fixed">Open file</span>
        {variant === "grid" ? (
          <button
            type="button"
            className="relative flex h-full w-full flex-col overflow-hidden rounded-md border border-solid bg-background text-left"
            onClick={handlePreviewFile}
          >
            <div className="flex min-h-24 flex-1 items-center justify-center transition-colors group-hover:bg-accent">
              <FileCardCategory
                category={fileCategory}
                variant="grid"
                Icon={Icon}
              />
              <div className="absolute top-2 right-2">
                <FileCardActions actions={actions} />
              </div>
            </div>
            <div className="flex items-center justify-between border-t px-4 py-2">
              <div className="flex flex-col gap-2">
                <div className="flex flex-row flex-wrap items-center gap-2">
                  <FileCardFilename filename={filename} variant="grid" />
                  <p className="text-muted-foreground text-xs">
                    {formatBytes(companyFile.size)}
                  </p>
                </div>
                <div className="flex flex-row items-center gap-2">
                  <FileCardAvatar createdBy={createdBy} variant="grid" />
                  <p className="text-muted-foreground text-xs">
                    {formatRelativeOrFullDate(companyFile.updatedAt)}
                  </p>
                </div>
                <FileCardBadges siteRefData={siteRefData} />
              </div>
            </div>
          </button>
        ) : (
          <button
            type="button"
            className="relative flex h-14 w-full items-center justify-between gap-4 overflow-hidden rounded-md border border-solid bg-background px-4 text-left shadow-sm hover:bg-accent"
            onClick={handlePreviewFile}
          >
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <FileCardCategory
                  category={fileCategory}
                  variant="list"
                  Icon={Icon}
                />
                <div className="flex flex-row flex-wrap items-center gap-2">
                  <FileCardFilename filename={filename} variant="list" />
                  <p className="text-muted-foreground text-sm">
                    {formatBytes(companyFile.size)}
                  </p>
                </div>
              </div>
              <FileCardBadges siteRefData={siteRefData} />
            </div>
            <div className="flex shrink-0 items-center gap-2">
              <div className="flex flex-row items-center gap-2">
                <p className="text-muted-foreground text-sm">
                  {formatRelativeOrFullDate(companyFile.updatedAt)}
                </p>
                <FileCardAvatar createdBy={createdBy} variant="list" />
              </div>
              <FileCardActions actions={actions} className="-mr-1.5" />
            </div>
          </button>
        )}
      </div>
      <ConfirmationDialog
        title="Confirm Deletion"
        description="Are you sure you want to delete this file? You will need to contact our development team to have it recovered."
        onConfirm={handleDeleteFile}
        isOpen={isDeleteFileDialogOpen}
        setIsOpen={setIsDeleteFileDialogOpen}
      />
    </>
  );
}

export const FileCard = memo(FileCardComponent, (prevProps, nextProps) => {
  // Deep comparison of the companyFile object and variant
  return (
    prevProps.variant === nextProps.variant &&
    prevProps.companyFile.id === nextProps.companyFile.id &&
    prevProps.companyFile.filename === nextProps.companyFile.filename &&
    prevProps.companyFile.updatedAt === nextProps.companyFile.updatedAt &&
    prevProps.companyFile.size === nextProps.companyFile.size &&
    prevProps.companyFile.type === nextProps.companyFile.type
  );
});
