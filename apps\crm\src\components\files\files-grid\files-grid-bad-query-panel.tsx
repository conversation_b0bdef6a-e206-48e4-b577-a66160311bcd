"use client";

import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { Button } from "@watt/crm/components/ui/button";
import { SearchXIcon } from "lucide-react";

type FilesGridBadQueryPanelProps = {
  onClearQuery: () => void;
};

export function FilesGridBadQueryPanel({
  onClearQuery
}: FilesGridBadQueryPanelProps) {
  return (
    <EmptyStatePanel
      title="No matching files found"
      description="Please try a different search query"
      Icon={SearchXIcon}
    >
      <Button variant="outline" onClick={onClearQuery}>
        Clear search
      </Button>
    </EmptyStatePanel>
  );
}
