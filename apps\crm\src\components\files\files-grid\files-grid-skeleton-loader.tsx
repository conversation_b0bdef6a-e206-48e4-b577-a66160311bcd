"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import type { FileCardVariant } from "./files-grid";

type FilesGridSkeletonLoaderProps = {
  variant: FileCardVariant;
};

export function FilesGridSkeletonLoader({
  variant
}: FilesGridSkeletonLoaderProps) {
  return Array.from({ length: 6 }, (_, i) => (
    <Skeleton
      key={`${i + 1}`}
      className={cn(variant === "grid" ? "h-64" : "h-14", "rounded-lg")}
    />
  ));
}
