"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { FileCard } from "@watt/crm/components/files/file-card";
import {
  type FileModalQueryParams,
  FileModalTypes
} from "@watt/crm/components/quick-actions/file/file-provider";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useCompanyFilesSearch } from "@watt/crm/hooks/use-company-files-search";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { LayoutGridIcon, LayoutListIcon } from "lucide-react";
import { memo, useMemo, useState } from "react";
import Dropzone from "react-dropzone";
import { FilesGridBadQueryPanel } from "./files-grid-bad-query-panel";
import { FilesGridGetStartedPanel } from "./files-grid-get-started-panel";
import { FilesGridSkeletonLoader } from "./files-grid-skeleton-loader";

export type FileCardVariant = "grid" | "list";

type FilesGridFilterData = {
  query: string;
  view: FileCardVariant;
};

type FilesGridProps = {
  companyId: string;
};

function FilesGridComponent({ companyId }: FilesGridProps) {
  const [filterData, setFilterData] = useState<FilesGridFilterData>({
    query: "",
    view: "grid"
  });

  const { files, isLoading } = useCompanyFilesSearch({
    companyId
  });

  const filteredFiles = useMemo(() => {
    const searchQuery = filterData.query.toLowerCase();

    return files.filter(file => {
      if (file.filename.toLowerCase().includes(searchQuery)) {
        return true;
      }

      return file.sites.some(site => {
        if (
          composeSiteRef(site.siteRefId)?.toLowerCase().includes(searchQuery)
        ) {
          return true;
        }

        return file.siteMeters.some(meter =>
          (
            meter.electricSiteMeter?.mpan?.value ??
            meter.gasSiteMeter?.mprn?.value
          )
            ?.toLowerCase()
            .includes(searchQuery)
        );
      });
    });
  }, [files, filterData.query]);

  const { setQueryParams } = useQueryParams<FileModalQueryParams>();

  const handleSearchChange = (query: string) => {
    setFilterData(prev => ({ ...prev, query }));
  };

  const handleViewTypeChange = () => {
    setFilterData(prev => ({
      ...prev,
      view: prev.view === "grid" ? "list" : "grid"
    }));
  };

  const handleUpload = () => {
    setQueryParams({ modal: FileModalTypes.uploadFiles });
  };

  return (
    <div className="h-full flex-1">
      <Dropzone onDrop={handleUpload}>
        {({ getRootProps, isDragActive }) => (
          <div
            {...getRootProps()}
            className={cn(
              "flex h-full w-full flex-col rounded-lg border-2 border-opacity-0 p-2",
              isDragActive && "border-muted-foreground/25 border-dashed"
            )}
          >
            <div className="flex flex-wrap justify-between gap-2">
              <div className="flex flex-wrap items-center gap-2">
                <SearchInput
                  placeholder="Type to search..."
                  onChange={e => handleSearchChange(e.target.value)}
                  value={filterData.query}
                  className="h-9 w-[250px]"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 gap-1 [&_svg]:size-4"
                  onClick={handleViewTypeChange}
                >
                  {filterData.view === "grid" ? (
                    <LayoutListIcon />
                  ) : (
                    <LayoutGridIcon />
                  )}
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                    {filterData.view === "grid" ? "List" : "Grid"}
                  </span>
                </Button>
                <Button
                  size="sm"
                  className="h-8"
                  variant="secondary"
                  onClick={handleUpload}
                >
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                    Upload
                  </span>
                </Button>
              </div>
            </div>
            <div
              className={cn(
                (isLoading || filteredFiles.length > 0) &&
                  (filterData.view === "grid"
                    ? "grid grid-cols-[repeat(auto-fill,minmax(17rem,1fr))] items-stretch justify-start gap-4"
                    : "flex flex-col space-y-2"),
                "py-4"
              )}
            >
              {isLoading ? (
                <FilesGridSkeletonLoader variant={filterData.view} />
              ) : filteredFiles.length > 0 ? (
                filteredFiles.map(file => (
                  <FileCard
                    key={file.id}
                    variant={filterData.view}
                    companyFile={file}
                  />
                ))
              ) : files.length > 0 ? (
                <FilesGridBadQueryPanel
                  onClearQuery={() => handleSearchChange("")}
                />
              ) : (
                <FilesGridGetStartedPanel />
              )}
            </div>
          </div>
        )}
      </Dropzone>
    </div>
  );
}

export const FilesGrid = memo(FilesGridComponent, (prevProps, nextProps) => {
  // Only re-render if companyId changes
  return prevProps.companyId === nextProps.companyId;
});
