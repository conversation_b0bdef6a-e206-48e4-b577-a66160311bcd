import type { Column } from "@tanstack/react-table";
import {
  DataTableFacetedFilter,
  type DataTableOption
} from "../data-table/data-table-faceted-filter";

const visibilityOptions: DataTableOption[] = [
  { label: "Hidden", value: "HIDDEN" },
  { label: "Unhidden", value: "UNHIDDEN" }
];

type DataTableVisibilityFilterProps<TData> = {
  column?: Column<TData>;
  title?: string;
};

export function DataTableVisibilityFilter<TData>({
  column,
  title = "Visibility"
}: DataTableVisibilityFilterProps<TData>) {
  return (
    <DataTableFacetedFilter
      column={column}
      title={title}
      options={visibilityOptions}
    />
  );
}
