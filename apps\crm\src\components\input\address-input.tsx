import type { Address_Find_Many } from "@watt/api/src/router";
import { useRef, useState } from "react";

import {
  AddressSelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "../lookup";
import { AddNewAddressTrigger } from "../quick-actions/address/add-new-address-trigger";

type AddressInputProps = {
  value: string;
  onSubmit: (id: string) => void;
  handleAddressSelected?: (address: Address_Find_Many[0] | null) => void;
  // @deprecated use handleAddressSelected instead provides access to the full address object
  handleAddressSelect?: (value: string) => void;
  addressData: Address_Find_Many | undefined;
  selectedAddress: Address_Find_Many[0] | undefined;
  handleAddressSearchInputChange: (addressSearch: string) => void;
  addressSearchInput: string | undefined;
  isLoading?: boolean;
  placeholder?: string;
  hideMetersInfo?: boolean;
};

export function AddressInput({
  value,
  isLoading,
  addressSearchInput,
  addressData,
  selectedAddress,
  hideMetersInfo,
  handleAddressSearchInputChange,
  handleAddressSelect,
  handleAddressSelected,
  onSubmit,
  placeholder = ""
}: AddressInputProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const containerRef = useRef<HTMLInputElement | null>(null);

  const onSelect = (value: string) => {
    if (handleAddressSelected) {
      handleAddressSelected(addressData?.find(a => a.id === value) ?? null);
    } else {
      handleAddressSelect?.(value);
    }
    setIsModalOpen(false);
  };

  return (
    <div ref={containerRef} className="flex flex-col overflow-x-hidden">
      <LookUp open={isModalOpen} onOpenChange={setIsModalOpen}>
        <LookUpTrigger fieldValue={value} isLoading={isLoading}>
          {selectedAddress ? (
            <AddressSelection
              address={selectedAddress}
              hideMetersInfo={hideMetersInfo}
            />
          ) : (
            <span className="font-normal italic">
              {placeholder || "Select an address..."}
            </span>
          )}
        </LookUpTrigger>
        <LookUpContent
          placeholder="Search by postcode"
          searchInput={addressSearchInput}
          onSearchInputChange={handleAddressSearchInputChange}
          isLoading={isLoading}
          container={containerRef.current}
          className="pb-10"
        >
          <LookUpGroup className="p-0">
            {addressData?.map(address => (
              <LookUpItem
                key={address.id}
                value={address.id}
                onSelect={onSelect}
              >
                <AddressSelection
                  address={address}
                  hideMetersInfo={hideMetersInfo}
                />
              </LookUpItem>
            ))}
          </LookUpGroup>
          <div className="absolute bottom-0 flex w-full justify-center border-t bg-background">
            <AddNewAddressTrigger
              title="Add New Address"
              onSubmit={onSubmit}
              isBasicAddress
            />
          </div>
        </LookUpContent>
      </LookUp>
    </div>
  );
}
