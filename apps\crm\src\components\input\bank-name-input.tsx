import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React, { useState } from "react";
import type { ControllerRenderProps } from "react-hook-form";

import { bankNames } from "@watt/crm/config/banks";

import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "../lookup";

type BankNameInputProps = ControllerRenderProps & {
  placeholder?: string;
} & React.ComponentProps<"input">;

export const BankNameInput: React.FC<BankNameInputProps> = ({
  ref,
  onChange,
  placeholder = "",
  ...field
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentBankSearch, setCurrentBankSearch] = useState("");
  const containerRef = React.useRef<HTMLInputElement | null>(null);

  return (
    <div ref={containerRef}>
      <LookUp open={isModalOpen} onOpenChange={setIsModalOpen}>
        <LookUpTrigger fieldValue={field.value ?? ""}>
          <span className={cn(!field.value && "text-muted-foreground")}>
            {field.value || placeholder}
          </span>
        </LookUpTrigger>
        <LookUpContent
          className="w-48 px-0"
          placeholder="Select bank name"
          searchInput={currentBankSearch}
          onSearchInputChange={currentBank => {
            setCurrentBankSearch(currentBank);
          }}
          shouldFilter={true}
          container={containerRef.current}
        >
          <LookUpGroup className="w-48 px-0">
            {Object.entries(bankNames).map(([key, value]) => (
              <LookUpItem
                ref={ref}
                value={value}
                key={key}
                onSelect={() => {
                  onChange(value);
                  setIsModalOpen(false);
                }}
              >
                {value}
              </LookUpItem>
            ))}
          </LookUpGroup>
        </LookUpContent>
      </LookUp>
    </div>
  );
};

BankNameInput.displayName = "BankNameInput";
