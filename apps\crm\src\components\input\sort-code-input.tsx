import React, { type ChangeEvent, useEffect, useState } from "react";
import type { ControllerRenderProps } from "react-hook-form";

import { Input } from "../ui/input";

// Utility function to format sort code
export const formatSortCode = (sortCode: string | null | undefined): string => {
  if (sortCode === null || sortCode === undefined) {
    return "";
  }
  if (sortCode.endsWith("-")) {
    return sortCode;
  }

  const trimmedSortCode = sortCode.slice(0, 8);
  const cleaned = trimmedSortCode.replace(/\D/g, "");
  const match = cleaned.match(/^(\d{0,2})(\d{0,2})(\d{0,2})$/);

  if (match) {
    const [_, part1, part2, part3] = match;
    return [part1, part2, part3].filter(Boolean).join("-");
  }
  return trimmedSortCode;
};

type SortCodeInputProps = ControllerRenderProps & {
  placeholder?: string;
};

export const SortCodeInput: React.FC<SortCodeInputProps> = ({
  ref,
  onChange,
  ...field
}) => {
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  const inputRef = React.useRef<HTMLInputElement | null>(null);

  const handleChange = (ev: ChangeEvent<HTMLInputElement>) => {
    const { value, selectionStart } = ev.target;
    const formattedValue = formatSortCode(value);

    onChange(formattedValue);
    if (selectionStart !== null) {
      setCursorPosition(selectionStart);
    }
  };

  useEffect(() => {
    if (!inputRef.current?.selectionStart) {
      return;
    }
    const thirdDigitCondition =
      cursorPosition === 3 && inputRef.current?.selectionStart < 5;
    const sixtDigitCondition =
      cursorPosition === 6 && inputRef.current?.selectionStart !== 6;

    // we need to add one extra position to include the sortcode dash
    if (thirdDigitCondition || sixtDigitCondition) {
      setInputCursorPosition(cursorPosition + 1);
      return;
    }

    setInputCursorPosition(cursorPosition);
  }, [cursorPosition]);

  const setInputCursorPosition = (position: number) => {
    if (inputRef.current) {
      inputRef.current.setSelectionRange(position, position);
    }
  };

  return (
    <Input
      {...field}
      value={formatSortCode(field.value)}
      ref={ref}
      onChange={handleChange}
    />
  );
};

SortCodeInput.displayName = "SortCodeInput";
