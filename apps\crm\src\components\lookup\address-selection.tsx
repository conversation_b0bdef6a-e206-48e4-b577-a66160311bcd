import type { Address_Find_Many } from "@watt/api/src/router/address";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { Flame, Zap } from "lucide-react";

type Address = Address_Find_Many[0];

type AddressSelectionProps = {
  address: Address;
  hideMetersInfo?: boolean;
};

export function AddressSelection({
  address,
  hideMetersInfo
}: AddressSelectionProps) {
  return (
    <div className="flex w-full items-center justify-between gap-2 hover:cursor-pointer">
      <div className="truncate text-xs">
        {getAddressDisplayName({
          displayName: address.displayName ?? null,
          postcode: address.postcode ?? ""
        })}
      </div>
      <div className="flex">
        {!hideMetersInfo && address.siteMeters && (
          <>
            {address.siteMeters[1] > 0 && <Zap className="h-4 w-4" />}
            {address.siteMeters[2] > 0 && <Flame className="h-4 w-4" />}
          </>
        )}
      </div>
    </div>
  );
}
