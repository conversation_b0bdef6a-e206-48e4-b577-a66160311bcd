import { BusinessType } from "@prisma/client";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { getBusinessType } from "@watt/db/src/maps/business-type-map";
import type { BusinessTargetResult } from "@watt/external-apis/src/libs/experian/business-targeter";
import {
  Building2,
  Hash,
  HelpingHand,
  MapPin,
  UserRound,
  X
} from "lucide-react";

import { Badge } from "@watt/crm/components/ui/badge";

type Company = Pick<
  BusinessTargetResult,
  | "businessType"
  | "commercialName"
  | "businessRef"
  | "postcode"
  | "businessStatus"
>;

// Local icon mapping for business types
const businessTypeIconMap = {
  [BusinessType.LTD]: Building2,
  [BusinessType.CHARITY]: HelpingHand,
  [BusinessType.SOLE_TRADER]: UserRound,
  [BusinessType.PLC]: Building2,
  [BusinessType.LLP]: Building2
};

export function CompanySelection(company: Company) {
  const businessType = getBusinessType(company.businessType);
  const BusinessTypeIcon = businessTypeIconMap[company.businessType];

  return (
    <div className="flex w-full flex-row justify-between gap-2">
      <div className="flex flex-row items-center gap-2">
        <BusinessTypeIcon className="h-4 w-4" />
        <span className="text-xs">{humanize(company.commercialName)}</span>
        <Badge variant="outline" className="gap-1 bg-muted">
          <Hash className="h-3 w-3" /> {company.businessRef}
        </Badge>
        {company.postcode && (
          <Badge variant="outline" className="gap-1 bg-muted">
            <MapPin className="h-3 w-3" /> {company.postcode}
          </Badge>
        )}
      </div>
      {company.businessStatus !== "A" && <X className="h-4 w-4" />}
    </div>
  );
}
