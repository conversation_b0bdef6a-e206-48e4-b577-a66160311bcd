import { type MPANLong, parseMpan } from "@watt/common/src/mpan/mpan";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

interface MpanInputFieldProps {
  mpan: string;
  isReadOnly?: boolean;
  className?: string;
  censored?: boolean;
}

export const MpanInputField: React.FC<MpanInputFieldProps> = ({
  mpan,
  isReadOnly = true,
  className,
  censored = false
}) => {
  const parsedMpan = parseMpan(mpan, censored);

  if (parsedMpan.error || !parsedMpan.data) {
    return <span>{mpan} Invalid MPAN</span>;
  }

  const {
    profileClass,
    meterTimeSwitchCode,
    lineLossFactorClass,
    distributor,
    uniqueIdentifier,
    checksum
  } = parsedMpan.data as MPANLong;

  const inputClassName = cn(
    className,
    "w-full border text-center focus:outline-none cursor-pointer"
  );

  return (
    <div className="grid grid-cols-5">
      <div className="col-span-1 row-span-2">
        <input
          type="text"
          className={cn(
            inputClassName,
            "h-full rounded-l-lg font-semibold text-lg"
          )}
          value="S"
          readOnly
        />
      </div>
      <div className="col-span-1">
        <input
          type="text"
          className={inputClassName}
          value={profileClass}
          readOnly={isReadOnly}
        />
      </div>
      <div className="col-span-2">
        <input
          type="text"
          className={inputClassName}
          value={meterTimeSwitchCode}
          readOnly={isReadOnly}
        />
      </div>
      <div className="col-span-1">
        <input
          type="text"
          className={cn(inputClassName, "rounded-tr-lg")}
          value={lineLossFactorClass}
          readOnly={isReadOnly}
        />
      </div>
      <div className="col-span-1">
        <input
          type="text"
          className={inputClassName}
          value={distributor}
          readOnly={isReadOnly}
        />
      </div>
      <div className="col-span-2">
        <input
          type="text"
          className={inputClassName}
          value={uniqueIdentifier.replace(/(.{4})/g, "$1 ")}
          readOnly={isReadOnly}
        />
      </div>
      <div className="col-span-1">
        <input
          type="text"
          className={cn(inputClassName, "rounded-br-lg")}
          value={checksum}
          readOnly={isReadOnly}
        />
      </div>
    </div>
  );
};
