import { parseMprn } from "@watt/common/src/mprn/mprn";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

interface MprnInputFieldProps {
  mprn: string;
  isReadOnly?: boolean;
  className?: string;
  censored?: boolean;
}

export const MprnInputField: React.FC<MprnInputFieldProps> = ({
  mprn,
  isReadOnly = true,
  className,
  censored = false
}) => {
  const parsedMprn = parseMprn(mprn, censored);

  if (parsedMprn.error || !parsedMprn.data) {
    return <span>{mprn} Invalid MPRN</span>;
  }

  const censoredMprn = parsedMprn.data;

  const inputClassName = cn(
    className,
    "w-full border text-center focus:outline-none h-full min-h-8 cursor-pointer"
  );

  return (
    <div className="grid grid-cols-5">
      <div className="col-span-1">
        <input
          type="text"
          className={cn(inputClassName, "rounded-l-lg font-semibold text-lg")}
          value="M"
          readOnly
        />
      </div>
      <div className="col-span-4">
        <input
          type="text"
          className={cn(inputClassName, "rounded-r-lg")}
          value={censoredMprn}
          readOnly={isReadOnly}
        />
      </div>
    </div>
  );
};
