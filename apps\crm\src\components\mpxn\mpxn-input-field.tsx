"use client";

import { copyToClipboard } from "@watt/crm/utils/copy";
import { UtilityType } from "@watt/db/src/enums";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Button } from "../ui/button";
import { MpanInputField } from "./mpan-input-field";
import { MprnInputField } from "./mprn-input-field";

type MpxnInputFieldProps = {
  meterIdentifier?: string;
  utilityType: UtilityType;
  censored?: boolean;
  className?: string;
};

export function MpxnInputField({
  meterIdentifier,
  utilityType,
  censored = false,
  className
}: MpxnInputFieldProps) {
  if (!meterIdentifier) {
    return <div>N/A</div>;
  }

  if (utilityType === UtilityType.ELECTRICITY) {
    return (
      <Button
        variant="link"
        onClick={() => copyToClipboard(meterIdentifier, "MPAN")}
        className={cn("flex w-[225px] text-xs", className)}
      >
        <MpanInputField mpan={meterIdentifier} censored={censored} />
      </Button>
    );
  }
  if (utilityType === UtilityType.GAS) {
    return (
      <Button
        variant="link"
        onClick={() => copyToClipboard(meterIdentifier, "MPRN")}
        className={cn("flex w-[225px] text-xs", className)}
      >
        <MprnInputField mprn={meterIdentifier} censored={censored} />
      </Button>
    );
  }

  return <div>N/A</div>;
}
