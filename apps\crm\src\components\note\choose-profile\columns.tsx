"use client";

import { BusinessType } from "@prisma/client";
import type { ColumnDef } from "@tanstack/react-table";
import type { CompaniesWith_Add_Con_Sit } from "@watt/api/src/router/company";
import type { SitesWith_Add_Com } from "@watt/api/src/router/site";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { CompanyAvatar } from "@watt/crm/components/avatar/company-avatar";
import { Badge } from "@watt/crm/components/ui/badge";

interface ChooseProfileColumns {
  companies: ColumnDef<CompaniesWith_Add_Con_Sit[number]>[];
  sites: ColumnDef<SitesWith_Add_Com[number]>[];
}

export const chooseProfileColumns: ChooseProfileColumns = {
  companies: [
    {
      accessorKey: "name",
      accessorFn: row => humanize(row.name),
      cell: ({ row }) => {
        const isSoleTrader: boolean =
          row.original.businessType === BusinessType.SOLE_TRADER;

        return (
          <div className="flex items-center space-x-2 hover:cursor-pointer">
            <CompanyAvatar displayName={row.getValue("name")} />
            <span>{row.getValue("name")}</span>
            <Badge variant="outline">
              {humanize(row.original.businessType)}
            </Badge>
            {!isSoleTrader && (
              <span className="text-gray-500 text-xs">
                {row.original.registrationNumber}
              </span>
            )}
          </div>
        );
      }
    },
    {
      accessorKey: "registrationNumber"
    },
    {
      accessorKey: "businessType",
      accessorFn: row => humanize(row.businessType)
    }
  ],
  sites: [
    {
      accessorKey: "siteRefId",
      accessorFn: row => composeSiteRef(row.siteRefId, "Ref. "),
      cell: ({ row }) => {
        return (
          <div className="flex items-center space-x-2 hover:cursor-pointer">
            <CompanyAvatar displayName={row.original.company.name} />
            <span>{row.getValue("siteRefId")}</span>
            <span className="text-gray-500 text-xs">
              {getAddressDisplayName(row.original.entityAddress)}
            </span>
          </div>
        );
      }
    },
    {
      accessorKey: "addressDisplayName",
      accessorFn: row => getAddressDisplayName(row.entityAddress)
    },
    {
      accessorKey: "companyName",
      accessorFn: row => humanize(row.company.name)
    }
  ]
};
