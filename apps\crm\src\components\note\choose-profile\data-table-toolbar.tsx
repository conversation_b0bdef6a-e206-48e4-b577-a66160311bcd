"use client";

import { X } from "lucide-react";

import type { Table } from "@tanstack/react-table";
import React from "react";
import { useDebounce } from "react-use";

import { Button } from "@watt/crm/components/ui/button";
import { Input } from "@watt/crm/components/ui/input";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isSiteProfile: boolean;
}

export function DataTableToolbar<TData>({
  table,
  isSiteProfile
}: DataTableToolbarProps<TData>) {
  const [debouncedSearchValue, setDebouncedSearchValue] = React.useState("");
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  useDebounce(
    () => {
      table.setGlobalFilter(debouncedSearchValue);
    },
    500, // You can adjust the debounce time as needed
    [debouncedSearchValue]
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearchValue(event.target.value);
  };

  const resetFilters = () => {
    table.resetColumnFilters();
    table.resetGlobalFilter();
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
    setDebouncedSearchValue("");
  };

  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  return (
    <div className="flex items-center space-x-2">
      <Input
        className="col-span-3 border-0 border-b py-6 font-medium placeholder:font-normal placeholder:text-gray-500 placeholder:italic focus-visible:ring-0 focus-visible:ring-offset-0"
        placeholder={
          isSiteProfile
            ? "Search Site Profile by Company Name, Site Ref. Number, Address or Postcode.."
            : "Search Company Profile by Company Name, Business Type or Registration Number..."
        }
        onChange={handleSearchChange}
        ref={searchInputRef}
      />
      {isFiltered && (
        <Button variant="ghost" onClick={resetFilters} size="sm">
          Reset
          <X className="ml-2 h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
