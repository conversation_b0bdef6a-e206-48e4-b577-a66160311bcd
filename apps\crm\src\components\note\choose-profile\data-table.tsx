import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable
} from "@tanstack/react-table";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { NoteModalData } from "@watt/crm/store/note";
import { NoteProfileType } from "@watt/db/src/enums";
import React from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableRow
} from "@watt/crm/components/ui/table";

import type { CompaniesWith_Add_Con_Sit } from "@watt/api/src/router/company";
import type { SitesWith_Add_Com } from "@watt/api/src/router/site";
import { trpcClient } from "@watt/crm/utils/api";
import { getNoteDisplayName } from "@watt/crm/utils/get-note-display-name";
import { chooseProfileColumns } from "./columns";
import { DataTableToolbar } from "./data-table-toolbar";

type ChooseProfileProps = {
  onSelect: (modalData: NoteModalData) => void;
  selectedProfileData: NoteModalData;
  isSiteProfile: boolean;
  filterSitesByCompanyId?: string;
};

type TableData = CompaniesWith_Add_Con_Sit[number] | SitesWith_Add_Com[number];

export function ChooseProfileDataTable({
  onSelect,
  selectedProfileData,
  isSiteProfile,
  filterSitesByCompanyId
}: ChooseProfileProps) {
  const { data: companies } = trpcClient.company.all.useQuery();
  const { data: sites } = trpcClient.site.all.useQuery();

  const siteData = React.useMemo(() => {
    return filterSitesByCompanyId
      ? (sites?.filter(site => site.companyId === filterSitesByCompanyId) ?? [])
      : (sites ?? []);
  }, [sites, filterSitesByCompanyId]);

  const selectedDataColumns = isSiteProfile
    ? {
        data: siteData ?? [],
        columns: chooseProfileColumns.sites
      }
    : {
        data: companies ?? [],
        columns: chooseProfileColumns.companies
      };

  const table = useReactTable<TableData>({
    data: selectedDataColumns.data,
    columns: selectedDataColumns.columns as ColumnDef<TableData>[],
    initialState: {
      pagination: {
        pageSize: selectedDataColumns.data.length
      },
      columnVisibility: {
        registrationNumber: false,
        addressDisplayName: false,
        businessType: false,
        companyName: false
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel()
  });

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} isSiteProfile={isSiteProfile} />
      <p className="font-medium text-xs">
        {isSiteProfile ? "Sites" : "Companies"}
      </p>
      <Table outerClassName="h-96 overflow-hidden hover:overflow-y-scroll">
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row, rowIndex) => (
              <TableRow
                className={cn(
                  "h-12 border-0 transition-all duration-300 hover:bg-muted-foreground/20",
                  (selectedProfileData.profile?.companyId ===
                    selectedDataColumns.data[rowIndex]?.id ||
                    selectedProfileData.profile?.siteId ===
                      selectedDataColumns.data[rowIndex]?.id) &&
                    "bg-muted-foreground/10"
                )}
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
                onClick={() => {
                  const rowData = row.original;
                  onSelect({
                    profile: {
                      ...("name" in rowData
                        ? {
                            companyId: rowData.id,
                            displayName: getNoteDisplayName({
                              companyName: rowData.name
                            }),
                            type: NoteProfileType.COMPANY
                          }
                        : {
                            companyId: rowData.company.id,
                            siteId: rowData.id,
                            displayName: getNoteDisplayName({
                              companyName: rowData.company.name,
                              siteRefId: rowData.siteRefId,
                              sitePostcode: rowData.entityAddress.postcode
                            }),
                            type: NoteProfileType.SITE
                          })
                    }
                  });
                }}
              >
                {row.getVisibleCells().map(cell => (
                  <TableCell key={cell.id} className="p-2 font-medium">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={selectedDataColumns.columns.length}
                className="h-24 text-center"
              >
                No results found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
