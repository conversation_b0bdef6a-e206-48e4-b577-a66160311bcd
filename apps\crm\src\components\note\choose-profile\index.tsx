import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { CompanyAvatar } from "@watt/crm/components/avatar/company-avatar";
import { Button } from "@watt/crm/components/ui/button";
import { Switch } from "@watt/crm/components/ui/switch";
import { useNoteStore } from "@watt/crm/store/note";
import { getNoteDisplayName } from "@watt/crm/utils/get-note-display-name";
import { NoteProfileType } from "@watt/db/src/enums";
import { useState } from "react";
import { ChooseProfileDataTable } from "./data-table";

interface ChooseProfileFormProps {
  onSubmit: () => void;
}

export function ChooseProfileForm({ onSubmit }: ChooseProfileFormProps) {
  const [isSiteProfile, setIsSiteProfile] = useState(false);

  const { noteProfileProps, noteModalData, setNoteModalData } = useNoteStore(
    state => ({
      noteProfileProps: state.noteProfileProps,
      noteModalData: state.noteModalData,
      setNoteModalData: state.setNoteModalData
    })
  );

  return (
    <div>
      <p className="font-medium">Select Profile</p>
      {noteProfileProps ? (
        // Creates a note for the Company profile
        <div className="mt-4 space-y-2 font-medium text-xs">
          <p>Company</p>
          <div className="max-h-40 overflow-auto">
            <Button
              variant="none"
              className={cn(
                "flex h-12 w-full items-center justify-start space-x-2 rounded-none p-2 hover:cursor-pointer hover:bg-muted-foreground/20",
                noteModalData.profile?.companyId ===
                  noteProfileProps.companyData.id &&
                  !noteModalData.profile?.siteId &&
                  "bg-muted-foreground/10"
              )}
              onClick={() => {
                setNoteModalData({
                  profile: {
                    companyId: noteProfileProps.companyData.id,
                    displayName: getNoteDisplayName({
                      companyName: noteProfileProps.companyData.name
                    }),
                    type: NoteProfileType.COMPANY
                  }
                });
              }}
            >
              <CompanyAvatar displayName={noteProfileProps.companyData.name} />
              <span className="text-sm">
                {humanize(noteProfileProps.companyData.name)}
              </span>
            </Button>
          </div>
          <ChooseProfileDataTable
            isSiteProfile={true}
            selectedProfileData={noteModalData}
            onSelect={setNoteModalData}
            filterSitesByCompanyId={noteProfileProps.companyData.id}
          />
        </div>
      ) : (
        // Creates note from the Notes page
        <div>
          <div className="my-4 flex items-center space-x-4">
            <Switch
              className="data-[state=checked]:bg-secondary"
              onCheckedChange={setIsSiteProfile}
            />
            <p className="font-medium text-sm">
              {isSiteProfile ? "Site" : "Company"}
            </p>
          </div>
          <ChooseProfileDataTable
            isSiteProfile={isSiteProfile}
            selectedProfileData={noteModalData}
            onSelect={setNoteModalData}
          />
        </div>
      )}
      <Button
        className="mt-4 w-full"
        variant="secondary"
        onClick={onSubmit}
        disabled={!noteModalData.profile}
      >
        Create Note
      </Button>
    </div>
  );
}
