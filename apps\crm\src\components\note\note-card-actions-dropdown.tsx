import type { ToggleHiddenNote } from "@watt/api/src/router/note";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@watt/crm/components/ui/alert-dialog";
import { Button, buttonVariants } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { useToast } from "@watt/crm/components/ui/use-toast";
import { trpcClient } from "@watt/crm/utils/api";
import { Eye, EyeOff, MoreVertical, Trash } from "lucide-react";
import { useState } from "react";

type NoteCardActionsDropdownProps = {
  noteId: string;
  showHideOption?: boolean;
  isHidden: boolean;
  showDeleteOption?: boolean;
  triggerClassName?: string;
  onToggleNoteVisibility?: (updateData: ToggleHiddenNote) => void;
  onDeleteNote?: () => void;
};

export function NoteCardActionsDropdown({
  noteId,
  showHideOption = false,
  isHidden,
  showDeleteOption = false,
  triggerClassName,
  onToggleNoteVisibility,
  onDeleteNote
}: NoteCardActionsDropdownProps) {
  const [hideDialogIsOpen, setHideDialogIsOpen] = useState(false);
  const [deleteDialogIsOpen, setDeleteDialogIsOpen] = useState(false);
  const { toast } = useToast();

  const noteUtils = trpcClient.useUtils().note;

  const toggleHiddenMutation = trpcClient.note.toggleHidden.useMutation({
    onSuccess: async () => {
      await noteUtils.invalidate();
    }
  });

  const deleteNoteMutation = trpcClient.note.delete.useMutation({
    onSuccess: async () => {
      await noteUtils.invalidate();
    }
  });

  const handleToggleVisibility = async () => {
    try {
      const newIsHidden = !isHidden;
      const updateData = await toggleHiddenMutation.mutateAsync({
        noteId,
        isHidden: newIsHidden
      });
      hideDialogIsOpen && setHideDialogIsOpen(false);
      toast({
        title: "Visibility updated",
        description: `The note is now ${newIsHidden ? "hidden" : "visible"} to other users.`,
        variant: "success"
      });
      onToggleNoteVisibility?.(updateData);
    } catch (err) {
      const error = err as Error;
      toast({
        title: "Visibility update failed",
        description: error?.message || "Unable to update the note visibility.",
        variant: "destructive"
      });
    }
  };

  const toggleNoteVisibility = async (event: React.MouseEvent) => {
    event.stopPropagation();
    if (!isHidden) {
      setHideDialogIsOpen(true);
    } else {
      await handleToggleVisibility();
    }
  };

  const confirmToggleVisibility = async (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    await handleToggleVisibility();
  };

  const handleDeleteNote = (event: React.MouseEvent) => {
    event.stopPropagation();
    setDeleteDialogIsOpen(true);
  };

  const confirmDeleteNote = async (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    try {
      await deleteNoteMutation.mutateAsync({ id: noteId });
      toast({
        title: "Note deleted",
        description: "The note has been removed from your list.",
        variant: "success"
      });
      setDeleteDialogIsOpen(false);
      onDeleteNote?.();
    } catch (err) {
      const error = err as Error;
      toast({
        title: "Note deletion failed",
        description: error?.message || "Unable to delete the note.",
        variant: "destructive"
      });
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className={triggerClassName}>
            <MoreVertical className="h-3.5 w-3.5 rotate-90" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          {showHideOption && (
            <DropdownMenuItem
              className="flex cursor-pointer items-center"
              onClick={toggleNoteVisibility}
            >
              {isHidden ? (
                <Eye className="mr-2 h-3.5 w-3.5" />
              ) : (
                <EyeOff className="mr-2 h-3.5 w-3.5" />
              )}
              {isHidden ? "Show Note" : "Hide Note"}
            </DropdownMenuItem>
          )}
          {showDeleteOption && (
            <DropdownMenuItem
              className="flex cursor-pointer items-center"
              onClick={handleDeleteNote}
            >
              <Trash className="mr-2 h-3.5 w-3.5" />
              Delete Note
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={hideDialogIsOpen} onOpenChange={setHideDialogIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to hide this note?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Hidden notes are not visible to sales agents, but are visible to
              other roles with the additional Hidden label.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={e => e.stopPropagation()}
              disabled={toggleHiddenMutation.isPending}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmToggleVisibility}
              className={buttonVariants({ variant: "destructive" })}
              disabled={toggleHiddenMutation.isPending}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog
        open={deleteDialogIsOpen}
        onOpenChange={setDeleteDialogIsOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete this note?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={e => e.stopPropagation()}
              disabled={deleteNoteMutation.isPending}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteNote}
              className={buttonVariants({ variant: "destructive" })}
              disabled={deleteNoteMutation.isPending}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
