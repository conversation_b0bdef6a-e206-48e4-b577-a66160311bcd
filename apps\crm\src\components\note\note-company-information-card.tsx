import type { AllNotes } from "@watt/api/src/router/note";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { isCreatedByMe } from "@watt/crm/app/utils/access-permissions";
import { Badge } from "@watt/crm/components/ui/badge";
import { useAppStore } from "@watt/crm/store/app-store";
import { getProfileCompanySiteUrl } from "@watt/crm/utils/get-company-site-url";
import { format } from "date-fns";
import Link from "next/link";
import { CompanyAvatar } from "../avatar/company-avatar";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { NoteCardActionsDropdown } from "./note-card-actions-dropdown";

type NoteCompanyInformationCardProps = {
  note: AllNotes["items"][number];
};

export function NoteCompanyInformationCard({
  note
}: NoteCompanyInformationCardProps) {
  const {
    id: noteId,
    companyId,
    isHidden,
    company,
    site,
    createdById,
    hiddenUpdatedAt,
    hiddenUpdatedBy
  } = note;
  const {
    permissions: { isAdmin, isManager }
  } = useAppStore(state => state.userData);

  const isAdminOrManager = isAdmin || isManager;
  const isNoteCreatedByMe = isCreatedByMe(createdById);
  const canManageNote = isAdminOrManager || isNoteCreatedByMe;

  return (
    <div
      className={cn(
        "flex w-full items-center justify-between gap-2 overflow-hidden py-1",
        {
          "items-start": !!site
        }
      )}
    >
      <div className="flex min-w-0 flex-1 items-center gap-3">
        <CompanyAvatar displayName={company.name} className="h-8 w-8" />
        <div className="flex min-w-0 flex-1 flex-col gap-0.5">
          <Link
            href={getProfileCompanySiteUrl(companyId, site?.siteRefId)}
            className="block truncate font-medium text-sm hover:cursor-pointer hover:underline"
            onClick={e => {
              e.stopPropagation();
            }}
          >
            {humanize(company.name)}
          </Link>

          {!!site && (
            <Link
              href={getProfileCompanySiteUrl(companyId, site.siteRefId)}
              className="truncate text-muted-foreground text-xs hover:cursor-pointer hover:underline"
              onClick={e => {
                e.stopPropagation();
              }}
            >
              {composeSiteRef(site.siteRefId)}
            </Link>
          )}
        </div>
      </div>
      <div className="flex items-center gap-x-2 pr-1">
        {isHidden && hiddenUpdatedBy && hiddenUpdatedAt && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="accent">Hidden</Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Hidden by {hiddenUpdatedBy.fullName} on{" "}
                {format(hiddenUpdatedAt, "dd/MM/yyyy HH:mm")}
              </p>
            </TooltipContent>
          </Tooltip>
        )}
        {canManageNote && (
          <NoteCardActionsDropdown
            noteId={noteId}
            showHideOption={isAdminOrManager}
            isHidden={!!isHidden}
            showDeleteOption={isNoteCreatedByMe}
            triggerClassName="size-6 border-0 p-0"
          />
        )}
      </div>
    </div>
  );
}
