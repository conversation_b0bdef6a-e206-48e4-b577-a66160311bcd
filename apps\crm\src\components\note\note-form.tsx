import type { NoteModalData, NotePinOrMinData } from "@watt/crm/store/note";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { z } from "zod";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@watt/crm/components/ui/alert-dialog";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

import type { ToggleHiddenNote } from "@watt/api/src/router/note";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { isCreatedByMe } from "@watt/crm/app/utils/access-permissions";
import { useAppStore } from "@watt/crm/store/app-store";
import { useDebounce } from "react-use";
import { Button, buttonVariants } from "../ui/button";
import { NoteCardActionsDropdown } from "./note-card-actions-dropdown";
import { NoteTemplates, type TemplatePresets } from "./note-templates";

export interface NoteFormProps {
  formData: NotePinOrMinData;
  onSubmit: () => void;
  className?: string;
  textAreaClassName?: string;
  handleFormDataChange: (data: NoteModalData | NotePinOrMinData) => void;
  handleToggleNoteVisibility: (updateData: ToggleHiddenNote) => void;
  handleDeleteNote: () => void;
  handleSaveChanges?: (save: boolean) => void;
  saveChangesDialogIsOpen?: boolean;
  setSaveChangesDialogIsOpen?: (value: boolean) => void;
  isPending: boolean;
}

export function NoteForm({
  onSubmit,
  className,
  textAreaClassName,
  formData,
  handleFormDataChange,
  handleToggleNoteVisibility,
  handleDeleteNote,
  handleSaveChanges,
  saveChangesDialogIsOpen,
  setSaveChangesDialogIsOpen,
  isPending
}: NoteFormProps) {
  const [isReadOnly, setIsReadOnly] = useState(true);

  const formSchema = z.object({
    title: z.string().optional(),
    description: z.string().optional()
  });

  const form = useZodForm({
    schema: formSchema,
    defaultValues: {
      title: formData?.title ?? "",
      description: formData?.description ?? ""
    }
  });

  const {
    permissions: { isAdmin, isManager }
  } = useAppStore(state => state.userData);

  const isAdminOrManager = isAdmin || isManager;
  const isNoteCreatedByMe = isCreatedByMe(formData?.createdById);
  const canManageNote = isAdminOrManager || isNoteCreatedByMe;
  const title = form.watch("title");
  const description = form.watch("description");
  const isEmptyTitle = title?.length === 0;
  const isEmptyDescription = description?.length === 0;

  const setTemplate = (template: TemplatePresets) => {
    isEmptyTitle &&
      form.setValue("title", template.value.text, { shouldDirty: true });
    isEmptyDescription &&
      form.setValue("description", template.value.description, {
        shouldDirty: true
      });
  };

  useEffect(() => {
    if (formData?.createdById) {
      setIsReadOnly(!isCreatedByMe(formData?.createdById));
    }
  }, [formData?.createdById]);

  useDebounce(
    () => {
      // When switching from modal to pinned view we want to retain the dirty state
      // Otherwise use the dirty state from the form
      const isNoteDirty =
        formData.isNoteDirty === true || form.formState.isDirty;
      handleFormDataChange({
        ...formData,
        title,
        description,
        isNoteDirty
      });
    },
    500,
    [title, description]
  );

  return (
    <FormWrapper
      form={form}
      handleSubmit={onSubmit}
      className={cn("mt-4 space-y-4", className)}
    >
      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormControl>
              <Input
                {...field}
                className="col-span-3 border-none font-medium text-2xl focus-visible:ring-0 focus-visible:ring-offset-0"
                placeholder="Untitled note"
                disabled={isReadOnly}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormControl>
              <Textarea
                {...field}
                className={cn(
                  "col-span-3 resize-none border-none text-md focus-visible:ring-0 focus-visible:ring-offset-0",
                  textAreaClassName
                )}
                placeholder={
                  isReadOnly ? "Empty" : "Start typing or use a template"
                }
                disabled={isReadOnly}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {!isReadOnly && isEmptyDescription && (
        <NoteTemplates setTemplate={setTemplate} className="" />
      )}
      <div className="flex space-x-6">
        <Button
          type="submit"
          variant="secondary"
          className="flex grow"
          disabled={isPending || isReadOnly}
        >
          {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Note
        </Button>
        {canManageNote && (
          <NoteCardActionsDropdown
            noteId={formData.id ?? ""}
            showHideOption={isAdminOrManager}
            isHidden={!!formData.isHidden}
            showDeleteOption={isNoteCreatedByMe}
            onToggleNoteVisibility={handleToggleNoteVisibility}
            onDeleteNote={handleDeleteNote}
          />
        )}
      </div>
      {/* Dialog 2 - Save changes prompt on modal close (only shows if the form is dirty)*/}
      {handleSaveChanges && (
        <AlertDialog
          open={saveChangesDialogIsOpen}
          onOpenChange={setSaveChangesDialogIsOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                Do you want to save the changes?
              </AlertDialogTitle>
              <AlertDialogDescription>
                Your changes will be lost if you do not save them.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction
                onClick={() => handleSaveChanges(false)}
                className={buttonVariants({
                  variant: "destructive",
                  width: "sm"
                })}
              >
                Discard
              </AlertDialogAction>
              <AlertDialogCancel
                onClick={() => handleSaveChanges(true)}
                className={buttonVariants({
                  variant: "secondary",
                  width: "sm",
                  className: "hover:text-secondary-foreground"
                })}
              >
                Save
              </AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </FormWrapper>
  );
}
