"use client";

import type { ToggleHiddenNote } from "@watt/api/src/router/note";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { isCreatedByMe } from "@watt/crm/app/utils/access-permissions";
import {
  DropdownMultiSelect,
  DropdownMultiSelectContent,
  DropdownMultiSelectGroup,
  DropdownMultiSelectItem,
  DropdownMultiSelectLinkTrigger
} from "@watt/crm/components/dropdown-checkbox/dropdown-multi-select";
import { Button } from "@watt/crm/components/ui/button";
import { type AssociatedSiteMeter, useNoteStore } from "@watt/crm/store/note";
import { trpcClient } from "@watt/crm/utils/api";
import { getProfileCompanySiteUrl } from "@watt/crm/utils/get-company-site-url";
import { format } from "date-fns";
import { ArrowDownRight, Check, Minus, Plus, X } from "lucide-react";
import Link from "next/link";
import { CompanyAvatar } from "../avatar/company-avatar";
import { UserAvatar } from "../avatar/user-avatar";
import { Badge } from "../ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { NoteForm } from "./note-form";

type NoteModalContentProps = {
  onModalClose: () => void;
  onMinimize: () => void;
  onPin: () => void;
  onSubmit: () => void;
  onToggleNoteVisibility: (updateData: ToggleHiddenNote) => void;
  onDeleteNote: () => void;
  handleSaveChanges: (save: boolean) => void;
  saveChangesDialogIsOpen: boolean;
  setSaveChangesDialogIsOpen: (isOpen: boolean) => void;
  isPending: boolean;
};

export function NoteModalContent({
  onModalClose,
  onMinimize,
  onPin,
  onSubmit,
  onToggleNoteVisibility,
  onDeleteNote,
  handleSaveChanges,
  saveChangesDialogIsOpen,
  setSaveChangesDialogIsOpen,
  isPending
}: NoteModalContentProps) {
  const { noteModalData, setNoteModalData } = useNoteStore(state => ({
    noteModalData: state.noteModalData,
    setNoteModalData: state.setNoteModalData
  }));
  const displayName = noteModalData.profile?.displayName;
  const siteRefId = composeSiteRef(noteModalData.profile?.siteRefId);
  const selectedMeterIds =
    noteModalData.associatedSiteMeters?.map(meter => meter.id) ?? [];
  const selectedMeterNumbers =
    noteModalData.associatedSiteMeters?.map(meter => meter.meterNumber) ?? [];

  const { data: siteMeters } =
    trpcClient.siteMeter.findCompanySiteSiteMeters.useQuery(
      { companySiteId: noteModalData.profile?.siteId ?? "" },
      { enabled: !!noteModalData.profile?.siteId }
    );

  const toggleMeter = (meter: AssociatedSiteMeter) => {
    if (!isCreatedByMe(noteModalData.createdById)) {
      return;
    }

    const currentMeterAssociations = noteModalData.associatedSiteMeters ?? [];
    const newMeterAssociations = currentMeterAssociations.find(
      association => association.id === meter.id
    )
      ? currentMeterAssociations.filter(
          association => association.id !== meter.id
        )
      : [...currentMeterAssociations, meter];
    setNoteModalData({
      ...noteModalData,
      associatedSiteMeters: newMeterAssociations,
      isNoteDirty: true
    });
  };

  return (
    <>
      <div className="flex items-start justify-between gap-2">
        <div
          className={cn(
            "flex",
            siteRefId ? "items-start" : "items-center",
            "gap-2"
          )}
        >
          <CompanyAvatar
            displayName={displayName}
            className={cn("h-8 w-8 hover:cursor-pointer")}
          />
          <div className="flex flex-col gap-0.5">
            <Link
              href={getProfileCompanySiteUrl(
                noteModalData.profile?.companyId,
                noteModalData.profile?.siteRefId
              )}
              className="line-clamp-1 font-medium text-sm hover:cursor-pointer hover:underline"
            >
              {displayName}
            </Link>
            {siteRefId && (
              <Link
                href={getProfileCompanySiteUrl(
                  noteModalData.profile?.companyId,
                  noteModalData.profile?.siteRefId
                )}
                className="font-medium text-sm hover:cursor-pointer hover:underline"
              >
                {siteRefId}
              </Link>
            )}
            {siteMeters && siteMeters.length > 0 && (
              <DropdownMultiSelect>
                <DropdownMultiSelectLinkTrigger
                  placeholder="Link meters"
                  fieldValue={selectedMeterNumbers}
                  disabled={!isCreatedByMe(noteModalData.createdById)}
                >
                  <Plus className="mr-1 h-3 w-3" />
                  Link Meter
                </DropdownMultiSelectLinkTrigger>
                <DropdownMultiSelectContent align="start">
                  <DropdownMultiSelectGroup>
                    {siteMeters.map(meter => (
                      <DropdownMultiSelectItem
                        key={meter.id}
                        onSelect={() => toggleMeter(meter)}
                        disabled={!isCreatedByMe(noteModalData.createdById)}
                      >
                        <div className="flex items-center">
                          <div
                            className={cn(
                              "mr-2 flex size-4 items-center justify-center rounded-sm border",
                              selectedMeterIds.includes(meter.id)
                                ? "bg-primary text-primary-foreground"
                                : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <Check className="size-4" />
                          </div>
                          {meter.meterNumber}
                        </div>
                      </DropdownMultiSelectItem>
                    ))}
                  </DropdownMultiSelectGroup>
                </DropdownMultiSelectContent>
              </DropdownMultiSelect>
            )}
          </div>
        </div>
        <div className="flex items-center gap-3">
          {noteModalData.isHidden &&
            noteModalData.hiddenUpdatedByFullName &&
            noteModalData.hiddenUpdatedAt && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge variant="accent">Hidden</Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Hidden by {noteModalData.hiddenUpdatedByFullName} on{" "}
                    {format(noteModalData.hiddenUpdatedAt, "dd/MM/yyyy HH:mm")}
                  </p>
                </TooltipContent>
              </Tooltip>
            )}
          <Tooltip>
            <TooltipTrigger asChild>
              {/* Without the span, the tooltip automatically opens when opening the note modal*/}
              <span className="hover:cursor-pointer">
                <UserAvatar
                  fullName={noteModalData.createdBy}
                  fallbackClassName="bg-secondary"
                />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{noteModalData.createdBy}</p>
            </TooltipContent>
          </Tooltip>
          <Button
            variant="dialog"
            className="relative h-auto p-0 opacity-70"
            onClick={onMinimize}
          >
            <Minus className="size-4" />
            <span className="sr-only fixed">Minimise</span>
          </Button>
          <Button
            variant="dialog"
            className="relative h-auto p-0 opacity-70"
            onClick={onPin}
          >
            <ArrowDownRight className="size-4" />
            <span className="sr-only fixed">Pin</span>
          </Button>
          <Button
            variant="dialog"
            className="relative h-auto p-0 opacity-70"
            onClick={onModalClose}
          >
            <X className="size-4" />
            <span className="sr-only fixed">Close</span>
          </Button>
        </div>
      </div>
      <NoteForm
        key={noteModalData.id}
        formData={noteModalData}
        handleFormDataChange={setNoteModalData}
        onSubmit={onSubmit}
        textAreaClassName="h-80"
        handleToggleNoteVisibility={onToggleNoteVisibility}
        handleDeleteNote={onDeleteNote}
        handleSaveChanges={handleSaveChanges}
        saveChangesDialogIsOpen={saveChangesDialogIsOpen}
        setSaveChangesDialogIsOpen={setSaveChangesDialogIsOpen}
        isPending={isPending}
      />
    </>
  );
}
