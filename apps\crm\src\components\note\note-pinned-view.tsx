"use client";

import type { ToggleHiddenNote } from "@watt/api/src/router/note";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { isCreatedByMe } from "@watt/crm/app/utils/access-permissions";
import {
  DropdownMultiSelect,
  DropdownMultiSelectContent,
  DropdownMultiSelectGroup,
  DropdownMultiSelectItem,
  DropdownMultiSelectLinkTrigger
} from "@watt/crm/components/dropdown-checkbox/dropdown-multi-select";
import { Button } from "@watt/crm/components/ui/button";
import { type AssociatedSiteMeter, useNoteStore } from "@watt/crm/store/note";
import { trpcClient } from "@watt/crm/utils/api";
import { getProfileCompanySiteUrl } from "@watt/crm/utils/get-company-site-url";
import { format } from "date-fns";
import { Check, Maximize, Minus, Plus, X } from "lucide-react";
import Link from "next/link";
import { CompanyAvatar } from "../avatar/company-avatar";
import { UserAvatar } from "../avatar/user-avatar";
import { Badge } from "../ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { NoteForm } from "./note-form";

type NotePinnedViewProps = {
  onMinimize: () => void;
  onExpand: () => void;
  onClose: () => void;
  onSubmit: () => void;
  onToggleNoteVisibility: (updateData: ToggleHiddenNote) => void;
  onDeleteNote: () => void;
  handleSaveChanges: (save: boolean) => void;
  saveChangesDialogIsOpen: boolean;
  setSaveChangesDialogIsOpen: (isOpen: boolean) => void;
  isPending: boolean;
};

export function NotePinnedView({
  onMinimize,
  onExpand,
  onClose,
  onSubmit,
  onToggleNoteVisibility,
  onDeleteNote,
  handleSaveChanges,
  saveChangesDialogIsOpen,
  setSaveChangesDialogIsOpen,
  isPending
}: NotePinnedViewProps) {
  const { notePinOrMinData, setNotePinOrMinData } = useNoteStore(state => ({
    notePinOrMinData: state.notePinOrMinData,
    setNotePinOrMinData: state.setNotePinOrMinData
  }));
  const displayName = notePinOrMinData.profile?.displayName;
  const selectedMeterIds =
    notePinOrMinData.associatedSiteMeters?.map(meter => meter.id) ?? [];
  const selectedMeterNumbers =
    notePinOrMinData.associatedSiteMeters?.map(meter => meter.meterNumber) ??
    [];

  const { data: siteMeters } =
    trpcClient.siteMeter.findCompanySiteSiteMeters.useQuery(
      { companySiteId: notePinOrMinData.profile?.siteId ?? "" },
      { enabled: !!notePinOrMinData.profile?.siteId }
    );

  const toggleMeter = (meter: AssociatedSiteMeter) => {
    if (!isCreatedByMe(notePinOrMinData.createdById)) {
      return;
    }

    const currentMeterAssociations =
      notePinOrMinData.associatedSiteMeters ?? [];
    const newMeterAssociations = currentMeterAssociations.find(
      association => association.id === meter.id
    )
      ? currentMeterAssociations.filter(
          association => association.id !== meter.id
        )
      : [...currentMeterAssociations, meter];
    setNotePinOrMinData({
      ...notePinOrMinData,
      associatedSiteMeters: newMeterAssociations
    });
  };

  return (
    <div className="absolute right-6 bottom-6 z-10 min-h-80 w-[95%] overflow-hidden rounded-md border bg-background p-6 shadow-lg transition-all duration-1000 sm:w-[600px]">
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-2">
          <CompanyAvatar displayName={displayName} />
          <div className="flex flex-col gap-0.5">
            <Link
              href={getProfileCompanySiteUrl(
                notePinOrMinData.profile?.companyId,
                notePinOrMinData.profile?.siteRefId
              )}
              className="line-clamp-1 font-medium text-sm hover:cursor-pointer hover:underline"
            >
              {displayName}
            </Link>
            {siteMeters && siteMeters.length > 0 && (
              <DropdownMultiSelect>
                <DropdownMultiSelectLinkTrigger
                  placeholder="Link meters"
                  fieldValue={selectedMeterNumbers}
                  disabled={!isCreatedByMe(notePinOrMinData.createdById)}
                >
                  <Plus className="mr-1 h-3 w-3" />
                  Link Meter
                </DropdownMultiSelectLinkTrigger>
                <DropdownMultiSelectContent>
                  <DropdownMultiSelectGroup>
                    {siteMeters.map(meter => (
                      <DropdownMultiSelectItem
                        key={meter.id}
                        onSelect={() =>
                          toggleMeter({
                            id: meter.id,
                            meterNumber: meter.meterNumber
                          })
                        }
                        disabled={!isCreatedByMe(notePinOrMinData.createdById)}
                      >
                        <div className="flex items-center">
                          <div
                            className={cn(
                              "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",
                              selectedMeterIds?.includes(meter.id)
                                ? "bg-primary text-primary-foreground"
                                : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <Check className="h-4 w-4" />
                          </div>
                          {meter.meterNumber}
                        </div>
                      </DropdownMultiSelectItem>
                    ))}
                  </DropdownMultiSelectGroup>
                </DropdownMultiSelectContent>
              </DropdownMultiSelect>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2.5">
          {notePinOrMinData.isHidden &&
            notePinOrMinData.hiddenUpdatedByFullName &&
            notePinOrMinData.hiddenUpdatedAt && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge variant="accent">Hidden</Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Hidden by {notePinOrMinData.hiddenUpdatedByFullName} on{" "}
                    {format(
                      notePinOrMinData.hiddenUpdatedAt,
                      "dd/MM/yyyy HH:mm"
                    )}
                  </p>
                </TooltipContent>
              </Tooltip>
            )}
          <Tooltip>
            <TooltipTrigger>
              <UserAvatar fullName={notePinOrMinData.createdBy} />
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{notePinOrMinData.createdBy}</p>
            </TooltipContent>
          </Tooltip>
          <Button
            variant="dialog"
            className="relative h-auto p-0 opacity-70"
            onClick={onMinimize}
          >
            <Minus className="h-4 w-4" />
            <span className="sr-only fixed">Minimise</span>
          </Button>
          <Button
            variant="dialog"
            className="relative h-auto p-0 opacity-70"
            onClick={onExpand}
          >
            <Maximize className="h-4 w-4" />
            <span className="sr-only fixed">Expand</span>
          </Button>
          <Button
            variant="dialog"
            className="relative h-auto p-0 opacity-70"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
            <span className="sr-only fixed">Close</span>
          </Button>
        </div>
      </div>
      <NoteForm
        key={notePinOrMinData.id}
        formData={notePinOrMinData}
        handleFormDataChange={setNotePinOrMinData}
        onSubmit={onSubmit}
        textAreaClassName="h-28"
        handleToggleNoteVisibility={onToggleNoteVisibility}
        handleDeleteNote={onDeleteNote}
        handleSaveChanges={handleSaveChanges}
        saveChangesDialogIsOpen={saveChangesDialogIsOpen}
        setSaveChangesDialogIsOpen={setSaveChangesDialogIsOpen}
        isPending={isPending}
      />
    </div>
  );
}
