"use client";

import { <PERSON><PERSON><PERSON>R<PERSON>, Loader2, Maximize, X } from "lucide-react";

import {
  type NoteModalData,
  type NotePinOrMinData,
  type ViewState,
  useNoteStore
} from "@watt/crm/store/note";
import { trpcClient } from "@watt/crm/utils/api";
import { useCallback, useEffect, useState } from "react";

import { Button } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";

import type { ToggleHiddenNote } from "@watt/api/src/router/note";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { VisuallyHidden } from "../ui/visually-hidden";
import { ChooseProfileForm } from "./choose-profile";
import { NoteModalContent } from "./note-modal-content";
import { NotePinnedView } from "./note-pinned-view";

export interface NoteQueryParams {
  modal: string;
  id: string;
  createdBy?: string;
  isHidden?: string;
  search?: string;
}

export function NoteProvider() {
  const { queryParams, setQueryParams, removeQueryParams } =
    useQueryParams<NoteQueryParams>();
  const { modal, id } = queryParams;
  const queryNoteData = trpcClient.note.get.useQuery(
    { id: id || "" },
    { enabled: false }
  );
  const [modelSaveChangesDialogOpen, setModalSaveChangesDialogOpen] =
    useState(false);
  const [pinnedSaveChangesDialogOpen, setPinnedSaveChangesDialogOpen] =
    useState(false);
  const {
    mutateAsync: upsertNoteMutation,
    isPending,
    isError: noteUpsertIsError,
    error: noteUpsertErrorMessage
  } = trpcClient.note.upsert.useMutation();

  const {
    chooseProfileModalIsOpen,
    setChooseProfileModal,
    noteModalIsOpen,
    setNoteModalIsOpen,
    notePinOrMinIsOpen,
    setNotePinOrMinIsOpen,
    noteModalData,
    setNoteModalData,
    notePinOrMinData,
    setNotePinOrMinData
  } = useNoteStore(state => ({
    chooseProfileModalIsOpen: state.chooseProfileModalIsOpen,
    setChooseProfileModal: state.setChooseProfileModal,
    noteModalIsOpen: state.noteModalIsOpen,
    setNoteModalIsOpen: state.setNoteModalIsOpen,
    notePinOrMinIsOpen: state.notePinOrMinIsOpen,
    setNotePinOrMinIsOpen: state.setNotePinOrMinIsOpen,
    noteModalData: state.noteModalData,
    setNoteModalData: state.setNoteModalData,
    notePinOrMinData: state.notePinOrMinData,
    setNotePinOrMinData: state.setNotePinOrMinData
  }));

  useEffect(() => {
    const handleQueryBasedModal = async () => {
      if (modal === "note" && id) {
        if (!noteModalData.id) {
          const responseFromQuery = await queryNoteData.refetch();

          if (responseFromQuery.isError) {
            return;
          }

          responseFromQuery.data &&
            setNoteModalData({
              id: responseFromQuery.data.id,
              title: responseFromQuery.data.title || "",
              description: responseFromQuery.data.description || "",
              profile: {
                companyId: responseFromQuery.data.companyId,
                displayName: humanize(responseFromQuery.data.company.name),
                type: responseFromQuery.data.profileType,
                siteId: responseFromQuery.data.siteId || undefined,
                siteRefId: responseFromQuery.data.site?.siteRefId || undefined
              },
              createdById: responseFromQuery.data.createdById,
              createdBy: responseFromQuery.data.createdBy.fullName,
              associatedSiteMeters: responseFromQuery.data.siteMeters.map(
                meter => ({
                  id: meter.id,
                  meterNumber:
                    meter.electricSiteMeter?.mpanValue ||
                    meter.gasSiteMeter?.mprnValue ||
                    ""
                })
              ),
              isHidden: !!responseFromQuery.data.isHidden,
              hiddenUpdatedByFullName:
                responseFromQuery.data.hiddenUpdatedBy?.fullName,
              hiddenUpdatedAt: responseFromQuery.data.hiddenUpdatedAt
            });
        }

        setNoteModalIsOpen(true);
      } else {
        setNoteModalIsOpen(false);
      }
    };

    handleQueryBasedModal();
  }, [
    modal,
    id,
    noteModalData.id,
    queryNoteData,
    setNoteModalData,
    setNoteModalIsOpen
  ]);

  const formatAndSubmitNoteData = useCallback(
    async (data: NoteModalData | NotePinOrMinData) => {
      if (!data.profile || !data.id) {
        return;
      }

      await upsertNoteMutation({
        id: data.id,
        title: data.title,
        description: data.description,
        companyId: data.profile.companyId,
        profileType: data.profile.type,
        ...(data.profile.siteId && { siteId: data.profile.siteId }),
        ...(data.associatedSiteMeters && {
          siteMeterIds: data.associatedSiteMeters.map(meter => meter.id)
        })
      });
    },
    [upsertNoteMutation]
  );

  // Note reset
  const resetModal = useCallback(() => {
    setNoteModalData({});
    removeQueryParams(["id", "modal"]);
  }, [setNoteModalData, removeQueryParams]);

  const resetPinMin = useCallback(() => {
    setNotePinOrMinIsOpen(false);
    setNotePinOrMinData({});
  }, [setNotePinOrMinIsOpen, setNotePinOrMinData]);

  // Note view state switch
  const switchModalToAnotherView = useCallback(
    (viewState: ViewState) => {
      setNotePinOrMinData({ ...noteModalData, viewState });
      setNotePinOrMinIsOpen(true);
      resetModal();
    },
    [noteModalData, setNotePinOrMinData, setNotePinOrMinIsOpen, resetModal]
  );

  const switchPinnedToMinimisedView = () => {
    setNotePinOrMinData({ ...notePinOrMinData, viewState: "minimised" });
  };

  const switchMinimisedToPinnedView = () => {
    setNotePinOrMinData({ ...notePinOrMinData, viewState: "pinned" });
  };

  const switchToModalView = () => {
    setNoteModalData({
      ...notePinOrMinData
    });
    setQueryParams({ id: notePinOrMinData.id, modal: "note" });
    resetPinMin();
  };

  // Note form close
  const handleModalFormClose = () => {
    if (noteModalData.isNoteDirty) {
      setModalSaveChangesDialogOpen(true);
      return;
    }
    resetModal();
  };

  const handlePinMinFormClose = () => {
    if (notePinOrMinData.isNoteDirty) {
      if (notePinOrMinData.viewState === "minimised") {
        switchMinimisedToPinnedView();
      }

      setPinnedSaveChangesDialogOpen(true);
      return;
    }
    resetPinMin();
  };

  // Clear note selection from choose profile
  const handleCancelChooseProfile = () => {
    setNoteModalData({});
    setChooseProfileModal(false);
  };

  // Create new note - once a company or site profile is selected we create an empty note with the selected profile
  const handleProfileSelection = async () => {
    if (noteModalData.profile) {
      const data = await upsertNoteMutation({
        companyId: noteModalData.profile.companyId,
        profileType: noteModalData.profile.type,
        ...(noteModalData.profile.siteId && {
          siteId: noteModalData.profile.siteId
        })
      });

      if (!data) {
        toast({
          title: "Failed to create Note",
          description: "Please try again."
        });
        return;
      }

      setNoteModalData({
        ...noteModalData,
        id: data.id,
        createdBy: data.createdBy.fullName,
        createdById: data.createdById
      });
      setQueryParams({ id: data.id, modal: "note" });
    }
    setChooseProfileModal(false);
  };

  // Note form submit
  const onSubmitModalNoteForm = useCallback(async () => {
    await formatAndSubmitNoteData(noteModalData);
    resetModal();
  }, [noteModalData, formatAndSubmitNoteData, resetModal]);

  const onSubmitPinnedNoteForm = useCallback(async () => {
    await formatAndSubmitNoteData(notePinOrMinData);
    resetPinMin();
  }, [notePinOrMinData, formatAndSubmitNoteData, resetPinMin]);

  // Note form save changes
  const handleModalSaveChanges = useCallback(
    async (save: boolean) => {
      if (save) {
        await onSubmitModalNoteForm();
        return;
      }
      resetModal();
    },
    [onSubmitModalNoteForm, resetModal]
  );

  const handlePinnedSaveChanges = useCallback(
    async (save: boolean) => {
      if (save) {
        await onSubmitPinnedNoteForm();
        return;
      }
      resetPinMin();
    },
    [onSubmitPinnedNoteForm, resetPinMin]
  );

  const updateNoteVisibility = useCallback(
    (updateData: ToggleHiddenNote) => {
      console.log("updateNoteVisibility", updateData);
      setNoteModalData({
        ...noteModalData,
        isHidden: updateData?.isHidden ?? undefined,
        hiddenUpdatedByFullName:
          updateData?.hiddenUpdatedBy?.fullName ?? undefined,
        hiddenUpdatedAt: updateData?.hiddenUpdatedAt ?? undefined
      });
    },
    [noteModalData, setNoteModalData]
  );

  const updatePinnedNoteVisibility = useCallback(
    (updateData: ToggleHiddenNote) => {
      console.log("updatePinnedNoteVisibility", updateData);
      setNotePinOrMinData({
        ...notePinOrMinData,
        isHidden: updateData?.isHidden ?? undefined,
        hiddenUpdatedByFullName:
          updateData?.hiddenUpdatedBy?.fullName ?? undefined,
        hiddenUpdatedAt: updateData?.hiddenUpdatedAt ?? undefined
      });
    },
    [notePinOrMinData, setNotePinOrMinData]
  );

  useEffect(() => {
    const handleBeforeUnload = (event: Event) => {
      event.preventDefault();
      // Chrome requires returnValue to be set.
      event.returnValue = true;
      return;
    };

    if (noteModalIsOpen) {
      // Add the event listener if noteModalIsOpen is true
      window.addEventListener("beforeunload", handleBeforeUnload);
    } else {
      // Remove the event listener if noteModalIsOpen is false
      window.removeEventListener("beforeunload", handleBeforeUnload);
    }

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [noteModalIsOpen]);

  if (noteUpsertIsError) {
    toast({
      title: "Failed to update Note",
      description: noteUpsertErrorMessage?.message
    });
    return;
  }

  return (
    <div>
      {/* Choose profile view */}
      <Dialog
        onOpenChange={setChooseProfileModal}
        open={chooseProfileModalIsOpen}
      >
        {isPending ? (
          <DialogContent
            className="flex max-w-xs items-center"
            hideCloseButton
            strictDialogClose
          >
            <VisuallyHidden>
              <DialogTitle>Creating note...</DialogTitle>
            </VisuallyHidden>
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Creating note...</span>
          </DialogContent>
        ) : (
          <DialogContent
            className="max-w-4xl"
            onDialogClose={handleCancelChooseProfile}
          >
            <VisuallyHidden>
              <DialogTitle>Choose profile</DialogTitle>
            </VisuallyHidden>
            <ChooseProfileForm onSubmit={handleProfileSelection} />
          </DialogContent>
        )}
      </Dialog>
      {/* Modal view */}
      <Dialog onOpenChange={setNoteModalIsOpen} open={noteModalIsOpen}>
        <DialogContent className="max-w-4xl" strictDialogClose hideCloseButton>
          <VisuallyHidden>
            <DialogHeader>
              <DialogTitle>Note</DialogTitle>
            </DialogHeader>
            <DialogDescription>Open note in modal view</DialogDescription>
          </VisuallyHidden>
          <NoteModalContent
            onModalClose={handleModalFormClose}
            onMinimize={() => switchModalToAnotherView("minimised")}
            onPin={() => switchModalToAnotherView("pinned")}
            onSubmit={onSubmitModalNoteForm}
            onToggleNoteVisibility={updateNoteVisibility}
            onDeleteNote={resetModal}
            handleSaveChanges={handleModalSaveChanges}
            saveChangesDialogIsOpen={modelSaveChangesDialogOpen}
            setSaveChangesDialogIsOpen={setModalSaveChangesDialogOpen}
            isPending={isPending}
          />
        </DialogContent>
      </Dialog>
      {notePinOrMinIsOpen &&
        // Pinned view
        (notePinOrMinData.viewState === "pinned" ? (
          <NotePinnedView
            onMinimize={switchPinnedToMinimisedView}
            onExpand={switchToModalView}
            onClose={handlePinMinFormClose}
            onSubmit={onSubmitPinnedNoteForm}
            onToggleNoteVisibility={updatePinnedNoteVisibility}
            onDeleteNote={resetPinMin}
            handleSaveChanges={handlePinnedSaveChanges}
            saveChangesDialogIsOpen={pinnedSaveChangesDialogOpen}
            setSaveChangesDialogIsOpen={setPinnedSaveChangesDialogOpen}
            isPending={isPending}
          />
        ) : (
          // Minimised view
          <div className="absolute right-6 bottom-4 z-10 flex h-12 w-96 items-center justify-between rounded-md border bg-background px-2 shadow-xl transition-all duration-1000">
            <h4 className="truncate font-medium text-sm">
              {notePinOrMinData.title || "Untitled note"}
            </h4>
            <div className="flex items-center gap-2">
              <Button
                variant="dialog"
                className="static h-auto p-0"
                onClick={switchMinimisedToPinnedView}
              >
                <ArrowUpRight className="h-4 w-4" />
                <span className="sr-only fixed">Minimise</span>
              </Button>
              <Button
                variant="dialog"
                className="static h-auto p-0"
                onClick={switchToModalView}
              >
                <Maximize className="h-4 w-4" />
                <span className="sr-only fixed">Expand</span>
              </Button>
              <Button
                variant="dialog"
                className="static h-auto p-0"
                onClick={handlePinMinFormClose}
              >
                <X className="h-4 w-4" />
                <span className="sr-only fixed">Close</span>
              </Button>
            </div>
          </div>
        ))}
    </div>
  );
}
