import { FileText } from "lucide-react";
import { useState } from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from "@watt/crm/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Button } from "../ui/button";

export interface TemplatePresets {
  label: string;
  value: {
    text: string;
    description: string;
  };
}

const templatePresets: TemplatePresets[] = [
  {
    value: {
      text: "N/A - Customer Unavailable",
      description: "Customer didn't pick up the phone."
    },
    label: "Customer Unavailable"
  },
  {
    value: {
      text: "Customer Already Engaged",
      description:
        "Customer picked up the call but not interested in further discussion due to already having another working broker."
    },
    label: "Customer Already Engaged"
  },
  {
    value: {
      text: "Callback Scheduled",
      description:
        "Customer picked up the call but not available at the moment; we have agreed on a callback time."
    },
    label: "Callback Scheduled"
  },
  {
    value: {
      text: "Future Interest Possible",
      description:
        "Customer picked up the call but not interested at the moment; might be interested in a few months' time."
    },
    label: "Future Interest Possible"
  },
  {
    value: {
      text: "Voicemail Left",
      description:
        "Customer didn't pick up the call and went directly to voicemail. Left a voicemail; will try again tomorrow."
    },
    label: "Voicemail Left"
  },
  {
    value: {
      text: "Connection Issue",
      description:
        "Customer didn't pick up the call. Not sure if it's a connection issue or if the customer hung up."
    },
    label: "Connection Issue"
  },
  {
    value: {
      text: "Incorrect Phone Number",
      description:
        "This phone number appears to be incorrect; unable to reach the customer."
    },
    label: "Incorrect Phone Number"
  },
  {
    value: {
      text: "Interested, Need More Information",
      description:
        "Customer expressed interest, requested more info. Ended call; will send information over."
    },
    label: "Interested, Need More Information"
  },
  {
    value: {
      text: "Positive Interaction, Follow-Up Required",
      description:
        "Positive interaction with the customer; follow-up needed to discuss contract details."
    },
    label: "Positive Interaction, Follow-Up Required"
  },
  {
    value: {
      text: "Customer Opt-Out Request",
      description:
        "The customer has requested the removal of their contact details from our CRM system."
    },
    label: "Customer Opt-Out Request"
  }
] as const;

interface NoteTemplateProps {
  className?: string;
  setTemplate: (template: TemplatePresets) => void;
}

export function NoteTemplates({ className, setTemplate }: NoteTemplateProps) {
  const [open, setOpen] = useState(false);

  const setSelectedTemplate = (selectedLabel: string) => {
    const selectedTemplate = templatePresets.find(
      template =>
        template.label.localeCompare(selectedLabel, undefined, {
          sensitivity: "base"
        }) === 0
    );

    if (selectedTemplate) {
      setTemplate(selectedTemplate);
    }

    setOpen(false);
  };

  return (
    <div className={cn("space-y-4 px-3 text-muted-foreground", className)}>
      <div className="flex flex-col">
        {templatePresets.slice(0, 5).map(template => (
          <Button
            type="button"
            variant="ghost"
            className="justify-start p-0 text-md"
            key={template.label}
            onClick={() => setSelectedTemplate(template.label)}
          >
            <FileText className="mr-2 h-5 w-5" />
            <span className="sr-only fixed">{template.label}</span>
            {template.label}
          </Button>
        ))}
      </div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="link"
            role="combobox"
            aria-expanded={open}
            className="p-0 text-md text-muted-foreground underline"
          >
            View all templates
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0">
          <Command>
            <CommandInput placeholder="Find a template" className="h-9" />
            <CommandEmpty>No templates found.</CommandEmpty>
            <CommandGroup>
              {templatePresets.map(template => (
                <CommandItem
                  key={template.label}
                  onSelect={setSelectedTemplate}
                >
                  {template.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
