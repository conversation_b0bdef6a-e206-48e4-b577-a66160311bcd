import { UtilityType } from "@prisma/client";
import type { ColumnDef } from "@tanstack/react-table";
import type { AllNotes } from "@watt/api/src/router/note";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { UserAvatar } from "@watt/crm/components/avatar/user-avatar";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { SiteMetersList } from "@watt/crm/components/site/site-meters/site-meters-list";
import { Separator } from "@watt/crm/components/ui/separator";
import { NoteCompanyInformationCard } from "../note-company-information-card";
export const noteColumns: ColumnDef<AllNotes["items"][number]>[] = [
  {
    accessorKey: "name",
    cell: ({ row }) => <NoteCompanyInformationCard note={row.original} />
  },
  {
    accessorKey: "meters",
    accessorFn: (row: {
      siteMeters: Array<{
        electricSiteMeter?: { mpanValue: string } | null;
        gasSiteMeter?: { mprnValue: string } | null;
      }>;
    }) =>
      row.siteMeters.map(meter => ({
        value:
          meter.electricSiteMeter?.mpanValue ?? meter.gasSiteMeter?.mprnValue,
        type: meter.electricSiteMeter
          ? UtilityType.ELECTRICITY
          : UtilityType.GAS
      })),
    cell: ({ row }) => {
      return <SiteMetersList meters={row.getValue("meters")} />;
    }
  },
  {
    accessorKey: "title",
    cell: ({ row }) => (
      <p className="line-clamp-1 font-medium">
        {row.getValue("title") || "Untitled note"}
      </p>
    )
  },
  {
    accessorKey: "description",
    cell: ({ row }) => (
      <p className="line-clamp-2 h-10 text-muted-foreground text-sm">
        {row.getValue("description")}
      </p>
    )
  },
  // Hidden - only for filtering purposes
  {
    accessorKey: "createdAt"
  },
  {
    accessorKey: "siteId"
  },
  {
    accessorKey: "siteAddress",
    accessorFn: row => getAddressDisplayName(row.site?.entityAddress)
  },
  {
    accessorKey: "companyRegistrationNumber",
    accessorFn: (row: { company: { registrationNumber: string } }) =>
      row.company.registrationNumber
  },
  {
    accessorKey: "createdBy",
    accessorFn: (row: { createdBy: { fullName: string } }) =>
      row.createdBy.fullName,
    cell: ({ row }) => {
      const fullName = row.getValue<string>("createdBy");
      return (
        <div className="flex min-h-[50px] w-full flex-col justify-between">
          <div className="flex-1" />
          <div className="mt-auto">
            <Separator />
            <div className="mt-3 flex w-full items-center justify-between">
              <div className="flex items-center space-x-2">
                <UserAvatar fullName={fullName} className="size-6" />
                <span className="font-medium">{fullName}</span>
              </div>
              <span className="text-muted-foreground text-sm">
                {formatDate(
                  row.getValue("createdAt"),
                  dateFormats.DD_MM_YYYY_HH_MM
                )}
              </span>
            </div>
          </div>
        </div>
      );
    },
    filterFn: textFilter
  },
  {
    accessorKey: "isHidden",
    filterFn: textFilter
  }
];
