import type { Table } from "@tanstack/react-table";
import { trpcClient } from "@watt/crm/utils/api";
import { X } from "lucide-react";
import React, { useMemo } from "react";

import { createZodEnumArray } from "@watt/common/src/utils/zod-literal-union";
import {
  DataTableFacetedFilter,
  type DataTableOption
} from "@watt/crm/components/data-table/data-table-faceted-filter";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { useSyncTableFilterWithQueryParams } from "@watt/crm/hooks/use-sync-table-filter";
import { useAppStore } from "@watt/crm/store/app-store";
import { isAllowedToViewHiddenOptions } from "@watt/db/src/supabase/role-permissions";
import { z } from "zod";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isFiltered?: boolean;
  children?: React.ReactNode;
}

export function DataTableToolbar<TData>({
  table,
  isFiltered,
  children
}: DataTableToolbarProps<TData>) {
  const { profileData } = useAppStore(state => state.userData);
  const role = profileData?.role;
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  const { data: allUsers } =
    trpcClient.userProfile.getAllIdsAndFullNames.useQuery();

  const usersList = allUsers?.length
    ? allUsers
    : ([{}, {}] as DataTableOption[]);

  const showVisibilityFilter = isAllowedToViewHiddenOptions(role);

  const visibilityOptions = useMemo(
    () => [
      { label: "Hidden", value: "true" },
      { label: "Visible", value: "false" }
    ],
    []
  );

  const queryParamsSchema = z.object({
    createdBy: createZodEnumArray(usersList),
    isHidden: z.enum(["true", "false"]).array().optional(),
    search: z.string().optional()
  });

  const { searchValue, handleSearchChange, handleFilterChange, resetFilters } =
    useSyncTableFilterWithQueryParams(table, queryParamsSchema);

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          value={searchValue}
          ref={searchInputRef}
        />
        <DataTableFacetedFilter
          column={table.getColumn("createdBy")}
          title="Created By"
          options={usersList}
          onFilterChange={handleFilterChange}
        />
        {table.getColumn("isHidden") && showVisibilityFilter && (
          <DataTableFacetedFilter
            column={table.getColumn("isHidden")}
            title="Visibility"
            options={visibilityOptions}
            onFilterChange={handleFilterChange}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex space-x-2">{children}</div>
    </div>
  );
}
