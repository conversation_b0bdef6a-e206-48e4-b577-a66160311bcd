"use client";

import {
  type TableOptions,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { EmptyStatePanel } from "@watt/crm/app/account/companies/components/empty-state";
import { useNoteStore } from "@watt/crm/store/note";

import type { AllNotes } from "@watt/api/src/router/note";
import { NoteSkeleton } from "@watt/crm/components/skeletons/notes/note-skeleton";
import { Button } from "@watt/crm/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableLoading,
  TableRow
} from "@watt/crm/components/ui/table";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { trpcClient } from "@watt/crm/utils/api";
import {
  type UIEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";
import { useDebounce } from "react-use";
import type { NoteQueryParams } from "../note-provider";
import { noteColumns } from "./notes-card-columns";
import { DataTableToolbar } from "./notes-card-data-table-toolbar";

const GRID_COLUMNS = 3;
const ESTIMATED_SIZE = 10;
const SCROLL_THRESHOLD = 50;

type ColumnFiltersState = {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  value: any;
}[];

interface NotesCardDataTableProps {
  companyId?: string;
}

export function NotesCardDataTable({ companyId }: NotesCardDataTableProps) {
  // This is used to determine if the API has initially loaded
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [debouncedColumnFilters, setDebouncedColumnFilters] =
    useState<ColumnFiltersState>([]);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const { setQueryParams } = useQueryParams<NoteQueryParams>();

  // Fetch company data if company ID is provided
  const { data: companyData } = trpcClient.company.find.useQuery(
    { id: companyId ?? "" },
    { enabled: !!companyId }
  );

  useDebounce(
    () => {
      setDebouncedColumnFilters(columnFilters);
    },
    1000,
    [columnFilters]
  );

  const { data, isFetching, isLoading, hasNextPage, fetchNextPage } =
    trpcClient.note.all.useInfiniteQuery(
      {
        companyId,
        searchFilters: {
          columnFilters: debouncedColumnFilters,
          globalFilter
        }
      },
      {
        getNextPageParam: lastPage => lastPage.nextCursor,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        placeholderData: prev => prev,
        trpc: { abortOnUnmount: true }
      }
    );

  useEffect(() => {
    if (!isLoading && !hasInitiallyLoaded) {
      setHasInitiallyLoaded(true);
    }
  }, [isLoading, hasInitiallyLoaded]);

  const isFiltered = useMemo(() => {
    return columnFilters?.length > 0 || globalFilter?.length > 0;
  }, [columnFilters, globalFilter]);

  const {
    setChooseProfileModal,
    setNoteProfileProps,
    notePinOrMinData,
    setNoteModalData,
    setNotePinOrMinIsOpen,
    setNotePinOrMinData
  } = useNoteStore(state => ({
    setChooseProfileModal: state.setChooseProfileModal,
    setNoteProfileProps: state.setNoteProfileProps,
    notePinOrMinData: state.notePinOrMinData,
    setNoteModalData: state.setNoteModalData,
    setNotePinOrMinIsOpen: state.setNotePinOrMinIsOpen,
    setNotePinOrMinData: state.setNotePinOrMinData
  }));

  const handleCreateNote = () => {
    if (companyData) {
      setNoteProfileProps({
        companyData
      });
    }
    setChooseProfileModal(true);
  };

  const handleOpenNoteModal = (noteId: string) => {
    // If the note is already pinned, change it to modal view
    if (notePinOrMinData.id && notePinOrMinData.id === noteId) {
      setNoteModalData({
        ...notePinOrMinData
      });
      setNotePinOrMinIsOpen(false);
      setNotePinOrMinData({});
    }

    setQueryParams({ id: noteId, modal: "note" });
  };

  const allItems = useMemo(() => {
    return data?.pages.flatMap(page => page.items) ?? [];
  }, [data]);

  const totalDBRowCount = useMemo(
    () => data?.pages?.[0]?.meta?.totalRowCount ?? 0,
    [data]
  );

  const totalFetched = useMemo(() => allItems.length, [allItems]);

  const fetchMoreOnBottomReached = useCallback(
    (event: UIEvent<HTMLDivElement>) => {
      const containerRefElement = event.currentTarget;
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        if (
          scrollHeight - scrollTop - clientHeight < SCROLL_THRESHOLD &&
          !isFetching &&
          (totalFetched < totalDBRowCount || hasNextPage)
        ) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, isFetching, totalFetched, totalDBRowCount, hasNextPage]
  );

  const tableState = useMemo(
    () => ({
      columnFilters,
      globalFilter,
      sorting: [
        {
          id: "createdAt",
          desc: true
        }
      ],
      columnVisibility: {
        createdAt: false,
        siteId: false,
        siteMeters: false,
        siteAddress: false,
        companyRegistrationNumber: false,
        isHidden: false
      }
    }),
    [columnFilters, globalFilter]
  );

  const tableOptions: TableOptions<AllNotes["items"][number]> = useMemo(
    () =>
      ({
        data: allItems,
        columns: noteColumns,
        state: tableState,
        enableRowSelection: true,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        debugTable: false,
        manualFiltering: true
      }) as const,
    [tableState, allItems]
  );

  const table = useReactTable(tableOptions);
  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => ESTIMATED_SIZE,
    count: rows.length,
    overscan: 5
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  const paddingTop = virtualRows.length > 0 ? virtualRows?.[0]?.start || 0 : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? totalSize - (virtualRows?.[virtualRows.length - 1]?.end || 0)
      : 0;

  // Loading states
  const isInitialLoading = isLoading && !hasInitiallyLoaded;
  const hasNoNotes =
    !isFiltered && rows.length === 0 && !isLoading && !isFetching;
  const hasNoFilteredResults =
    isFiltered && rows.length === 0 && !isLoading && !isFetching;

  if (isInitialLoading) {
    return <NoteSkeleton />;
  }

  return (
    <div className="h-full space-y-4">
      <DataTableToolbar table={table} isFiltered={isFiltered}>
        <Button
          variant="secondary"
          size="sm"
          className="mr-8"
          onClick={handleCreateNote}
        >
          Create Note
        </Button>
      </DataTableToolbar>

      <div className="relative h-[calc(100vh-150px)]" ref={tableContainerRef}>
        <div
          className="absolute inset-0 overflow-auto pr-4"
          onScroll={fetchMoreOnBottomReached}
        >
          <Table outerClassName="overflow-visible">
            <TableHeader className="sticky top-0 z-10 bg-background">
              {isFetching && (
                <TableLoading
                  colSpan={GRID_COLUMNS}
                  rowClassName="col-span-3 grid grid-cols-full"
                />
              )}
            </TableHeader>
            <TableBody
              className={cn(
                rows.length > 0 &&
                  "grid grid-cols-1 gap-4 lg:grid-cols-2 xl:grid-cols-3"
              )}
            >
              {rows?.length ? (
                <>
                  {paddingTop > 0 && (
                    <TableRow className="col-span-3 grid grid-cols-full gap-4">
                      <TableCell style={{ height: `${paddingTop}px` }} />
                    </TableRow>
                  )}
                  {virtualRows.map(virtualRow => {
                    const row = rows[virtualRow.index];
                    if (!row) {
                      return null;
                    }
                    return (
                      <TableRow
                        key={row.id}
                        className="!border flex flex-col rounded-md bg-background p-4 pb-2 hover:cursor-pointer hover:bg-background/80 lg:mb-4"
                        onClick={() => handleOpenNoteModal(row.original.id)}
                      >
                        {row.getVisibleCells().map(cell => (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              "flex px-0",
                              cell.column.id === "createdBy" && "mt-auto flex-1"
                            )}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    );
                    // });
                  })}
                  {!hasNextPage && (
                    <TableRow className="col-span-full my-4 flex w-full justify-center">
                      <TableCell className="text-center text-muted-foreground text-sm">
                        {isLoading || isFetching ? "" : "No more results."}
                      </TableCell>
                    </TableRow>
                  )}
                  {paddingBottom > 0 && (
                    <TableRow className="col-span-3 grid grid-cols-full gap-4">
                      <TableCell style={{ height: `${paddingBottom}px` }} />
                    </TableRow>
                  )}
                </>
              ) : hasNoFilteredResults ? (
                <TableRow>
                  <TableCell
                    colSpan={noteColumns.length}
                    className="h-24 text-center"
                  >
                    <EmptyStatePanel
                      title="No matching notes"
                      description="We couldn't find any notes that match your filters. Try adjusting your search criteria."
                    />
                  </TableCell>
                </TableRow>
              ) : hasNoNotes ? (
                <TableRow>
                  <TableCell
                    colSpan={noteColumns.length}
                    className="h-24 text-center"
                  >
                    <EmptyStatePanel
                      title="No notes found"
                      description="It looks like you haven't created any notes yet. Get started by creating your first note."
                    >
                      <Button
                        variant="outline"
                        className="font-medium"
                        onClick={handleCreateNote}
                      >
                        Create Note
                      </Button>
                    </EmptyStatePanel>
                  </TableCell>
                </TableRow>
              ) : null}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
