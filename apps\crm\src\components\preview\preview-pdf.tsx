"use client";

import { skipToken } from "@tanstack/react-query";
import type { StorageBucket } from "@watt/common/src/constants/storage-buckets";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { trpcClient } from "@watt/crm/utils/api";
import { memo } from "react";
import { LazyPDFViewer } from "../pdf/lazy-pdf-viewer";
import { Skeleton } from "../ui/skeleton";

type BasePreviewPDFProps = {
  validator?: () => true | string | React.ReactNode;
  className?: string;
};

type BucketPreviewProps = {
  bucketName: StorageBucket;
  filePath: string;
  signedUrl?: never;
};

type SignedUrlPreviewProps = {
  signedUrl: string;
  bucketName?: never;
  filePath?: never;
};

type PreviewSource = BucketPreviewProps | SignedUrlPreviewProps;

/**
 * Check if the props are for a bucket preview
 * @param props - The props to check
 * @returns True if the props are for a bucket preview, false otherwise
 */
function isBucketPreview(
  props: Omit<PreviewSource, keyof BasePreviewPDFProps>
): props is BucketPreviewProps {
  return (
    "bucketName" in props &&
    "filePath" in props &&
    typeof props.bucketName === "string" &&
    typeof props.filePath === "string" &&
    !(props as unknown as SignedUrlPreviewProps).signedUrl
  );
}

const PDFWrapper = memo(function PDFWrapper({
  children,
  className
}: React.PropsWithChildren<{ className?: string }>) {
  return (
    <div
      className={cn(
        "prose dark:prose-invert prose-sm overflow-auto border border-transparent p-4 prose-headings:font-cal",
        className
      )}
    >
      {children}
    </div>
  );
});

/**
 * Component for previewing PDF documents via a pre-signed URL.
 *
 * @param props - The props to pass to the PDF viewer
 * @returns The PDF viewer component
 */
const PDFViewer = memo(function PDFViewer({ url }: { url: string }) {
  return (
    <LazyPDFViewer
      documentData={{
        id: "",
        data: url,
        initialData: "",
        type: ""
      }}
    />
  );
});

/**
 * Component for previewing PDF documents either via a pre-signed URL or by generating
 * a signed URL from bucket/path information.
 *
 * @example Using pre-signed URL
 * ```tsx
 * <PreviewPDF signedUrl="https://..." />
 * ```
 *
 * @example Using bucket and file path
 * ```tsx
 * <PreviewPDF bucketName={STORAGE_BUCKETS.COMPANY_FILES} filePath="path/to/file.pdf" />
 * ```
 */
export function PreviewPDF<T extends PreviewSource>(
  props: BasePreviewPDFProps & T
) {
  const { className, validator, ...rest } = props;

  const { data: pdfData, error } =
    trpcClient.files.getSignedDownloadUrl.useQuery(
      isBucketPreview(rest)
        ? {
            bucketName: rest.bucketName,
            filename: rest.filePath
          }
        : skipToken
    );

  if (validator) {
    const validatorResult = validator();
    if (validatorResult !== true) {
      return <PDFWrapper className={className}>{validatorResult}</PDFWrapper>;
    }
  }

  if ("signedUrl" in rest && rest.signedUrl) {
    return (
      <PDFWrapper className={className}>
        <PDFViewer url={rest.signedUrl} />
      </PDFWrapper>
    );
  }

  if (error) {
    return (
      <PDFWrapper className={className}>
        Error loading PDF: {error.message}
      </PDFWrapper>
    );
  }

  const signedUrl = pdfData?.data?.signedUrl;

  if (!signedUrl) {
    return (
      <PDFWrapper className={className}>
        <Skeleton className="h-screen bg-slate-200" />
      </PDFWrapper>
    );
  }

  return (
    <PDFWrapper className={className}>
      <PDFViewer url={signedUrl} />
    </PDFWrapper>
  );
}
