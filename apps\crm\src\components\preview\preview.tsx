"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { trpcClient } from "@watt/crm/utils/api";

import { LazyPDFViewer } from "../pdf/lazy-pdf-viewer";
import { Skeleton } from "../ui/skeleton";

interface Props {
  pdfFile: string | null;
  userSelectedContract: boolean;
  className?: string;
}

export function PreviewVerbalContract({
  pdfFile: pdfPath,
  userSelectedContract,
  className
}: Props) {
  const { data, error } =
    trpcClient.verbalContractTemplates.getSignedUrl.useQuery(
      { pdfFileName: pdfPath || "" },
      { enabled: !!pdfPath }
    );

  const getContent = () => {
    if (!pdfPath) {
      return "No verbal script available for the selected template";
    }

    if (!userSelectedContract) {
      return "Please select an appropriate verbal script for the selected quote";
    }

    if (error) {
      return <div>Error loading PDF: {error.message}</div>;
    }

    if (!data?.signedUrl) {
      return <Skeleton className="h-screen bg-slate-200" />;
    }

    return (
      <LazyPDFViewer
        documentData={{
          id: "",
          data: data.signedUrl,
          initialData: "",
          type: ""
        }}
      />
    );
  };

  return (
    <div
      className={cn(
        "prose dark:prose-invert prose-sm overflow-auto border border-transparent p-4 prose-headings:font-cal",
        className
      )}
    >
      {getContent()}
    </div>
  );
}
