import { useEffect, useState } from "react";
import * as prod from "react/jsx-runtime";
import rehypeRaw from "rehype-raw";
import rehypeReact from "rehype-react";
import rehypeSanitize, { defaultSchema } from "rehype-sanitize";
import remarkParse from "remark-parse";
import remarkRehype from "remark-rehype";
import { unified } from "unified";

const production = { Fragment: prod.Fragment, jsx: prod.jsx, jsxs: prod.jsxs };

export function useProcessor(text: string) {
  const [content, setContent] = useState<React.ReactNode>(null);

  useEffect(() => {
    unified()
      .use(remarkParse)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeSanitize, defaultSchema)
      .use(rehypeReact, production)
      .process(text)
      // @ts-expect-error: the react types are missing.
      .then(file => {
        setContent(file.result);
      });
  }, [text]);

  return content;
}
