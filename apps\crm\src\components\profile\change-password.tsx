"use client";

import { z } from "zod";

import { <PERSON><PERSON> } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { useAuthentication } from "@watt/crm/hooks/use-authentication";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

type ChangePasswordForm = {
  newPassword: string;
  confirmPassword: string;
};

export function ChangePassword() {
  const { isLoading, handleChangePassword } = useAuthentication();
  const form = useZodForm({
    schema: z.object({
      newPassword: z.string().min(8, "Password must be at least 8 characters"),
      confirmPassword: z
        .string()
        .min(8, "Password must be at least 8 characters")
    })
  });

  const onSubmit = async (formData: ChangePasswordForm) => {
    await handleChangePassword(formData.newPassword, formData.confirmPassword);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg">Change password</h3>
        <p className="text-muted-foreground text-sm">
          If you have not set up a password for your account, you must set one
          up here.
        </p>
      </div>
      <FormWrapper
        form={form}
        handleSubmit={data => {
          onSubmit(data);
        }}
        className="my-4 space-y-6"
      >
        <FormField
          control={form.control}
          name="newPassword"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>New password</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  autoComplete="new-password"
                  placeholder="New password"
                  {...field}
                  className="col-span-3"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Confirm password</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  autoComplete="new-password"
                  placeholder="Confirm password"
                  {...field}
                  value={field.value ?? ""}
                  className="col-span-3"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          variant="secondary"
          disabled={isLoading}
          className="button-click-animation w-full"
        >
          Change password
        </Button>
      </FormWrapper>
    </div>
  );
}
