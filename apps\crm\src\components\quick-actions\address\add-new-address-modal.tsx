"use client";

import { toast } from "@watt/crm/components/ui/use-toast";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "../../ui/dialog";
import { AddressForm } from "./address-form";

type AddNewAddressModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  handleSumbit: () => void;
};

export function AddNewAddressModal({
  isOpen,
  closeModal,
  handleSumbit
}: AddNewAddressModalProps) {
  const handleSubmitForm = () => {
    toast({
      title: "New address added",
      description: "The address has been added successfully.",
      variant: "success"
    });
    handleSumbit();
  };
  return (
    <Dialog open={isOpen}>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        onEscapeKeyDown={closeModal}
        onDialogClose={closeModal}
      >
        <DialogHeader>
          <DialogTitle>Add New Address</DialogTitle>
          <DialogDescription>
            Enter the address details to add a new address.
          </DialogDescription>
        </DialogHeader>
        <AddressForm onSubmitForm={handleSubmitForm} />
      </DialogContent>
    </Dialog>
  );
}
