"use client";

import { Plus } from "lucide-react";

import { useState } from "react";

import { But<PERSON> } from "../../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "../../ui/dialog";
import { AddressForm } from "./address-form";

type AddNewAddressTriggerProps = {
  title?: string;
  onSubmit: (id: string) => void;
  isBasicAddress?: boolean;
};

export function AddNewAddressTrigger({
  title,
  isBasicAddress,
  onSubmit
}: AddNewAddressTriggerProps) {
  const [addressModalOpen, setAddressModalOpen] = useState(false);
  const handleSubmitForm = (id: string) => {
    onSubmit(id);
    setAddressModalOpen(false);
  };
  return (
    <Dialog onOpenChange={setAddressModalOpen} open={addressModalOpen}>
      <DialogTrigger asChild>
        <Button variant="link" className="w-full hover:no-underline">
          <Plus className="mr-1 h-4 w-4" />
          {title || "Add New Address"}
        </Button>
      </DialogTrigger>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Add New Address</DialogTitle>
          <DialogDescription>
            Enter the address details to add a new address.
          </DialogDescription>
        </DialogHeader>
        <AddressForm
          onSubmitForm={handleSubmitForm}
          isBasicAddress={isBasicAddress}
        />
      </DialogContent>
    </Dialog>
  );
}
