"use client";

import { Loader2, XCircle } from "lucide-react";

import { TRPCClientError } from "@trpc/client";
import { trpcClient } from "@watt/crm/utils/api";
import { useRef, useState } from "react";

import { <PERSON><PERSON> } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

import { log } from "@watt/common/src/utils/axiom-logger";
import { CreateNewAddressFormSchema } from "@watt/db/src/types/new-address-form";
import {
  Combobox,
  ComboboxContent,
  ComboboxGroup,
  ComboboxItem,
  ComboboxTrigger
} from "../../combobox/combobox-input";

type AddressFormProps = {
  onSubmitForm: (id: string) => void;
  isBasicAddress?: boolean;
};

export function AddressForm({
  onSubmitForm,
  isBasicAddress
}: AddressFormProps) {
  const [mpanLists, setMpanLists] = useState<string[]>([]);
  const mpanNumberContainerRef = useRef<HTMLDivElement>(null);
  const [mrpnLists, setMrpnLists] = useState<string[]>([]);
  const mprnNumberContainerRef = useRef<HTMLDivElement>(null);
  const addressMutation = trpcClient.address.createNewAddress.useMutation();

  const form = useZodForm({
    schema: CreateNewAddressFormSchema,
    defaultValues: {}
  });

  const removeMpan = (mpanToRemove: string) => {
    setMpanLists(mpanLists.filter(mpan => mpan !== mpanToRemove));
    form.setValue(
      "mpanNumbers",
      mpanLists.filter(mpan => mpan !== mpanToRemove)
    );
  };

  const handleAddMpanItem = (newMpan: string) => {
    if (newMpan && !mpanLists.includes(newMpan)) {
      setMpanLists([...mpanLists, newMpan]);
      form.setValue("mpanNumbers", [...mpanLists, newMpan]);
    }
  };

  const removeMprn = (mprnToRemove: string) => {
    setMrpnLists(mrpnLists.filter(mprn => mprn !== mprnToRemove));
    form.setValue(
      "mprnNumbers",
      mrpnLists.filter(mprn => mprn !== mprnToRemove)
    );
  };

  const handleAddMprnItem = (newMprn: string) => {
    if (newMprn && !mrpnLists.includes(newMprn)) {
      setMrpnLists([...mrpnLists, newMprn]);
      form.setValue("mprnNumbers", [...mrpnLists, newMprn]);
    }
  };

  const handleFormSubmit = async () => {
    try {
      const newAddressData = {
        isBasicAddress,
        ...form.getValues()
      };
      const result = await addressMutation.mutateAsync(newAddressData);
      onSubmitForm(result.id);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while adding the new address. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to add new address",
        description,
        variant: "destructive",
        duration: 12000
      });
    }
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleFormSubmit}
      className="my-4 space-y-4"
    >
      <FormField
        control={form.control}
        name="addressLine1"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Address Line 1 *</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="addressLine2"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Address Line 2</FormLabel>{" "}
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="postalTown"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Town *</FormLabel>{" "}
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="county"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>County *</FormLabel>{" "}
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="postcode"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Postcode *</FormLabel>{" "}
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {!isBasicAddress && (
        <FormField
          control={form.control}
          name="mpanNumbers"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>MPAN</FormLabel>{" "}
              <FormControl>
                <div
                  ref={mpanNumberContainerRef}
                  className="flex w-full flex-col"
                >
                  <Combobox>
                    <ComboboxTrigger
                      fieldValue={field.value ?? []}
                      placeholder="Add one or more MPAN core numbers"
                    />
                    <ComboboxContent
                      placeholder="Type to add MPAN number"
                      container={mpanNumberContainerRef.current}
                      allowAddItem={true}
                      onAddItem={handleAddMpanItem}
                    >
                      <ComboboxGroup>
                        {mpanLists.map(item => (
                          <ComboboxItem key={item}>
                            {item}
                            {field.value?.includes(item) && (
                              <Button
                                type="button"
                                variant="link"
                                className="h-4 p-2"
                                onClick={() => removeMpan(item)}
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </ComboboxItem>
                        ))}
                      </ComboboxGroup>
                    </ComboboxContent>
                  </Combobox>
                </div>
              </FormControl>
              {!!form.formState.errors.mpanNumbers?.length && (
                <FormMessage>MPAN is invalid</FormMessage>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      {!isBasicAddress && (
        <FormField
          control={form.control}
          name="mprnNumbers"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>MPRN</FormLabel>{" "}
              <FormControl>
                <div
                  ref={mprnNumberContainerRef}
                  className="flex w-full flex-col"
                >
                  <Combobox>
                    <ComboboxTrigger
                      fieldValue={field.value ?? []}
                      placeholder="Add one or more MPRN numbers"
                    />
                    <ComboboxContent
                      placeholder="Type to add MPRN number"
                      container={mprnNumberContainerRef.current}
                      allowAddItem={true}
                      onAddItem={handleAddMprnItem}
                    >
                      <ComboboxGroup>
                        {mrpnLists.map(item => (
                          <ComboboxItem key={item}>
                            {item}
                            {field.value?.includes(item) && (
                              <Button
                                type="button"
                                variant="link"
                                className="h-4 p-2"
                                onClick={() => removeMprn(item)}
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </ComboboxItem>
                        ))}
                      </ComboboxGroup>
                    </ComboboxContent>
                  </Combobox>
                </div>
              </FormControl>
              {!!form.formState.errors.mprnNumbers?.length && (
                <FormMessage>MPRN is invalid</FormMessage>
              )}
            </FormItem>
          )}
        />
      )}
      <Button
        type="submit"
        variant="secondary"
        disabled={
          !form.watch("addressLine1") ||
          !form.watch("postalTown") ||
          !form.watch("county") ||
          !form.watch("postcode") ||
          addressMutation.isPending
        }
        className="button-click-animation w-full"
      >
        {addressMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        Confirm
      </Button>
    </FormWrapper>
  );
}
