"use client";

import { useQueryParams } from "@watt/crm/hooks/use-query-params";

import { AddNewAddressModal } from "./add-new-address-modal";

type AddNewAddressModalQueryParams = {
  modal: "add-new-address";
};

export function AddressProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<AddNewAddressModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  return (
    <AddNewAddressModal
      isOpen={queryParams.modal === "add-new-address"}
      closeModal={handleModalClose}
      handleSumbit={handleModalClose}
    />
  );
}
