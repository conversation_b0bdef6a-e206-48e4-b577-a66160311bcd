"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "../../ui/dialog";
import { BusinessTargetForm } from "./business-target-form";

type AddNewBusinessTargetModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  handleSumbit: (quoteListId: string) => void;
};

export function AddNewBusinessTargetModal({
  isOpen,
  closeModal,
  handleSumbit
}: AddNewBusinessTargetModalProps) {
  return (
    <Dialog open={isOpen}>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        onEscapeKeyDown={closeModal}
        onDialogClose={closeModal}
      >
        <DialogHeader>
          <DialogTitle>Add New Business</DialogTitle>
          <DialogDescription>
            Enter the business details to add a new business target.
          </DialogDescription>
        </DialogHeader>
        <BusinessTargetForm onSubmitForm={handleSumbit} />
      </DialogContent>
    </Dialog>
  );
}
