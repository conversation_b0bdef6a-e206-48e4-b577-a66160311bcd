"use client";

import { Plus } from "lucide-react";
import { useState } from "react";
import { Button } from "../../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "../../ui/dialog";
import { BusinessTargetForm } from "./business-target-form";

type AddNewBusinessTargetTriggerProps = {
  onSubmit: (businessRef: string) => void;
};

export function AddNewBusinessTargetTrigger({
  onSubmit
}: AddNewBusinessTargetTriggerProps) {
  const [businessTargetModalOpen, setBusinessTargetModalOpen] = useState(false);
  const handleSubmitForm = (businessRef: string) => {
    onSubmit(businessRef);
    setBusinessTargetModalOpen(false);
  };
  return (
    <Dialog
      onOpenChange={setBusinessTargetModalOpen}
      open={businessTargetModalOpen}
    >
      <DialogTrigger asChild>
        <Button variant="link" className="w-full hover:no-underline">
          <Plus className="mr-1 h-4 w-4" />
          Add business
        </Button>
      </DialogTrigger>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Add New Business</DialogTitle>
          <DialogDescription>
            Enter the business details to add a new business target.
          </DialogDescription>
        </DialogHeader>
        <BusinessTargetForm onSubmitForm={handleSubmitForm} />
      </DialogContent>
    </Dialog>
  );
}
