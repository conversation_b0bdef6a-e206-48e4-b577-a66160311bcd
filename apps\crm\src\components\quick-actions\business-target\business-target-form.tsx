"use client";

import { Loader2 } from "lucide-react";

import { TRPCClientError } from "@trpc/client";
import { trpcClient } from "@watt/crm/utils/api";
import { BusinessType } from "@watt/db/src/enums";
import { useMemo } from "react";
import { z } from "zod";

import { isValidCompanyNumber } from "@watt/common/src/regex/company-number";
import { isCharitableEntity } from "@watt/common/src/regex/company-number";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { postcodeFormSchema } from "@watt/common/src/utils/format-postcode";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { Label } from "@watt/crm/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

const BaseSchema = z.object({
  commercialName: z.string(),
  legalStatus: z.string(),
  postcode: postcodeFormSchema
});

const LtdSchema = BaseSchema.extend({
  businessType: z.enum([BusinessType.LTD]),
  businessRef: z.string().refine(value => isValidCompanyNumber(value), {
    message: "Invalid business reference number."
  })
});

const ChartiySchema = BaseSchema.extend({
  businessType: z.enum([BusinessType.CHARITY]),
  businessRef: z.string().refine(value => isCharitableEntity(value), {
    message: "Invalid business reference number."
  })
});
const SoleTraderSchema = BaseSchema.extend({
  businessType: z.enum([BusinessType.SOLE_TRADER])
});

const BusinessTargetFormSchema = z.discriminatedUnion("businessType", [
  LtdSchema,
  ChartiySchema,
  SoleTraderSchema
]);

type BusinessTargetFormProps = {
  onSubmitForm: (businessRef: string) => void;
};

export function BusinessTargetForm({ onSubmitForm }: BusinessTargetFormProps) {
  const businessTargetMutation =
    trpcClient.businessTarget.createNewBusinessTarget.useMutation();

  const form = useZodForm({
    schema: BusinessTargetFormSchema
  });

  const watchBusinessType = form.watch("businessType");
  const showBusinessRegNumber = useMemo(
    () =>
      watchBusinessType === BusinessType.LTD ||
      watchBusinessType === BusinessType.CHARITY,
    [watchBusinessType]
  );

  const handleBusinessTypeChange = (
    value: Extract<BusinessType, "LTD" | "CHARITY" | "SOLE_TRADER">
  ) => {
    const legalStatusValue = value === BusinessType.LTD ? "L" : "N";
    form.setValue("businessType", value);
    form.setValue("legalStatus", legalStatusValue);
  };

  const handleFormSubmit = async () => {
    try {
      const result = await businessTargetMutation.mutateAsync(form.getValues());
      onSubmitForm(result.businessRef);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while adding the new business target. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to add new business target",
        description,
        variant: "destructive"
      });
    }
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleFormSubmit}
      className="my-4 space-y-4"
    >
      <FormField
        control={form.control}
        name="commercialName"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Commercial Name *</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="businessType"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Business Type *</FormLabel>
            <FormControl>
              <Select
                onValueChange={handleBusinessTypeChange}
                value={field.value}
              >
                <SelectTrigger
                  className={cn(!field.value && "text-muted-foreground italic")}
                >
                  <SelectValue placeholder="Select the business type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={BusinessType.LTD}>
                    Limited Company
                  </SelectItem>
                  <SelectItem value={BusinessType.CHARITY}>Charity</SelectItem>
                  <SelectItem value={BusinessType.SOLE_TRADER}>
                    Sole Trader
                  </SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {showBusinessRegNumber && (
        <FormField
          control={form.control}
          name="businessRef"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              {watchBusinessType === BusinessType.LTD ? (
                <Label>Business Registration Number *</Label>
              ) : (
                <Label>Charity Number *</Label>
              )}
              <FormControl>
                <Input {...field} className="col-span-3" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      <FormField
        control={form.control}
        name="postcode"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Postcode *</FormLabel>
            <FormControl>
              <Input {...field} className="col-span-3" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <Button
        type="submit"
        variant="secondary"
        className="button-click-animation w-full"
        disabled={businessTargetMutation.isPending}
      >
        {businessTargetMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        Confirm
      </Button>
    </FormWrapper>
  );
}
