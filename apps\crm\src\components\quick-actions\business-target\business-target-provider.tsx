"use client";

import { useQueryParams } from "@watt/crm/hooks/use-query-params";

import { AddNewBusinessTargetModal } from "./add-new-business-target-modal";

type AddNewBusinessTargetModalQueryParams = {
  modal: "add-new-business-target";
};

export function BusinessTargetProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<AddNewBusinessTargetModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  return (
    <AddNewBusinessTargetModal
      isOpen={queryParams.modal === "add-new-business-target"}
      closeModal={handleModalClose}
      handleSumbit={handleModalClose}
    />
  );
}
