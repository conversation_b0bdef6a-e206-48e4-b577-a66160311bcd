import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { ContactForm } from "../../contact/contact-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "../../ui/dialog";

type AddNewContactModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  handleSumbit: () => void;
};

export function AddNewContactModal({
  isOpen,
  closeModal,
  handleSumbit
}: AddNewContactModalProps) {
  const { queryParams } = useQueryParams<{
    companyId: string;
    siteId: string;
    setAsPrimaryContact: string;
  }>();

  const { setAsPrimaryContact, companyId, siteId } = queryParams;

  const isSetAsPrimaryContact = setAsPrimaryContact === "true";

  return (
    <Dialog open={isOpen}>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        onEscapeKeyDown={closeModal}
        onDialogClose={closeModal}
      >
        <DialogHeader className="space-y-4">
          <DialogTitle>Add New Contact Person</DialogTitle>
          <DialogDescription className="italic">
            Please enter all required fields to create a new contact for the
            business entity.
          </DialogDescription>
        </DialogHeader>
        <ContactForm
          onSubmitForm={handleSumbit}
          companyId={companyId}
          siteId={siteId}
          setAsPrimaryContact={isSetAsPrimaryContact}
        />
      </DialogContent>
    </Dialog>
  );
}
