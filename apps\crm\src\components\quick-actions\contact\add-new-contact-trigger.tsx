"use client";

import { User } from "lucide-react";
import { useState } from "react";

import type { Contact } from "@watt/api/src/types/people";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { ContactForm } from "../../contact/contact-form";
import { Button } from "../../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "../../ui/dialog";

type AddNewContactTriggerProps = {
  companyId: string;
  siteId: string;
  onSubmit: (email: string, contactId: string) => void;
  className?: string;
  setAsPrimaryContact?: boolean;
};

export function AddNewContactTrigger({
  companyId,
  siteId,
  setAsPrimaryContact,
  onSubmit,
  className
}: AddNewContactTriggerProps) {
  const [addressModalOpen, setAddressModalOpen] = useState(false);
  const handleSubmitForm = (contact: Contact) => {
    const primaryEmail = contact.emails?.find(email => email.isPrimary)?.email;
    primaryEmail && onSubmit(primaryEmail, contact.id);
    setAddressModalOpen(false);
  };

  return (
    <Dialog onOpenChange={setAddressModalOpen} open={addressModalOpen}>
      <DialogTrigger asChild>
        <Button
          variant="none"
          className={cn(className ?? "w-full hover:no-underline")}
        >
          <User className="mr-2 h-4 w-4" />
          Add Contact
        </Button>
      </DialogTrigger>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader className="space-y-4">
          <DialogTitle>Add New Contact Person</DialogTitle>
          <DialogDescription className="italic">
            Please enter all required fields to create a new contact for the
            business entity.
          </DialogDescription>
        </DialogHeader>
        <ContactForm
          companyId={companyId}
          siteId={siteId}
          onSubmitForm={handleSubmitForm}
          setAsPrimaryContact={setAsPrimaryContact}
        />
      </DialogContent>
    </Dialog>
  );
}
