import type { PhoneNumberType } from "@prisma/client";
import { getPrimaryEmail } from "@watt/api/src/utils/get-primary-email";
import { getPrimaryPhoneNumber } from "@watt/api/src/utils/get-primary-phone-number";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { MailIcon, PhoneIcon } from "lucide-react";

type CompanyContactInfoProps = {
  forename: string;
  surname: string;
  emails: { email: string; isPrimary: boolean }[];
  phoneNumbers: {
    phoneNumber: string;
    type: PhoneNumberType;
    isPrimary: boolean;
  }[];
};

export function CompanyContactInfo({
  forename,
  surname,
  emails,
  phoneNumbers
}: CompanyContactInfoProps) {
  const fullName = `${humanize(forename)} ${humanize(surname)}`;
  const primaryEmail = getPrimaryEmail(emails);
  const primaryPhoneNumber = getPrimaryPhoneNumber(phoneNumbers);

  return (
    <div className="flex gap-2">
      {fullName}
      <span className="inline-flex items-center gap-2">
        <MailIcon className="size-3" /> {primaryEmail}
      </span>
      {primaryPhoneNumber && (
        <span className="inline-flex items-center gap-2">
          <PhoneIcon className="size-3" /> {primaryPhoneNumber}
        </span>
      )}
    </div>
  );
}
