"use client";

import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { AddNewContactModal } from "./add-new-contact-modal";

export const ContactModalTypes = {
  addNewContact: "add-new-contact"
} as const;

export type ContactModalType =
  (typeof ContactModalTypes)[keyof typeof ContactModalTypes];

export type ContactModalQueryParams = {
  modal: ContactModalType;
};

export function ContactProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<ContactModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  return (
    <>
      <AddNewContactModal
        isOpen={queryParams.modal === ContactModalTypes.addNewContact}
        closeModal={handleModalClose}
        handleSumbit={handleModalClose}
      />
    </>
  );
}
