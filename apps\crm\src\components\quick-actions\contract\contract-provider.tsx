"use client";

import {
  type ContractModalQueryParams,
  ContractModalTypes,
  useContractModal
} from "@watt/crm/hooks/use-contract-modal";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { Suspense } from "react";
import { ViewVerbalContract } from "./details/view-verbal-contract";
import { ViewWrittenContract } from "./details/view-written-contract";
import { ContractDataConfirmation } from "./form/contract-data-confirmation";
import { VerbalContract } from "./form/verbal-contract";
import { WrittenContract } from "./form/written-contract/written-contract";

export function ContractProvider() {
  const { queryParams } = useQueryParams<ContractModalQueryParams>();
  const { closeContractModal } = useContractModal();

  const handleModalClose = () => {
    closeContractModal();
  };

  return (
    <>
      {queryParams.modal === ContractModalTypes.createContract && (
        <Suspense>
          <ContractDataConfirmation
            isOpen={queryParams.modal === ContractModalTypes.createContract}
            closeModal={handleModalClose}
          />
        </Suspense>
      )}

      {queryParams.modal === ContractModalTypes.createVerbalContract && (
        <Suspense>
          <VerbalContract
            isOpen={
              queryParams.modal === ContractModalTypes.createVerbalContract
            }
            closeModal={handleModalClose}
          />
        </Suspense>
      )}

      {queryParams.modal === ContractModalTypes.createWrittenContract && (
        <Suspense>
          <WrittenContract
            isOpen={
              queryParams.modal === ContractModalTypes.createWrittenContract
            }
            closeModal={handleModalClose}
          />
        </Suspense>
      )}

      {queryParams.modal === ContractModalTypes.viewVerbalContract && (
        <Suspense>
          <ViewVerbalContract
            isOpen={queryParams.modal === ContractModalTypes.viewVerbalContract}
            closeModal={handleModalClose}
          />
        </Suspense>
      )}

      {queryParams.modal === ContractModalTypes.viewWrittenContract && (
        <Suspense>
          <ViewWrittenContract
            isOpen={
              queryParams.modal === ContractModalTypes.viewWrittenContract
            }
            closeModal={handleModalClose}
          />
        </Suspense>
      )}
    </>
  );
}
