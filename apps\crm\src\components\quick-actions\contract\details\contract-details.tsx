import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";

import { Button } from "@watt/crm/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@watt/crm/components/ui/collapsible";

import type {
  GetVerbalContractDetails,
  GetWrittenContractDetails
} from "@watt/api/src/router/contract";
import type { PropsWithChildren } from "react";

import { ChevronsUpDownIcon } from "lucide-react";

type CollapsibleDetailProps = PropsWithChildren<{
  title: string;
}>;

type ContractDetailsProps = PropsWithChildren<{
  contractData: GetWrittenContractDetails | GetVerbalContractDetails;
  signedDate: Date;
}>;

function CollapsibleDetail({ title, children }: CollapsibleDetailProps) {
  return (
    <Collapsible defaultOpen>
      <div className="flex items-center ">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm">
            <ChevronsUpDownIcon className="size-4" />
            <span className="sr-only fixed">{title} Toggle</span>
          </Button>
        </CollapsibleTrigger>
        <h2 className="font-semibold">{title}</h2>
      </div>
      <CollapsibleContent className="ml-10 space-y-3">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
}

export function ContractDetails({
  contractData,
  signedDate
}: ContractDetailsProps) {
  return (
    <div className="w-1/3 space-y-4 p-3">
      <CollapsibleDetail title="Contract Details">
        {contractData.contractDetails.map(
          data =>
            data.value && (
              <div className="flex justify-between" key={data.title}>
                <p className="text-muted-foreground">{data.title} </p>
                <p>
                  {data.value} {data.unit}
                </p>
              </div>
            )
        )}
      </CollapsibleDetail>
      <CollapsibleDetail title="Broker Details">
        {contractData.brokerDetails.map(
          data =>
            data.value && (
              <div className="flex justify-between" key={data.title}>
                <p className="text-muted-foreground">{data.title}</p>
                <p>{data.value}</p>
              </div>
            )
        )}
      </CollapsibleDetail>
      <CollapsibleDetail title="Business Details">
        {contractData.businessDetails.map(
          data =>
            data.value && (
              <div key={data.title}>
                <p className="text-muted-foreground">{data.title}</p>
                <p>{data.value}</p>
              </div>
            )
        )}
      </CollapsibleDetail>
      <CollapsibleDetail title="Customer Details">
        {contractData.customerDetails.map(
          data =>
            data.value && (
              <div className="flex justify-between" key={data.title}>
                <p className="text-muted-foreground">{data.title}</p>
                <p>{data.value}</p>
              </div>
            )
        )}
      </CollapsibleDetail>
      <div className="ml-10">
        <h2 className="font-semibold">Date</h2>
        <span className="text-muted-foreground">
          {formatDate(signedDate, dateFormats.DD_MM_YYYY)}
        </span>
      </div>
    </div>
  );
}
