import { Skeleton } from "@watt/crm/components/ui/skeleton";

export function VerbalContractSkeleton() {
  return (
    <div className="flex p-8">
      <div className="w-2/3">
        <Skeleton className="my-4 h-10 bg-slate-200" />
        <Skeleton className="my-4 h-10 bg-slate-200" />
        <Skeleton className="my-4 h-20 bg-slate-200" />
        <Skeleton className="h-screen bg-slate-200" />
      </div>
      <div className="w-1/3 space-y-4 px-3">
        <Skeleton className="h-56 bg-slate-200" />
        <Skeleton className="h-56 bg-slate-200" />
        <Skeleton className="h-56 bg-slate-200" />
        <Skeleton className="h-56 bg-slate-200" />
      </div>
    </div>
  );
}

export function ViewVerbalContractSkeleton() {
  return (
    <div className="flex p-8">
      <div className="w-2/3">
        <Skeleton className="h-screen bg-slate-200" />
      </div>
      <div className="w-1/3 space-y-4 px-3">
        <Skeleton className="h-56 bg-slate-200" />
        <Skeleton className="h-56 bg-slate-200" />
        <Skeleton className="h-56 bg-slate-200" />
        <Skeleton className="h-56 bg-slate-200" />
      </div>
    </div>
  );
}
