"use client";

import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { PreviewPDF } from "@watt/crm/components/preview/preview-pdf";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { VisuallyHidden } from "@watt/crm/components/ui/visually-hidden";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { trpcClient } from "@watt/crm/utils/api";
import { XIcon } from "lucide-react";
import { useMemo } from "react";
import { ContractDetails } from "./contract-details";
import { ViewWrittenContractSkeleton } from "./written-contract-skeleton";

type QueryParams = {
  contractId: string;
};

type ViewWrittenContractProps = {
  isOpen: boolean;
  closeModal: () => void;
};

export function ViewWrittenContract({
  isOpen,
  closeModal
}: ViewWrittenContractProps) {
  const { queryParams } = useQueryParams<QueryParams>();

  const { data, error, isLoading } =
    trpcClient.contract.viewWrittenContract.useQuery(
      { contractId: queryParams.contractId || "" },
      {
        enabled: !!queryParams.contractId && isOpen
      }
    );

  const memoizedPreviewPDF = useMemo(
    () => (
      <PreviewPDF
        bucketName={STORAGE_BUCKETS.COMPANY_FILES}
        filePath={data?.filePath || ""}
        className="max-h-screen w-2/3 max-w-full rounded-md border-2 border-secondary"
        validator={() => {
          if (!data?.filePath) {
            return "Failed to retrieve file path for the selected contract";
          }

          return true;
        }}
      />
    ),
    [data?.filePath]
  );

  if (!isOpen) {
    // Prevent backend queries when the drawer is not open
    return null;
  }

  if (error) {
    return <div>Error fetching contract details</div>;
  }

  if (!data) {
    return null;
  }

  return (
    <Drawer
      direction="right"
      dismissible={false}
      open={isOpen}
      key={queryParams.contractId}
    >
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="w-[70%] max-w-[1400px]"
        onEscapeKeyDown={closeModal}
      >
        <VisuallyHidden>
          <DrawerTitle>View Written Contract Details</DrawerTitle>
        </VisuallyHidden>
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={closeModal}
        >
          <XIcon className="size-4" />
          <span className="sr-only fixed">Close</span>
        </Button>
        {isLoading ? (
          <ViewWrittenContractSkeleton />
        ) : (
          <div className="overflow-y-scroll p-8">
            <div className="flex">
              {memoizedPreviewPDF}
              <ContractDetails
                contractData={data}
                signedDate={data?.signedDate}
              />
            </div>
          </div>
        )}
      </DrawerContentWithDirection>
    </Drawer>
  );
}
