"use client";

import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { XIcon } from "lucide-react";
import { ContractForm } from "./contract-form";

type ContractDataConfirmationProps = {
  isOpen: boolean;
  closeModal: () => void;
};

export function ContractDataConfirmation({
  isOpen,
  closeModal
}: ContractDataConfirmationProps) {
  if (!isOpen) {
    // Prevent backend queries when the drawer is not open
    return null;
  }

  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="max-w-[700px]"
        onEscapeKeyDown={closeModal}
      >
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={closeModal}
        >
          <XIcon className="size-4" />
          <span className="sr-only fixed">Close</span>
        </Button>
        <div className="flex h-screen flex-col overflow-y-scroll p-8">
          <div className="flex flex-col space-y-4">
            <DrawerTitle className="font-semibold text-xl tracking-tight">
              Create Contract Form
            </DrawerTitle>
            <DrawerDescription className="text-muted-foreground text-sm italic">
              Please enter details to identify relevant quote lists.
            </DrawerDescription>
          </div>
          <ContractForm />
        </div>
      </DrawerContentWithDirection>
    </Drawer>
  );
}
