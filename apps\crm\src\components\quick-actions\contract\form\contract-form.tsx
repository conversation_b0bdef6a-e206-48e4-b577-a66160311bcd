"use client";

import { TRPCClientError } from "@trpc/client";
import type { CompaniesWith_Find_Many } from "@watt/api/src/router";
import type { Address_Find_Many } from "@watt/api/src/router/address";
import { isValidBusinessReference } from "@watt/common/src/regex/company-number";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2Icon } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useDebounce } from "react-use";
import { z } from "zod";

import {
  AddressSelection,
  CompanySelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { toast } from "@watt/crm/components/ui/use-toast";
import { routes } from "@watt/crm/config/routes";
import { useAddressSearch } from "@watt/crm/hooks/use-address-search";
import { useNavigateToRoute } from "@watt/crm/hooks/use-navigate-to-route";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

const ContractFormSchema = z.object({
  businessNumber: z.string().refine(isValidBusinessReference, {
    message: "Please enter a valid business reference."
  }),
  businessName: z.string(),
  siteAddressId: z.string().min(1, "Please enter a valid site address"),
  meterIdentifier: z.string()
});

type QueryParams = {
  businessSearch: string; // Keyword used to search for an entity
  selectedBusinessRef: string; // Selected business reference number from the search results
  addressSearch: string; // Postcode used to search for an address
  selectedAddressId: string; // Selected address id from the search results
  meterIdentifier: string; // Meter identifier selected from the dropdown after selecting an address and utility type
};

export function ContractForm() {
  const navigateToRoute = useNavigateToRoute();
  const businessNumberContainerRef = useRef<HTMLDivElement>(null);
  const addressContainerRef = useRef<HTMLDivElement>(null);
  // (Bidur): As the Command component is inside the Popover component, on select does not close the modal so we pass controlled state
  const [isBusinessModalOpen, setIsBusinessModalOpen] = useState(false);
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
  const { queryParams, setQueryParams } =
    useQueryParams<Partial<QueryParams>>();
  const [companySearchResults, setCompanySearchResults] =
    useState<CompaniesWith_Find_Many>([]);
  const [addressData, setAddressData] = useState<
    Address_Find_Many | undefined
  >();
  const [meterNumbersList, setMeterNumbersList] = useState<
    string[] | undefined
  >();

  const { fetchAddress, isFetchingAddress } = useAddressSearch();
  const businessMutation = trpcClient.company.findMany.useMutation();
  const companySiteMeterListMutation =
    trpcClient.siteMeter.findCompanySiteMeters.useMutation();

  const form = useZodForm({
    schema: ContractFormSchema,
    mode: "onChange"
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function defaultSearchToSelectedReferenceOnMount() {
    if (queryParams.businessSearch || !queryParams.selectedBusinessRef) {
      return;
    }

    setQueryParams({ businessSearch: queryParams.selectedBusinessRef });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function fullResetBusinessFormData() {
    form.resetField("businessNumber");
    form.resetField("businessName");
    setQueryParams({ businessSearch: "", selectedBusinessRef: "" });
    setCompanySearchResults([]);
  }

  function fullResetAddressFormData() {
    form.resetField("meterIdentifier");
    setQueryParams({
      addressSearch: "",
      selectedAddressId: "",
      meterIdentifier: ""
    });
    setAddressData(undefined);
  }

  useDebounce(
    async function lookupBusinessRefOnSearchInput() {
      try {
        if (!queryParams.businessSearch) {
          fullResetBusinessFormData();
          return;
        }

        const isBusinessRef = isValidBusinessReference(
          queryParams.businessSearch
        );
        const result = await businessMutation.mutateAsync({
          ...(isBusinessRef
            ? { businessRef: queryParams.businessSearch }
            : { name: queryParams.businessSearch })
        });

        setCompanySearchResults(result);

        if (queryParams.selectedBusinessRef) {
          const selectedCompany = result.find(
            company =>
              company.registrationNumber === queryParams.selectedBusinessRef
          );
          if (!selectedCompany) {
            return;
          }

          form.setValue("businessNumber", selectedCompany.registrationNumber);
          form.setValue("businessName", selectedCompany.name);
        }
      } catch (e) {
        const error = e as Error;
        const description =
          error instanceof TRPCClientError && !!error.data.zodError
            ? "Error fetching business information. Please check the input and try again."
            : error.message;
        toast({
          title: "Unable to get business entity",
          description,
          variant: "destructive"
        });
      }
    },
    500,
    [queryParams.businessSearch]
  );

  useDebounce(
    async function lookupAddressOnSearchInput() {
      try {
        if (!queryParams.addressSearch) {
          fullResetAddressFormData();
          return;
        }

        const result = await fetchAddress(queryParams.addressSearch);
        if (result) {
          setAddressData(result);

          if (queryParams.selectedAddressId) {
            const selectedAddressObj = result?.find(
              address => address.id === queryParams.selectedAddressId
            );

            if (
              !selectedAddressObj?.displayName ||
              !selectedAddressObj.postcode
            ) {
              fullResetAddressFormData();

              toast({
                title: "Invalid address in the URL",
                description:
                  "Selected address does not have a valid address or postcode",
                variant: "destructive"
              });
              return;
            }
          }
        }
      } catch (e) {
        const error = e as Error;
        const description =
          error instanceof TRPCClientError && !!error.data.zodError
            ? "Error fetching address. Please check the input and try again."
            : error.message;
        toast({
          title: "Unable to get address",
          description,
          variant: "destructive"
        });
      }
    },
    1000,
    [queryParams.addressSearch]
  );

  const selectedCompany = useMemo(
    () =>
      companySearchResults?.find(
        company =>
          company.registrationNumber ===
          queryParams.selectedBusinessRef?.toLocaleUpperCase()
      ),
    [queryParams.selectedBusinessRef, companySearchResults]
  );

  const selectedAddress = useMemo(
    () =>
      addressData?.find(
        address => address.id === queryParams.selectedAddressId
      ),
    [queryParams.selectedAddressId, addressData]
  );

  useDebounce(
    async function updateMeterNumbersList() {
      if (!queryParams.selectedAddressId || !addressData) {
        return;
      }

      const selectedAddressObj = addressData.find(
        address => address.id === queryParams.selectedAddressId
      );

      if (!selectedAddressObj) {
        return;
      }

      const meterNumbers = selectedAddressObj.meterNumbers;

      if (!meterNumbers || !meterNumbers.length) {
        setMeterNumbersList([]);
        setQueryParams({ meterIdentifier: "" });
        form.setValue("meterIdentifier", "");
        return;
      }

      const meterNumbersForSelectedAddress =
        meterNumbers?.map(meter => meter.meterNumber) ?? [];

      if (queryParams.selectedBusinessRef) {
        const availableSiteMetersForSelectedCompanyAndAddress =
          await companySiteMeterListMutation.mutateAsync({
            companyReg: queryParams.selectedBusinessRef,
            addressId: queryParams.selectedAddressId
          });
        meterNumbersForSelectedAddress.push(
          ...availableSiteMetersForSelectedCompanyAndAddress
        );
      }

      if (!meterNumbersForSelectedAddress.length) {
        setMeterNumbersList([]);
        setQueryParams({ meterIdentifier: "" });
        form.setValue("meterIdentifier", "");
        return;
      }

      // Remove potential duplicates from site meter data before setting the meter numbers list
      const uniqueMeterNumbers = [...new Set(meterNumbersForSelectedAddress)];
      setMeterNumbersList(uniqueMeterNumbers);

      if (queryParams.meterIdentifier && meterNumbers) {
        const selectedMeterNumber = meterNumbers.find(
          meter => meter.meterNumber === queryParams.meterIdentifier
        )?.meterNumber;

        if (!selectedMeterNumber) {
          setQueryParams({ meterIdentifier: "" });
          form.setValue("meterIdentifier", "");

          return;
        }

        form.setValue("meterIdentifier", selectedMeterNumber);
      }
    },
    500,
    [queryParams.selectedAddressId, addressData]
  );

  const handleAddressSelect = (addressId: string) => {
    const selectedAddressObj = addressData?.find(
      address => address.id === addressId
    );

    if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
      toast({
        title: "Invalid address",
        description:
          "Selected address does not have a valid address or postcode",
        variant: "destructive"
      });
      return;
    }

    setQueryParams({ selectedAddressId: addressId });
    form.setValue("siteAddressId", addressId, { shouldValidate: true });
    setIsAddressModalOpen(false);
  };

  const handleBusinessEntitySelect = (businessRefRaw: string) => {
    const businessRef = businessRefRaw.toLocaleUpperCase();
    const selectedCompany = companySearchResults.find(
      company => company.registrationNumber === businessRef
    );
    if (!selectedCompany) {
      return;
    }

    setQueryParams({ selectedBusinessRef: businessRef });
    form.setValue("businessNumber", businessRef, { shouldValidate: true });
    form.setValue("businessName", selectedCompany.name, {
      shouldValidate: true
    });
    setIsBusinessModalOpen(false);
  };

  const handleQuoteSubmit = async () => {
    try {
      navigateToRoute(routes.quotes, {
        queryParams: {
          companyName: form.getValues().businessName,
          meterIdentifier: form.getValues().meterIdentifier,
          verbalContract: true
        }
      });
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while getting the new quotes. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to get quotes",
        description,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="flex h-[100%] max-w-[600px] flex-col px-4">
      <FormWrapper
        form={form}
        handleSubmit={handleQuoteSubmit}
        className="my-4 flex flex-1 flex-col space-y-6"
      >
        <FormField
          control={form.control}
          name="businessNumber"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Business Name *</FormLabel>
              <FormControl>
                <div
                  ref={businessNumberContainerRef}
                  className="flex w-full flex-col"
                >
                  <LookUp
                    open={isBusinessModalOpen}
                    onOpenChange={setIsBusinessModalOpen}
                  >
                    <LookUpTrigger
                      fieldValue={field.value}
                      isLoading={businessMutation.isPending}
                    >
                      {selectedCompany ? (
                        <CompanySelection
                          businessRef={selectedCompany.registrationNumber}
                          businessType={selectedCompany.businessType}
                          businessStatus="A"
                          commercialName={selectedCompany.name}
                        />
                      ) : (
                        <span className="font-normal italic">
                          Select a business
                        </span>
                      )}
                    </LookUpTrigger>
                    <LookUpContent
                      placeholder="Search by business name or number"
                      searchInput={queryParams.businessSearch}
                      onSearchInputChange={businessSearch => {
                        setQueryParams({ businessSearch });
                      }}
                      isLoading={businessMutation.isPending}
                      container={businessNumberContainerRef.current}
                    >
                      <LookUpGroup className="p-0">
                        {companySearchResults?.map(company => (
                          <LookUpItem
                            key={company.registrationNumber}
                            value={company.registrationNumber}
                            onSelect={handleBusinessEntitySelect}
                          >
                            <CompanySelection
                              businessRef={company.registrationNumber}
                              businessType={company.businessType}
                              businessStatus="A"
                              commercialName={company.name}
                            />
                          </LookUpItem>
                        ))}
                      </LookUpGroup>
                    </LookUpContent>
                  </LookUp>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="siteAddressId"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Site Address *</FormLabel>
              <FormControl>
                <div ref={addressContainerRef} className="flex w-full flex-col">
                  <LookUp
                    open={isAddressModalOpen}
                    onOpenChange={setIsAddressModalOpen}
                  >
                    <LookUpTrigger
                      fieldValue={field.value}
                      isLoading={isFetchingAddress}
                    >
                      {selectedAddress ? (
                        <AddressSelection address={selectedAddress} />
                      ) : (
                        <span className="font-normal italic">
                          Select a site...
                        </span>
                      )}
                    </LookUpTrigger>
                    <LookUpContent
                      placeholder="Search by postcode, mpan or mprn number" // TODO (Bidur): Also extend search by address line
                      searchInput={queryParams.addressSearch}
                      onSearchInputChange={(addressSearch: string) => {
                        setQueryParams({
                          addressSearch,
                          selectedAddressId: ""
                        });
                        form.setValue("siteAddressId", "");
                      }}
                      isLoading={isFetchingAddress}
                      container={addressContainerRef.current}
                    >
                      <LookUpGroup className="p-0">
                        {addressData?.map(address => (
                          <LookUpItem
                            key={address.id}
                            value={address.id}
                            disabled={
                              address.siteMeters[1] === 0 &&
                              address.siteMeters[2] === 0
                            }
                            onSelect={handleAddressSelect}
                          >
                            <AddressSelection address={address} />
                          </LookUpItem>
                        ))}
                      </LookUpGroup>
                    </LookUpContent>
                  </LookUp>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="meterIdentifier"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Meter Identifier *</FormLabel>
              <FormControl>
                <Select
                  onValueChange={meterNo => {
                    setQueryParams({ meterIdentifier: meterNo });
                    form.setValue("meterIdentifier", meterNo, {
                      shouldValidate: true
                    });
                  }}
                  value={field.value}
                  key={field.value}
                >
                  <SelectTrigger
                    className={cn(
                      !field.value && "flex gap-2 text-muted-foreground italic"
                    )}
                  >
                    <SelectValue
                      placeholder={
                        meterNumbersList && !meterNumbersList?.length
                          ? "No meter found for this utility at this address"
                          : "Select a meter number"
                      }
                    >
                      {field.value}
                    </SelectValue>
                  </SelectTrigger>
                  {!!selectedAddress && (
                    <SelectContent>
                      {isFetchingAddress ? (
                        <Loader2Icon className="m-auto size-4 animate-spin text-muted-foreground" />
                      ) : (
                        meterNumbersList?.map(meterNo => (
                          <SelectItem key={meterNo} value={meterNo}>
                            {meterNo}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  )}
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" variant="secondary" className="!mt-auto w-full">
          Confirm
        </Button>
      </FormWrapper>
    </div>
  );
}
