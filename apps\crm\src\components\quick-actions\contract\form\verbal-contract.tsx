"use client";

import { TRPCClientError } from "@trpc/client";
import {
  CreateVerbalContractSchema,
  type VerbalContractTemplateItem
} from "@watt/api/src/types/contract";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { PreviewPDF } from "@watt/crm/components/preview/preview-pdf";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from "@watt/crm/components/react-hook-form/form";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { FormWrapper } from "@watt/crm/components/ui/form";
import { Label } from "@watt/crm/components/ui/label";
import { toast } from "@watt/crm/components/ui/use-toast";
import { VisuallyHidden } from "@watt/crm/components/ui/visually-hidden";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2Icon, XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useRef, useState } from "react";
import { ContractDetails } from "../details/contract-details";
import { VerbalContractSkeleton } from "../details/verbal-contract-skeleton";

type VerbalContractProps = {
  isOpen: boolean;
  closeModal: () => void;
};

type QueryParams = {
  quoteId: string;
  contactId: string;
};

export function VerbalContract({ isOpen, closeModal }: VerbalContractProps) {
  const router = useRouter();
  const { queryParams } = useQueryParams<QueryParams>();
  const verbalContractTemplateContainerRef = useRef<HTMLDivElement>(null);
  const [openVerbalContractTemplateModal, setOpenVerbalContractTemplateModal] =
    useState(false);
  const [templateSearch, setTemplateSearch] = useState("");
  const [selectedTemplate, setSelectedTemplate] =
    useState<VerbalContractTemplateItem>({
      id: "",
      friendlyName: "",
      path: ""
    });

  const memoizedPreviewPDF = useMemo(
    () => (
      <PreviewPDF
        bucketName={STORAGE_BUCKETS.VERBAL_CONTRACT_TEMPLATES}
        filePath={selectedTemplate.path}
        className="max-h-screen max-w-full rounded-md border-2 border-secondary"
        validator={() => {
          if (!selectedTemplate.path) {
            return "No verbal script available for the selected template";
          }

          return true;
        }}
      />
    ),
    [selectedTemplate.path]
  );

  const verbalContractMutation =
    trpcClient.contract.createVerbalContract.useMutation();

  const {
    data: verbalContract,
    error,
    isLoading
  } = trpcClient.contract.getVerbalContractDetails.useQuery(
    {
      quoteId: queryParams.quoteId || "",
      contactId: queryParams.contactId || ""
    },
    {
      enabled: !!queryParams.quoteId && !!queryParams.contactId && isOpen
    }
  );

  const form = useZodForm({
    schema: CreateVerbalContractSchema,
    mode: "onChange"
  });

  useEffect(
    function updateDynamicInputFields() {
      if (!verbalContract) {
        return;
      }

      form.setValue("utilityType", verbalContract?.additionalData.utilityType);
      form.setValue("companyId", verbalContract?.additionalData.companyId);
      form.setValue("siteId", verbalContract?.additionalData.siteId);
      form.setValue("quoteId", verbalContract?.additionalData.quoteId);
      form.setValue("contactId", verbalContract?.additionalData.contactId);
      form.setValue(
        "contractStartDate",
        verbalContract?.additionalData.contractStartDate?.toString()
      );
      form.setValue(
        "contractEndDate",
        verbalContract?.additionalData.contractEndDate?.toString()
      );
      form.setValue(
        "contractDuration",
        verbalContract?.additionalData.contractDuration
      );
    },
    [verbalContract, form]
  );

  const handleVerbalContractSubmit = async () => {
    try {
      await verbalContractMutation.mutateAsync(form.getValues());
      toast({
        title: "Success",
        variant: "success",
        description: "Verbal contract has been created successfully"
      });
      closeModal();
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while creating verbal contract. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to create verbal contract",
        description,
        variant: "destructive"
      });
    }
  };

  if (!isOpen) {
    // Prevent backend queries when the drawer is not open
    return null;
  }

  if (error) {
    return <div>Error fetching contract details</div>;
  }

  if (!verbalContract) {
    return null;
  }

  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="w-[70%] max-w-[1400px]"
        onEscapeKeyDown={closeModal}
      >
        <VisuallyHidden>
          <DrawerTitle>Verbal Contract</DrawerTitle>
        </VisuallyHidden>
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={closeModal}
        >
          <XIcon className="size-4" />
          <span className="sr-only fixed">Close</span>
        </Button>
        {isLoading ? (
          <VerbalContractSkeleton />
        ) : (
          <div className="overflow-y-scroll p-8">
            <div className="flex">
              <div className="w-2/3">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-28"
                  onClick={() => {
                    router.back();
                  }}
                >
                  Back
                </Button>
                <FormWrapper
                  form={form}
                  handleSubmit={handleVerbalContractSubmit}
                  className="my-4 space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="verbalContractTemplateId"
                    render={() => (
                      <FormItem className="mt-8 flex flex-col space-y-4">
                        <Label className="font-bold text-3xl">
                          Verbal Script
                        </Label>
                        {verbalContract.supplierVerbalTemplates?.note && (
                          <span className="text-destructive text-sm italic">
                            {verbalContract.supplierVerbalTemplates.note}
                          </span>
                        )}
                        <FormControl>
                          <div
                            ref={verbalContractTemplateContainerRef}
                            className="flex max-w-[500px] flex-col"
                          >
                            <LookUp
                              open={openVerbalContractTemplateModal}
                              onOpenChange={setOpenVerbalContractTemplateModal}
                            >
                              <LookUpTrigger
                                fieldValue={selectedTemplate.friendlyName}
                              >
                                <span className="font-normal">
                                  {selectedTemplate.friendlyName ||
                                    "Please select the verbal script"}
                                </span>
                              </LookUpTrigger>
                              <LookUpContent
                                placeholder="Enter a template name"
                                searchInput={templateSearch}
                                onSearchInputChange={setTemplateSearch}
                                container={
                                  verbalContractTemplateContainerRef.current
                                }
                                className="max-w-[500px]"
                                shouldFilter
                              >
                                {verbalContract.supplierVerbalTemplates && (
                                  <LookUpGroup
                                    heading={
                                      verbalContract.additionalData
                                        .providerUdcoreId
                                    }
                                  >
                                    {verbalContract.supplierVerbalTemplates.templates.map(
                                      template => {
                                        return (
                                          <LookUpItem
                                            key={template.path}
                                            value={template.friendlyName}
                                            onSelect={() => {
                                              form.setValue(
                                                "verbalContractTemplateId",
                                                template.id
                                              );
                                              setSelectedTemplate(template);
                                              setOpenVerbalContractTemplateModal(
                                                false
                                              );
                                            }}
                                          >
                                            <span>{template.friendlyName}</span>
                                          </LookUpItem>
                                        );
                                      }
                                    )}
                                  </LookUpGroup>
                                )}
                              </LookUpContent>
                            </LookUp>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </FormWrapper>
                {memoizedPreviewPDF}
              </div>
              <ContractDetails
                contractData={verbalContract}
                signedDate={new Date()}
              />
            </div>
            <Button
              onClick={form.handleSubmit(handleVerbalContractSubmit)}
              variant="secondary"
              className="my-4 w-full font-medium text-base"
              disabled={
                !selectedTemplate.path || verbalContractMutation.isPending
              }
            >
              {verbalContractMutation.isPending && (
                <Loader2Icon className="mr-2 size-4 animate-spin" />
              )}
              Confirm
            </Button>
          </div>
        )}
      </DrawerContentWithDirection>
    </Drawer>
  );
}
