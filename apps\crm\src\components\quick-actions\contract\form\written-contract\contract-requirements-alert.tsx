import type { ContractRequirementChecklist } from "@watt/api/src/types/contract";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@watt/crm/components/ui/alert";
import { AlertCircleIcon } from "lucide-react";

type FailedRequirement = {
  index: number; // mainly for key prop
  message: string;
};

export function ContractRequirementsAlert({
  checklist
}: {
  checklist: ContractRequirementChecklist;
}) {
  if (checklist.isValid) {
    return null;
  }

  const failedRequirements: FailedRequirement[] = [];

  if (!checklist.hasPhoneNumber) {
    failedRequirements.push({
      index: 0,
      message: "Contact is missing a phone number"
    });
  }

  if (!checklist.hasDateOfBirth) {
    failedRequirements.push({
      index: 1,
      message: "Contact is missing a date of birth"
    });
  }

  if (!checklist.hasCurrentAddress) {
    failedRequirements.push({
      index: 2,
      message: "Contact must have a current address"
    });
  }

  if (!checklist.hasValidAddressHistory) {
    failedRequirements.push({
      index: 3,
      message:
        "Contact must have at least 2 addresses or an address with more than 3 years of residency"
    });
  }

  return (
    <Alert
      variant="warn"
      className="mb-6 rounded-none border-0 border-badge-orange border-l-4 text-black"
    >
      <AlertCircleIcon className="size-4" />
      <AlertTitle>Warning</AlertTitle>
      <AlertDescription className="mt-2 text-xs">
        The following information is missing from the contract:
        <ul className="mt-2 list-inside list-disc">
          {failedRequirements.map(requirement => (
            <li key={requirement.index}>{requirement.message}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
