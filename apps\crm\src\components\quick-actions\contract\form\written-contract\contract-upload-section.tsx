"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from "@watt/crm/components/react-hook-form/form";
import { FormLabel } from "@watt/crm/components/ui/form";
import { Label } from "@watt/crm/components/ui/label";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger
} from "@watt/crm/components/ui/tabs";
import { EditFilenameSection } from "./edit-filename-section";
import { FileUploadSection } from "./file-upload-section";
import { PrefilledContractSection } from "./prefilled-contract-section";
import { SelectFromCloudSection } from "./select-from-cloud-section";
import type { FileHandlingProps } from "./written-contract";

export function ContractUploadSection(props: FileHandlingProps) {
  const { form } = props;

  const storagePath = form.watch("storagePath");

  return (
    <FormField
      control={form.control}
      name="signedContract"
      render={({ field }) => (
        <FormItem className="mt-8 grid gap-6">
          <Label className="flex-1 shrink-0 font-bold text-3xl">
            Written Contract
          </Label>
          <PrefilledContractSection {...props} />
          <div className="grid gap-4">
            <FormLabel>Signed Contract *</FormLabel>
            {field.value ? (
              <EditFilenameSection {...props} />
            ) : (
              <FormControl>
                <Tabs
                  defaultValue="upload"
                  className="w-full [&_[role=tabpanel]]:mt-2"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="upload">Upload from Device</TabsTrigger>
                    <TabsTrigger value="cloud">Select from Cloud</TabsTrigger>
                  </TabsList>
                  <TabsContent value="upload">
                    <FileUploadSection {...props} />
                  </TabsContent>
                  <TabsContent value="cloud">
                    <SelectFromCloudSection {...props} />
                  </TabsContent>
                </Tabs>
              </FormControl>
            )}
            {!(field.value && storagePath) && <FormMessage />}
          </div>
        </FormItem>
      )}
    />
  );
}
