"use client";

import { UploadedCompanyFile } from "@watt/crm/components/quick-actions/file/uploaded-company-file";
import { useCallback } from "react";
import type { FileHandlingProps } from "./written-contract";

export function EditFilenameSection({
  companyId,
  form,
  isProcessing
}: FileHandlingProps) {
  const signedContract = form.watch("signedContract");
  const finalFilename = form.watch("finalFilename");
  const isFilenameEditable = !!form.watch("storagePath");

  const handleFilenameChange = useCallback(
    (filename: string | null) => form.setValue("finalFilename", filename ?? ""),
    [form]
  );

  const handleResetNonEssentialFields = useCallback(() => {
    form.setValue("finalFilename", "");
    form.setValue("storagePath", "");
    form.resetField("signedContract");
  }, [form]);

  return (
    <UploadedCompanyFile
      isFilenameEditable={isFilenameEditable}
      disabled={isProcessing}
      companyId={companyId}
      file={signedContract}
      filename={finalFilename}
      onRemove={handleResetNonEssentialFields}
      onFilenameChange={handleFilenameChange}
    />
  );
}
