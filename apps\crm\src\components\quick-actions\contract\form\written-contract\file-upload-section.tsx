"use client";

import { FileUploader } from "../../../file/file-uploader";
import { UPLOAD_CONFIG } from "./written-contract";
import type { FileHandlingProps } from "./written-contract";

export function FileUploadSection({ form, onFileUpload }: FileHandlingProps) {
  return (
    <FileUploader
      value={[]}
      onValueChange={files => onFileUpload({ files, form })}
      accept={UPLOAD_CONFIG.ACCEPTED_TYPES}
      maxFileCount={1}
      maxSize={UPLOAD_CONFIG.MAX_SIZE}
      multiple={false}
      title="Drag and drop your signed contract here or click to browse"
    />
  );
}
