"use client";

import { TRPCClientError } from "@trpc/client";
import { log } from "@watt/common/src/utils/axiom-logger";
import { Button } from "@watt/crm/components/ui/button";
import { toast } from "@watt/crm/components/ui/use-toast";
import { trpcClient } from "@watt/crm/utils/api";
import {
  getWrittenContractTemplateKeyByUdCoreId,
  providersByUdcoreId
} from "@watt/db/src/constants/providers/providers-sold-by-us";
import { DownloadIcon, Loader2Icon } from "lucide-react";
import { useState } from "react";
import { ContractRequirementsAlert } from "./contract-requirements-alert";
import type { FileHandlingProps } from "./written-contract";

export function PrefilledContractSection(props: FileHandlingProps) {
  const {
    providerUdcoreId,
    utilityType,
    quoteId,
    requirementChecklist,
    requiresPortalForWrittenContract
  } = props;

  const generatePrefilledContract =
    trpcClient.contract.generatePrefilledContract.useMutation();

  const [isLoading, setIsLoading] = useState(false);

  const contractTemplate = getWrittenContractTemplateKeyByUdCoreId(
    providerUdcoreId,
    utilityType
  );

  const contractFilename = `prefilled-${quoteId}.pdf`;

  async function handleDownload() {
    setIsLoading(true);

    try {
      const result = await generatePrefilledContract.mutateAsync({
        quoteId: props.quoteId,
        contactId: props.contactId
      });

      if (result.signedUrl) {
        // TODO: This looks really odd, using document.createElement
        // is not the React way to do this, adding it to trigger an auto click
        // then remove it is strange.
        const response = await fetch(result.signedUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = contractFilename;
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
      }
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while downloading the prefilled contract"
          : error.message;
      toast({
        title: "Unable to download prefilled contract",
        description,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }

  if (
    !providersByUdcoreId[providerUdcoreId] ||
    !contractTemplate ||
    requiresPortalForWrittenContract
  ) {
    return null;
  }

  return (
    <>
      <div className="flex items-center justify-between gap-4 rounded-md border p-4 shadow-sm">
        <div className="flex flex-col gap-1">
          <span className="font-semibold">Pre-filled Contract</span>
          <span className="text-muted-foreground text-sm">
            {contractFilename}
          </span>
        </div>
        <Button
          onClick={handleDownload}
          type="button"
          size="sm"
          variant="secondary"
          disabled={isLoading}
          className="[&_svg]:mr-2 [&_svg]:size-4"
        >
          {isLoading ? (
            <Loader2Icon className="animate-spin" />
          ) : (
            <DownloadIcon />
          )}
          Download
        </Button>
      </div>
      {requirementChecklist && !requirementChecklist.isValid && (
        <ContractRequirementsAlert checklist={requirementChecklist} />
      )}
    </>
  );
}
