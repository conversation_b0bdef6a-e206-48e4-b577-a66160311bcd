"use client";

import type { CompanyFile } from "@prisma/client";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatBytes } from "@watt/common/src/utils/format-bytes";
import { EmptyStatePanel } from "@watt/crm/components/empty-state/empty-state-panel";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { useCompanyFilesSearch } from "@watt/crm/hooks/use-company-files-search";
import { CompanyFileType } from "@watt/db/src/enums";
import { format } from "date-fns";
import {
  ClockIcon,
  FileIcon,
  Grid2x2Icon,
  ListIcon,
  SearchXIcon
} from "lucide-react";
import { useState } from "react";
import type { FileHandlingProps } from "./written-contract";

type HandleSelectCloudFileProps = {
  file: CompanyFile;
  form: FileHandlingProps["form"];
};

export function handleSelectCloudFile({
  file,
  form
}: HandleSelectCloudFileProps) {
  const fileSizeInBytes = new Uint8Array(file.size);

  const cloudFile = Object.assign(
    new File([fileSizeInBytes], file.filename, {
      type: file.mimeType
    }),
    {
      companyFileId: file.id
    }
  );

  form.setValue("finalFilename", file.filename);
  form.setValue("storagePath", file.path);
  form.setValue("signedContract", cloudFile);
}

export function SelectFromCloudSection({ companyId, form }: FileHandlingProps) {
  const [view, setView] = useState<"grid" | "list">("grid");

  const { files, isLoading, searchQuery, setSearchQuery } =
    useCompanyFilesSearch({
      companyId,
      fileTypes: [CompanyFileType.WRITTEN_CONTRACT]
    });

  if (isLoading) {
    return <SkeletonLoader />;
  }

  if (!files.length && !searchQuery) {
    return (
      <div className="rounded-lg bg-muted">
        <EmptyStatePanel
          title="No suitable files found"
          description="None of this company's uploaded files meet the requirements for a written contract."
          Icon={FileIcon}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <SearchInput
          placeholder="Type to search..."
          onChange={e => setSearchQuery(e.target.value)}
          className="h-9 w-[250px]"
          value={searchQuery}
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              {view === "grid" ? (
                <Grid2x2Icon className="size-4" />
              ) : (
                <ListIcon className="size-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setView("grid")}>
              <Grid2x2Icon className="mr-2 size-4" /> Grid View
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setView("list")}>
              <ListIcon className="mr-2 size-4" /> List View
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {!files.length ? (
        <div className="rounded-lg bg-muted">
          <EmptyStatePanel
            title="No matching files found"
            description="Please try a different search query"
            Icon={SearchXIcon}
          >
            <Button variant="outline" onClick={() => setSearchQuery("")}>
              Clear search
            </Button>
          </EmptyStatePanel>
        </div>
      ) : (
        <div
          className={cn(
            "grid",
            view === "grid" ? "grid-cols-2 gap-4" : "gap-2"
          )}
        >
          {files.map(file => (
            <CloudFileCard
              key={file.id}
              file={file}
              onSelectFile={selectedFile =>
                handleSelectCloudFile({
                  file: selectedFile,
                  form
                })
              }
            />
          ))}
        </div>
      )}
    </div>
  );
}

type CloudFileCardProps = {
  file: CompanyFile;
  onSelectFile: (file: CompanyFile) => void;
};

export function CloudFileCard({ file, onSelectFile }: CloudFileCardProps) {
  return (
    <Button
      variant="outline"
      className="h-auto justify-start rounded-lg py-4 text-left shadow-sm"
      onClick={() => onSelectFile(file)}
    >
      <div className="flex items-center gap-3">
        <ClockIcon className="size-4 shrink-0 text-muted-foreground" />
        <div className="grid gap-px">
          <p className="break-all font-medium text-base">{file.filename}</p>
          <p className="font-normal text-muted-foreground text-sm">
            Modified {format(file.createdAt, "yyyy-MM-dd")} •{" "}
            {formatBytes(file.size)}
          </p>
        </div>
      </div>
    </Button>
  );
}

function SkeletonLoader() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-9 w-[250px] rounded-lg bg-slate-200" />
        <Skeleton className="size-10 rounded-lg bg-slate-200" />
      </div>
      <div className="grid grid-cols-2 gap-4">
        {Array.from({ length: 8 }, (_, i) => (
          <Skeleton key={`${i + 1}`} className="h-20 rounded-lg bg-slate-200" />
        ))}
      </div>
    </div>
  );
}
