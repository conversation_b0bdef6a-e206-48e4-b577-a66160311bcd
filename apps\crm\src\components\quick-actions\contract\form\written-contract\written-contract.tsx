"use client";

import { TRPCClientError } from "@trpc/client";
import type { ContractRequirementChecklist } from "@watt/api/src/types/contract";
import { CreateWrittenContractFormSchema } from "@watt/api/src/types/contract";
import { FIFTEEN_MB } from "@watt/common/src/constants/file-sizes";
import { DOCUMENT_MIME_TYPES } from "@watt/common/src/constants/mime-types";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { log } from "@watt/common/src/utils/axiom-logger";
import { PreviewPDF } from "@watt/crm/components/preview/preview-pdf";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { FormWrapper } from "@watt/crm/components/ui/form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { VisuallyHidden } from "@watt/crm/components/ui/visually-hidden";
import { useContractQuery } from "@watt/crm/hooks/use-contract-query";
import { usePreventUnload } from "@watt/crm/hooks/use-prevent-unload";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useUploadFile } from "@watt/crm/hooks/use-upload-file";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { getFilePathAndId } from "@watt/crm/utils/generate-file-path-by-company-id";
import type { UtilityType } from "@watt/db/src/enums";
import { CompanyFileType } from "@watt/db/src/enums";
import { Loader2Icon, XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { ContractDetails } from "../../details/contract-details";
import { WrittenContractSkeleton } from "../../details/written-contract-skeleton";
import { ContractUploadSection } from "./contract-upload-section";

export const UPLOAD_CONFIG = {
  MAX_SIZE: FIFTEEN_MB,
  ACCEPTED_TYPES: {
    [DOCUMENT_MIME_TYPES.PDF]: []
  },
  BUCKET_NAME: STORAGE_BUCKETS.COMPANY_FILES
};

type WrittenContractFormSchema = typeof CreateWrittenContractFormSchema;

type WrittenContractForm = ReturnType<
  typeof useZodForm<WrittenContractFormSchema>
>;

type FileUploadParams = {
  files: File[];
  form: WrittenContractForm;
};

export type FileHandlingProps = {
  companyId: string;
  providerUdcoreId: string;
  quoteId: string;
  contactId: string;
  businessName: string;
  postcode: string;
  utilityType: UtilityType;
  form: WrittenContractForm;
  isProcessing: boolean;
  onFileUpload: (params: FileUploadParams) => void;
  requirementChecklist?: ContractRequirementChecklist;
  requiresPortalForWrittenContract: boolean;
};

type WrittenContractProps = {
  isOpen: boolean;
  closeModal: () => void;
};

export function WrittenContract({ isOpen, closeModal }: WrittenContractProps) {
  const { back } = useRouter();
  const { queryParams } = useQueryParams();

  const previewRef = useRef<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    data: writtenContract,
    error,
    isLoading
  } = useContractQuery(isOpen, queryParams);

  const { onUpload, isUploading } = useUploadFile({
    bucketName: UPLOAD_CONFIG.BUCKET_NAME
  });

  const registerFile = trpcClient.companyFiles.registerFile.useMutation();

  const createWrittenContract =
    trpcClient.contract.createWrittenContract.useMutation();

  const form = useZodForm<WrittenContractFormSchema>({
    schema: CreateWrittenContractFormSchema,
    mode: "onChange"
  });

  useEffect(
    function updateDynamicInputFields() {
      if (!writtenContract) {
        return;
      }

      form.setValue("utilityType", writtenContract?.additionalData.utilityType);
      form.setValue("companyId", writtenContract?.additionalData.companyId);
      form.setValue("siteId", writtenContract?.additionalData.siteId);
      form.setValue("quoteId", writtenContract?.additionalData.quoteId);
      form.setValue("contactId", writtenContract?.additionalData.contactId);
      form.setValue(
        "contractStartDate",
        writtenContract?.additionalData.contractStartDate?.toString()
      );
      form.setValue(
        "contractEndDate",
        writtenContract?.additionalData.contractEndDate?.toString()
      );
      form.setValue(
        "contractDuration",
        writtenContract?.additionalData.contractDuration
      );
    },
    [writtenContract, form]
  );

  const signedContract = form.watch("signedContract");
  const finalFilename = form.watch("finalFilename");
  const storagePath = form.watch("storagePath");

  const previewProps = useMemo(() => {
    if (storagePath) {
      return {
        bucketName: UPLOAD_CONFIG.BUCKET_NAME,
        filePath: storagePath
      };
    }

    if (!signedContract) {
      return { signedUrl: "" };
    }

    if (!previewRef.current) {
      previewRef.current = URL.createObjectURL(signedContract);
    }

    return { signedUrl: previewRef.current };
  }, [storagePath, signedContract]);

  const onFileUpload = ({ files, form }: FileUploadParams) => {
    if (!files.length) {
      return;
    }

    if (previewRef.current) {
      URL.revokeObjectURL(previewRef.current);
      previewRef.current = null;
    }

    const firstFile = files[0];
    if (firstFile) {
      form.setValue("signedContract", firstFile);
    }
  };

  const handleSubmit = useCallback(async () => {
    if (!writtenContract) {
      return;
    }

    try {
      setIsProcessing(true);

      const { path, fileId } = getFilePathAndId(
        writtenContract.additionalData.companyId,
        finalFilename
      );

      let actualFileId = fileId;

      // Scenario 1: New file upload - signedContract is a File object with path property
      if ("path" in signedContract) {
        const renamedFile = new File([signedContract], path, {
          type: signedContract.type
        });

        await onUpload([renamedFile]);

        await registerFile.mutateAsync({
          id: actualFileId,
          path,
          filename: finalFilename,
          mimeType: signedContract.type,
          size: signedContract.size,
          companyId: writtenContract.additionalData.companyId,
          type: CompanyFileType.WRITTEN_CONTRACT,
          sites: [writtenContract.additionalData.siteId],
          siteMeters: [writtenContract.additionalData.siteMeterId]
        });
      }
      // Scenario 2: Existing file selected - signedContract is a reference to an already uploaded file
      else if (
        "companyFileId" in signedContract &&
        typeof signedContract.companyFileId === "string"
      ) {
        actualFileId = signedContract.companyFileId;
      }

      await createWrittenContract.mutateAsync({
        companyFileId: actualFileId,
        utilityType: writtenContract.additionalData.utilityType,
        companyId: writtenContract.additionalData.companyId,
        siteId: writtenContract.additionalData.siteId,
        quoteId: writtenContract.additionalData.quoteId,
        contactId: writtenContract.additionalData.contactId,
        contractStartDate:
          writtenContract.additionalData.contractStartDate.toString(),
        contractEndDate:
          writtenContract.additionalData.contractEndDate.toString(),
        contractDuration: writtenContract.additionalData.contractDuration
      });

      toast({
        title: "Written contract created",
        description: "Quote has been converted to a signed contract deal",
        variant: "success"
      });

      closeModal();
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while creating the written contract."
          : error.message;
      toast({
        title: "Unable to create written contract",
        description,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [
    onUpload,
    closeModal,
    registerFile,
    writtenContract,
    signedContract,
    finalFilename,
    createWrittenContract
  ]);

  usePreventUnload({
    shouldPreventUnload: isProcessing
  });

  if (!isOpen) {
    return null;
  }

  if (error) {
    return <div>Error fetching contract details</div>;
  }

  if (!writtenContract) {
    return null;
  }

  const {
    companyId,
    providerUdcoreId,
    utilityType,
    quoteId,
    contactId,
    businessName,
    sitePostcode: postcode
  } = writtenContract.additionalData;

  const requirementChecklist = writtenContract.requirementChecklist;

  const fileHandlingProps: FileHandlingProps = {
    companyId,
    providerUdcoreId,
    quoteId,
    contactId,
    businessName,
    postcode,
    utilityType,
    form,
    isProcessing,
    onFileUpload,
    requirementChecklist,
    requiresPortalForWrittenContract:
      writtenContract.additionalData.requiresPortalForWrittenContract ?? true
  };

  const isSubmitDisabled =
    isProcessing || !signedContract || (!storagePath && !finalFilename);

  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="w-[70%] max-w-[1400px]"
        onEscapeKeyDown={closeModal}
      >
        <VisuallyHidden>
          <DrawerTitle>Written Contract</DrawerTitle>
        </VisuallyHidden>
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={closeModal}
          disabled={isProcessing}
        >
          <XIcon className="size-4" />
          <span className="sr-only fixed">Close</span>
        </Button>
        {isLoading ? (
          <WrittenContractSkeleton />
        ) : (
          <FormWrapper
            form={form}
            handleSubmit={handleSubmit}
            className="overflow-y-scroll p-8"
          >
            <div className="flex">
              <div className="w-2/3">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="w-28"
                  onClick={back}
                  disabled={isProcessing}
                >
                  Back
                </Button>
                <div className="my-4 space-y-6">
                  <ContractUploadSection {...fileHandlingProps} />
                  {!!signedContract && (
                    <PreviewPDF
                      {...previewProps}
                      className="max-h-screen max-w-full rounded-md border-2 border-secondary"
                    />
                  )}
                  {isProcessing && (
                    <div className="flex items-center gap-2 text-muted-foreground text-sm">
                      <Loader2Icon className="size-4 animate-spin" />
                      {isUploading
                        ? "Uploading file..."
                        : "Registering file..."}
                    </div>
                  )}
                </div>
              </div>
              <ContractDetails
                contractData={writtenContract}
                signedDate={new Date()}
              />
            </div>
            <Button
              variant="secondary"
              className="my-4 w-full font-medium text-base"
              disabled={isSubmitDisabled}
            >
              Confirm
            </Button>
          </FormWrapper>
        )}
      </DrawerContentWithDirection>
    </Drawer>
  );
}
