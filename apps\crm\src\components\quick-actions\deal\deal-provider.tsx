"use client";

import type { AllSiteDeals } from "@watt/api/src/router/deal";
import { DealCancellationModal } from "@watt/crm/components/site/site-deals/deal-cancellation/deal-cancellation-modal";
import { DealResubmissionModal } from "@watt/crm/components/site/site-deals/deal-resubmisison/deal-resubmission-modal";
import { DealSubmissionModal } from "@watt/crm/components/site/site-deals/deal-submission/deal-submission-modal";
import { DealTimelineModal } from "@watt/crm/components/site/site-deals/deal-timeline/deal-timeline-modal";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";

export type DealModalQueryParams = {
  modal:
    | "submit-deal"
    | "resubmit-deal"
    | "view-cancelled-deal"
    | "view-timeline-deal";
  dealId: string;
  dealData?: string; // JSON stringified deal data for resubmission and cancellation
};

export function DealProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<DealModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  if (!queryParams.dealId) {
    return null;
  }

  const dealData = queryParams.dealData
    ? (JSON.parse(queryParams.dealData) as AllSiteDeals[number])
    : undefined;

  return (
    <>
      <DealSubmissionModal
        openModal={queryParams.modal === "submit-deal"}
        setOpenModal={() => handleModalClose()}
        dealId={queryParams.dealId}
      />
      {dealData && (
        <>
          <DealResubmissionModal
            openModal={queryParams.modal === "resubmit-deal"}
            setOpenModal={() => handleModalClose()}
            dealData={dealData}
          />
          <DealCancellationModal
            openModal={queryParams.modal === "view-cancelled-deal"}
            setOpenModal={() => handleModalClose()}
            dealData={dealData}
          />
          <DealTimelineModal
            openModal={queryParams.modal === "view-timeline-deal"}
            setOpenModal={() => handleModalClose()}
            dealData={dealData}
          />
        </>
      )}
    </>
  );
}
