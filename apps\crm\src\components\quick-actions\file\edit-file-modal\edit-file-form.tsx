"use client";

import { TRPCClientError } from "@trpc/client";
import { log } from "@watt/common/src/utils/axiom-logger";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { Button } from "@watt/crm/components/ui/button";
import { FormWrapper } from "@watt/crm/components/ui/form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useCompanyFilesSearch } from "@watt/crm/hooks/use-company-files-search";
import { usePreventUnload } from "@watt/crm/hooks/use-prevent-unload";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2Icon } from "lucide-react";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { MeterSelector } from "../upload-files-modal/meter-selector";
import { SiteSelector } from "../upload-files-modal/site-selector";
import { UPLOAD_CONFIGS } from "../upload-files-modal/types-and-data";
import { UploadedCompanyFile } from "../uploaded-company-file";
import { EditFileFormSkeleton } from "./edit-file-form-skeleton";
import {
  type EditFileFormProps,
  type QueryParams,
  type RouterParams,
  editFileFormSchema
} from "./types-and-data";

export function EditFileForm({ closeModal }: EditFileFormProps) {
  const { id: companyId } = useParams<RouterParams>();
  const {
    queryParams: { companyFileId }
  } = useQueryParams<QueryParams>();

  const [isProcessing, setIsProcessing] = useState(false);

  const {
    files,
    isLoading: isFilesLoading,
    error: searchError
  } = useCompanyFilesSearch({
    companyId,
    fileIds: [companyFileId ?? ""],
    options: {
      enabled: !!companyId && !!companyFileId,
      staleTime: Number.POSITIVE_INFINITY
    }
  });

  const form = useZodForm({
    schema: editFileFormSchema,
    defaultValues: {
      category: null,
      filename: {
        input: null,
        originalFilename: "",
        acceptedFilename: null
      },
      sites: [],
      meters: []
    }
  });

  const editFile = trpcClient.companyFiles.editFile.useMutation();

  const {
    data: sitesData,
    error: sitesError,
    isLoading: isSitesLoading
  } = trpcClient.site.getAllCompanySites.useQuery(
    {
      companyId
    },
    {
      enabled: !!companyId
    }
  );

  const {
    data: metersData,
    error: metersError,
    isLoading: isMetersLoading
  } = trpcClient.siteMeter.findCompanySiteMetersByCompanyId.useQuery(
    {
      companyId
    },
    {
      enabled: !!companyId
    }
  );

  const category = form.watch("category");
  const sites = form.watch("sites");
  const meters = form.watch("meters");
  const filename = form.watch("filename");

  const metersBySite = useMemo(() => {
    if (!metersData) {
      return {};
    }

    return metersData.reduce<Record<string, typeof metersData>>(
      (acc, meter) => {
        const siteRef = composeSiteRef(meter.companySite.siteRefId);

        if (!siteRef) {
          return acc;
        }

        if (!acc[siteRef]) {
          acc[siteRef] = [];
        }

        acc[siteRef].push(meter);

        return acc;
      },
      {}
    );
  }, [metersData]);

  const existingFile = useMemo(
    () => files.find(file => file.id === companyFileId),
    [companyFileId, files]
  );

  useEffect(() => {
    if (existingFile) {
      form.reset({
        category: existingFile.type,
        filename: {
          input: existingFile.filename,
          originalFilename: existingFile.filename,
          acceptedFilename: existingFile.filename
        },
        sites: existingFile.sites.map(site => site.id),
        meters: existingFile.siteMeters.map(meter => meter.id)
      });
    }
  }, [existingFile, form]);

  const uploadConfig = useMemo(
    () => (category ? UPLOAD_CONFIGS[category] : null),
    [category]
  );

  const sharedProps = useMemo(
    () =>
      uploadConfig
        ? {
            selectedSites: sites,
            setSelectedSites: (sites: string[]) =>
              form.setValue("sites", sites),
            selectedMeters: meters,
            setSelectedMeters: (meters: string[]) =>
              form.setValue("meters", meters),
            sitesData: sitesData ?? [],
            isRequired: !!uploadConfig.minSites,
            disabled: false
          }
        : null,
    [form, sites, meters, sitesData, uploadConfig]
  );

  const file = useMemo(() => {
    if (!existingFile) {
      return null;
    }

    return new File(
      [new Uint8Array(existingFile.size)],
      existingFile.filename,
      {
        type: existingFile.type
      }
    );
  }, [existingFile]);

  const handleFilenameChange = useCallback(
    (value: string | null) =>
      form.setValue("filename", {
        ...filename,
        acceptedFilename: value
      }),
    [form, filename]
  );

  const handleFilenameInputChange = useCallback(
    (value: string) =>
      form.setValue("filename", {
        ...filename,
        input: value
      }),
    [form, filename]
  );

  const handleSubmit = useCallback(async () => {
    setIsProcessing(true);

    try {
      const newFilename =
        filename.acceptedFilename ??
        (filename.input === filename.originalFilename
          ? filename.originalFilename
          : null);

      if (!companyFileId) {
        throw new Error("Missing company file ID");
      }

      if (!newFilename) {
        throw new Error("New filename is invalid");
      }

      await editFile.mutateAsync({
        fileId: companyFileId,
        filename: newFilename,
        sites,
        siteMeters: meters
      });

      toast({
        title: "File edited successfully",
        description: "Your changes have been saved.",
        variant: "success"
      });

      closeModal();
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while editing the file."
          : error.message;
      toast({
        title: `Unable to edit ${filename.acceptedFilename}`,
        description,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  }, [companyFileId, filename, editFile, sites, meters, closeModal]);

  usePreventUnload({
    shouldPreventUnload: isProcessing
  });

  const renderSelectors =
    uploadConfig && typeof uploadConfig.minSites === "number" && sharedProps;

  const isSubmitDisabled =
    isProcessing ||
    (!filename.acceptedFilename &&
      (!filename.input
        ? false
        : filename.input !== filename?.originalFilename));

  if (typeof companyId !== "string") {
    return <div>Invalid company ID</div>;
  }

  if (typeof companyFileId !== "string") {
    return <div>Invalid company file ID</div>;
  }

  if (isFilesLoading || isSitesLoading || isMetersLoading) {
    return <EditFileFormSkeleton />;
  }

  if (sitesError) {
    return <div>Error fetching company sites</div>;
  }

  if (metersError) {
    return <div>Error fetching company site meters</div>;
  }

  if (searchError) {
    return <div>Error loading file data</div>;
  }

  if (!existingFile || !file) {
    return <div>Company file not found</div>;
  }

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleSubmit}
      className="mt-2 flex w-full flex-grow flex-col space-y-8"
    >
      {renderSelectors && (
        <>
          {uploadConfig.minSites === 0 && (
            <SiteSelector
              {...sharedProps}
              metersData={metersData ?? []}
              error={form.formState.errors.sites?.message}
            />
          )}
          <MeterSelector
            {...sharedProps}
            metersBySite={metersBySite}
            error={form.formState.errors.meters?.message}
          />
        </>
      )}

      <UploadedCompanyFile
        key={existingFile.id}
        companyId={companyId}
        file={file}
        isFilenameEditable={false}
        filename={filename.acceptedFilename}
        existingFilename={filename.originalFilename}
        onFilenameChange={handleFilenameChange}
        onFilenameInputChange={handleFilenameInputChange}
        disabled={isProcessing}
      />

      <Button
        type="submit"
        variant="secondary"
        className="w-full"
        disabled={isSubmitDisabled}
      >
        {isProcessing && <Loader2Icon className="mr-2 size-4 animate-spin" />}
        Confirm
      </Button>
    </FormWrapper>
  );
}
