"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { EditFileForm } from "./edit-file-form";
import type { EditFileModalProps } from "./types-and-data";

export function EditFileModal({ isOpen, closeModal }: EditFileModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        className="max-h-[90vh] overflow-y-auto sm:max-w-[60ch]"
      >
        <DialogHeader>
          <DialogTitle className="text-xl">Edit File Properties</DialogTitle>
          <DialogDescription className="italic">
            Update meter and site associations for your uploaded file
          </DialogDescription>
        </DialogHeader>
        <EditFileForm closeModal={closeModal} />
      </DialogContent>
    </Dialog>
  );
}
