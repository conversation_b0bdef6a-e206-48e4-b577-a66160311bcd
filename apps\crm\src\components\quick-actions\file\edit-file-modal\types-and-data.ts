"use client";

import { CompanyFileType } from "@prisma/client";
import { z } from "zod";
import type { FileModalQueryParams } from "../file-provider";
import { UPLOAD_CONFIGS } from "../upload-files-modal/types-and-data";

export type EditFileModalProps = {
  isOpen: boolean;
  closeModal: () => void;
};

export type EditFileFormProps = {
  closeModal: () => void;
};

export type RouterParams = {
  id: string;
};

export type QueryParams = {
  companyFileId?: string;
} & FileModalQueryParams;

export const editFileFormSchema = z
  .object({
    category: z.nativeEnum(CompanyFileType).nullable(),
    filename: z.object({
      input: z.string().nullable(),
      originalFilename: z.string(),
      acceptedFilename: z.string().nullable()
    }),
    sites: z.array(z.string()),
    meters: z.array(z.string())
  })
  .superRefine((data, ctx) => {
    if (!data.category) {
      return;
    }

    const config = UPLOAD_CONFIGS[data.category];

    if (typeof config.minSites === "number" && config.minSites > 0) {
      if (!data.sites.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one site",
          path: ["sites"]
        });
      }

      if (!data.meters.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one meter",
          path: ["meters"]
        });
      }
    }
  });

export type EditFileFormData = z.infer<typeof editFileFormSchema>;
