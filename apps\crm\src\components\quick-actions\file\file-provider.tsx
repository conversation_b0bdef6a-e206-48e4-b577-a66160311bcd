"use client";

import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { EditFileModal } from "./edit-file-modal";
import { UploadFilesModal } from "./upload-files-modal";

export const FileModalTypes = {
  uploadFiles: "upload-files",
  editFile: "edit-file"
} as const;

export type FileModalType =
  (typeof FileModalTypes)[keyof typeof FileModalTypes];

export type FileModalQueryParams = {
  modal: FileModalType;
};

export function FileProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<FileModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  return (
    <>
      <UploadFilesModal
        isOpen={queryParams.modal === FileModalTypes.uploadFiles}
        closeModal={handleModalClose}
      />
      <EditFileModal
        isOpen={queryParams.modal === FileModalTypes.editFile}
        closeModal={handleModalClose}
      />
    </>
  );
}
