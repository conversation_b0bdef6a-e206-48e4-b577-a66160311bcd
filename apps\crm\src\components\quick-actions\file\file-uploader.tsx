"use client";

import { TWO_MB } from "@watt/common/src/constants/file-sizes";
import {
  AUDIO_MIME_TYPES,
  FRIENDLY_NAMES_BY_MIME
} from "@watt/common/src/constants/mime-types";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatBytes } from "@watt/common/src/utils/format-bytes";
import { Button } from "@watt/crm/components/ui/button";
import { Progress } from "@watt/crm/components/ui/progress";
import { ScrollArea } from "@watt/crm/components/ui/scroll-area";
import { useControllableState } from "@watt/crm/hooks/use-controllable-state";
import { Upload, X } from "lucide-react";
import { AudioLinesIcon, FileTextIcon } from "lucide-react";
import mime from "mime/lite";
import Image from "next/image";
import { useCallback, useEffect } from "react";
import Dropzone, {
  type DropzoneProps,
  type FileRejection
} from "react-dropzone";
import { toast } from "sonner";
/**
 * Returns the appropriate icon component based on the MIME type
 * @param mimeType - The MIME type to check
 * @returns LucideIcon component
 */
export function getMimeTypeIcon(mimeType: string) {
  if (
    Object.values(AUDIO_MIME_TYPES).includes(
      mimeType as (typeof AUDIO_MIME_TYPES)[keyof typeof AUDIO_MIME_TYPES]
    )
  ) {
    return AudioLinesIcon;
  }
  return FileTextIcon;
}

interface FileUploaderProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Value of the uploader.
   * @type File[]
   * @default undefined
   * @example value={files}
   */
  value?: File[];

  /**
   * Function to be called when the value changes.
   * @type (files: File[]) => void
   * @default undefined
   * @example onValueChange={(files) => setFiles(files)}
   */
  onValueChange?: (files: File[]) => void;

  /**
   * Function to be called when files are uploaded.
   * @type (files: File[]) => Promise<void>
   * @default undefined
   * @example onUpload={(files) => uploadFiles(files)}
   */
  onUpload?: (files: File[]) => Promise<void>;

  /**
   * Progress of the uploaded files.
   * @type Record<string, number> | undefined
   * @default undefined
   * @example progresses={{ "file1.png": 50 }}
   */
  progresses?: Record<string, number>;

  /**
   * Accepted file types for the uploader.
   * @type { [key: string]: string[]}
   * @default
   * ```ts
   * { "image/*": [] }
   * ```
   * @example accept={["image/png", "image/jpeg"]}
   */
  accept?: DropzoneProps["accept"];

  /**
   * Maximum file size for the uploader.
   * @type number | undefined
   * @default TWO_MB
   * @example maxSize={TWO_MB}
   */
  maxSize?: DropzoneProps["maxSize"];

  /**
   * Maximum number of files for the uploader.
   * @type number | undefined
   * @default 1
   * @example maxFileCount={4}
   */
  maxFileCount?: DropzoneProps["maxFiles"];

  /**
   * Whether the uploader should accept multiple files.
   * @type boolean
   * @default false
   * @example multiple
   */
  multiple?: boolean;

  /**
   * Whether the uploader is disabled.
   * @type boolean
   * @default false
   * @example disabled
   */
  disabled?: boolean;

  /**
   * Custom description for the uploader.
   * @type string | undefined
   * @default undefined
   * @example description="Upload a PDF or Word document"
   */
  description?: string;

  /**
   * Custom title for the uploader.
   * @type string | undefined
   * @default undefined
   * @example title="Upload a PDF or Word document"
   */
  title?: string;

  /**
   * Whether to hide the uploaded files.
   * @type boolean
   * @default false
   */
  hideUploadedFiles?: boolean;
}

/**
 * Map file types to human-friendly strings.
 * @param accept - The accepted file types.
 * @returns A string of supported file types.
 */
function mapFileTypes(accept: DropzoneProps["accept"]) {
  if (!accept) {
    return "";
  }

  return Object.keys(accept)
    .map(mimeType => {
      if (FRIENDLY_NAMES_BY_MIME[mimeType]) {
        return FRIENDLY_NAMES_BY_MIME[mimeType];
      }

      const fileExtension = mime.getExtension(mimeType);

      if (fileExtension) {
        return fileExtension.toUpperCase();
      }

      return mimeType;
    })
    .join(", ");
}

export function FileUploader(props: FileUploaderProps) {
  const {
    value: valueProp,
    onValueChange,
    onUpload,
    progresses,
    accept = { "image/*": [] },
    maxSize = TWO_MB,
    maxFileCount = 1,
    multiple = false,
    disabled = false,
    hideUploadedFiles = false,
    description,
    title,
    className,
    ...dropzoneProps
  } = props;

  const [files, setFiles] = useControllableState({
    prop: valueProp,
    onChange: onValueChange
  });

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      if (!multiple && maxFileCount === 1 && acceptedFiles.length > 1) {
        toast.error("Cannot upload more than 1 file at a time");
        return;
      }

      if ((files?.length ?? 0) + acceptedFiles.length > maxFileCount) {
        toast.error(`Cannot upload more than ${maxFileCount} files`);
        return;
      }

      const newFiles = acceptedFiles.map(file =>
        Object.assign(file, {
          preview: URL.createObjectURL(file)
        })
      );

      const updatedFiles = files ? [...files, ...newFiles] : newFiles;
      setFiles(updatedFiles);

      if (rejectedFiles.length > 0) {
        for (const { file } of rejectedFiles) {
          toast.error(`File ${file.name} was rejected`);
        }
      }

      // If onUpload is provided, we can call it here for direct upload
      if (
        onUpload &&
        updatedFiles.length > 0 &&
        updatedFiles.length <= maxFileCount
      ) {
        const target =
          updatedFiles.length > 1
            ? `${updatedFiles.length} files`
            : `${updatedFiles.length} file`;

        toast.promise(onUpload(updatedFiles), {
          loading: `Uploading ${target}...`,
          success: () => {
            // After a successful upload, reset files if desired
            // setFiles([]);
            return `${target} uploaded`;
          },
          error: `Failed to upload ${target}`
        });
      }
    },
    [files, maxFileCount, multiple, onUpload, setFiles]
  );

  function onRemove(name: string) {
    if (!files) {
      return;
    }

    const newFiles = files.filter(file => file.name !== name);

    setFiles(newFiles);
    onValueChange?.(newFiles);
  }

  useEffect(() => {
    return () => {
      if (!files) {
        return;
      }

      for (const file of files) {
        if (isFileWithPreview(file)) {
          URL.revokeObjectURL(file.preview);
        }
      }
    };
  }, [files]);

  const isDisabled = disabled || (files?.length ?? 0) >= maxFileCount;

  return (
    <div className="relative space-y-6 overflow-hidden">
      <div className="space-y-4">
        <Dropzone
          onDrop={onDrop}
          accept={accept}
          maxSize={maxSize}
          maxFiles={maxFileCount}
          multiple={maxFileCount > 1 || multiple}
          disabled={isDisabled}
          minSize={1}
        >
          {({ getRootProps, getInputProps, isDragActive }) => (
            <div
              {...getRootProps()}
              className={cn(
                "group relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-muted-foreground/25 border-dashed px-5 py-2.5 text-center transition hover:bg-muted/25",
                "ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                isDragActive && "border-muted-foreground/50",
                isDisabled && "pointer-events-none opacity-60",
                className
              )}
              {...dropzoneProps}
            >
              <input {...getInputProps()} />
              {isDragActive ? (
                <div className="flex flex-col items-center justify-center gap-4 sm:px-5">
                  <div className="rounded-full border border-dashed p-3">
                    <Upload
                      className="size-7 text-muted-foreground"
                      aria-hidden="true"
                    />
                  </div>
                  <p className="font-medium text-muted-foreground">
                    Drop the files here
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center gap-4 sm:px-5">
                  <div className="rounded-full border border-dashed p-3">
                    <Upload
                      className="size-7 text-muted-foreground"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="flex flex-col gap-px">
                    <p className="font-medium text-muted-foreground">
                      {title ??
                        `Drag and drop your file${
                          maxFileCount > 1 ? "s" : ""
                        } here or click to browse`}
                    </p>
                    <p className="text-muted-foreground/70 text-sm">
                      {description ??
                        (maxFileCount === 1 ? (
                          <>File size can not exceed {formatBytes(maxSize)}</>
                        ) : (
                          <>
                            Each file size can not exceed {formatBytes(maxSize)}
                            {maxFileCount !== Number.POSITIVE_INFINITY && (
                              <> (up to {maxFileCount} files)</>
                            )}
                          </>
                        ))}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </Dropzone>
        <p className="text-muted-foreground/70 text-sm">
          Supported formats: {mapFileTypes(accept)}
        </p>
      </div>
      {!hideUploadedFiles && files?.length ? (
        <ScrollArea className="h-fit w-full pr-4">
          <div className="flex max-h-48 flex-col gap-4">
            {files?.map(file => (
              <FileCard
                key={file.name}
                file={file}
                onRemove={() => onRemove(file.name)}
                progress={progresses?.[file.name]}
              />
            ))}
          </div>
        </ScrollArea>
      ) : null}
    </div>
  );
}

interface FileCardProps {
  file: File;
  onRemove: () => void;
  progress?: number;
  className?: string;
}

export function FileCard({
  file,
  progress,
  onRemove,
  className
}: FileCardProps) {
  return (
    <div className={cn("relative flex items-center gap-2.5", className)}>
      <div className="flex flex-1 gap-2.5">
        {isFileWithPreview(file) ? <FilePreview file={file} /> : null}
        <div className="flex w-full flex-col gap-2">
          <div className="flex flex-col gap-px">
            <p className="line-clamp-1 font-medium text-foreground/80 text-sm">
              {file.name}
            </p>
            <p className="text-muted-foreground text-xs">
              {formatBytes(file.size)}
            </p>
          </div>
          {progress ? <Progress value={progress} /> : null}
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button
          type="button"
          variant="outline"
          size="icon"
          className="size-7"
          onClick={onRemove}
        >
          <X className="size-4" aria-hidden="true" />
          <span className="sr-only">Remove file</span>
        </Button>
      </div>
    </div>
  );
}

function isFileWithPreview(file: File): file is File & { preview: string } {
  return (
    "preview" in file &&
    typeof (file as unknown as File & { preview: string }).preview === "string"
  );
}

interface FilePreviewProps {
  file: File & { preview: string };
}

function FilePreview({ file }: FilePreviewProps) {
  if (file.type.startsWith("image/")) {
    return (
      <Image
        src={file.preview}
        alt={file.name}
        width={48}
        height={48}
        loading="lazy"
        className="aspect-square shrink-0 rounded-md object-cover"
      />
    );
  }

  const Icon = getMimeTypeIcon(file.type);

  return <Icon className="size-10 text-muted-foreground" aria-hidden="true" />;
}
