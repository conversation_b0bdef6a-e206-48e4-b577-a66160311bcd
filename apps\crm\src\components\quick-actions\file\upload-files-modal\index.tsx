"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import type { UploadFilesModalProps } from "./types-and-data";
import { UploadFilesForm } from "./upload-files-form";

export function UploadFilesModal({
  isOpen,
  closeModal
}: UploadFilesModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        className="max-h-[90vh] overflow-y-auto sm:max-w-[60ch]"
      >
        <DialogHeader>
          <DialogTitle className="text-xl">Upload Files</DialogTitle>
          <DialogDescription className="italic">
            Choose a file to upload and optionally associate it with site(s) and
            meter(s)
          </DialogDescription>
        </DialogHeader>
        <UploadFilesForm closeModal={closeModal} />
      </DialogContent>
    </Dialog>
  );
}
