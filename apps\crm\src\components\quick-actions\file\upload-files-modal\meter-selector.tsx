"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { UtilityTypeIcon } from "@watt/crm/app/account/companies/[id]/components/site/profile";
import { Badge } from "@watt/crm/components/ui/badge";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from "@watt/crm/components/ui/command";
import { Label } from "@watt/crm/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import { CheckIcon, ChevronsUpDownIcon, XIcon } from "lucide-react";
import { useCallback, useMemo } from "react";
import type {
  MeterBadgeProps,
  MeterCommandItemProps,
  MeterGroupProps,
  MeterSelectorProps
} from "./types-and-data";

export function MeterSelector({
  selectedMeters,
  setSelectedMeters,
  selectedSites,
  setSelectedSites,
  metersBySite,
  sitesData,
  isRequired,
  disabled,
  error
}: MeterSelectorProps) {
  const handleUpdateMeterSelection = useCallback(
    (meterId: string) => {
      const isSelected = selectedMeters.includes(meterId);

      const newValue = isSelected
        ? selectedMeters.filter(id => id !== meterId)
        : [...selectedMeters, meterId];

      setSelectedMeters(newValue);

      const currentSites = selectedSites;

      const meter = Object.values(metersBySite)
        .flat()
        .find(m => m.id === meterId);

      const siteId = meter?.companySite.id;

      if (!siteId) {
        return;
      }

      if (!isSelected) {
        if (!currentSites.includes(siteId)) {
          setSelectedSites([...currentSites, siteId]);
        }
      } else {
        const remainingMetersForSite = newValue.filter(
          id =>
            Object.values(metersBySite)
              .flat()
              .find(m => m.id === id)?.companySite.id === siteId
        );

        if (remainingMetersForSite.length === 0) {
          setSelectedSites(currentSites.filter(id => id !== siteId));
        }
      }
    },
    [
      selectedMeters,
      setSelectedMeters,
      selectedSites,
      setSelectedSites,
      metersBySite
    ]
  );

  return (
    <div className="flex flex-col space-y-2">
      <Label>Meter Association {isRequired && "*"}</Label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            className={cn(
              "justify-between",
              !selectedMeters.length && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <span className="max-w-[50ch] overflow-hidden text-ellipsis whitespace-nowrap">
              {selectedMeters.length > 0 ? (
                selectedMeters.map(meterId => (
                  <MeterBadge
                    key={meterId}
                    meterId={meterId}
                    metersBySite={metersBySite}
                    onRemove={handleUpdateMeterSelection}
                  />
                ))
              ) : (
                <span className="font-normal italic">
                  Select applicable meters
                </span>
              )}
            </span>
            <ChevronsUpDownIcon className="ml-2 size-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="max-h-80 w-[var(--radix-popover-trigger-width)] overflow-y-auto p-0">
          <Command>
            <CommandInput placeholder="Search meters..." />
            <CommandEmpty>No meters found.</CommandEmpty>
            {Object.entries(metersBySite).map(([siteRef, siteMeters]) => (
              <MeterGroup
                key={siteRef}
                siteRef={siteRef}
                siteMeters={siteMeters}
                sitesData={sitesData}
                selectedMeters={selectedMeters}
                onMeterSelect={handleUpdateMeterSelection}
              />
            ))}
          </Command>
        </PopoverContent>
      </Popover>
      {error && (
        <span className="font-medium text-destructive text-sm">{error}</span>
      )}
    </div>
  );
}

function MeterBadge({ meterId, metersBySite, onRemove }: MeterBadgeProps) {
  const meter = useMemo(
    () =>
      Object.values(metersBySite)
        .flat()
        .find(m => m.id === meterId),
    [metersBySite, meterId]
  );

  if (!meter) {
    return null;
  }

  const meterNumber =
    meter.electricSiteMeter?.mpanValue ?? meter.gasSiteMeter?.mprnValue;

  return (
    <Badge className="mr-1 bg-muted" variant="outline">
      <div className="flex flex-row items-center gap-1">
        <UtilityTypeIcon utilityType={meter.utilityType} className="size-3.5" />
        {meterNumber}
        <button type="button" onClick={() => onRemove(meterId)}>
          <XIcon className="size-3.5" />
        </button>
      </div>
    </Badge>
  );
}

function MeterCommandItem({
  meter,
  isSelected,
  onSelect
}: MeterCommandItemProps) {
  const meterNumber =
    meter.electricSiteMeter?.mpanValue ?? meter.gasSiteMeter?.mprnValue;

  return (
    <CommandItem key={meter.id} onSelect={() => onSelect(meter.id)}>
      <div
        className={cn(
          "mr-2 flex size-4 items-center justify-center rounded-sm border border-primary",
          isSelected
            ? "bg-primary text-primary-foreground"
            : "opacity-50 [&_svg]:invisible"
        )}
      >
        <CheckIcon className="size-4" />
      </div>
      <div className="flex flex-row items-center gap-1">
        <UtilityTypeIcon utilityType={meter.utilityType} className="size-5" />
        {meterNumber}
      </div>
    </CommandItem>
  );
}

function MeterGroup({
  siteRef,
  siteMeters,
  sitesData,
  selectedMeters,
  onMeterSelect
}: MeterGroupProps) {
  const siteData = useMemo(
    () =>
      sitesData?.find(
        site => site.siteRefId === siteMeters[0]?.companySite.siteRefId
      ),
    [sitesData, siteMeters]
  );

  const sortedMeters = useMemo(
    () =>
      [...siteMeters].sort((a, b) => {
        if (a.utilityType === b.utilityType) {
          return 0;
        }

        return a.utilityType === "ELECTRICITY" ? -1 : 1;
      }),
    [siteMeters]
  );

  return (
    <CommandGroup
      key={siteRef}
      heading={`${siteRef} ${siteData?.entityAddress?.postcode}`}
    >
      {sortedMeters.map(meter => (
        <MeterCommandItem
          key={meter.id}
          meter={meter}
          isSelected={selectedMeters.includes(meter.id)}
          onSelect={onMeterSelect}
        />
      ))}
    </CommandGroup>
  );
}
