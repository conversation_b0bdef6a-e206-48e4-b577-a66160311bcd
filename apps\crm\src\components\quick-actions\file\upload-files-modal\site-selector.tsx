"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { Badge } from "@watt/crm/components/ui/badge";
import { <PERSON><PERSON> } from "@watt/crm/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem
} from "@watt/crm/components/ui/command";
import { Label } from "@watt/crm/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/crm/components/ui/popover";
import { CheckIcon, ChevronsUpDownIcon, XIcon } from "lucide-react";
import { useCallback, useMemo } from "react";
import type {
  SiteBadgeProps,
  SiteCommandItemProps,
  SiteSelectorProps
} from "./types-and-data";

export function SiteSelector({
  selectedSites,
  setSelectedSites,
  selectedMeters,
  setSelectedMeters,
  sitesData,
  metersData,
  isRequired,
  disabled,
  error
}: SiteSelectorProps) {
  const handleUpdateSiteSelection = useCallback(
    (siteId: string) => {
      const newValue = selectedSites.includes(siteId)
        ? selectedSites.filter(id => id !== siteId)
        : [...selectedSites, siteId];

      setSelectedSites(newValue);

      if (!newValue.includes(siteId)) {
        const metersToRemove =
          metersData?.filter(m => m.companySite.id === siteId).map(m => m.id) ??
          [];

        const currentMeters = selectedMeters;

        const updatedMeters = currentMeters.filter(
          meterId => !metersToRemove.includes(meterId)
        );

        setSelectedMeters(updatedMeters);
      }
    },
    [
      selectedMeters,
      selectedSites,
      setSelectedMeters,
      setSelectedSites,
      metersData
    ]
  );

  return (
    <div className="flex flex-col space-y-2">
      <Label>Site Association {isRequired && "*"}</Label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            className={cn(
              "justify-between",
              !selectedSites.length && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <span className="max-w-[50ch] overflow-hidden text-ellipsis whitespace-nowrap">
              {selectedSites.length > 0 ? (
                selectedSites.map(siteId => (
                  <SiteBadge
                    key={siteId}
                    siteId={siteId}
                    sitesData={sitesData}
                    onRemove={handleUpdateSiteSelection}
                  />
                ))
              ) : (
                <span className="font-normal italic">
                  Select applicable sites
                </span>
              )}
            </span>
            <ChevronsUpDownIcon className="ml-2 size-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="max-h-80 w-[var(--radix-popover-trigger-width)] overflow-y-auto p-0">
          <Command>
            <CommandInput placeholder="Search sites..." />
            <CommandEmpty>No sites found.</CommandEmpty>
            <CommandGroup>
              {sitesData?.map(site => (
                <SiteCommandItem
                  key={site.id}
                  site={site}
                  isSelected={selectedSites.includes(site.id)}
                  onSelect={handleUpdateSiteSelection}
                />
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
      {error && (
        <span className="font-medium text-destructive text-sm">{error}</span>
      )}
    </div>
  );
}

function SiteBadge({ siteId, sitesData, onRemove }: SiteBadgeProps) {
  const siteData = useMemo(
    () => sitesData?.find(s => s.id === siteId),
    [sitesData, siteId]
  );

  return (
    <Badge className="mr-1 bg-muted" variant="outline">
      <div className="flex flex-row items-center gap-1">
        {`${composeSiteRef(siteData?.siteRefId || 0)} ${
          siteData?.entityAddress?.postcode
        }`}
        <button type="button" onClick={() => onRemove(siteId)}>
          <XIcon className="size-3.5" />
        </button>
      </div>
    </Badge>
  );
}

function SiteCommandItem({ site, isSelected, onSelect }: SiteCommandItemProps) {
  return (
    <CommandItem key={site.id} onSelect={() => onSelect(site.id)}>
      <div
        className={cn(
          "mr-2 flex size-4 items-center justify-center rounded-sm border border-primary",
          isSelected
            ? "bg-primary text-primary-foreground"
            : "opacity-50 [&_svg]:invisible"
        )}
      >
        <CheckIcon className="size-4" />
      </div>
      {`${composeSiteRef(site.siteRefId)} ${site.entityAddress?.postcode}`}
    </CommandItem>
  );
}
