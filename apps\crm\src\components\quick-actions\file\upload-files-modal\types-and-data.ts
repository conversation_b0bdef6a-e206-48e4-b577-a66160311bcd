"use client";

import { CompanyFileType } from "@prisma/client";
import type {
  Company_Sites,
  FindCompanySiteMetersByCompanyId
} from "@watt/api/src/router";
import {
  ALL_RECOGNIZED_MIME_TYPES,
  AUDIO_MIME_TYPES,
  DOCUMENT_MIME_TYPES
} from "@watt/common/src/constants/mime-types";
import { z } from "zod";

export type UploadFilesModalProps = {
  isOpen: boolean;
  closeModal: () => void;
};

export type UploadFilesFormProps = {
  closeModal: () => void;
};

export type RouterParams = {
  id: string;
};

export type UploadConfig = {
  label: string;
  mimeTypes: string[];
  maxFileCount: number;
  minSites?: number;
};

export type UploadConfigMap = Record<CompanyFileType, UploadConfig>;

export const UPLOAD_CONFIGS: UploadConfigMap = {
  [CompanyFileType.WRITTEN_LOA]: {
    label: "Written LOA",
    mimeTypes: [DOCUMENT_MIME_TYPES.PDF],
    maxFileCount: 1
  },
  [CompanyFileType.VERBAL_LOA]: {
    label: "Verbal LOA",
    mimeTypes: Object.values(AUDIO_MIME_TYPES),
    maxFileCount: 1
  },
  [CompanyFileType.WRITTEN_CONTRACT]: {
    label: "Written Contract",
    mimeTypes: [DOCUMENT_MIME_TYPES.PDF],
    maxFileCount: 1,
    minSites: 1
  },
  [CompanyFileType.VERBAL_CONTRACT]: {
    label: "Verbal Contract",
    mimeTypes: Object.values(AUDIO_MIME_TYPES),
    maxFileCount: 5,
    minSites: 1
  },
  [CompanyFileType.OTHER]: {
    label: "Other",
    mimeTypes: Object.values(ALL_RECOGNIZED_MIME_TYPES),
    maxFileCount: 5,
    minSites: 0
  }
} as const;

export const uploadFilesFormSchema = z
  .object({
    category: z.nativeEnum(CompanyFileType),
    files: z
      .array(z.instanceof(File))
      .min(1, { message: "Please select at least one file" }),
    filenames: z.array(
      z.object({
        originalFilename: z.string(),
        acceptedFilename: z.string().nullable(),
        isUploadComplete: z.boolean().nullable(),
        error: z.string().optional()
      })
    ),
    sites: z.array(z.string()),
    meters: z.array(z.string())
  })
  .superRefine((data, ctx) => {
    const config = UPLOAD_CONFIGS[data.category];

    if (typeof config.minSites === "number" && config.minSites > 0) {
      if (!data.sites.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one site",
          path: ["sites"]
        });
      }

      if (!data.meters.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one meter",
          path: ["meters"]
        });
      }
    }
  });

export type UploadFilesFormData = z.infer<typeof uploadFilesFormSchema>;

export type SharedSelectorProps = {
  selectedSites: string[];
  setSelectedSites: (sites: string[]) => void;
  selectedMeters: string[];
  setSelectedMeters: (meters: string[]) => void;
  sitesData: Company_Sites;
  error: string | undefined;
  isRequired: boolean;
  disabled: boolean;
};

export type SiteSelectorProps = SharedSelectorProps & {
  metersData: FindCompanySiteMetersByCompanyId;
};

export type MeterSelectorProps = SharedSelectorProps & {
  metersBySite: Record<string, FindCompanySiteMetersByCompanyId>;
};

export type MeterGroupProps = {
  siteRef: string;
  siteMeters: NonNullable<MeterSelectorProps["metersBySite"]>[string];
  sitesData: MeterSelectorProps["sitesData"];
  selectedMeters: string[];
  onMeterSelect: (meterId: string) => void;
};

export type MeterBadgeProps = {
  meterId: string;
  metersBySite: MeterSelectorProps["metersBySite"];
  onRemove: (meterId: string) => void;
};

export type SiteBadgeProps = {
  siteId: string;
  sitesData: SiteSelectorProps["sitesData"];
  onRemove: (siteId: string) => void;
};

export type MeterCommandItemProps = {
  meter: NonNullable<MeterSelectorProps["metersBySite"]>[string][number];
  isSelected: boolean;
  onSelect: (meterId: string) => void;
};

export type SiteCommandItemProps = {
  site: NonNullable<SiteSelectorProps["sitesData"]>[number];
  isSelected: boolean;
  onSelect: (siteId: string) => void;
};
