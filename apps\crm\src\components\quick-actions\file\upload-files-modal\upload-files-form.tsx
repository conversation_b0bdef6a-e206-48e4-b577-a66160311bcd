"use client";

import { CompanyFileType } from "@prisma/client";
import { TRPCClientError } from "@trpc/client";
import { FIFTEEN_MB } from "@watt/common/src/constants/file-sizes";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { log } from "@watt/common/src/utils/axiom-logger";
import { composeSiteRef } from "@watt/common/src/utils/site-ref";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { toast } from "@watt/crm/components/ui/use-toast";
import { usePreventUnload } from "@watt/crm/hooks/use-prevent-unload";
import { useUploadFile } from "@watt/crm/hooks/use-upload-file";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { getFilePathAndId } from "@watt/crm/utils/generate-file-path-by-company-id";
import { Loader2Icon } from "lucide-react";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { FileUploader } from "../file-uploader";
import { UploadedCompanyFile } from "../uploaded-company-file";
import { MeterSelector } from "./meter-selector";
import { SiteSelector } from "./site-selector";
import {
  type RouterParams,
  UPLOAD_CONFIGS,
  type UploadFilesFormProps,
  uploadFilesFormSchema
} from "./types-and-data";

export function UploadFilesForm({ closeModal }: UploadFilesFormProps) {
  const { id: companyId } = useParams<RouterParams>();

  const [isProcessing, setIsProcessing] = useState(false);

  const { onUpload } = useUploadFile({
    bucketName: STORAGE_BUCKETS.COMPANY_FILES
  });

  const form = useZodForm({
    schema: uploadFilesFormSchema,
    defaultValues: {
      category: CompanyFileType.WRITTEN_LOA,
      files: [],
      filenames: [],
      sites: [],
      meters: []
    }
  });

  if (typeof companyId !== "string") {
    return null;
  }

  const registerFile = trpcClient.companyFiles.registerFile.useMutation();

  const { data: sitesData, error: sitesError } =
    trpcClient.site.getAllCompanySites.useQuery(
      {
        companyId
      },
      {
        enabled: !!companyId
      }
    );

  if (sitesError) {
    return <div>Error fetching company sites</div>;
  }

  const { data: metersData, error: metersError } =
    trpcClient.siteMeter.findCompanySiteMetersByCompanyId.useQuery(
      {
        companyId
      },
      {
        enabled: !!companyId
      }
    );

  if (metersError) {
    return <div>Error fetching company site meters</div>;
  }

  const files = form.watch("files");
  const filenames = form.watch("filenames");
  const category = form.watch("category");
  const sites = form.watch("sites");
  const meters = form.watch("meters");

  const metersBySite = useMemo(() => {
    if (!metersData) {
      return {};
    }

    return metersData.reduce<Record<string, typeof metersData>>(
      (acc, meter) => {
        const siteRef = composeSiteRef(meter.companySite.siteRefId);

        if (!siteRef) {
          return acc;
        }

        if (!acc[siteRef]) {
          acc[siteRef] = [];
        }

        acc[siteRef].push(meter);

        return acc;
      },
      {}
    );
  }, [metersData]);

  function handleFileChange(files: File[]) {
    const updatedFilenames = files.map(file => ({
      originalFilename: file.name,
      acceptedFilename: null,
      isUploadComplete: null
    }));

    form.setValue("filenames", updatedFilenames);
    form.setValue("files", files);

    if (files.length > 0) {
      form.clearErrors("files");
    }
  }

  const hasDuplicateFilenames = useMemo(() => {
    const acceptedNames = filenames
      .map(f => f.acceptedFilename)
      .filter((name): name is string => name !== null);

    const uniqueNames = new Set(acceptedNames);

    return uniqueNames.size !== acceptedNames.length;
  }, [filenames]);

  const isSubmitDisabled = useMemo(
    () =>
      isProcessing ||
      filenames.some(f => !f.acceptedFilename) ||
      hasDuplicateFilenames,
    [isProcessing, filenames, hasDuplicateFilenames]
  );

  useEffect(() => {
    if (hasDuplicateFilenames) {
      form.setError("files", {
        type: "custom",
        message: "Each file must have a unique name"
      });
    } else {
      form.clearErrors("files");
    }
  }, [hasDuplicateFilenames, form]);

  function handleFilenameChange(
    originalFilename: string,
    acceptedFilename: string | null
  ) {
    const currentFilenames = form.getValues("filenames") ?? [];

    const updatedFilenames = currentFilenames.map(f =>
      f.originalFilename === originalFilename ? { ...f, acceptedFilename } : f
    );

    form.setValue("filenames", updatedFilenames);
  }

  function handleFileRemove(filename: string) {
    const currentFilenames = form.getValues("filenames") ?? [];
    const currentFiles = form.getValues("files") ?? [];

    const updatedFilenames = currentFilenames.filter(
      f => f.originalFilename !== filename
    );

    const updatedFiles = currentFiles.filter(f => f.name !== filename);

    form.setValue("filenames", updatedFilenames);
    form.setValue("files", updatedFiles);
  }

  const handleSubmit = useCallback(async () => {
    setIsProcessing(true);

    let hasError = false;
    let lastSuccessfulIndex = -1;

    for (let i = 0; i < filenames.length; i++) {
      try {
        if (hasError) {
          break;
        }

        form.setValue(`filenames.${i}.isUploadComplete`, false);

        const uploadedFile = files[i];
        const filenameMeta = filenames[i];
        if (!filenameMeta) {
          throw new Error(`No filename metadata for file at index ${i}`);
        }

        const { acceptedFilename: filename } = filenameMeta;

        if (!filename) {
          throw new Error("No accepted filename provided");
        }

        if (!uploadedFile) {
          throw new Error(`No file found at index ${i}`);
        }

        const { path, fileId } = getFilePathAndId(companyId, filename);

        const renamedFile = new File([uploadedFile], path, {
          type: uploadedFile.type
        });

        await onUpload([renamedFile]);

        await registerFile.mutateAsync({
          id: fileId,
          path,
          filename,
          mimeType: uploadedFile.type,
          size: uploadedFile.size,
          companyId,
          type: category,
          sites,
          siteMeters: meters
        });

        form.setValue(`filenames.${i}.isUploadComplete`, true);
        lastSuccessfulIndex = i;
      } catch (e) {
        hasError = true;
        const error = e as Error;
        log.error(error.message);
        const description =
          error instanceof TRPCClientError && !!error.data.zodError
            ? "Error occurred while uploading the file"
            : error.message;
        const errorFilename = filenames[i]?.acceptedFilename;
        toast({
          title: `Unable to upload ${errorFilename || "file"}`,
          description,
          variant: "destructive"
        });
      }
    }

    if (hasError) {
      if (lastSuccessfulIndex >= 0) {
        const updatedFiles = files.slice(lastSuccessfulIndex + 1);
        const updatedFilenames = filenames
          .slice(lastSuccessfulIndex + 1)
          .map(f => ({ ...f, isUploadComplete: null }));

        form.setValue("files", updatedFiles);
        form.setValue("filenames", updatedFilenames);
      }

      setIsProcessing(false);

      return;
    }

    toast({
      title: `File${filenames.length > 1 ? "s" : ""} uploaded successfully`,
      description: `Your file${filenames.length > 1 ? "s have" : " has"} been uploaded successfully.`,
      variant: "success"
    });

    closeModal();
    setIsProcessing(false);
  }, [
    form,
    filenames,
    files,
    closeModal,
    companyId,
    category,
    sites,
    meters,
    onUpload,
    registerFile
  ]);

  usePreventUnload({
    shouldPreventUnload: isProcessing
  });

  const uploadConfig = useMemo(() => UPLOAD_CONFIGS[category], [category]);

  const sharedProps = useMemo(
    () => ({
      selectedSites: sites,
      setSelectedSites: (sites: string[]) => form.setValue("sites", sites),
      selectedMeters: meters,
      setSelectedMeters: (meters: string[]) => form.setValue("meters", meters),
      sitesData: sitesData ?? [],
      isRequired: !!uploadConfig.minSites,
      disabled: isProcessing
    }),
    [form, sites, meters, sitesData, uploadConfig.minSites, isProcessing]
  );

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleSubmit}
      className="mt-2 flex w-full flex-grow flex-col space-y-8"
    >
      <FormField
        control={form.control}
        name="category"
        render={({ field }) => (
          <FormItem>
            <FormLabel>File Category *</FormLabel>
            <FormControl>
              <Select
                onValueChange={value => {
                  form.setValue("sites", []);
                  form.setValue("meters", []);
                  form.clearErrors();
                  field.onChange(value);
                }}
                value={field.value}
                disabled={files.length > 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a file category" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(UPLOAD_CONFIGS).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {typeof uploadConfig.minSites === "number" && (
        <>
          {uploadConfig.minSites === 0 && (
            <SiteSelector
              {...sharedProps}
              metersData={metersData ?? []}
              error={form.formState.errors.sites?.message}
            />
          )}
          <MeterSelector
            {...sharedProps}
            metersBySite={metersBySite}
            error={form.formState.errors.meters?.message}
          />
        </>
      )}

      <FormField
        control={form.control}
        name="files"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>
              Select File{uploadConfig.maxFileCount > 1 ? "s" : ""} *
            </FormLabel>
            <FormControl>
              <FileUploader
                hideUploadedFiles
                disabled={isProcessing}
                value={field.value}
                onValueChange={handleFileChange}
                maxFileCount={uploadConfig.maxFileCount}
                maxSize={FIFTEEN_MB}
                multiple={uploadConfig.maxFileCount > 1}
                accept={Object.fromEntries(
                  uploadConfig.mimeTypes.map(type => [type, []])
                )}
              />
            </FormControl>
            {isProcessing && (
              <div className="flex items-center gap-2 pt-4 text-muted-foreground">
                <Loader2Icon className="size-4 animate-spin" />
                <p className="text-sm">
                  Processing file
                  {filenames.length > 1
                    ? ` ${filenames.filter(f => f.isUploadComplete).length + 1} of ${filenames.length}`
                    : ""}
                  ...
                </p>
              </div>
            )}
            {files.length > 0 && (
              <div className="space-y-4 pt-4">
                {files.map(
                  (file, index) =>
                    !filenames.find(f => f.originalFilename === file.name)
                      ?.isUploadComplete && (
                      <UploadedCompanyFile
                        isFilenameEditable={false}
                        key={`${file.name}-${index}`}
                        disabled={isProcessing}
                        file={file}
                        companyId={companyId}
                        filename={
                          filenames.find(f => f.originalFilename === file.name)
                            ?.acceptedFilename ?? null
                        }
                        onFilenameChange={value =>
                          handleFilenameChange(file.name, value)
                        }
                        onRemove={() => handleFileRemove(file.name)}
                        error={
                          filenames.find(f => f.originalFilename === file.name)
                            ?.error
                        }
                      />
                    )
                )}
              </div>
            )}
            <div className="pt-2">
              <FormMessage />
            </div>
          </FormItem>
        )}
      />

      <Button
        type="submit"
        variant="secondary"
        className="w-full"
        disabled={isSubmitDisabled}
      >
        Confirm
      </Button>
    </FormWrapper>
  );
}
