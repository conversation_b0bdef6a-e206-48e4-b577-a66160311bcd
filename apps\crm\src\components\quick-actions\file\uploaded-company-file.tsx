"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatBytes } from "@watt/common/src/utils/format-bytes";
import { Button } from "@watt/crm/components/ui/button";
import { Input } from "@watt/crm/components/ui/input";
import { useCompanyFilesSearch } from "@watt/crm/hooks/use-company-files-search";
import {
  getFileExtensionFromFilename,
  getFilenameWithoutExtension
} from "@watt/crm/utils/files";
import { XIcon } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useDebounce } from "react-use";
import { getMimeTypeIcon } from "./file-uploader";

/**
 * Interface for props used by the UploadedCompanyFile component
 */
interface UploadedCompanyFileProps {
  /** Whether to allow changing the filename. Defaults to true */
  isFilenameEditable?: boolean;
  /** Whether the component is disabled */
  disabled: boolean;
  /** ID of the company the file belongs to */
  companyId: string;
  /** The uploaded file object */
  file: File;
  /** Current filename (with extension) or null if invalid/unset */
  filename: string | null;
  /** Current name of the file being edited, if provided, user wont see existing file error for this file */
  existingFilename?: string;
  /** Callback to remove the uploaded file, if not provided, the file cannot be removed */
  onRemove?: () => void;
  /** Callback when filename changes. Returns null if filename is invalid */
  onFilenameChange: (filename: string | null) => void;
  /** Callback when filename input changes */
  onFilenameInputChange?: (filename: string) => void;
  /** Error message to display */
  error?: string;
}

/**
 * Displays an uploaded file with optional filename editing capabilities.
 * Validates filename uniqueness against existing company files.
 * Shows file size and type icon.
 */
export function UploadedCompanyFile({
  isFilenameEditable = true,
  disabled,
  companyId,
  file,
  filename,
  onRemove,
  onFilenameChange,
  onFilenameInputChange,
  error,
  existingFilename
}: UploadedCompanyFileProps) {
  const defaultFilename = getFilenameWithoutExtension(file.name);
  const fileExtension = getFileExtensionFromFilename(file.name);

  const [filenameInput, setFilenameInput] = useState({
    current: defaultFilename,
    debounced: defaultFilename
  });

  const {
    files,
    isLoading,
    error: searchError
  } = useCompanyFilesSearch({
    companyId,
    filterByFilename: filenameInput.debounced,
    options: {
      enabled: !!filenameInput.debounced && !isFilenameEditable,
      staleTime: 0
    }
  });

  const filesMap = useMemo(
    () => new Map(files.map(file => [file.filename, file])),
    [files]
  );

  const previousCurrentRef = useRef(filenameInput.current);

  const checkFilename = `${filenameInput.current}.${fileExtension}`;
  const existingFile = filesMap.get(checkFilename);

  const isFilenamesMatching = filenameInput.current === filenameInput.debounced;

  useEffect(() => {
    if (
      onFilenameInputChange &&
      filenameInput.current !== previousCurrentRef.current
    ) {
      onFilenameInputChange(`${filenameInput.current}.${fileExtension}`);
      previousCurrentRef.current = filenameInput.current;
    }

    if (!isFilenamesMatching || isLoading) {
      return;
    }

    const newFilename = existingFile
      ? null
      : `${filenameInput.debounced}.${fileExtension}`;

    if (newFilename !== filename) {
      onFilenameChange(newFilename);
    }
  }, [
    existingFile,
    isFilenamesMatching,
    filenameInput,
    fileExtension,
    isLoading,
    onFilenameChange,
    filename,
    onFilenameInputChange
  ]);

  const handleFilenameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value: current } = e.target;

    onFilenameChange(null);
    setFilenameInput(prev => ({ ...prev, current }));
  };

  useDebounce(
    () => setFilenameInput(prev => ({ ...prev, debounced: prev.current })),
    500,
    [filenameInput.current]
  );

  const Icon = getMimeTypeIcon(file.type);

  if (searchError) {
    return <div>Error fetching company files</div>;
  }

  return (
    <div className="flex flex-col gap-4">
      <div
        className={cn(
          "flex gap-4 rounded-lg border p-4 shadow-sm",
          disabled && "pointer-events-none cursor-not-allowed opacity-50"
        )}
      >
        <Icon className="mt-1 size-8 shrink-0 text-muted-foreground" />
        <div className="flex w-full justify-between gap-2">
          {!isFilenameEditable ? (
            <div className="flex w-full flex-col gap-2">
              <div className="flex w-full rounded-lg shadow-sm">
                <Input
                  className="-me-px rounded-e-none shadow-none"
                  placeholder={defaultFilename}
                  type="text"
                  value={filenameInput.current}
                  onChange={handleFilenameChange}
                />
                <span className="-z-10 inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-muted-foreground text-sm">
                  .{fileExtension}
                </span>
              </div>
              <span className="text-muted-foreground text-sm">
                {formatBytes(file.size)}
              </span>
            </div>
          ) : (
            <div className="flex flex-col gap-px">
              <p className="break-all font-medium">{file.name}</p>
              <span className="text-muted-foreground text-sm">
                {formatBytes(file.size)}
              </span>
            </div>
          )}
          {onRemove && (
            <Button
              type="button"
              onClick={onRemove}
              variant="outline"
              size="icon"
              className="size-10"
            >
              <XIcon className="size-4" />
            </Button>
          )}
        </div>
      </div>
      {existingFile &&
        !isFilenameEditable &&
        checkFilename !== existingFilename && (
          <div className="font-medium text-destructive text-sm">
            A file with this name already exists
          </div>
        )}
      {error && (
        <div className="font-medium text-destructive text-sm">{error}</div>
      )}
    </div>
  );
}
