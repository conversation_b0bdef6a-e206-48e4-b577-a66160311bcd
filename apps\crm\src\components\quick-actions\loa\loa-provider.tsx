"use client";

import { LoaWidget } from "@watt/crm/components/quick-actions/loa/loa-widget";
import type { LoaModalQueryParams } from "@watt/crm/hooks/use-loa-modal";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";

export function LoaProvider() {
  const { queryParams, setQueryParams, removeQueryParams } =
    useQueryParams<LoaModalQueryParams>();

  const handleClose = () => {
    setQueryParams({ modal: undefined });
    removeQueryParams(["companyName", "companyAddress"]);
  };

  return (
    <LoaWidget
      isOpen={queryParams.modal === "generate-loa"}
      onClose={handleClose}
    />
  );
}
