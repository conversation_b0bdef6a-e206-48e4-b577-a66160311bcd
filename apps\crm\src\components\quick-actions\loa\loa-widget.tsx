"use client";

import { buildSafeUrl } from "@watt/common/src/utils/safe-url-builder";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { Info, X } from "lucide-react";

type LoaWidgetProps = {
  isOpen: boolean;
  onClose: () => void;
};

export function LoaWidget({ isOpen, onClose }: LoaWidgetProps) {
  const { queryParams } = useQueryParams<{
    companyName?: string;
    companyAddress?: string;
  }>();
  const { companyName, companyAddress } = queryParams;

  const iframeUrl = buildSafeUrl({
    baseUrl: "https://app.signable.co.uk",
    path: "/widget/url/VVzw7Mmjwd",
    queryParams: {
      company: companyName,
      name: "",
      address: companyAddress
    }
  }).toString();

  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="w-full max-w-[75%]"
        onEscapeKeyDown={onClose}
      >
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
          <span className="sr-only fixed">Close</span>
        </Button>
        <div className="h-screen overflow-y-scroll p-8">
          <div className="flex h-full flex-col space-y-4">
            <div className="flex items-center space-x-2">
              <DrawerTitle className="font-semibold text-xl tracking-tight">
                Letter of Authority
              </DrawerTitle>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 cursor-pointer text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[300px]">
                    <p>
                      This is the same signable widget that is available at
                      watt.co.uk/loa. Please note that signable LOAs created
                      with this widget are not yet integrated into the app and
                      do not appear elsewhere.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex-1">
              <iframe
                src={iframeUrl}
                className="h-full w-full border-none"
                title="Signable LOA widget"
              />
              <p>Once completed, you can close this window.</p>
            </div>
          </div>
        </div>
      </DrawerContentWithDirection>
    </Drawer>
  );
}
