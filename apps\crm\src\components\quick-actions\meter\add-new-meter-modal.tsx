"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "../../ui/dialog";
import { type IncompleteLinkMeterData, MeterForm } from "./meter-form";

type AddNewMeterModalProps = {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  addressId: string;
  companyReg?: string;
  onSubmit?: (incompleteLinkMeterData: IncompleteLinkMeterData) => void;
};

export function AddNewMeterModal({
  isModalOpen,
  setIsModalOpen,
  addressId,
  companyReg,
  onSubmit
}: AddNewMeterModalProps) {
  const handleSubmitForm = (
    incompleteLinkMeterData: IncompleteLinkMeterData
  ) => {
    onSubmit?.(incompleteLinkMeterData);
    setIsModalOpen(false);
  };
  return (
    <Dialog onOpenChange={setIsModalOpen} open={isModalOpen}>
      <DialogContent onPointerDownOutside={e => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Add New Meter</DialogTitle>
          <DialogDescription>
            Enter the meter details to add a new meter to this address.
          </DialogDescription>
        </DialogHeader>
        <MeterForm
          addressId={addressId}
          companyReg={companyReg}
          onSubmitForm={handleSubmitForm}
        />
      </DialogContent>
    </Dialog>
  );
}
