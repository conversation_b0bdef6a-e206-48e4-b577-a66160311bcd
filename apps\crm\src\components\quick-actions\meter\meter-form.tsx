"use client";

import { TRPCClientError } from "@trpc/client";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2, XCircle } from "lucide-react";
import { useRef, useState } from "react";

import { CreateOrLinkMetersFormSchema } from "@watt/api/src/types/address";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

import { log } from "@watt/common/src/utils/axiom-logger";
import {
  Combobox,
  ComboboxContent,
  ComboboxGroup,
  ComboboxItem,
  ComboboxTrigger
} from "../../combobox/combobox-input";

export type IncompleteLinkMeterData = {
  addressId: string;
  mpanNumbers?: string[];
  mprnNumbers?: string[];
} | null;

type MeterFormProps = {
  addressId: string;
  companyReg?: string;
  onSubmitForm: (incompleteLinkMeterData: IncompleteLinkMeterData) => void;
};

export function MeterForm({
  addressId,
  companyReg,
  onSubmitForm
}: MeterFormProps) {
  const [mpanLists, setMpanLists] = useState<string[]>([]);
  const mpanNumberContainerRef = useRef<HTMLDivElement>(null);
  const [mrpnLists, setMrpnLists] = useState<string[]>([]);
  const mprnNumberContainerRef = useRef<HTMLDivElement>(null);
  const meterMutation = trpcClient.address.createOrLinkMeters.useMutation();

  const form = useZodForm({
    schema: CreateOrLinkMetersFormSchema,
    defaultValues: {
      addressId,
      companyReg
    }
  });

  const removeMpan = (mpanToRemove: string) => {
    setMpanLists(mpanLists.filter(mpan => mpan !== mpanToRemove));
    form.setValue(
      "mpanNumbers",
      mpanLists.filter(mpan => mpan !== mpanToRemove)
    );
  };

  const handleAddMpanItem = (newMpan: string) => {
    if (newMpan && !mpanLists.includes(newMpan)) {
      setMpanLists([...mpanLists, newMpan]);
      form.setValue("mpanNumbers", [...mpanLists, newMpan]);
    }
  };

  const removeMprn = (mprnToRemove: string) => {
    setMrpnLists(mrpnLists.filter(mprn => mprn !== mprnToRemove));
    form.setValue(
      "mprnNumbers",
      mrpnLists.filter(mprn => mprn !== mprnToRemove)
    );
  };

  const handleAddMprnItem = (newMprn: string) => {
    if (newMprn && !mrpnLists.includes(newMprn)) {
      setMrpnLists([...mrpnLists, newMprn]);
      form.setValue("mprnNumbers", [...mrpnLists, newMprn]);
    }
  };

  const handleFormSubmit = async () => {
    try {
      const result = await meterMutation.mutateAsync(form.getValues());
      onSubmitForm(result.incompleteLinkMeterData);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while adding the new meter. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to add new meter",
        description,
        variant: "destructive",
        duration: 10000
      });
    }
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleFormSubmit}
      className="my-4 space-y-4"
    >
      <FormField
        control={form.control}
        name="mpanNumbers"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>MPAN</FormLabel>
            <FormControl>
              <div
                ref={mpanNumberContainerRef}
                className="flex w-full flex-col"
              >
                <Combobox>
                  <ComboboxTrigger
                    fieldValue={field.value ?? []}
                    placeholder="Add one or more MPAN core numbers"
                  />
                  <ComboboxContent
                    placeholder="Type to add MPAN number"
                    container={mpanNumberContainerRef.current}
                    allowAddItem={true}
                    onAddItem={handleAddMpanItem}
                  >
                    <ComboboxGroup>
                      {mpanLists.map(item => (
                        <ComboboxItem key={item}>
                          {item}
                          {field.value?.includes(item) && (
                            <Button
                              type="button"
                              variant="link"
                              className="h-4 p-2"
                              onClick={() => removeMpan(item)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </ComboboxItem>
                      ))}
                    </ComboboxGroup>
                  </ComboboxContent>
                </Combobox>
              </div>
            </FormControl>
            {!!form.formState.errors.mpanNumbers?.length && (
              <FormMessage>MPAN is invalid</FormMessage>
            )}
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="mprnNumbers"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>MPRN</FormLabel>
            <FormControl>
              <div
                ref={mprnNumberContainerRef}
                className="flex w-full flex-col"
              >
                <Combobox>
                  <ComboboxTrigger
                    fieldValue={field.value ?? []}
                    placeholder="Add one or more MPRN numbers"
                  />
                  <ComboboxContent
                    placeholder="Type to add MPRN number"
                    container={mprnNumberContainerRef.current}
                    allowAddItem={true}
                    onAddItem={handleAddMprnItem}
                  >
                    <ComboboxGroup>
                      {mrpnLists.map(item => (
                        <ComboboxItem key={item}>
                          {item}
                          {field.value?.includes(item) && (
                            <Button
                              type="button"
                              variant="link"
                              className="h-4 p-2"
                              onClick={() => removeMprn(item)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </ComboboxItem>
                      ))}
                    </ComboboxGroup>
                  </ComboboxContent>
                </Combobox>
              </div>
            </FormControl>
            {!!form.formState.errors.mprnNumbers?.length && (
              <FormMessage>MPRN is invalid</FormMessage>
            )}
          </FormItem>
        )}
      />
      <Button
        disabled={
          meterMutation.isPending ||
          (mpanLists.length === 0 && mrpnLists.length === 0)
        }
        type="submit"
        variant="secondary"
        className="button-click-animation w-full"
      >
        {meterMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        Confirm
      </Button>
    </FormWrapper>
  );
}
