"use client";

import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { X } from "lucide-react";
import { AddNotificationForm } from "./add-notification-form";

type AddNotificationDrawerProps = {
  isOpen: boolean;
  closeModal: () => void;
};

export function AddNotificationDrawer({
  isOpen,
  closeModal
}: AddNotificationDrawerProps) {
  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        onEscapeKeyDown={closeModal}
        className="w-[45ch]"
      >
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={closeModal}
        >
          <X className="h-4 w-4" />
          <span className="sr-only fixed">Close</span>
        </Button>

        <div className="flex flex-col overflow-y-scroll p-8">
          <DrawerTitle className="pb-2 text-xl ">
            Create Notification
          </DrawerTitle>
          <DrawerDescription className="italic">
            Please complete all required fields (*) to create your notification.
          </DrawerDescription>

          <AddNotificationForm closeModal={closeModal} />
        </div>
      </DrawerContentWithDirection>
    </Drawer>
  );
}
