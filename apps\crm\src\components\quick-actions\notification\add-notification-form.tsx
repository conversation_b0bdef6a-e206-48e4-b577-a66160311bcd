"use client";

import { NotificationType } from "@prisma/client";
import {
  ANNOUNCEMENT_CATEGORIES,
  ANNOUNCEMENT_TEAMS_OPTIONS,
  CreateAnnouncementNotificationSchema
} from "@watt/api/src/types/notification";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  DropdownMultiSelect,
  DropdownMultiSelectContent,
  DropdownMultiSelectGroup,
  DropdownMultiSelectItem,
  DropdownMultiSelectTrigger
} from "@watt/crm/components/dropdown-checkbox/dropdown-multi-select";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Input } from "@watt/crm/components/ui/input";
import { Label } from "@watt/crm/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectWrapper
} from "@watt/crm/components/ui/select";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { Check, Loader2 } from "lucide-react";

type AddNotificationFormProps = {
  closeModal: () => void;
};
type TeamOption = keyof typeof ANNOUNCEMENT_TEAMS_OPTIONS;

export function AddNotificationForm({ closeModal }: AddNotificationFormProps) {
  const form = useZodForm({
    schema: CreateAnnouncementNotificationSchema,
    defaultValues: {
      // Stephen: This is unsafe, I can hack this and use this tRPC endpoint to send any notification.
      type: NotificationType.ANNOUNCEMENT_NOTIFICATION,
      teams: [],
      category: "",
      subject: "",
      message: ""
    }
  });

  const notificationMutation =
    trpcClient.notification.sendAnnouncementNotification.useMutation();

  const { isPending } = notificationMutation;

  const handleNotificationSubmit = async () => {
    const payload = form.getValues();
    try {
      const result = await notificationMutation.mutateAsync(payload);
      if (result?.data?.status === "processed") {
        const description = payload.teams.includes("ALL")
          ? "Notification successfully created and sent to all staff."
          : `Notification successfully created and sent to: ${payload.teams
              .map(team => ANNOUNCEMENT_TEAMS_OPTIONS[team])
              .join(", ")}`;
        toast({
          title: "Notification Sent",
          variant: "success",
          description
        });
      }
      closeModal();
    } catch (e) {
      const error = e as Error;
      log.error(error.message);

      toast({
        title: "System Error",
        description:
          "Your request could not be completed. If this persists, please contact support.",
        variant: "destructive"
      });
    }
  };

  const toggleItem = (teamKey: TeamOption, values: TeamOption[]) => {
    let newValues: TeamOption[];

    if (teamKey === "ALL") {
      newValues = values.includes("ALL") ? [] : ["ALL"];
    } else {
      newValues = values.includes("ALL")
        ? [teamKey]
        : values.includes(teamKey)
          ? values.filter(selectedItem => selectedItem !== teamKey)
          : [...values, teamKey];
    }

    form.setValue("teams", newValues);
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleNotificationSubmit}
      className="my-4 flex w-full flex-grow flex-col space-y-6"
    >
      <FormField
        control={form.control}
        name="category"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <p className="mb-2 font-semibold">Category Selection</p>
            <Label>Category Type*</Label>
            <FormControl>
              <SelectWrapper>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger
                    className={cn(
                      !field.value && "text-muted-foreground italic"
                    )}
                  >
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>

                  <SelectContent position="popper">
                    {Object.keys(ANNOUNCEMENT_CATEGORIES).map(key => {
                      const categoryKey =
                        key as keyof typeof ANNOUNCEMENT_CATEGORIES;

                      return (
                        <SelectItem key={key} value={categoryKey}>
                          {ANNOUNCEMENT_CATEGORIES[categoryKey]}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </SelectWrapper>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="teams"
        render={({ field }) => (
          <FormItem className=" flex flex-col">
            <p className="mb-2 font-semibold">Targeted Audience</p>
            <Label>Teams*</Label>
            <FormControl>
              <DropdownMultiSelect>
                <DropdownMultiSelectTrigger
                  placeholder="Select teams"
                  fieldValue={field.value.map(
                    value => ANNOUNCEMENT_TEAMS_OPTIONS[value] || value
                  )}
                />
                <DropdownMultiSelectContent className="w-[38ch]">
                  <DropdownMultiSelectGroup>
                    {Object.keys(ANNOUNCEMENT_TEAMS_OPTIONS).map(key => {
                      const teamKey =
                        key as keyof typeof ANNOUNCEMENT_TEAMS_OPTIONS;
                      return (
                        <DropdownMultiSelectItem
                          key={teamKey}
                          onSelect={() => toggleItem(teamKey, field.value)}
                        >
                          <div className="flex items-center">
                            <div
                              className={`mr-2 flex h-4 w-4 items-center justify-center rounded-sm border ${
                                field.value.includes(teamKey)
                                  ? "bg-primary text-primary-foreground"
                                  : "opacity-50 [&_svg]:invisible"
                              }`}
                            >
                              <Check className="size-4" />
                            </div>
                            {ANNOUNCEMENT_TEAMS_OPTIONS[teamKey]}
                          </div>
                        </DropdownMultiSelectItem>
                      );
                    })}
                  </DropdownMultiSelectGroup>
                </DropdownMultiSelectContent>
              </DropdownMultiSelect>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="subject"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <p className="mb-2 font-semibold">Content Details</p>

            <Label>Subject*</Label>
            <FormControl>
              <Input
                {...field}
                placeholder="Subject"
                className={cn(!field.value && "text-muted-foreground italic")}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="message"
        render={({ field }) => (
          <FormItem className="flex flex-grow flex-col pb-4">
            <FormLabel>Content*</FormLabel>
            <FormControl>
              <Textarea
                {...field}
                className={cn(
                  !field.value && "text-muted-foreground italic",
                  "flex-grow resize-none"
                )}
                placeholder="Notification message"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <Button type="submit" variant="secondary" className="w-full">
        {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        Confirm
      </Button>
    </FormWrapper>
  );
}
