"use client";

import { useQueryParams } from "@watt/crm/hooks/use-query-params";

import { AddNotificationDrawer } from "./add-notification-drawer";

export type AddNotificationModalQueryParams = {
  modal: string;
};

export function NotificationProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<AddNotificationModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  return (
    <AddNotificationDrawer
      isOpen={queryParams.modal === "create-notification"}
      closeModal={handleModalClose}
    />
  );
}
