import dynamic from "next/dynamic";

const NoteProvider = dynamic(() =>
  import("../note/note-provider").then(mod => ({
    default: mod.NoteProvider
  }))
);

const AddressProvider = dynamic(() =>
  import("./address/address-provider").then(mod => ({
    default: mod.AddressProvider
  }))
);

const BusinessTargetProvider = dynamic(() =>
  import("./business-target/business-target-provider").then(mod => ({
    default: mod.BusinessTargetProvider
  }))
);

const ContactProvider = dynamic(() =>
  import("./contact/contact-provider").then(mod => ({
    default: mod.ContactProvider
  }))
);

const ContractProvider = dynamic(() =>
  import("./contract/contract-provider").then(mod => ({
    default: mod.ContractProvider
  }))
);

const QuoteProvider = dynamic(() =>
  import("./quote/quote-provider").then(mod => ({
    default: mod.QuoteProvider
  }))
);

const LoaProvider = dynamic(() =>
  import("./loa/loa-provider").then(mod => ({
    default: mod.LoaProvider
  }))
);

const SiteMeterProvider = dynamic(() =>
  import("./site-meter/site-meter-provider").then(mod => ({
    default: mod.SiteMeterProvider
  }))
);

const NotificationProvider = dynamic(() =>
  import("./notification/notification-provider").then(mod => ({
    default: mod.NotificationProvider
  }))
);

const FileProvider = dynamic(() =>
  import("./file/file-provider").then(mod => ({
    default: mod.FileProvider
  }))
);

const DealProvider = dynamic(() =>
  import("./deal/deal-provider").then(mod => ({
    default: mod.DealProvider
  }))
);

export function QuickActionProviders() {
  return (
    <>
      <QuoteProvider />
      <ContractProvider />
      <ContactProvider />
      <AddressProvider />
      <BusinessTargetProvider />
      <NoteProvider />
      <LoaProvider />
      <SiteMeterProvider />
      <NotificationProvider />
      <FileProvider />
      <DealProvider />
    </>
  );
}
