import { QuoteStatus } from "@prisma/client";
import { useState } from "react";

import { Button } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@watt/crm/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";

import {
  CustomQuoteForm,
  type CustomQuoteFormProps
} from "./custom-quote-form";

export interface CustomQuoteModalProps extends CustomQuoteFormProps {
  quoteListStatus: QuoteStatus;
}

export function CustomQuoteModal({
  utilityType,
  tariffRates,
  showCapacityCharge,
  quoteListId,
  quoteListStatus,
  onSubmitForm
}: CustomQuoteModalProps) {
  const [customQuoteModalOpen, setCustomQuoteModalOpen] = useState(false);
  const isCustomQuoteDisabled =
    quoteListStatus === QuoteStatus.ACCEPTED ||
    quoteListStatus === QuoteStatus.EXPIRED;

  const disableCustomQuoteReason = (quoteListStatus: QuoteStatus) => {
    switch (quoteListStatus) {
      case QuoteStatus.ACCEPTED:
        return "One of the quotes has been accepted and can no longer be edited.";
      default:
        return "This quote has expired and can no longer be edited.";
    }
  };

  const handleOpenModal = (open: boolean) => {
    if (quoteListStatus === QuoteStatus.ACCEPTED) {
      toast({
        title: "Quote accepted",
        description: disableCustomQuoteReason(quoteListStatus),
        variant: "destructive"
      });
      return;
    }

    if (quoteListStatus === QuoteStatus.EXPIRED) {
      toast({
        title: "Quote expired",
        description: disableCustomQuoteReason(quoteListStatus),
        variant: "destructive"
      });
      return;
    }
    setCustomQuoteModalOpen(open);
  };

  const handleSubmitForm = (id: string) => {
    onSubmitForm(id);
    setCustomQuoteModalOpen(false);
  };
  return (
    <Dialog onOpenChange={handleOpenModal} open={customQuoteModalOpen}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground"
                size="sm"
                disabled={isCustomQuoteDisabled}
              >
                Custom Quote
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent>
            {isCustomQuoteDisabled
              ? disableCustomQuoteReason(quoteListStatus)
              : "Add a custom quote to this quote list."}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <DialogContent
        onPointerDownOutside={e => e.preventDefault()}
        className="flex max-w-2xl flex-col px-2"
      >
        <DialogHeader className="space-y-4 px-4">
          <DialogTitle>Add New Custom Quote</DialogTitle>
          <DialogDescription className="space-y-1 italic">
            Add a custom quote to this quote list.
          </DialogDescription>
        </DialogHeader>
        <CustomQuoteForm
          utilityType={utilityType}
          tariffRates={tariffRates}
          showCapacityCharge={showCapacityCharge}
          onSubmitForm={handleSubmitForm}
          quoteListId={quoteListId}
        />
      </DialogContent>
    </Dialog>
  );
}
