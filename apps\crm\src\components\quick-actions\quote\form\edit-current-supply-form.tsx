"use client";

import { TRPCClientError } from "@trpc/client";
import type { CurrentSupplier } from "@watt/api/src/types/supplier";
import { CurrentSupplierFormSchema } from "@watt/api/src/types/supplier";
import { log } from "@watt/common/src/utils/axiom-logger";
import type { TariffRates } from "@watt/common/src/utils/split-usage-by-rate";
import { SuffixInput } from "@watt/crm/components/suffix-input";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { trpcClient } from "@watt/crm/utils/api";
import { UtilityType } from "@watt/db/src/enums";
import { Loader2Icon } from "lucide-react";
import { useMemo } from "react";

type EditCurrentSupplyFormProps = {
  initialData: CurrentSupplier | null;
  tariffRates: TariffRates;
  showCapacityCharge: boolean;
  updateColumnVisibility: (column: string, visible: boolean) => void;
};

export function EditCurrentSupplyForm({
  initialData,
  tariffRates,
  showCapacityCharge,
  updateColumnVisibility
}: EditCurrentSupplyFormProps) {
  const currentSupplierMutation =
    trpcClient.supplier.updateCurrentSupplier.useMutation();

  const defaultValues = useMemo(
    () =>
      initialData
        ? {
            id: initialData.id,
            unitRate: initialData.unitRate,
            standingCharge: initialData.standingCharge,
            mpanId: "mpanId" in initialData ? initialData.mpanId : undefined,
            mprnId: "mprnId" in initialData ? initialData.mprnId : undefined,
            utilityType:
              initialData.utilityType === UtilityType.ELECTRICITY
                ? UtilityType.ELECTRICITY
                : UtilityType.GAS,
            nightUnitRate:
              "nightUnitRate" in initialData
                ? initialData.nightUnitRate
                : undefined,
            weekendUnitRate:
              "weekendUnitRate" in initialData
                ? initialData.weekendUnitRate
                : undefined,
            capacityChargeKva:
              "capacityChargeKva" in initialData
                ? initialData.capacityChargeKva
                : undefined
          }
        : undefined,
    [initialData]
  );

  const form = useZodForm({
    schema: CurrentSupplierFormSchema,
    defaultValues
  });

  const isDisabled = useMemo(() => {
    return (
      currentSupplierMutation.isPending ||
      form.formState.isSubmitting ||
      !Object.keys(form.formState.dirtyFields).length
    );
  }, [
    currentSupplierMutation.isPending,
    form.formState.isSubmitting,
    form.formState.dirtyFields
  ]);

  async function handleSubmit() {
    try {
      const values = form.getValues();
      const result = await currentSupplierMutation.mutateAsync(values);

      if (result) {
        updateColumnVisibility("priceDifference", !!result.annualPrice);

        toast({
          title: "Current supplier updated",
          description: "The current supplier has been updated successfully.",
          variant: "success"
        });
      }
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while updating the current supplier. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to update current supplier",
        description,
        variant: "destructive"
      });
    }
  }

  const unitRateProps = {
    suffix: "pence/kWh",
    type: "number",
    step: "0.01"
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleSubmit}
      className="my-4 space-y-4"
    >
      {tariffRates.day && (
        <FormField
          control={form.control}
          name="unitRate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Unit Rate</FormLabel>
              <FormControl>
                <SuffixInput {...field} {...unitRateProps} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {tariffRates.night && (
        <FormField
          control={form.control}
          name="nightUnitRate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Night Unit Rate</FormLabel>
              <FormControl>
                <SuffixInput {...field} {...unitRateProps} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {tariffRates.weekend && (
        <FormField
          control={form.control}
          name="weekendUnitRate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Weekend Unit Rate</FormLabel>
              <FormControl>
                <SuffixInput {...field} {...unitRateProps} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="standingCharge"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Standing Charge</FormLabel>
            <FormControl>
              <SuffixInput
                {...field}
                suffix="pence/day"
                type="number"
                step="0.01"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {showCapacityCharge && (
        <FormField
          control={form.control}
          name="capacityChargeKva"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Capacity Charge</FormLabel>
              <FormControl>
                <SuffixInput {...field} suffix="pence/day" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <Button
        type="submit"
        variant="secondary"
        className="w-full"
        disabled={isDisabled}
      >
        {currentSupplierMutation.isPending && (
          <Loader2Icon className="mr-2 size-4 animate-spin" />
        )}
        Update
      </Button>
    </FormWrapper>
  );
}
