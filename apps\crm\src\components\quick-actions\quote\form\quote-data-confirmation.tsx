import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { X } from "lucide-react";

import { QuoteForm } from "./quote-form";

type QuoteDataConfirmationProps = {
  isOpen: boolean;
  closeModal: () => void;
  handleSumbit: (quoteListId: string) => void;
};

export function QuoteDataConfirmation({
  isOpen,
  closeModal,
  handleSumbit
}: QuoteDataConfirmationProps) {
  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="max-w-[700px]"
        onEscapeKeyDown={closeModal}
      >
        <Button
          variant="dialog"
          className="top-6 right-6 h-auto p-0"
          onClick={closeModal}
        >
          <X className="h-4 w-4" />
          <span className="sr-only fixed">Close</span>
        </Button>
        <div className="h-screen overflow-y-scroll p-8">
          <div className="flex flex-col space-y-4">
            <DrawerTitle className="text-xl">
              Quote Data Confirmation
            </DrawerTitle>
            <DrawerDescription className="italic">
              Please confirm the information with the customer before generating
              the quote list.
            </DrawerDescription>
            <QuoteForm onSubmit={handleSumbit} />
          </div>
        </div>
      </DrawerContentWithDirection>
    </Drawer>
  );
}
