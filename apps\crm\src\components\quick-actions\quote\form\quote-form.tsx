import { TRPCClientError } from "@trpc/client";
import type { Address_Find_Many } from "@watt/api/src/router/address";
import { trpcClient } from "@watt/crm/utils/api";
import { BusinessType, UtilityType } from "@watt/db/src/enums";
import type { BusinessTargetResult } from "@watt/external-apis/src/libs/experian/business-targeter";
import { Loader2, Plus } from "lucide-react";
import {
  startTransition,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";
import { useDebounce } from "react-use";

import { GenerateQuoteInputSchema } from "@watt/api/src/types/quote/quote-schemas";
import udProvidersList from "@watt/common/src/constants/ud-providers-list.json";
import { isShortMpan } from "@watt/common/src/mpan/mpan";
import { isValidMPRN } from "@watt/common/src/mprn/mprn";
import { isValidBusinessReference } from "@watt/common/src/regex/company-number";
import { isValidPostcode } from "@watt/common/src/regex/postcode";
import { isValidUuid } from "@watt/common/src/regex/uuid";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  convertLocalDateToUTCString,
  convertUTCStringToLocalDate
} from "@watt/common/src/utils/format-date";
import type { UsageTariffs } from "@watt/common/src/utils/split-usage-by-rate";
import { splitUsageByRate } from "@watt/common/src/utils/split-usage-by-rate";
import { DatePickerInput } from "@watt/crm/components/date-picker-input";
import {
  AddressSelection,
  CompanySelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { AddNewAddressTrigger } from "@watt/crm/components/quick-actions/address/add-new-address-trigger";
import { AddNewBusinessTargetTrigger } from "@watt/crm/components/quick-actions/business-target/add-new-business-target-trigger";
import { SuffixInput } from "@watt/crm/components/suffix-input";
import { Button } from "@watt/crm/components/ui/button";
import { Checkbox } from "@watt/crm/components/ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Label } from "@watt/crm/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import { Switch } from "@watt/crm/components/ui/switch";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useAddressSearch } from "@watt/crm/hooks/use-address-search";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { AddNewMeterModal } from "../../meter/add-new-meter-modal";
import type { IncompleteLinkMeterData } from "../../meter/meter-form";

// TODO (Bidur): resolve duplicate code in this file vs credit check form
type CompanyResultGroups<T = BusinessTargetResult[]> = {
  active: T;
  disolved: T;
};

type QueryParams = {
  businessSearch: string; // Keyword used to search for an entity
  selectedBusinessRef: string; // Selected business reference number from the search results
  addressSearch: string; // Postcode used to search for an address
  selectedAddressId: string; // Selected address id from the search results
  businessType: string; // Business type of the entity selected from the search results
  utilityType: string; // Utility type selected from the dropdown currently defaults to electricity
  meterIdentifier: string; // Meter identifier selected from the dropdown after selecting an address and utility type
  creditScore: string; // Credit score input value
  contractStartDate: string; // Contract start date input value
  upliftRate: string; // Uplift rate input value
  currentSupplier: string; // Current supplier dropdown, list of ud suppliers
  manualConsumptionEntry: string; // Toggle for manual consumption entry
  dayUsage: string; // Day usage input value
  nightUsage: string; // Night usage input value
  weekendUsage: string; // Weekend usage input value
  totalUsage: string; // Total usage input value calculated from day, night and weekend usage OR manually entered if auto split is on
  manualSplit: string; // Manual split usage by tariff rates
  isSmartMeter: string; // Smart meter toggle
  isCustomQuotesOnly: string; // Custom quotes toggle
};

type ResultGroups<T = BusinessTargetResult[]> = {
  active: T;
  disolved: T;
};

type SetUsage = {
  day: number;
  night: number;
  weekend: number;
  totalUsage: number;
};

function groupSearchResults(results: BusinessTargetResult[]): ResultGroups {
  return {
    active: results.filter(company => company.businessStatus !== "D"),
    disolved: results.filter(company => company.businessStatus === "D")
  };
}

type QuoteFormProps = {
  onSubmit: (quoteListId: string) => void;
};

export function QuoteForm({ onSubmit }: QuoteFormProps) {
  const businessNumberContainerRef = useRef<HTMLDivElement>(null);
  const addressContainerRef = useRef<HTMLDivElement>(null);
  const currentSupplierContainerRef = useRef<HTMLDivElement>(null);
  // (Bidur): As the Command component is inside the Popover component, on select does not close the modal so we pass controlled state
  const [isBusinessModalOpen, setIsBusinessModalOpen] = useState(false);
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);
  const [isMeterModalOpen, setIsMeterModalOpen] = useState(false);
  const [metersRequireLinking, setMetersRequireLinking] =
    useState<IncompleteLinkMeterData>(null);
  const { queryParams, setQueryParams } =
    useQueryParams<Partial<QueryParams>>();
  const [companySearchResults, setCompanySearchResults] =
    useState<CompanyResultGroups>({
      active: [],
      disolved: []
    });
  const [addressData, setAddressData] = useState<
    Address_Find_Many | undefined
  >();
  const { fetchAddress, isFetchingAddress } = useAddressSearch();
  const [meterNumbersList, setMeterNumbersList] = useState<
    string[] | undefined
  >();
  const [splitRatio, setSplitRatio] = useState<UsageTariffs>();
  const businessTargetMutation = trpcClient.businessTarget.get.useMutation();
  const companySiteMeterListMutation =
    trpcClient.siteMeter.findCompanySiteMeters.useMutation();
  const quotesMutation = trpcClient.quote.generateQuotes.useMutation();
  const isManualSplit = queryParams.manualSplit === "true";
  const tariffWithSplitRatioMutation =
    trpcClient.quote.getTariffWithSplitRatio.useMutation();
  const addressUtilityMetersMutation =
    trpcClient.address.fetchMetersForAddressAndUtility.useMutation();

  const form = useZodForm({
    schema: GenerateQuoteInputSchema,
    mode: "onChange",
    defaultValues: {
      businessNumber: queryParams.selectedBusinessRef ?? undefined,
      creditScore: 100,
      contractStartDate: queryParams.contractStartDate ?? undefined,
      manualConsumptionEntry: queryParams.manualConsumptionEntry === "true",
      isSmartMeter: queryParams.isSmartMeter !== "false",
      isCustomQuotesOnly: queryParams.isCustomQuotesOnly === "true"
    }
  });

  const toggleManualConsumptionEntry = (value: boolean) => {
    setQueryParams({ manualConsumptionEntry: value.toString() });
    form.setValue("manualConsumptionEntry", true);
  };

  function updateTotalUsage() {
    const dayUsage = form.watch("dayUsage") || 0;
    const nightUsage = form.watch("nightUsage") || 0;
    const weekendUsage = form.watch("weekendUsage") || 0;
    const newTotalUsage = dayUsage + nightUsage + weekendUsage;
    if (Number.isNaN(newTotalUsage) || newTotalUsage === 0) {
      return;
    }
    form.setValue("totalUsage", newTotalUsage);
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function defaultSearchToSelectedReferenceOnMount() {
    if (queryParams.businessSearch || !queryParams.selectedBusinessRef) {
      return;
    }

    setQueryParams({ businessSearch: queryParams.selectedBusinessRef });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // (Bidur): Instead of setting the queryParams value as the form's default value we set the form value through this useEffect
  // This gives us the ability to reset the input to undefined when the user clears the input field otherwise the input field resets back to the default value that was initially set
  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function updateDynamicInputFields() {
    if (queryParams.creditScore) {
      form.setValue("creditScore", Number.parseInt(queryParams.creditScore));
    }

    if (queryParams.upliftRate) {
      form.setValue("upliftRate", Number.parseFloat(queryParams.upliftRate));
    }

    if (queryParams.currentSupplier) {
      form.setValue("currentSupplier", queryParams.currentSupplier);
    }

    if (queryParams.utilityType) {
      form.setValue("utilityType", queryParams.utilityType as UtilityType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function fullResetBusinessFormData() {
    form.resetField("businessNumber");
    form.resetField("businessType");
    form.resetField("businessName");
    setQueryParams({
      businessSearch: "",
      selectedBusinessRef: "",
      businessType: ""
    });
    setCompanySearchResults({
      active: [],
      disolved: []
    });
  }

  const fullResetAddressFormData = useCallback(() => {
    form.resetField("siteAddressId");
    form.resetField("sitePostcode");
    form.resetField("meterIdentifier");
    setQueryParams({
      addressSearch: "",
      selectedAddressId: "",
      meterIdentifier: ""
    });
    setAddressData(undefined);
  }, [setQueryParams, form.resetField]);

  const resetAllUsageFields = useCallback(() => {
    form.resetField("dayUsage");
    form.resetField("nightUsage");
    form.resetField("weekendUsage");
    form.resetField("totalUsage");
    setQueryParams({
      dayUsage: "",
      nightUsage: "",
      weekendUsage: "",
      totalUsage: ""
    });
  }, [setQueryParams, form.resetField]);

  const resetConsumption = useCallback(() => {
    resetAllUsageFields();
    form.setValue("manualConsumptionEntry", false);
    setQueryParams({ manualConsumptionEntry: "false" });
  }, [resetAllUsageFields, form.setValue, setQueryParams]);

  useDebounce(
    async function lookupBusinessRefOnSearchInput() {
      try {
        if (!queryParams.businessSearch) {
          fullResetBusinessFormData();
          return;
        }

        const isBusinessRef = isValidBusinessReference(
          queryParams.businessSearch
        );
        const isPostcode = isValidPostcode(queryParams.businessSearch);

        const postcode = isPostcode ? queryParams.businessSearch : undefined;
        const businessRef = isBusinessRef
          ? queryParams.businessSearch
          : undefined;

        // If businessSearch query is a postcode then name must also be set to the postcode to get results
        const name = postcode ?? queryParams.businessSearch;

        const result = await businessTargetMutation.mutateAsync({
          ...(businessRef ? { businessRef } : { name }),
          ...(isPostcode ? { postcode } : {})
        });

        setCompanySearchResults(groupSearchResults(result));

        if (queryParams.selectedBusinessRef) {
          const selectedCompany = result.find(
            company => company.businessRef === queryParams.selectedBusinessRef
          );
          if (!selectedCompany) {
            return;
          }

          form.setValue("businessNumber", queryParams.selectedBusinessRef);
          form.setValue("businessName", selectedCompany.commercialName);
        }
      } catch (e) {
        const error = e as Error;
        const description =
          error instanceof TRPCClientError && !!error.data.zodError
            ? "Error fetching business information. Please check the input and try again."
            : error.message;
        toast({
          title: "Unable to get business entity",
          description,
          variant: "destructive"
        });
      }
    },
    500,
    [queryParams.businessSearch]
  );

  const lookupAddressOnSearchInput = useCallback(async () => {
    try {
      if (!queryParams.addressSearch) {
        fullResetAddressFormData();
        return;
      }

      const postcodeIsValid = isValidPostcode(queryParams.addressSearch);
      const mpanIsValid = isShortMpan(queryParams.addressSearch);
      const mprnIsValid = isValidMPRN(queryParams.addressSearch);
      const idIsValid = isValidUuid(queryParams.addressSearch);

      if (!postcodeIsValid && !mpanIsValid && !mprnIsValid && !idIsValid) {
        return;
      }

      // TODO (Bidur): Enable address search by address line, disabled for now due to slow response
      const result = await fetchAddress(queryParams.addressSearch);

      setAddressData(result);

      if (queryParams.selectedAddressId) {
        const selectedAddressObj = result?.find(
          address => address.id === queryParams.selectedAddressId
        );

        if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
          fullResetAddressFormData();

          // Reset consumption fields if selected address is not found
          if (queryParams.manualConsumptionEntry === "true") {
            resetConsumption();
          }

          toast({
            title: "Invalid address in the URL",
            description:
              "Selected address does not have a valid address or postcode",
            variant: "destructive"
          });
          return;
        }
        form.setValue("siteAddressId", queryParams.selectedAddressId);
        form.setValue("sitePostcode", selectedAddressObj.postcode);
      } else if (queryParams.manualConsumptionEntry === "true") {
        // Addresses are found but the address is not selected therefore we reset the meter identifier and consumption fields
        resetConsumption();
      }
    } catch (e) {
      const error = e as Error;
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error fetching address. Please check the input and try again."
          : error.message;
      toast({
        title: "Unable to get address",
        description,
        variant: "destructive"
      });
    }
  }, [
    fetchAddress,
    queryParams.addressSearch,
    form,
    fullResetAddressFormData,
    queryParams.manualConsumptionEntry,
    queryParams.selectedAddressId,
    resetConsumption
  ]);

  useDebounce(lookupAddressOnSearchInput, 1000, [queryParams.addressSearch]);

  const handleMeterIdentifierChange = (meterNo: string) => {
    setQueryParams({ meterIdentifier: meterNo });
    form.setValue("meterIdentifier", meterNo);

    // Reset usage fields when meter changes
    startTransition(() => {
      const currentMeterIdentifier = queryParams.meterIdentifier;
      if (currentMeterIdentifier !== meterNo) {
        resetAllUsageFields();
      }
    });
  };

  useDebounce(
    async function lookupTariffRatesOnManualEntry() {
      if (queryParams.manualConsumptionEntry === "true") {
        try {
          const {
            meterIdentifier,
            dayUsage,
            nightUsage,
            weekendUsage,
            totalUsage
          } = queryParams;

          if (!meterIdentifier) {
            resetConsumption();
            return;
          }

          if (queryParams.utilityType === UtilityType.GAS) {
            setSplitRatio({
              day: 1,
              night: 0,
              weekend: 0
            });

            form.setValue(
              "dayUsage",
              dayUsage ? Number.parseFloat(dayUsage) : 0
            );
            form.setValue("nightUsage", 0);
            form.setValue("weekendUsage", 0);
            updateTotalUsage();
            return;
          }

          const splitRatioResult =
            await tariffWithSplitRatioMutation.mutateAsync({
              meterIdentifier
            });

          if (!splitRatioResult) {
            throw new Error("No traiff rates found");
          }

          setSplitRatio(splitRatioResult);

          if (splitRatioResult.day) {
            dayUsage && form.setValue("dayUsage", Number.parseFloat(dayUsage));
          } else {
            // If the rate is not available we set it's default value to 0 to skip required field validation
            form.setValue("dayUsage", 0);
          }

          if (splitRatioResult.night) {
            nightUsage &&
              form.setValue("nightUsage", Number.parseFloat(nightUsage));
          } else {
            form.setValue("nightUsage", 0);
          }

          if (splitRatioResult.weekend) {
            weekendUsage &&
              form.setValue("weekendUsage", Number.parseFloat(weekendUsage));
          } else {
            form.setValue("weekendUsage", 0);
          }

          if (isManualSplit) {
            updateTotalUsage();
          } else {
            totalUsage && handleTotalUsageChange(totalUsage, splitRatioResult);
          }
        } catch (e) {
          const error = e as Error;
          log.error(error.message);
          toast({
            title: "Unable to retrieve tariff rates",
            description:
              "Error occurred while retrieving tariff rates. Defaulting to single rate.",
            variant: "destructive"
          });

          // Default to day if no prompts are provided
          setSplitRatio({
            day: 1,
            night: 0,
            weekend: 0
          });
          form.setValue("nightUsage", 0);
          form.setValue("weekendUsage", 0);
        }
      }
    },
    500,
    [queryParams.manualConsumptionEntry, queryParams.meterIdentifier]
  );

  const selectedCompany = useMemo(
    () =>
      companySearchResults?.active.find(
        company =>
          company.businessRef ===
          queryParams.selectedBusinessRef?.toLocaleUpperCase()
      ),
    [queryParams.selectedBusinessRef, companySearchResults?.active]
  );

  const selectedAddress = useMemo(
    () =>
      addressData?.find(
        address => address.id === queryParams.selectedAddressId
      ),
    [queryParams.selectedAddressId, addressData]
  );

  const isMoreThanSingleRate = !!splitRatio?.night || !!splitRatio?.weekend;

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(
    function updateBusinessType() {
      if (!queryParams.selectedBusinessRef || !companySearchResults.active) {
        return;
      }

      const businessType = selectedCompany?.businessType;
      if (!businessType) {
        return;
      }

      setQueryParams({ businessType });
      form.setValue("businessType", businessType);
      form.setValue("businessName", selectedCompany.commercialName);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [queryParams.selectedBusinessRef, companySearchResults.active]
  );

  // Update the handleSubmitAddMeter to pass the current search value
  const handleSubmitAddMeter = useCallback(
    (incompleteLinkMeterData: IncompleteLinkMeterData) => {
      if (incompleteLinkMeterData) {
        if (
          metersRequireLinking &&
          metersRequireLinking.addressId === incompleteLinkMeterData.addressId
        ) {
          const updatedMetersRequireLinking = {
            addressId: metersRequireLinking.addressId,
            mpanNumbers: [
              ...new Set([
                ...(metersRequireLinking.mpanNumbers ?? []),
                ...(incompleteLinkMeterData.mpanNumbers ?? [])
              ])
            ],
            mprnNumbers: [
              ...new Set([
                ...(metersRequireLinking.mprnNumbers ?? []),
                ...(incompleteLinkMeterData.mprnNumbers ?? [])
              ])
            ]
          };
          setMetersRequireLinking(updatedMetersRequireLinking);
        } else {
          setMetersRequireLinking(incompleteLinkMeterData);
        }
      }

      lookupAddressOnSearchInput();
    },
    [metersRequireLinking, lookupAddressOnSearchInput]
  );

  // Add meter numbers to the meterNumbersList if the address and utility type is already selected;

  useDebounce(
    async function updateMeterNumbersList() {
      if (
        !queryParams.selectedAddressId ||
        !addressData ||
        !queryParams.utilityType
      ) {
        return;
      }

      const selectedAddressObj = addressData.find(
        address => address.id === queryParams.selectedAddressId
      );

      if (!selectedAddressObj) {
        return;
      }

      let meterNumbersForSelectedAddress: string[] = [];

      const meterNumbers = selectedAddressObj?.meterNumbers?.filter(
        meter => meter.utilityType === queryParams.utilityType
      );

      if (meterNumbers) {
        meterNumbersForSelectedAddress = meterNumbers.map(
          meter => meter.meterNumber
        );
      } else {
        // If the address does not exist in the entity address table, fetch from Aperture
        try {
          const addressMeterNumbers =
            await addressUtilityMetersMutation.mutateAsync({
              addressId: queryParams.selectedAddressId,
              utilityType: queryParams.utilityType as UtilityType
            });

          if (addressMeterNumbers) {
            meterNumbersForSelectedAddress = [...addressMeterNumbers];
          }
        } catch (error) {
          const err = error as Error;
          log.error("Failed to fetch meter numbers for address", err);
          toast({
            title: "Error fetching meter numbers",
            description: "Could not load meter numbers for this address",
            variant: "destructive"
          });
        }
      }

      if (queryParams.selectedBusinessRef) {
        try {
          const availableSiteMetersForSelectedCompanyAndAddress =
            await companySiteMeterListMutation.mutateAsync({
              companyReg: queryParams.selectedBusinessRef,
              addressId: queryParams.selectedAddressId,
              utilityType: queryParams.utilityType as UtilityType
            });
          meterNumbersForSelectedAddress.push(
            ...availableSiteMetersForSelectedCompanyAndAddress
          );
        } catch (error) {
          const err = error as Error;
          log.error("Failed to fetch company site meters", err);
        }
      }

      // Check if there are any meters that require linking and add those meters to meterNumbersForSelectedAddress based on the selected utility type
      if (
        metersRequireLinking &&
        metersRequireLinking.addressId === queryParams.selectedAddressId
      ) {
        if (queryParams.utilityType === UtilityType.ELECTRICITY) {
          metersRequireLinking?.mpanNumbers &&
            meterNumbersForSelectedAddress.push(
              ...metersRequireLinking.mpanNumbers
            );
        } else if (queryParams.utilityType === UtilityType.GAS) {
          metersRequireLinking?.mprnNumbers &&
            meterNumbersForSelectedAddress.push(
              ...metersRequireLinking.mprnNumbers
            );
        }
      }

      if (!meterNumbersForSelectedAddress.length) {
        setMeterNumbersList([]);
        setQueryParams({ meterIdentifier: "" });
        form.setValue("meterIdentifier", "");
        return;
      }

      // Remove potential duplicates from site meter data before setting the meter numbers list
      const uniqueMeterNumbers = [...new Set(meterNumbersForSelectedAddress)];
      setMeterNumbersList(uniqueMeterNumbers);

      if (
        queryParams.meterIdentifier &&
        meterNumbersForSelectedAddress.length
      ) {
        const selectedMeterNumber = meterNumbersForSelectedAddress.find(
          meterNo => meterNo === queryParams.meterIdentifier
        );

        if (!selectedMeterNumber) {
          setQueryParams({ meterIdentifier: "" });
          form.setValue("meterIdentifier", "");

          return;
        }

        form.setValue("meterIdentifier", selectedMeterNumber);
      }

      // Automatically turn on manual consumption entry if a valid meter identifier is found
      if (meterNumbersForSelectedAddress.length > 0) {
        toggleManualConsumptionEntry(true);
      }
    },
    500,
    [
      queryParams.selectedAddressId,
      queryParams.utilityType,
      queryParams.selectedBusinessRef,
      addressData
    ]
  );

  const handleAddressSelect = (addressId: string) => {
    const selectedAddressObj = addressData?.find(
      address => address.id === addressId
    );

    if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
      toast({
        title: "Invalid address",
        description:
          "Selected address does not have a valid address or postcode",
        variant: "destructive"
      });
      return;
    }

    setQueryParams({ selectedAddressId: addressId });
    form.setValue("siteAddressId", addressId);
    form.setValue("sitePostcode", selectedAddressObj.postcode);
    setIsAddressModalOpen(false);
  };

  const handleBusinessEntitySelect = (businessRefRaw: string) => {
    const businessRef = businessRefRaw.toLocaleUpperCase();
    const selectedCompany = companySearchResults.active.find(
      company => company.businessRef === businessRef
    );
    if (!selectedCompany) {
      return;
    }

    // Batch updates in one React state update
    startTransition(() => {
      setQueryParams({ selectedBusinessRef: businessRef });
      form.setValue("businessNumber", businessRef);
      form.setValue("businessName", selectedCompany.commercialName);
      setIsBusinessModalOpen(false);
    });
  };

  const handleUtilityTypeChange = (data: UtilityType) => {
    if (!data) {
      return;
    }

    setQueryParams({ utilityType: data });
    form.setValue("utilityType", data);
  };

  const setAllUsageFields = (usage: SetUsage, splitRatio: UsageTariffs) => {
    if (splitRatio.day) {
      form.setValue("dayUsage", usage.day);
      setQueryParams({
        dayUsage: usage.day.toString()
      });
    }

    if (splitRatio.night) {
      form.setValue("nightUsage", usage.night);
      setQueryParams({
        nightUsage: usage.night.toString()
      });
    }

    if (splitRatio.weekend) {
      form.setValue("weekendUsage", usage.weekend);
      setQueryParams({
        weekendUsage: usage.weekend.toString()
      });
    }

    form.setValue("totalUsage", usage.totalUsage);
    setQueryParams({
      totalUsage: usage.totalUsage.toString()
    });
  };

  const handleTotalUsageChange = (
    totalUsage: string,
    splitRatio: UsageTariffs
  ) => {
    setQueryParams({ totalUsage });
    if (totalUsage === "") {
      resetAllUsageFields();
      return;
    }

    const floatValue = Number.parseFloat(totalUsage);
    if (!Number.isNaN(floatValue)) {
      const splitUsage = splitUsageByRate({
        totalAnnualUsage: floatValue,
        splitRatio
      });
      setAllUsageFields({ ...splitUsage, totalUsage: floatValue }, splitRatio);
    }
  };

  //make sure if the utility type is updated then the manual consumption entry is set to false until the meter identifier is selected

  useEffect(() => {
    if (queryParams.utilityType) {
      form.setValue("manualConsumptionEntry", false);
      setQueryParams({ manualConsumptionEntry: "false" });
    }
  }, [queryParams.utilityType, form.setValue, setQueryParams]);

  useEffect(() => {
    if (!queryParams.utilityType) {
      return;
    }

    const isManual = !!queryParams.meterIdentifier;

    form.setValue("manualConsumptionEntry", isManual);
    setQueryParams({ manualConsumptionEntry: isManual?.toString() });
  }, [
    queryParams.utilityType,
    queryParams.meterIdentifier,
    form.setValue,
    setQueryParams
  ]);

  const handleAddressSearchInputChange = (addressSearch: string) => {
    setQueryParams({ addressSearch, selectedAddressId: "" });
    form.setValue("siteAddressId", "");
  };

  const handleQuoteSubmit = async () => {
    try {
      const result = await quotesMutation.mutateAsync({
        ...form.getValues(),
        metersRequireLinking: metersRequireLinking ?? undefined
      });
      onSubmit(result.quoteListId);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while getting the new quotes. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to get quotes",
        description,
        variant: "destructive"
      });
    }
  };

  return (
    <FormWrapper
      form={form}
      handleSubmit={handleQuoteSubmit}
      className="my-4 space-y-6"
    >
      <FormField
        control={form.control}
        name="businessNumber"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Business Entity *</FormLabel>{" "}
            <FormControl>
              <div
                ref={businessNumberContainerRef}
                className="flex w-full flex-col"
              >
                <LookUp
                  open={isBusinessModalOpen}
                  onOpenChange={setIsBusinessModalOpen}
                >
                  <LookUpTrigger
                    fieldValue={field.value}
                    isLoading={businessTargetMutation.isPending}
                  >
                    {selectedCompany ? (
                      <CompanySelection {...selectedCompany} />
                    ) : (
                      <span className="font-normal italic">
                        Select a company, charity or sole trader
                      </span>
                    )}
                  </LookUpTrigger>
                  <LookUpContent
                    placeholder="Search by Company name/number, Charity name, or Sole trader name."
                    searchInput={queryParams.businessSearch}
                    onSearchInputChange={businessSearch => {
                      setQueryParams({ businessSearch });
                    }}
                    isLoading={businessTargetMutation.isPending}
                    container={businessNumberContainerRef.current}
                    className="pb-10"
                  >
                    <LookUpGroup className="p-0">
                      {companySearchResults?.active.map(company => (
                        <LookUpItem
                          key={company.businessRef}
                          value={company.businessRef}
                          disabled={company.businessStatus !== "A"}
                          onSelect={handleBusinessEntitySelect}
                        >
                          <CompanySelection {...company} />
                        </LookUpItem>
                      ))}
                    </LookUpGroup>
                    {!!companySearchResults?.disolved.length && (
                      <LookUpGroup heading="Disolved" className="p-0">
                        {companySearchResults.disolved.map(company => (
                          <LookUpItem
                            key={company.businessRef}
                            value={company.businessRef}
                            disabled={company.businessStatus !== "A"}
                          >
                            <CompanySelection {...company} />
                          </LookUpItem>
                        ))}
                      </LookUpGroup>
                    )}
                    <div className="absolute bottom-0 flex w-full justify-center border-t bg-background">
                      <AddNewBusinessTargetTrigger
                        onSubmit={(businessRef: string) => {
                          setQueryParams({
                            businessSearch: businessRef,
                            selectedBusinessRef: businessRef
                          });
                        }}
                      />
                    </div>
                  </LookUpContent>
                </LookUp>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="businessType"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Business Type *</FormLabel>{" "}
            <FormControl>
              <Select
                onValueChange={(data: BusinessType) => {
                  setQueryParams({ businessType: data });
                  form.setValue("businessType", data);
                }}
                value={field.value}
              >
                <SelectTrigger
                  className={cn(!field.value && "text-muted-foreground italic")}
                >
                  <SelectValue placeholder="Select the business type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={BusinessType.LTD}>
                    Limited Company
                  </SelectItem>
                  <SelectItem value={BusinessType.CHARITY}>Charity</SelectItem>
                  <SelectItem value={BusinessType.SOLE_TRADER}>
                    Sole Trader
                  </SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="siteAddressId"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Site Address *</FormLabel>{" "}
            <FormControl>
              <div ref={addressContainerRef} className="flex w-full flex-col">
                <LookUp
                  open={isAddressModalOpen}
                  onOpenChange={setIsAddressModalOpen}
                >
                  <LookUpTrigger
                    fieldValue={field.value}
                    isLoading={isFetchingAddress}
                  >
                    {selectedAddress ? (
                      <AddressSelection address={selectedAddress} />
                    ) : (
                      <span className="font-normal italic">
                        Select a site...
                      </span>
                    )}
                  </LookUpTrigger>
                  <LookUpContent
                    placeholder="Search by postcode, mpan or mprn number" // TODO (Bidur): Also extend search by address line
                    searchInput={queryParams.addressSearch}
                    onSearchInputChange={handleAddressSearchInputChange}
                    isLoading={isFetchingAddress}
                    container={addressContainerRef.current}
                    className="pb-10"
                  >
                    <LookUpGroup className="p-0">
                      {addressData?.map(address => (
                        <LookUpItem
                          key={address.id}
                          value={address.id}
                          onSelect={handleAddressSelect}
                        >
                          <AddressSelection address={address} />
                        </LookUpItem>
                      ))}
                    </LookUpGroup>
                    <div className="absolute bottom-0 flex w-full justify-center border-t bg-background">
                      <AddNewAddressTrigger
                        title="Add New Address"
                        onSubmit={(id: string) => {
                          setQueryParams({
                            addressSearch: id,
                            selectedAddressId: id
                          });
                        }}
                      />
                    </div>
                  </LookUpContent>
                </LookUp>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="utilityType"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Utility Type *</FormLabel>{" "}
            <FormControl>
              <Select
                onValueChange={handleUtilityTypeChange}
                value={field.value || ""}
              >
                <SelectTrigger
                  className={cn(!field.value && "text-muted-foreground italic")}
                >
                  <SelectValue placeholder="Select utility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={UtilityType.ELECTRICITY}>
                    Electricity
                  </SelectItem>
                  <SelectItem value={UtilityType.GAS}>Gas</SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="meterIdentifier"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Meter Identifier *</FormLabel>{" "}
            <FormControl>
              <Select
                onValueChange={handleMeterIdentifierChange}
                value={field.value}
                key={field.value}
                disabled={
                  companySiteMeterListMutation.isPending ||
                  addressUtilityMetersMutation.isPending
                }
              >
                <SelectTrigger
                  className={cn(!field.value && "text-muted-foreground italic")}
                >
                  <SelectValue
                    placeholder={
                      meterNumbersList && !meterNumbersList?.length
                        ? "No meter found for this utility at this address"
                        : "Select a meter number"
                    }
                  >
                    {field.value}
                  </SelectValue>
                </SelectTrigger>
                {!!selectedAddress && (
                  <SelectContent>
                    {meterNumbersList?.map(meterNo => (
                      <SelectItem key={meterNo} value={meterNo}>
                        {meterNo}
                      </SelectItem>
                    ))}
                    <div
                      className={cn(
                        "flex w-full justify-center bg-background",
                        meterNumbersList?.length && "border-t"
                      )}
                    >
                      <Button
                        variant="link"
                        className="w-full hover:no-underline"
                        onClick={() => setIsMeterModalOpen(true)}
                      >
                        <Plus className="mr-1 h-4 w-4" />
                        Add New Meter
                      </Button>
                    </div>
                  </SelectContent>
                )}
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="creditScore"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Credit Score</FormLabel>{" "}
            <FormControl>
              <SuffixInput
                type="number"
                suffix="delphi score"
                value={field.value === undefined ? "" : field.value} // Resolves the uncontrolled to controlled error
                disabled
                onWheel={e => e.currentTarget.blur()}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="contractStartDate"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Contract Start Date *</FormLabel>{" "}
            <DatePickerInput
              date={
                field.value
                  ? convertUTCStringToLocalDate(field.value)
                  : undefined
              }
              setDate={date => {
                if (!date) {
                  return;
                }
                const dateStr = convertLocalDateToUTCString(date);
                setQueryParams({ contractStartDate: dateStr });
                form.setValue("contractStartDate", dateStr);
              }}
              placeholder="Select the start date"
              calendarProps={{
                fromDate: new Date()
              }}
            />
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="upliftRate"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Unit Rate Uplift *</FormLabel>{" "}
            <FormControl>
              <SuffixInput
                type="number"
                suffix="pence"
                placeholder="Any value between 0.1-3 pence"
                value={field.value === undefined ? "" : field.value} // Resolves the uncontrolled to controlled error
                className="placeholder:italic"
                onChange={e => {
                  const { value } = e.target;
                  setQueryParams({ upliftRate: value });
                  if (value === "") {
                    form.resetField("upliftRate");
                  } else {
                    const floatValue = Number.parseFloat(value);
                    if (!Number.isNaN(floatValue)) {
                      form.setValue("upliftRate", floatValue);
                    }
                  }
                }}
                onWheel={e => e.currentTarget.blur()}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="currentSupplier"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Current Supplier *</FormLabel>{" "}
            <FormControl>
              <div
                ref={currentSupplierContainerRef}
                className="flex w-full flex-col"
              >
                <LookUp
                  open={isSupplierModalOpen}
                  onOpenChange={setIsSupplierModalOpen}
                >
                  <LookUpTrigger fieldValue={field.value ?? ""}>
                    <span
                      className={cn(
                        "font-normal",
                        !field.value && "text-muted-foreground italic"
                      )}
                    >
                      {field.value || "Select the current supplier"}
                    </span>
                  </LookUpTrigger>
                  <LookUpContent
                    placeholder="Search provider..."
                    searchInput={queryParams.currentSupplier}
                    onSearchInputChange={currentSupplier => {
                      if (!currentSupplier || currentSupplier === "") {
                        setQueryParams({ currentSupplier: "" });
                        form.setValue("currentSupplier", "");
                      } else {
                        setQueryParams({ currentSupplier });
                      }
                    }}
                    shouldFilter={true}
                    container={currentSupplierContainerRef.current}
                  >
                    <LookUpGroup>
                      {udProvidersList.map(providerName => (
                        <LookUpItem
                          value={providerName}
                          key={providerName}
                          onSelect={() => {
                            setQueryParams({ currentSupplier: providerName });
                            form.setValue("currentSupplier", providerName);
                            setIsSupplierModalOpen(false);
                          }}
                        >
                          {providerName}
                        </LookUpItem>
                      ))}
                    </LookUpGroup>
                  </LookUpContent>
                </LookUp>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="manualConsumptionEntry"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Consumption *</FormLabel>{" "}
            <div className="space-y-4 rounded-md border p-4">
              <div className="flex items-center justify-between gap-2">
                <div className="space-y-0.5">
                  <FormLabel className="text-xs">
                    Turn on Toggle for manual Input
                  </FormLabel>
                  <FormDescription className="text-xs">
                    Alternatively, leave it off for system auto-input.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    className="data-[state=checked]:bg-secondary"
                    disabled={tariffWithSplitRatioMutation.isPending}
                  />
                </FormControl>
                <FormMessage />
              </div>
              {tariffWithSplitRatioMutation.isPending ? (
                <div className="flex flex-row items-center justify-center gap-2 text-sm">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              ) : (
                <div>
                  {!!field.value && splitRatio && (
                    <div className="space-y-2">
                      <FormField
                        control={form.control}
                        name="totalUsage"
                        render={() => {
                          const totalUsage = form.getValues().totalUsage;
                          return (
                            <FormItem className="flex flex-col">
                              <Label className="text-xs">Total EAC</Label>
                              <FormControl>
                                <SuffixInput
                                  type="number"
                                  suffix="kWh"
                                  value={
                                    totalUsage === undefined ? "" : totalUsage
                                  } // Resolves the uncontrolled to controlled error
                                  className="h-8 disabled:bg-gray-800 disabled:text-white"
                                  disabled={isManualSplit}
                                  onChange={e =>
                                    handleTotalUsageChange(
                                      e.target.value,
                                      splitRatio
                                    )
                                  }
                                  onWheel={e => e.currentTarget.blur()}
                                />
                              </FormControl>
                              <FormMessage className="text-xs" />
                            </FormItem>
                          );
                        }}
                      />
                      {isMoreThanSingleRate && (
                        <>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="manualSplit"
                              className="h-3 w-3"
                              iconClassName="h-3 w-3"
                              checked={isManualSplit}
                              onClick={() =>
                                setQueryParams({
                                  manualSplit: isManualSplit ? "false" : "true"
                                })
                              }
                            />
                            <label
                              htmlFor="manualSplit"
                              className="font-light text-xs"
                            >
                              I would like to input the exact split.
                            </label>
                          </div>
                          {isManualSplit && (
                            <>
                              {!!splitRatio.day && (
                                <FormField
                                  control={form.control}
                                  name="dayUsage"
                                  render={() => {
                                    const dayUsage = form.getValues().dayUsage;
                                    return (
                                      <FormItem className="flex flex-col">
                                        <Label className="text-xs">
                                          Day Rate Usage
                                        </Label>
                                        <FormControl>
                                          <SuffixInput
                                            type="number"
                                            suffix="kWh"
                                            value={
                                              dayUsage === undefined
                                                ? ""
                                                : dayUsage
                                            } // Resolves the uncontrolled to controlled error
                                            className="h-8"
                                            onChange={e => {
                                              const { value } = e.target;
                                              setQueryParams({
                                                dayUsage: value
                                              });
                                              if (value === "") {
                                                form.resetField("dayUsage");
                                              } else {
                                                const floatValue =
                                                  Number.parseFloat(value);
                                                if (!Number.isNaN(floatValue)) {
                                                  form.setValue(
                                                    "dayUsage",
                                                    floatValue
                                                  );
                                                }
                                              }
                                              updateTotalUsage();
                                            }}
                                            onWheel={e =>
                                              e.currentTarget.blur()
                                            }
                                          />
                                        </FormControl>
                                        <FormMessage className="text-xs" />
                                      </FormItem>
                                    );
                                  }}
                                />
                              )}
                              {!!splitRatio.night && (
                                <FormField
                                  control={form.control}
                                  name="nightUsage"
                                  render={() => {
                                    const nightUsage =
                                      form.getValues().nightUsage;
                                    return (
                                      <FormItem className="flex flex-col">
                                        <Label className="text-xs">
                                          Night Rate Usage
                                        </Label>
                                        <FormControl>
                                          <SuffixInput
                                            type="number"
                                            suffix="kWh"
                                            value={
                                              nightUsage === undefined
                                                ? ""
                                                : nightUsage
                                            } // Resolves the uncontrolled to controlled error
                                            className="h-8"
                                            onChange={e => {
                                              const { value } = e.target;
                                              setQueryParams({
                                                nightUsage: value
                                              });
                                              if (value === "") {
                                                form.resetField("nightUsage");
                                              } else {
                                                const floatValue =
                                                  Number.parseFloat(value);
                                                if (!Number.isNaN(floatValue)) {
                                                  form.setValue(
                                                    "nightUsage",
                                                    floatValue
                                                  );
                                                }
                                              }
                                              updateTotalUsage();
                                            }}
                                            onWheel={e =>
                                              e.currentTarget.blur()
                                            }
                                          />
                                        </FormControl>
                                        <FormMessage className="text-xs" />
                                      </FormItem>
                                    );
                                  }}
                                />
                              )}
                              {!!splitRatio.weekend && (
                                <FormField
                                  control={form.control}
                                  name="weekendUsage"
                                  render={() => {
                                    const weekendUsage =
                                      form.getValues().weekendUsage;
                                    return (
                                      <FormItem className="flex flex-col">
                                        <Label className="text-xs">
                                          Weekend Rate Usage
                                        </Label>
                                        <FormControl>
                                          <SuffixInput
                                            type="number"
                                            suffix="kWh"
                                            value={
                                              weekendUsage === undefined
                                                ? ""
                                                : weekendUsage
                                            } // Resolves the uncontrolled to controlled error
                                            className="h-8"
                                            onChange={e => {
                                              const { value } = e.target;
                                              setQueryParams({
                                                weekendUsage: value
                                              });
                                              if (value === "") {
                                                form.resetField("weekendUsage");
                                              } else {
                                                const floatValue =
                                                  Number.parseFloat(value);
                                                if (!Number.isNaN(floatValue)) {
                                                  form.setValue(
                                                    "weekendUsage",
                                                    floatValue
                                                  );
                                                }
                                              }
                                              updateTotalUsage();
                                            }}
                                            onWheel={e =>
                                              e.currentTarget.blur()
                                            }
                                          />
                                        </FormControl>
                                        <FormMessage className="text-xs" />
                                      </FormItem>
                                    );
                                  }}
                                />
                              )}
                            </>
                          )}
                        </>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="isSmartMeter"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Smart Meter</FormLabel>{" "}
            <div className="flex items-center justify-between gap-2 rounded-md border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-xs">
                  Turn on Smart Meter Toggle
                </FormLabel>
                <FormDescription className="text-xs">
                  Include the Smart Meter to provide more offers
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={value => {
                    setQueryParams({ isSmartMeter: value.toString() });
                    form.setValue("isSmartMeter", value);
                  }}
                  className="data-[state=checked]:bg-secondary"
                />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="isCustomQuotesOnly"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Custom Quotes Only</FormLabel>{" "}
            <div className="flex items-center justify-between gap-2 rounded-md border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-xs">
                  Turn on Custom Quotes Toggle
                </FormLabel>
                <FormDescription className="text-xs">
                  An empty quote list is created to add custom quotes
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={value => {
                    setQueryParams({ isCustomQuotesOnly: value.toString() });
                    form.setValue("isCustomQuotesOnly", value);
                  }}
                  className="data-[state=checked]:bg-secondary"
                />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />
      <Button
        type="submit"
        disabled={quotesMutation.isPending}
        variant="secondary"
        className="w-full"
      >
        {quotesMutation.isPending && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        Confirm
      </Button>
      <AddNewMeterModal
        isModalOpen={isMeterModalOpen}
        setIsModalOpen={setIsMeterModalOpen}
        addressId={selectedAddress?.id ?? ""}
        companyReg={queryParams.selectedBusinessRef}
        onSubmit={handleSubmitAddMeter}
      />
    </FormWrapper>
  );
}
