import type { ElectricityUsage, GasUsage } from "@prisma/client";
import type { QuoteList_And_Quotes } from "@watt/api/src/router";
import { useQuoteStore } from "@watt/crm/store/quote";
import { trpcClient } from "@watt/crm/utils/api";
import { QuoteStatus } from "@watt/db/src/enums";
import { Loader2 } from "lucide-react";
import { useEffect } from "react";

import { UpdateQuoteInputSchema } from "@watt/api/src/types/quote/quote-schemas";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  convertLocalDateToUTCString,
  convertUTCStringToLocalDate,
  dateFormats,
  formatDate
} from "@watt/common/src/utils/format-date";
import { DatePickerInput } from "@watt/crm/components/date-picker-input";
import { SuffixInput } from "@watt/crm/components/suffix-input";
import { Button } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormWrapper
} from "@watt/crm/components/ui/form";
import { Switch } from "@watt/crm/components/ui/switch";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";

type QuoteUpdateSearchFormProps = {
  submitButtonRef: React.RefObject<HTMLButtonElement | null>;
  quoteList: NonNullable<QuoteList_And_Quotes["quoteList"]>;
  utilityUsage: ElectricityUsage | GasUsage;
  meterIdentifier: string;
  onSubmit: (quoteListId: string) => void;
  showCapacityCharge: boolean;
};

export function QuoteUpdateSearchForm({
  submitButtonRef,
  quoteList,
  utilityUsage,
  meterIdentifier,
  onSubmit,
  showCapacityCharge
}: QuoteUpdateSearchFormProps) {
  const {
    id,
    siteMeterId,
    isCustomQuotesOnly,
    upliftRate,
    contractStartDate,
    status
  } = quoteList;
  const updateQuotesMutation = trpcClient.quote.updateQuotes.useMutation();
  const {
    bespokeSupplierUplifts,
    resetBespokeSupplierUplifts,
    updateQuoteIsDirty,
    setUpdateQuoteIsDirty
  } = useQuoteStore(state => ({
    bespokeSupplierUplifts: state.bespokeSupplierUplifts,
    resetBespokeSupplierUplifts: state.resetBespokeSupplierUplifts,
    updateQuoteIsDirty: state.updateQuoteIsDirty,
    setUpdateQuoteIsDirty: state.setUpdateQuoteIsDirty
  }));

  const form = useZodForm({
    schema: UpdateQuoteInputSchema,
    mode: "onChange",
    defaultValues: {
      quoteListId: id,
      siteMeterId,
      meterIdentifier,
      contractStartDate: formatDate(
        contractStartDate,
        dateFormats.YYYY_MM_DD_HYPHEN
      ),
      isCustomQuotesOnly: isCustomQuotesOnly ?? false,
      upliftRate: upliftRate ?? undefined,
      totalUsage: utilityUsage.totalUsage ?? undefined,
      capacityFigureKva:
        showCapacityCharge && "capacityFigureKva" in utilityUsage
          ? (utilityUsage.capacityFigureKva ?? undefined)
          : undefined
    }
  });

  useEffect(() => {
    const isDirty = status === QuoteStatus.EXPIRED || form.formState.isDirty;
    setUpdateQuoteIsDirty(isDirty);
  }, [form.formState.isDirty, setUpdateQuoteIsDirty, status]);

  const handleUpdateSearch = async () => {
    if (
      showCapacityCharge &&
      (form.getValues().capacityFigureKva === null ||
        form.getValues().capacityFigureKva === undefined)
    ) {
      form.setError("capacityFigureKva", {
        message: "Please enter a valid kVA figure"
      });
      return;
    }

    try {
      const result = await updateQuotesMutation.mutateAsync({
        ...form.getValues(),
        bespokeSupplierUplifts
      });

      setUpdateQuoteIsDirty(false);

      // User is already on the quote details page
      if (id === result.quoteListId) {
        return;
      }

      resetBespokeSupplierUplifts();
      onSubmit(result.quoteListId);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      toast({
        title: "Unable to get quotes",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="ml-10 py-4 xl:ml-14">
      <FormWrapper
        form={form}
        handleSubmit={handleUpdateSearch}
        className="flex items-end justify-between overflow-auto py-2"
      >
        <div className="mr-4 flex items-center space-x-2 *:w-60">
          <FormField
            control={form.control}
            name="contractStartDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Contract Start Date *</FormLabel>{" "}
                <DatePickerInput
                  date={
                    field.value
                      ? convertUTCStringToLocalDate(field.value)
                      : undefined
                  }
                  placeholder="Select the start date"
                  setDate={date => {
                    if (!date) {
                      return;
                    }
                    form.setValue(
                      "contractStartDate",
                      convertLocalDateToUTCString(date),
                      {
                        shouldDirty: true
                      }
                    );
                  }}
                  calendarProps={{
                    fromDate: new Date()
                  }}
                />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="upliftRate"
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col">
                  <FormLabel>Unit Rate Uplift *</FormLabel>{" "}
                  <FormControl>
                    <SuffixInput
                      {...field}
                      type="number"
                      suffix="pence/kWh"
                      step="0.01"
                      placeholder="Between 0.1-3 pence"
                      className=" placeholder:italic"
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
          <FormField
            control={form.control}
            name="totalUsage"
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col">
                  <FormLabel>Total Usage *</FormLabel>{" "}
                  <FormControl>
                    <SuffixInput
                      {...field}
                      type="number"
                      suffix="kWh/year"
                      step="1000"
                      placeholder="Enter current usage"
                      className=" placeholder:italic"
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
          {showCapacityCharge && (
            <FormField
              control={form.control}
              name="capacityFigureKva"
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col">
                    <FormLabel>Capacity Figure (kVA) *</FormLabel>{" "}
                    <FormControl>
                      <SuffixInput
                        {...field}
                        type="number"
                        suffix="per day"
                        step="1"
                        placeholder="Enter capacity figure"
                        className=" placeholder:italic"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
          )}
          <FormField
            control={form.control}
            name="isCustomQuotesOnly"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Custom Quotes Only</FormLabel>{" "}
                <div className="flex h-10 items-center justify-between gap-2 rounded-md border p-2">
                  <FormDescription className="text-xs">
                    Ignores UD quote
                  </FormDescription>
                  <FormControl>
                    <Switch
                      size="sm"
                      checked={field.value}
                      onCheckedChange={value => {
                        form.setValue("isCustomQuotesOnly", value, {
                          shouldDirty: true
                        });
                      }}
                      className="data-[state=checked]:bg-secondary"
                    />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />
        </div>
        <Button
          ref={submitButtonRef}
          type="submit"
          variant="secondary"
          disabled={updateQuotesMutation.isPending || !updateQuoteIsDirty}
        >
          {updateQuotesMutation.isPending && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          Update Search
        </Button>
      </FormWrapper>
      {Object.keys(form.formState.errors).length > 0 && (
        <span className="text-destructive text-sm">
          Please enter valid values for the fields
        </span>
      )}
    </div>
  );
}
