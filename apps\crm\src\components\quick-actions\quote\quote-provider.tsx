"use client";

import {
  ContractModalTypes,
  useContractModal
} from "@watt/crm/hooks/use-contract-modal";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import {
  type QuoteModalQueryParams,
  QuoteModalTypes,
  useQuoteModal
} from "@watt/crm/hooks/use-quote-modal";

import { ContractType } from "@watt/db/src/enums";
import { QuoteDataConfirmation } from "./form/quote-data-confirmation";
import { QuoteDetails } from "./results/quote-details";

export function QuoteProvider() {
  const { queryParams } = useQueryParams<QuoteModalQueryParams>();
  const { openQuoteModal, closeQuoteModal } = useQuoteModal();
  const { openContractModal } = useContractModal();

  const handleModalClose = () => {
    closeQuoteModal();
  };

  const handleQuoteCreationSuccess = (quoteListId: string) => {
    openQuoteModal(QuoteModalTypes.viewQuotes, {
      quoteListId
    });
  };

  const handleCreateContract = (
    contractType: ContractType,
    quoteId: string,
    contactId: string
  ) => {
    const isVerbal = contractType === ContractType.VERBAL;

    openContractModal(
      isVerbal
        ? ContractModalTypes.createVerbalContract
        : ContractModalTypes.createWrittenContract,
      {
        quoteId,
        contactId
      }
    );
  };

  return (
    <>
      <QuoteDataConfirmation
        isOpen={queryParams.modal === QuoteModalTypes.getQuotes}
        closeModal={handleModalClose}
        handleSumbit={handleQuoteCreationSuccess}
      />

      <QuoteDetails
        isOpen={queryParams.modal === QuoteModalTypes.viewQuotes}
        closeModal={handleModalClose}
        handleCreateContract={handleCreateContract}
      />
    </>
  );
}
