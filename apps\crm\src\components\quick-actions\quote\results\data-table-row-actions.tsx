import { QuoteStatus } from "@prisma/client";
import { TRPCClientError } from "@trpc/client";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2, MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import { useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@watt/crm/components/ui/alert-dialog";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@watt/crm/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { toast } from "@watt/crm/components/ui/use-toast";

import {
  CustomQuoteForm,
  type CustomQuoteFormProps
} from "../form/custom-quote-form";

export interface CustomQuoteRowActionProps extends CustomQuoteFormProps {
  quoteListStatus: QuoteStatus;
  quoteStatus: QuoteStatus;
}

export function DataTableRowActions({
  utilityType,
  tariffRates,
  showCapacityCharge,
  quoteListId,
  quoteListStatus,
  quoteStatus,
  customQuoteData,
  onSubmitForm
}: CustomQuoteRowActionProps) {
  const [dropdownIsOpen, setDropdownOpen] = useState(false);
  const [customQuoteModalOpen, setCustomQuoteModalOpen] = useState(false);
  const deleteCustomQuoteMutation =
    trpcClient.customQuote.deleteCustomQuoteFromQuoteList.useMutation();

  const isEditDisabled =
    quoteStatus !== QuoteStatus.GENERATED ||
    quoteListStatus === QuoteStatus.ACCEPTED ||
    quoteListStatus === QuoteStatus.EXPIRED;

  const handleOpenDropdown = (open: boolean) => {
    if (quoteListStatus === QuoteStatus.ACCEPTED) {
      toast({
        title: "Quote accepted",
        description:
          "One of the quotes has been accepted and can no longer be edited.",
        variant: "destructive"
      });
      return;
    }

    if (quoteListStatus === QuoteStatus.EXPIRED) {
      toast({
        title: "Quote expired",
        description: "This quote has expired and can no longer be edited.",
        variant: "destructive"
      });
      return;
    }

    if (isEditDisabled) {
      toast({
        title: "Unable to edit custom quote",
        description:
          "This custom quote has been sent to the customer and can no longer be edited.",
        variant: "destructive"
      });
      return;
    }
    setDropdownOpen(open);
  };

  const handleSubmitForm = (id: string) => {
    onSubmitForm(id);
    setCustomQuoteModalOpen(false);
  };

  const handleDeleteCustomQuote = async () => {
    if (!customQuoteData) {
      toast({
        title: "Unable to delete custom quote",
        description: "Custom quote data not found",
        variant: "destructive"
      });
      setDropdownOpen(false);
      return;
    }
    try {
      await deleteCustomQuoteMutation.mutateAsync({
        customQuoteId: customQuoteData.id
      });
      toast({
        title: "Custom quote deleted",
        description: `The custom quote for ${customQuoteData.provider.displayName} has been deleted`,
        variant: "success"
      });
    } catch (e) {
      const error = e as Error;
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Unable to delete custom quote. The quote might be in use."
          : error.message;
      toast({
        title: "Error deleting the custom quote",
        description,
        variant: "destructive"
      });
    }
    setDropdownOpen(false);
  };

  return (
    <DropdownMenu open={dropdownIsOpen} onOpenChange={handleOpenDropdown}>
      <DropdownMenuTrigger
        asChild
        disabled={deleteCustomQuoteMutation.isPending}
      >
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          disabled={isEditDisabled}
        >
          {deleteCustomQuoteMutation.isPending ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <MoreHorizontal className="h-4 w-4" />
          )}
          <span className="sr-only fixed">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <Dialog
          onOpenChange={setCustomQuoteModalOpen}
          open={customQuoteModalOpen}
        >
          <DialogTrigger asChild>
            <DropdownMenuItem onSelect={event => event.preventDefault()}>
              <Pencil className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
              Edit
            </DropdownMenuItem>
          </DialogTrigger>
          <DialogContent
            onPointerDownOutside={e => e.preventDefault()}
            className="flex max-w-2xl flex-col px-2"
          >
            <DialogHeader className="space-y-4 px-4">
              <DialogTitle>Edit Custom Quote</DialogTitle>
              <DialogDescription className="space-y-1 italic">
                Edit the custom quote for this quote list.
              </DialogDescription>
            </DialogHeader>
            <CustomQuoteForm
              utilityType={utilityType}
              tariffRates={tariffRates}
              showCapacityCharge={showCapacityCharge}
              onSubmitForm={handleSubmitForm}
              customQuoteData={customQuoteData}
              quoteListId={quoteListId}
            />
          </DialogContent>
        </Dialog>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem onSelect={event => event.preventDefault()}>
              <Trash2 className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
              Delete
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                Are you sure you want to delete this custom quote?
              </AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                selected custom quote.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteCustomQuote}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
