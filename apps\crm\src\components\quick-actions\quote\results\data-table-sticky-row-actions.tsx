"use client";

import { QuoteStatus, UtilityType } from "@prisma/client";
import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import type { TariffRates } from "@watt/common/src/utils/split-usage-by-rate";
import { Button } from "@watt/crm/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";
import { Pen } from "lucide-react";
import { useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "../../../ui/dialog";
import { EditCurrentSupplyForm } from "../form/edit-current-supply-form";

type DataTableStickyRowActionsProps = {
  quote: FindUniqueQuoteListSelectQuotesGetPayload | null;
  tariffRates: TariffRates;
  quoteListStatus: QuoteStatus | undefined;
  showCapacityCharge: boolean;
  updateColumnVisibility: (column: string, visible: boolean) => void;
};

export function DataTableStickyRowActions({
  quote,
  tariffRates,
  quoteListStatus,
  showCapacityCharge,
  updateColumnVisibility
}: DataTableStickyRowActionsProps) {
  const { shouldRender, initialData } = useMemo(() => {
    const shouldRender =
      quote &&
      (quote.utilityType === UtilityType.ELECTRICITY ||
        quote.utilityType === UtilityType.GAS);

    const initialData = shouldRender
      ? quote.utilityType === UtilityType.ELECTRICITY && quote.electricQuote
        ? {
            id: quote.id,
            utilityType: UtilityType.ELECTRICITY,
            unitRate: quote.electricQuote.unitRate,
            nightUnitRate: quote.electricQuote.nightUnitRate ?? undefined,
            weekendUnitRate: quote.electricQuote.weekendUnitRate ?? undefined,
            standingCharge: quote.electricQuote.standingCharge ?? undefined,
            capacityChargeKva:
              quote.electricQuote.capacityChargeKva ?? undefined,
            duration: quote.duration
          }
        : {
            id: quote.id,
            utilityType: UtilityType.GAS,
            unitRate: quote.gasQuote?.unitRate,
            standingCharge: quote.gasQuote?.standingCharge,
            duration: quote.duration
          }
      : null;

    return { shouldRender, initialData };
  }, [quote]);

  if (!shouldRender) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        {quoteListStatus === QuoteStatus.EXPIRED ? (
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              className="flex size-8 p-0 data-[state=open]:bg-muted"
              onClick={() =>
                toast({
                  title: "Quote expired",
                  description:
                    "This quote has expired and can no longer be edited.",
                  variant: "destructive"
                })
              }
            >
              <Pen className="size-4" />
              <span className="sr-only fixed">Open menu</span>
            </Button>
          </TooltipTrigger>
        ) : (
          <Dialog>
            <DialogTrigger asChild>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex size-8 p-0 data-[state=open]:bg-muted"
                >
                  <Pen className="size-4" />
                  <span className="sr-only fixed">Open menu</span>
                </Button>
              </TooltipTrigger>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Current Supply</DialogTitle>
              </DialogHeader>
              <EditCurrentSupplyForm
                initialData={initialData}
                tariffRates={tariffRates}
                showCapacityCharge={showCapacityCharge}
                updateColumnVisibility={updateColumnVisibility}
              />
            </DialogContent>
          </Dialog>
        )}
        <TooltipContent>
          {quoteListStatus === QuoteStatus.EXPIRED
            ? "This quote has expired and can no longer be edited."
            : "Edit current supplier"}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
