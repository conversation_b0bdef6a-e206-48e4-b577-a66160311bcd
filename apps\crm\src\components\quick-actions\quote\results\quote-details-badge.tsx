import type { ElectricityUsage } from "@prisma/client";
import { formatkWhConsumption } from "@watt/common/src/utils/format-kwh-consumption";
import { copyToClipboard } from "@watt/crm/utils/copy";

import { Button } from "@watt/crm/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@watt/crm/components/ui/card";

type ConsumptionData = Partial<
  Pick<
    ElectricityUsage,
    "dayUsage" | "nightUsage" | "weekendUsage" | "totalUsage"
  >
>;

export function QuoteDetailsBadge({
  label,
  value
}: { label: string; value: string }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3 pb-0.5">
        <CardTitle className="font-medium text-xs">{label}</CardTitle>
      </CardHeader>
      <CardContent className="p-3">
        <Button
          variant="link"
          onClick={() => copyToClipboard(value, label)}
          className="font-bold text-lg"
        >
          {value}
        </Button>
      </CardContent>
    </Card>
  );
}

export function renderConsumptionBadges({
  dayUsage,
  nightUsage,
  weekendUsage,
  totalUsage
}: ConsumptionData) {
  if (!totalUsage) {
    return;
  }

  if (!nightUsage && !weekendUsage) {
    return (
      <QuoteDetailsBadge
        label="Consumption"
        value={formatkWhConsumption(totalUsage)}
      />
    );
  }

  return (
    <>
      {!!dayUsage && (
        <QuoteDetailsBadge
          label="Day Consumption"
          value={formatkWhConsumption(dayUsage)}
        />
      )}
      {!!nightUsage && (
        <QuoteDetailsBadge
          label="Night Consumption"
          value={formatkWhConsumption(nightUsage)}
        />
      )}
      {!!weekendUsage && (
        <QuoteDetailsBadge
          label="Weekend Consumption"
          value={formatkWhConsumption(weekendUsage)}
        />
      )}
    </>
  );
}
