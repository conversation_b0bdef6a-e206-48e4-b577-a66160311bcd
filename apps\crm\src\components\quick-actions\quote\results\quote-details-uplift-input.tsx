import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import { determineApplicableUplift } from "@watt/api/src/utils/quote";
import upliftMaxima from "@watt/common/src/constants/uplift-maxima.json";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { normalizeSupplierName } from "@watt/common/src/utils/normalize-supplier-name";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@watt/crm/components/ui/alert-dialog";
import { Button } from "@watt/crm/components/ui/button";
import { Input } from "@watt/crm/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/crm/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useQuoteStore } from "@watt/crm/store/quote";
import { QuoteStatus, QuoteType, UtilityType } from "@watt/db/src/enums";
import { Undo2 } from "lucide-react";
import { useMemo, useState } from "react";

type QuoteDetailsUpliftInputProps = {
  quote: FindUniqueQuoteListSelectQuotesGetPayload;
  baseUplift?: number;
  quoteListStatus?: QuoteStatus;
  isCurrentProviderRow?: boolean;
};

const MAX_CUSTOM_UPLIFT = "3";

export function QuoteDetailsUpliftInput({
  quote,
  baseUplift,
  quoteListStatus,
  isCurrentProviderRow
}: QuoteDetailsUpliftInputProps) {
  const {
    bespokeSupplierUplifts,
    removeBespokeSupplierUplift,
    setBespokeSupplierUplift
  } = useQuoteStore(state => ({
    bespokeSupplierUplifts: state.bespokeSupplierUplifts,
    setBespokeSupplierUplift: state.setBespokeSupplierUplift,
    removeBespokeSupplierUplift: state.removeBespokeSupplierUplift
  }));

  const { utilityType, provider, duration, status } = quote;
  const utilityQuote =
    utilityType === UtilityType.ELECTRICITY
      ? quote.electricQuote
      : quote.gasQuote;
  const supplyType =
    utilityType === UtilityType.ELECTRICITY ? "electric" : "gas";
  const supplierUdcoreId = provider.udcoreId;
  const [newUpliftRate, setNewUpliftRate] = useState(
    utilityQuote?.unitRateUplift?.toString() || ""
  );
  const [isInvalidInput, setIsInvalidInput] = useState(false);

  const matchedUpliftMaxima = useMemo(() => {
    if (!supplierUdcoreId) {
      return null;
    }

    if (quote.type === QuoteType.CUSTOM || quote.type === QuoteType.BESPOKE) {
      return {
        uplifts: MAX_CUSTOM_UPLIFT
      };
    }
    return upliftMaxima.find(u => {
      return (
        u.supplyType === supplyType &&
        normalizeSupplierName(u.supplier) ===
          normalizeSupplierName(supplierUdcoreId)
      );
    });
  }, [quote.type, supplyType, supplierUdcoreId]);

  const showResetUpliftButton = useMemo(() => {
    if (quote.type === QuoteType.CUSTOM || quote.type === QuoteType.BESPOKE) {
      return false;
    }

    return bespokeSupplierUplifts.some(u => u.quoteId === quote.id);
  }, [quote.type, bespokeSupplierUplifts, quote.id]);

  const allowedUplift = useMemo(() => {
    if (!matchedUpliftMaxima) {
      return;
    }

    if (matchedUpliftMaxima.uplifts.includes(";")) {
      return matchedUpliftMaxima.uplifts
        .split(";")
        .map(u => Number.parseFloat(u))
        .filter(u => !Number.isNaN(u) && u > 0)
        .sort((a, b) => a - b);
    }
    return Number.parseFloat(matchedUpliftMaxima.uplifts);
  }, [matchedUpliftMaxima]);

  const inputUpliftIsValid = (uplift: number) => {
    if (!allowedUplift) {
      return false;
    }

    if (Array.isArray(allowedUplift)) {
      return allowedUplift.includes(uplift);
    }

    return uplift > 0 && uplift <= allowedUplift;
  };

  const handleInputValueChange = (newUplift: string) => {
    if (!supplierUdcoreId) {
      return;
    }

    setNewUpliftRate(newUplift);
    const parsedNewUplift = Number.parseFloat(newUplift);
    if (Number.isNaN(parsedNewUplift) || !inputUpliftIsValid(parsedNewUplift)) {
      setIsInvalidInput(true);
      return;
    }

    setIsInvalidInput(false);
    setBespokeSupplierUplift(
      {
        quoteId: quote.id,
        quoteType: quote.type,
        utilityType: utilityType,
        supplierId: supplierUdcoreId,
        duration: duration,
        planType: utilityQuote.contractType,
        uplift: parsedNewUplift,
        previousUplift: utilityQuote.unitRateUplift
      },
      {
        defaultUplift: utilityQuote.unitRateUplift,
        isBespokeUplift: utilityQuote.isBespokeUplift
      }
    );
  };

  const checkIfInputIsAllowed = (input: string) => {
    const parsedInput = Number.parseFloat(input);
    if (!allowedUplift) {
      return;
    }

    // Select input checks
    if (Array.isArray(allowedUplift)) {
      if (allowedUplift.includes(parsedInput)) {
        return;
      }
      // Reset the input to the current uplift if the input is invalid
      handleInputValueChange(utilityQuote.unitRateUplift.toString());
      return;
    }

    // Number input checks
    if (Number.isNaN(parsedInput)) {
      // Reset the input to the current uplift if the input is invalid
      setNewUpliftRate(utilityQuote.unitRateUplift.toString());
      setIsInvalidInput(false);
    }
    if (parsedInput <= 0) {
      // Set the input to the minimum allowed uplift
      handleInputValueChange("0.1");
    }
    if (parsedInput > allowedUplift) {
      // Set the input to the maximum allowed uplift
      handleInputValueChange(allowedUplift.toString());
    }
  };

  const hanldeResetUplift = () => {
    if (!matchedUpliftMaxima) {
      return;
    }

    removeBespokeSupplierUplift(quote.id);
    const inputUplift = baseUplift ?? utilityQuote.unitRateUplift;
    const resetUpliftValue = determineApplicableUplift(
      inputUplift,
      matchedUpliftMaxima.uplifts
    ).toString();
    setNewUpliftRate(resetUpliftValue);
  };

  const isInputDisabled =
    (quote.type === QuoteType.CUSTOM || quote.type === QuoteType.BESPOKE) &&
    (status !== QuoteStatus.GENERATED ||
      quoteListStatus === QuoteStatus.ACCEPTED ||
      quoteListStatus === QuoteStatus.EXPIRED);

  const handleDisabledClick = () => {
    if (!isInputDisabled) {
      return;
    }

    if (quote.type === QuoteType.CUSTOM || quote.type === QuoteType.BESPOKE) {
      if (quoteListStatus === QuoteStatus.ACCEPTED) {
        toast({
          title: "Quote accepted",
          description:
            "One of the quote has been accepted and can no longer be edited.",
          variant: "destructive"
        });
        return;
      }

      if (quoteListStatus === QuoteStatus.EXPIRED) {
        toast({
          title: "Quote expired",
          description: "This quote has expired and can no longer be edited.",
          variant: "destructive"
        });
        return;
      }

      if (isInputDisabled) {
        toast({
          title: "Unable to edit custom quote",
          description:
            "This custom quote has been sent to the customer and can no longer be edited.",
          variant: "destructive"
        });
      }
    }
  };

  if (isCurrentProviderRow) {
    return null;
  }

  if (!utilityQuote || !supplierUdcoreId) {
    return <span className="text-muted-foreground italic">N/A</span>;
  }

  if (!matchedUpliftMaxima) {
    return <span className="text-muted-foreground italic">Invalid uplift</span>;
  }

  return (
    <div className="flex">
      <Button
        disabled={isInputDisabled}
        onClick={handleDisabledClick}
        variant="none"
        size="none"
        className={cn(
          "cursor-default justify-start",
          isInputDisabled && "pointer-events-none cursor-not-allowed"
        )}
      >
        {Array.isArray(allowedUplift) ? (
          <div>
            <Select
              onValueChange={value => handleInputValueChange(value)}
              value={newUpliftRate}
              disabled={isInputDisabled}
            >
              <SelectTrigger className="w-16 rounded-none border-0 border-b bg-transparent hover:rounded-sm focus:rounded-sm">
                <SelectValue placeholder="Uplift" />
              </SelectTrigger>
              <SelectContent className="min-w-20">
                {allowedUplift.map((uplift, index) => (
                  // TODO: Add a key to the allowedUplift as uplift values are not unique
                  <SelectItem
                    // biome-ignore lint/suspicious/noArrayIndexKey: <allowed uplifts don't have unique keys>
                    key={`${uplift}-${index}`}
                    value={uplift.toString()}
                    className="w-20"
                  >
                    {uplift.toFixed(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ) : (
          <Input
            type="number"
            onChange={e => handleInputValueChange(e.target.value)}
            onBlur={e => checkIfInputIsAllowed(e.target.value)}
            onWheel={e => e.currentTarget.blur()}
            className={cn(
              "w-16 rounded-none border-0 border-b bg-transparent text-center hover:rounded-sm focus:rounded-sm",
              isInvalidInput && "focus-visible:ring-destructive",
              isInputDisabled && "pointer-events-none"
            )}
            value={newUpliftRate}
            disabled={isInputDisabled}
          />
        )}
      </Button>
      {showResetUpliftButton && (
        <AlertDialog>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    className="group ml-4 flex h-8 w-8 rounded-full p-0 hover:bg-destructive"
                  >
                    <Undo2 className="h-4 w-4 group-hover:text-white" />
                    <span className="sr-only fixed">Revert uplift</span>
                  </Button>
                </AlertDialogTrigger>
              </TooltipTrigger>
              <TooltipContent>
                Click here to revert the inline uplift to the base uplift value.
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                Are you sure to revert the inline uplift to the base uplift
                value?
              </AlertDialogTitle>
              <AlertDialogDescription>
                This action can not be undone once confirmed. If you wish to
                proceed, please click on Confirm.
              </AlertDialogDescription>
              <AlertDialogDescription className="text-destructive text-sm italic">
                *Important: After confirming, the uplift field will be reverted,
                but you will need to click the &quot;Update Search&quot; button
                to reflect the latest total annual price in your results.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={hanldeResetUplift}>
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}
