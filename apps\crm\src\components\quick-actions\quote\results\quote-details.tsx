"use client";

import {
  type ElectricityUsage,
  type GasUsage,
  UtilityType
} from "@prisma/client";
import type { QuoteList_And_Quotes } from "@watt/api/src/router/quote";
import { log } from "@watt/common/src/utils/axiom-logger";
import { formatStringToDateShort } from "@watt/common/src/utils/format-date";
import { getFullMeterIdentifier } from "@watt/common/src/utils/get-full-meter-identifier";
import { DataTableSkeleton } from "@watt/crm/components/data-table/data-table-skeleton";
import { MpxnInputField } from "@watt/crm/components/mpxn/mpxn-input-field";
import { QuoteResultsDataTable } from "@watt/crm/components/quick-actions/quote/results/quote-results-data-table";
import { Button } from "@watt/crm/components/ui/button";
import {
  Drawer,
  DrawerContentWithDirection,
  DrawerTitle
} from "@watt/crm/components/ui/drawer";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { VisuallyHidden } from "@watt/crm/components/ui/visually-hidden";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import type { QuoteModalQueryParams } from "@watt/crm/hooks/use-quote-modal";
import { useQuoteStore } from "@watt/crm/store/quote";
import { trpcClient } from "@watt/crm/utils/api";
import type { ContractType } from "@watt/db/src/enums";
import { XIcon } from "lucide-react";
import { type ComponentRef, useEffect, useMemo, useRef } from "react";
import { QuoteUpdateSearchForm } from "../form/quote-update-search-form";
import {
  QuoteDetailsBadge,
  renderConsumptionBadges
} from "./quote-details-badge";

type QuoteDetailsDrawerProps = {
  isOpen: boolean;
  closeModal: () => void;
  children: React.ReactNode;
};

export function QuoteDetailsDrawer({
  isOpen,
  closeModal,
  children
}: QuoteDetailsDrawerProps) {
  return (
    <Drawer direction="right" dismissible={false} open={isOpen}>
      <DrawerContentWithDirection
        variant="right"
        scroll={false}
        className="w-[90%]"
        onEscapeKeyDown={closeModal}
      >
        <VisuallyHidden>
          <DrawerTitle>Quote Details</DrawerTitle>
        </VisuallyHidden>
        {children}
      </DrawerContentWithDirection>
    </Drawer>
  );
}

type QuoteDetailsContentProps = {
  closeModal: () => void;
  handleCreateContract: (
    contractType: ContractType,
    quoteId: string,
    contactId: string
  ) => void;
  handleUpdateSearchSubmit: (newQuoteListId: string) => void;
  quotesResult: NonNullable<QuoteList_And_Quotes>;
};

export function QuoteDetailsContent({
  closeModal,
  handleCreateContract,
  handleUpdateSearchSubmit,
  quotesResult
}: QuoteDetailsContentProps) {
  const { quoteList, additionalMeterData } = quotesResult;

  const buttonRef = useRef<ComponentRef<"button">>(null);

  if (!quoteList) {
    return null;
  }

  const { createdAt, siteMeter, electricityUsage, gasUsage, id } = quoteList;

  const { utilityType, electricSiteMeter, gasSiteMeter } = siteMeter;

  const utilityUsage = (electricityUsage ?? gasUsage) as
    | ElectricityUsage
    | GasUsage;

  const meterIdentifier =
    utilityType === UtilityType.ELECTRICITY
      ? electricSiteMeter?.mpanValue
      : gasSiteMeter?.mprnValue;

  if (!meterIdentifier) {
    log.error("Meter identifier is null");
    return null;
  }

  const fullMeterIdentifier = useMemo(
    () =>
      getFullMeterIdentifier(
        utilityType === UtilityType.ELECTRICITY
          ? electricSiteMeter?.mpan
          : gasSiteMeter?.mprnValue
      ),
    [electricSiteMeter, gasSiteMeter, utilityType]
  );

  const showCapacityCharge =
    additionalMeterData?.profileClass === "00" &&
    additionalMeterData?.measurementClass?.toUpperCase() !== "G";

  return (
    <>
      <Button
        variant="dialog"
        className="top-6 right-4 h-auto p-0"
        onClick={closeModal}
      >
        <XIcon className="size-4" />
        <span className="sr-only fixed">Close</span>
      </Button>
      <div className="flex flex-col overflow-y-scroll p-8">
        <h1 className="pb-4 text-center font-bold text-3xl">Quote Details</h1>
        <div className="flex flex-col items-center justify-center gap-2">
          <MpxnInputField
            meterIdentifier={fullMeterIdentifier}
            utilityType={utilityType}
          />
          <div className="flex items-center justify-center gap-2">
            <QuoteDetailsBadge
              label="Generated Date"
              value={formatStringToDateShort(createdAt.toString())}
            />
            {renderConsumptionBadges({
              dayUsage:
                "dayUsage" in utilityUsage ? utilityUsage.dayUsage : undefined,
              nightUsage:
                "nightUsage" in utilityUsage
                  ? utilityUsage.nightUsage
                  : undefined,
              weekendUsage:
                "weekendUsage" in utilityUsage
                  ? utilityUsage.weekendUsage
                  : undefined,
              totalUsage: utilityUsage.totalUsage
            })}
          </div>
        </div>
        <h2 className="my-4 ml-10 font-bold text-xl xl:ml-14">
          Update Your Quote Search
        </h2>
        <QuoteUpdateSearchForm
          submitButtonRef={buttonRef}
          quoteList={quotesResult.quoteList}
          meterIdentifier={meterIdentifier}
          utilityUsage={utilityUsage}
          onSubmit={handleUpdateSearchSubmit}
          showCapacityCharge={showCapacityCharge}
        />
        <h2 className="my-4 ml-10 font-bold text-xl xl:ml-14">Quote List</h2>
        <QuoteResultsDataTable
          key={id}
          data={quotesResult}
          quoteListId={quoteList.id}
          saveChanges={() => null}
          closeModal={closeModal}
          showCapacityCharge={showCapacityCharge}
          handleCreateContract={handleCreateContract}
        />
      </div>
    </>
  );
}

type QuoteDetailsProps = {
  isOpen: boolean;
  closeModal: () => void;
  handleCreateContract: (
    contractType: ContractType,
    quoteId: string,
    contactId: string
  ) => void;
};

export function QuoteDetails({
  isOpen,
  closeModal,
  handleCreateContract
}: QuoteDetailsProps) {
  const { initialiseBespokeSupplierUplifts } = useQuoteStore(state => ({
    initialiseBespokeSupplierUplifts: state.initialiseBespokeSupplierUplifts
  }));

  const { queryParams, setQueryParams } =
    useQueryParams<QuoteModalQueryParams>();

  const {
    data: quotesResult,
    error,
    isLoading
  } = trpcClient.quote.getQuotesByQuoteListId.useQuery(
    { quoteListId: queryParams.quoteListId ?? "" },
    { enabled: !!queryParams.quoteListId, refetchOnWindowFocus: false }
  );

  useEffect(() => {
    if (quotesResult?.quoteList) {
      initialiseBespokeSupplierUplifts(quotesResult.bespokeSupplierUplifts);
    }
  }, [
    quotesResult?.quoteList,
    initialiseBespokeSupplierUplifts,
    quotesResult?.bespokeSupplierUplifts
  ]);

  const handleUpdateSearchSubmit = (newQuoteListId: string) => {
    setQueryParams({ quoteListId: newQuoteListId });
  };

  const canRenderContent = !!(
    quotesResult?.quoteList?.siteMeter &&
    quotesResult?.quoteList?.createdAt &&
    !isLoading
  );

  if (error) {
    return <div>Error fetching quote list for {queryParams.quoteListId}</div>;
  }

  if (!queryParams.quoteListId) {
    return null;
  }

  return (
    <QuoteDetailsDrawer isOpen={isOpen} closeModal={closeModal}>
      {!canRenderContent ? (
        <QuoteDetailsSkeleton />
      ) : (
        <QuoteDetailsContent
          closeModal={closeModal}
          handleCreateContract={handleCreateContract}
          handleUpdateSearchSubmit={handleUpdateSearchSubmit}
          quotesResult={quotesResult}
        />
      )}
    </QuoteDetailsDrawer>
  );
}

function QuoteDetailsSkeleton() {
  return (
    <>
      {/* Close Button (non-skeleton for a consistent UI) */}
      <Button variant="dialog" className="top-6 right-4 h-auto p-0">
        <XIcon className="size-4" />
        <span className="sr-only fixed">Close</span>
      </Button>

      {/* Main Content Area */}
      <div className="flex flex-col gap-6 overflow-y-scroll p-8">
        {/* 1) Title + Badges */}
        <Skeleton className="mx-auto h-8 w-52" />

        {/* Meter / Identifier placeholders */}
        <div className="flex flex-col items-center justify-center gap-2">
          <Skeleton className="h-10 w-64" />
          <div className="flex items-center justify-center gap-2">
            <Skeleton className="h-24 w-36" />
            <Skeleton className="h-24 w-36" />
          </div>
        </div>

        {/* 2) Update Your Quote Search */}
        <div className="space-y-2">
          {/* Skeleton heading */}
          <Skeleton className="h-5 w-56" />
          {/* Four fields in a row or stacked, adjusting as needed */}
          <div className="flex flex-wrap gap-4">
            <Skeleton className="h-9 w-44" />
            <Skeleton className="h-9 w-44" />
            <Skeleton className="h-9 w-44" />
            <Skeleton className="h-9 w-44" />
          </div>
        </div>

        {/* 3) Quote List */}
        <div className="space-y-2">
          {/* Skeleton heading */}
          <Skeleton className="h-5 w-36" />
          {/* Possibly a single search input + filters row */}
          <div className="flex flex-wrap items-center gap-4">
            <Skeleton className="h-9 w-60" />
            {/* Example radio or filter placeholders */}
            <Skeleton className="h-9 w-28" />
            <Skeleton className="h-9 w-28" />
          </div>
        </div>

        {/* 4) Table Skeleton */}
        <DataTableSkeleton
          columnCount={9} // e.g., selection column + 7 data columns
          rowCount={12} // Per your request
          searchableColumnCount={0}
          filterableColumnCount={0}
          cellWidths={[
            "3rem",
            "8rem",
            "8rem",
            "8rem",
            "8rem",
            "8rem",
            "8rem",
            "8rem",
            "8rem"
          ]}
          withPagination={true}
          shrinkZero
        />

        {/* 5) Recipient Details */}
        <div className="space-y-2">
          <Skeleton className="h-5 w-48" />
          {/* One or two input fields, depending on your actual form */}
          <Skeleton className="h-9 w-80" />
        </div>
      </div>
    </>
  );
}
