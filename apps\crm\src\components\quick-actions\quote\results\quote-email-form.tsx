"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { QuoteType } from "@prisma/client";
import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/crm/components/lookup";
import { AddNewContactTrigger } from "@watt/crm/components/quick-actions/contact/add-new-contact-trigger";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/crm/components/react-hook-form/form";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@watt/crm/components/ui/alert";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { Separator } from "@watt/crm/components/ui/separator";
import { ToastAction } from "@watt/crm/components/ui/toast";
import { toast } from "@watt/crm/components/ui/use-toast";
import { featureToggles } from "@watt/crm/feature-toggles";
import { useQuoteStore } from "@watt/crm/store/quote";
import { trpcClient } from "@watt/crm/utils/api";
import { ContractType } from "@watt/db/src/enums";
import {
  AlertCircleIcon,
  AudioLinesIcon,
  ChevronDownIcon,
  FilePenLineIcon,
  Loader2Icon,
  MailIcon
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { CompanyContactInfo } from "../../contact/company-contact-info";

const QuoteEmailFormSchema = z.object({
  contactId: z.string(),
  customerEmail: z
    .string()
    .min(1, {
      message: "Recipient email address is required"
    })
    .email()
});

export type QuoteEmailFormValues = z.infer<typeof QuoteEmailFormSchema>;

type QuoteEmailFormProps = {
  selectedQuotes: FindUniqueQuoteListSelectQuotesGetPayload[];
  allQuoteIds: string[];
  quoteListId: string;
  companyId: string;
  siteId: string;
  verbalSupportedSuppliers: string[];
  saveChanges: () => void;
  closeModal: () => void;
  handleCreateContract: (
    contractType: ContractType,
    quoteId: string,
    contactId: string
  ) => void;
  noSendReason:
    | {
        message: string;
        disable: boolean;
      }
    | undefined;
};

export function QuoteEmailForm({
  selectedQuotes,
  allQuoteIds,
  quoteListId,
  companyId,
  siteId,
  verbalSupportedSuppliers,
  saveChanges,
  closeModal,
  handleCreateContract,
  noSendReason
}: QuoteEmailFormProps) {
  const contactFormContainerRef = useRef<HTMLDivElement>(null);
  const [openContactFormModal, setOpenContactFormModal] = useState(false);
  const [contactSearch, setContactSearch] = useState("");
  const { updateQuoteIsDirty } = useQuoteStore(state => ({
    updateQuoteIsDirty: state.updateQuoteIsDirty
  }));
  const QuoteEmailUtils = trpcClient.useUtils().email;
  const router = useRouter();
  const companyContactData = trpcClient.people.getCompanyContacts.useQuery({
    companyId
  });
  const sendQuoteEmailMutation = trpcClient.email.sendQuoteEmail.useMutation({
    onSuccess: async () => {
      await QuoteEmailUtils.invalidate();
    }
  });

  // determine if the new contact will be set as the primary contact for the site
  const setAsPrimaryContact = useMemo(() => {
    return !companyContactData.data?.find(contact => contact.siteId === siteId);
  }, [companyContactData, siteId]);

  const form = useForm<QuoteEmailFormValues>({
    resolver: zodResolver(QuoteEmailFormSchema),
    mode: "onChange",
    defaultValues: {
      customerEmail: ""
    }
  });

  const sendEmail = async ({
    quoteIds,
    quoteListId
  }: {
    quoteIds: string[];
    quoteListId: string;
  }) => {
    const customerEmail = form.getValues("customerEmail");
    const result = await sendQuoteEmailMutation.mutateAsync({
      customerEmail,
      quoteListId,
      quoteIds
    });

    const errorMessage =
      "Error sending email. It's taking too long to generate the PDF.";

    if (
      (!result.success || sendQuoteEmailMutation.isError) &&
      result.message === errorMessage
    ) {
      toast({
        title: "Timeout",
        description: "Sorry, this is taking longer than expected. Retrying..."
      });

      const retryResult = await sendQuoteEmailMutation.mutateAsync({
        customerEmail,
        quoteListId,
        quoteIds
      });

      if (!retryResult.success && retryResult.message === errorMessage) {
        toast({
          variant: "destructive",
          title: "Timeout",
          description: errorMessage,
          action: (
            <ToastAction
              className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
              onClick={async () =>
                sendEmail({
                  quoteIds,
                  quoteListId
                })
              }
              altText="Retry"
            >
              Retry
            </ToastAction>
          )
        });
        return retryResult;
      }

      return retryResult;
    }

    if (!result.success || sendQuoteEmailMutation.isError) {
      toast({
        title: "Timeout",
        description: "Failed to send quote to customer, please try again."
      });
    }

    return result;
  };

  async function sendQuoteEmail({
    quoteIds,
    quoteListId
  }: {
    quoteIds: string[];
    quoteListId: string;
  }) {
    try {
      const result = await sendEmail({ quoteIds, quoteListId });

      if (!result.success || sendQuoteEmailMutation.isError) {
        return;
      }

      toast({
        title: "Success",
        variant: "success",
        description:
          quoteIds.length === 1
            ? "Quote sent successfully"
            : `${quoteIds.length} quotes sent successfully`
      });
      closeModal();
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      toast({
        title: "Error",
        variant: "destructive",
        description: error.message
      });
    }
  }

  async function sendAllQuotes() {
    await sendQuoteEmail({ quoteIds: allQuoteIds, quoteListId });
  }

  async function onSubmit() {
    if (!performEmailQuotesChecks()) {
      return;
    }

    await sendQuoteEmail({
      quoteIds: selectedQuotes.map(quote => quote.id),
      quoteListId
    });
  }

  function performIsQuoteDirtyChecks(): boolean {
    if (updateQuoteIsDirty) {
      toast({
        title: "Unsaved changes",
        description:
          "Please update search before sending the quote to the customer.",
        action: (
          <ToastAction
            className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
            onClick={saveChanges}
            altText="Update"
          >
            Update
          </ToastAction>
        )
      });

      return false;
    }

    return true;
  }

  function performEmailQuotesChecks(): boolean {
    if (!performIsQuoteDirtyChecks()) {
      return false;
    }

    if (selectedQuotes.length === 0) {
      toast({
        title: "No quotes selected",
        description:
          "You haven't selected any quotes. Choose individual quotes or select 'Send All' to email all quotes to the customer.",
        action: (
          <ToastAction
            className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
            onClick={sendAllQuotes}
            altText="Send All"
          >
            Send All
          </ToastAction>
        )
      });
      return false;
    }

    return true;
  }

  function performSharedContractChecks(): boolean {
    if (!performIsQuoteDirtyChecks()) {
      return false;
    }

    if (selectedQuotes.length === 0) {
      toast({
        title: "No Quote Selected",
        description: "Please select a quote to create a contract.",
        variant: "destructive"
      });

      return false;
    }

    return true;
  }

  function performVerbalContractChecks(): boolean {
    if (!performSharedContractChecks()) {
      return false;
    }

    if (selectedQuotes.length > 1) {
      const hasCustomQuote = selectedQuotes.some(
        quote =>
          quote.type === QuoteType.CUSTOM || quote.type === QuoteType.BESPOKE
      );

      const hasUnsupportedSupplier = selectedQuotes.some(
        quote =>
          !verbalSupportedSuppliers.includes(quote.provider.udcoreId ?? "")
      );

      if (hasCustomQuote || hasUnsupportedSupplier) {
        toast({
          title: "Too Many Quotes Selected",
          description: `Multiple quotes are selected. We only support UD quotes from ${verbalSupportedSuppliers.slice(0, -1).join(", ")} and ${verbalSupportedSuppliers.slice(-1)}. Selected: ${selectedQuotes.length} quotes`,
          variant: "destructive"
        });

        return false;
      }

      toast({
        title: "Too Many Quotes Selected",
        description: `Please select only one quote to turn into a contract. Selected: ${selectedQuotes.length} quotes`,
        variant: "destructive"
      });

      return false;
    }

    const firstQuote = selectedQuotes[0];
    if (!firstQuote) {
      toast({
        title: "No Quote Selected",
        description: "Please select a quote.",
        variant: "destructive"
      });
      return false;
    }

    if (firstQuote.type === QuoteType.CUSTOM) {
      toast({
        title: "Custom Quote Not Supported",
        description: "Verbal contracts are not available for custom quotes.",
        variant: "destructive"
      });
      return false;
    }

    if (firstQuote.type === QuoteType.BESPOKE) {
      toast({
        title: "Bespoke Quote Not Supported",
        description: "Verbal contracts are not available for bespoke quotes.",
        variant: "destructive"
      });
      return false;
    }

    if (verbalSupportedSuppliers.length === 0) {
      toast({
        title: "No Supported Providers",
        description: "There are no supported providers for verbal contracts.",
        variant: "destructive"
      });
      return false;
    }

    if (
      !verbalSupportedSuppliers.includes(firstQuote.provider.udcoreId ?? "")
    ) {
      toast({
        title: "Supplier does not accept verbal",
        description: `Verbals are only supported with ${verbalSupportedSuppliers.slice(0, -1).join(", ")} and ${verbalSupportedSuppliers.slice(-1)}.`,
        variant: "destructive"
      });
      return false;
    }

    return true;
  }

  function performWrittenContractChecks(): boolean {
    if (!performSharedContractChecks()) {
      return false;
    }

    if (selectedQuotes.length > 1) {
      toast({
        title: "Too Many Quotes Selected",
        description: `Please select only one quote to turn into a contract. Selected: ${selectedQuotes.length} quotes`,
        variant: "destructive"
      });

      return false;
    }

    return true;
  }

  function createContract(contractType: ContractType): void {
    const check =
      contractType === ContractType.VERBAL
        ? performVerbalContractChecks
        : performWrittenContractChecks;

    if (check()) {
      const firstQuoteId = selectedQuotes[0]?.id;
      if (firstQuoteId) {
        handleCreateContract(
          contractType,
          firstQuoteId,
          form.getValues().contactId
        );
      }
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="contactId"
          render={({ field }) => {
            const selectedContact = companyContactData.data?.find(
              contact => contact.id === field.value
            );
            return (
              <FormItem className="flex flex-col">
                <FormLabel>Contact Email *</FormLabel>
                <FormControl>
                  <div
                    ref={contactFormContainerRef}
                    className="flex w-full max-w-[600px] flex-col"
                  >
                    <LookUp
                      open={openContactFormModal}
                      onOpenChange={setOpenContactFormModal}
                    >
                      <LookUpTrigger fieldValue={field.value}>
                        {selectedContact ? (
                          <CompanyContactInfo {...selectedContact} />
                        ) : (
                          <span className="font-normal italic">
                            Select a contact
                          </span>
                        )}
                      </LookUpTrigger>
                      <LookUpContent
                        placeholder="Type contact name or email address"
                        searchInput={contactSearch}
                        onSearchInputChange={setContactSearch}
                        container={contactFormContainerRef.current}
                        className="pb-10"
                        shouldFilter
                      >
                        <LookUpGroup className="p-0">
                          {companyContactData.data?.map(contact => {
                            const primaryEmail = contact.emails?.find(
                              email => email.isPrimary
                            )?.email;

                            if (!primaryEmail) {
                              // There will always be a primary email
                              return null;
                            }

                            const lookupValue = `${primaryEmail} ${contact.forename} ${contact.surname}`;
                            return (
                              <LookUpItem
                                key={contact.id}
                                value={lookupValue}
                                onSelect={() => {
                                  form.setValue("customerEmail", primaryEmail);
                                  form.setValue("contactId", contact.id);
                                }}
                              >
                                <CompanyContactInfo {...contact} />
                              </LookUpItem>
                            );
                          })}
                        </LookUpGroup>
                        <div className="absolute bottom-0 flex w-full justify-center border-t bg-background">
                          <AddNewContactTrigger
                            companyId={companyId}
                            siteId={siteId}
                            setAsPrimaryContact={setAsPrimaryContact}
                            onSubmit={(email, contactId) => {
                              form.setValue("customerEmail", email);
                              form.setValue("contactId", contactId);
                            }}
                          />
                        </div>
                      </LookUpContent>
                    </LookUp>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="w-28"
            onClick={() => {
              router.back();
            }}
          >
            Back
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger
              asChild
              disabled={
                sendQuoteEmailMutation.isPending || noSendReason?.disable
              }
            >
              <Button
                type="button"
                size="sm"
                variant="secondary"
                className="w-28"
              >
                {sendQuoteEmailMutation.isPending ? (
                  <Loader2Icon className="size-4 animate-spin" />
                ) : (
                  <>
                    <span className="m-auto">Confirm</span>
                    <ChevronDownIcon className="ml-auto size-4" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <Button
                type="button"
                variant="none"
                size="none"
                className="w-full font-normal"
                onClick={form.handleSubmit(onSubmit)}
              >
                <DropdownMenuItem className="w-full">
                  <MailIcon className="mr-2 size-3.5" />
                  Email Quote
                </DropdownMenuItem>
              </Button>
              <Separator />
              <Button
                type="button"
                variant="none"
                size="none"
                className="w-full font-normal"
                onClick={form.handleSubmit(() =>
                  createContract(ContractType.VERBAL)
                )}
              >
                <DropdownMenuItem className="w-full">
                  <AudioLinesIcon className="mr-2 size-3.5" />
                  Create Verbal
                </DropdownMenuItem>
              </Button>
              <Separator />
              {featureToggles.features.writtenContract && (
                <Button
                  type="button"
                  variant="none"
                  size="none"
                  className="w-full font-normal"
                  onClick={form.handleSubmit(() =>
                    createContract(ContractType.WRITTEN)
                  )}
                >
                  <DropdownMenuItem className="w-full">
                    <FilePenLineIcon className="mr-2 size-3.5" />
                    Create Written
                  </DropdownMenuItem>
                </Button>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {noSendReason && (
          <Alert
            variant="warn"
            className="mb-6 rounded-none border-0 border-badge-orange border-l-4 text-black"
          >
            <AlertCircleIcon className="size-4" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription className="mt-2 text-xs">
              {noSendReason.message.replace(/^Hi\s*,?\s*/i, "")}
            </AlertDescription>
          </Alert>
        )}
      </form>
    </Form>
  );
}
