"use client";

import { QuoteType } from "@prisma/client";
import type { ColumnDef } from "@tanstack/react-table";
import type { QuoteList_And_Quotes } from "@watt/api/src/router";
import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { calculatePriceDifference } from "@watt/common/src/utils/calculate-price-difference";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatAndPadCurrency } from "@watt/common/src/utils/format-currency";
import type { TariffRates } from "@watt/common/src/utils/split-usage-by-rate";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { Badge } from "@watt/crm/components/ui/badge";
import { Button } from "@watt/crm/components/ui/button";
import { Checkbox } from "@watt/crm/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { HUNDRED_THOUSAND_LENGTH_WITH_SYMBOLS } from "@watt/crm/config/currency";
import { copyToClipboard } from "@watt/crm/utils/copy";
import {
  CalendarX2Icon,
  ContainerIcon,
  FileCheck2Icon,
  MailCheckIcon,
  MailMinusIcon,
  MailOpenIcon,
  MailWarningIcon,
  MailXIcon
} from "lucide-react";
import type { JSX } from "react";
import { DataTableRowActions } from "./data-table-row-actions";
import { DataTableStickyRowActions } from "./data-table-sticky-row-actions";
import { QuoteDetailsUpliftInput } from "./quote-details-uplift-input";

type UtilityQuote =
  | FindUniqueQuoteListSelectQuotesGetPayload["electricQuote"]
  | FindUniqueQuoteListSelectQuotesGetPayload["gasQuote"];

function extractQuoteValue<T>(
  quote: FindUniqueQuoteListSelectQuotesGetPayload,
  extractor: (utilityQuote: UtilityQuote) => T | undefined
): T | undefined {
  return quote.electricQuote
    ? extractor(quote.electricQuote)
    : quote.gasQuote
      ? extractor(quote.gasQuote)
      : undefined;
}

type QuoteStatus =
  | "GENERATED"
  | "SENDING"
  | "PROCESSED"
  | "DELIVERED"
  | "OPEN"
  | "CLICK"
  | "ACCEPTED"
  | "PENDING"
  | "DROPPED"
  | "DEFERRED"
  | "BOUNCE"
  | "BLOCKED"
  | "SPAMREPORT"
  | "UNSUBSCRIBE"
  | "GROUP_UNSUBSCRIBE"
  | "GROUP_RESUBSCRIBE"
  | "EXPIRED";

type QuoteStatusInfo = {
  component: JSX.Element | null;
  description: string;
};

const QuoteStatusIconMap: Readonly<Record<QuoteStatus, QuoteStatusInfo>> = {
  GENERATED: { component: null, description: "Quote created" },
  SENDING: {
    component: <ContainerIcon className="size-4" />,
    description: "Sending quote email"
  },
  PROCESSED: {
    component: <ContainerIcon className="size-4" />,
    description: "Processed in queue to be sent"
  },
  DELIVERED: {
    component: <MailCheckIcon className="size-4" />,
    description: "Quote email delivered"
  },
  OPEN: {
    component: <MailOpenIcon className="size-4" />,
    description: "Quote email opened"
  },
  CLICK: {
    component: <MailOpenIcon className="size-4" />,
    description: "Quote email link clicked"
  },
  ACCEPTED: {
    component: <FileCheck2Icon className="size-4" />,
    description: "Quote accepted"
  },
  PENDING: {
    component: <ContainerIcon className="size-4" />,
    description: "Quote pending"
  },
  DROPPED: {
    component: <MailWarningIcon className="size-4" />,
    description: "Quote email rejected by email server"
  },
  DEFERRED: {
    component: <ContainerIcon className="size-4" />,
    description: "Quote email deferred, re-sending in progress"
  },
  BOUNCE: {
    component: <MailXIcon className="size-4" />,
    description: "Quote bounced, inbox likely full"
  },
  BLOCKED: {
    component: <MailXIcon className="size-4" />,
    description: "Quote blocked, user has blocked our email"
  },
  SPAMREPORT: {
    component: <MailMinusIcon className="size-4" />,
    description: "Customer reported as spam"
  },
  UNSUBSCRIBE: {
    component: <MailMinusIcon className="size-4" />,
    description: "Unsubscribed"
  },
  GROUP_UNSUBSCRIBE: {
    component: <MailMinusIcon className="size-4" />,
    description: "Customer unsubscribed"
  },
  GROUP_RESUBSCRIBE: {
    component: <MailMinusIcon className="size-4" />,
    description: "Customer resubscribed"
  },
  EXPIRED: {
    component: <CalendarX2Icon className="size-4" />,
    description: "Quote expired, no longer valid"
  }
} as const;

export const quoteResultsColumns = (
  maxDecimalPlaces: number,
  currentPrice: number,
  tariffRates: TariffRates,
  quoteList: QuoteList_And_Quotes["quoteList"],
  showCapacityCharge: boolean,
  updateColumnVisibility: (column: string, visible: boolean) => void
): ColumnDef<FindUniqueQuoteListSelectQuotesGetPayload>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <div>
        <div className="flex items-start gap-2 ">
          <Checkbox
            checked={table.getIsAllRowsSelected()}
            onCheckedChange={value => table.toggleAllRowsSelected(!!value)}
            aria-label="Select all rows"
            className="rounded-full"
          />
        </div>
      </div>
    ),
    cell: ({ row }) => {
      if (row.original.sticky) {
        return null;
      }

      return (
        <div>
          <div className="flex gap-2 ">
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => {
                return row.toggleSelected(!!value);
              }}
              aria-label="Select row"
              className="rounded-full"
            />
            {row.original.status !== "GENERATED" && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    {
                      QuoteStatusIconMap[row.original.status as QuoteStatus]
                        .component
                    }
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {
                        QuoteStatusIconMap[row.original.status as QuoteStatus]
                          .description
                      }
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
      );
    }
  },
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Quote ID" disableSorting />
    ),
    cell: ({ row }) => {
      const quoteId = row.original.id;
      return (
        <div className="pb-4">
          <Button
            variant="none"
            className="h-0 p-0"
            onClick={() => {
              copyToClipboard(quoteId, "quote id");
            }}
          >
            {quoteId}
          </Button>
        </div>
      );
    },
    filterFn: textFilter,
    enableGlobalFilter: true
  },
  {
    accessorKey: "unitRateUplift",
    accessorFn: quote => extractQuoteValue(quote, q => q?.unitRateUplift),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Unit Rate Uplift"
        description="(pence/kWh)"
      />
    ),
    cell: ({ row }) => {
      return (
        <QuoteDetailsUpliftInput
          quote={row.original}
          baseUplift={quoteList?.upliftRate}
          quoteListStatus={quoteList?.status}
          isCurrentProviderRow={row.original.sticky}
        />
      );
    },
    meta: {
      dropdownLabel: "Unit Rate Uplift"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Quote Source" />
    ),
    cell: ({ getValue }) => {
      return <div>{getValue<string>()}</div>;
    },
    meta: {
      dropdownLabel: "Quote Source"
    },
    filterFn: textFilter,
    enableGlobalFilter: true
  },
  {
    accessorKey: "supplier",
    accessorFn: quote => quote.provider.udcoreId,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Supplier" />
    ),
    cell: ({ getValue, row }) => {
      const { logoFileName, displayName } = row.original.provider;
      return (
        <div className="flex flex-col items-center justify-center gap-1">
          <ProviderLogo
            logoFileName={logoFileName}
            displayName={displayName}
            width={60}
            height={20}
            className="h-auto w-[60px] object-scale-down"
            responsive
          />
        </div>
      );
    },
    meta: {
      dropdownLabel: "Supplier"
    },
    filterFn: textFilter,
    enableGlobalFilter: true
  },
  {
    accessorKey: "contractType",
    accessorFn: quote =>
      extractQuoteValue(quote, q => q?.contractType) ?? "Default",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Product"
        description="(plan type)"
      />
    ),
    cell: ({ getValue }) => {
      const contractType = getValue() as string;
      const contractTypes = contractType.split(" | ");

      return (
        <div className="flex flex-col gap-1">
          {contractTypes.map(type => (
            <Badge className="justify-center" key={type}>
              {type}
            </Badge>
          ))}
        </div>
      );
    },
    meta: {
      dropdownLabel: "Product"
    },
    filterFn: textFilter,
    enableGlobalFilter: true
  },
  {
    accessorKey: "duration",
    accessorFn: quote => quote.duration,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Term" />
    ),
    cell: ({ row, getValue }) => {
      const duration = getValue<number>();

      if (!duration) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }

      return <div>{duration} months</div>;
    },
    meta: {
      dropdownLabel: "Term"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "unitRate",
    accessorFn: quote => {
      const rateValue = extractQuoteValue(quote, q => q?.unitRate);
      return typeof rateValue === "number"
        ? rateValue.toFixed(maxDecimalPlaces)
        : undefined;
    },
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Unit Rate"
        description="(pence/kWh)"
      />
    ),
    cell: ({ row, getValue }) => {
      const unitRate = getValue<string | undefined>();
      if (!unitRate) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }

      return (
        <div className="flex">
          <span className="w-20 text-right">{unitRate}</span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Unit Rate"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "nightUnitRate",
    accessorFn: quote => {
      const rateValue = extractQuoteValue(quote, q =>
        q && "nightUnitRate" in q ? q.nightUnitRate : undefined
      );
      return typeof rateValue === "number" && rateValue > 0
        ? rateValue.toFixed(maxDecimalPlaces)
        : undefined;
    },
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Night Rate"
        description="(pence/kWh)"
      />
    ),
    cell: ({ row, getValue }) => {
      const nightUnitRate = getValue() as string | undefined;
      if (!nightUnitRate) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }
      return (
        <div className="flex">
          <span className="w-20 text-right">{nightUnitRate}</span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Night Rate"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "weekendUnitRate",
    accessorFn: quote => {
      const rateValue = extractQuoteValue(quote, q =>
        q && "weekendUnitRate" in q ? q.weekendUnitRate : undefined
      );
      return typeof rateValue === "number" && rateValue > 0
        ? rateValue.toFixed(maxDecimalPlaces)
        : undefined;
    },
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Weekend Rate"
        description="(pence/kWh)"
      />
    ),
    cell: ({ row, getValue }) => {
      const weekendUnitRate = getValue<string | undefined>();
      if (!weekendUnitRate) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }
      return (
        <div className="flex">
          <span className="w-20 text-right">{weekendUnitRate}</span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Weekend Rate"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "standingCharge",
    accessorFn: quote =>
      extractQuoteValue(quote, q =>
        q?.standingCharge?.toFixed(maxDecimalPlaces)
      ),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Standing Charge"
        description="(pence/day)"
      />
    ),
    cell: ({ row, getValue }) => {
      const standingCharge = getValue() as string | undefined;
      if (!standingCharge) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }
      return (
        <div className="flex">
          <span className="w-20 text-right">{standingCharge}</span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Standing Charge"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "capacityChargeKva",
    accessorFn: quote =>
      quote.electricQuote?.capacityChargeKva
        ? Number.parseFloat(quote.electricQuote.capacityChargeKva).toFixed(
            maxDecimalPlaces
          )
        : undefined,
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Capacity Charge"
        description="(pence/day)"
      />
    ),
    cell: ({ row, getValue }) => {
      const capacityChargeKva = getValue() as string | undefined;
      if (!capacityChargeKva) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }
      return (
        <div className="flex">
          <span className="w-20 text-right">{capacityChargeKva ?? "N/A"}</span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Capacity Charge"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "annualCost",
    accessorFn: quote => extractQuoteValue(quote, q => q?.annualPrice),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Annual Cost"
        description="(excluding VAT and CCL)"
      />
    ),
    cell: ({ row, getValue }) => {
      const annualCost = getValue() as number | undefined;
      if (!annualCost) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }
      return (
        <div className="flex">
          <span className="w-20 whitespace-pre text-right">
            {formatAndPadCurrency(
              annualCost,
              HUNDRED_THOUSAND_LENGTH_WITH_SYMBOLS
            )}
          </span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Annual Cost"
    },
    filterFn: textFilter
  },
  {
    accessorKey: "priceDifference",
    accessorFn: quote => extractQuoteValue(quote, q => q?.priceDifference),
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Price Difference"
        description="(£ and %)"
      />
    ),
    cell: ({ getValue, row }) => {
      if (row.original.sticky) {
        return null;
      }

      const difference = getValue<number | undefined>();

      if (difference === undefined) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }

      const { colorClass, formattedDifference, formattedPercentage } =
        calculatePriceDifference(currentPrice, difference);

      if (!formattedDifference || !formattedPercentage) {
        return (
          <span
            className={cn(
              !row.original.sticky && "text-muted-foreground",
              "italic"
            )}
          >
            N/A
          </span>
        );
      }

      return (
        <div className="flex justify-between gap-2 font-medium">
          <span className={cn(colorClass, "text-nowrap")}>
            {formattedDifference}
          </span>
          <span className={cn(colorClass, "text-nowrap")}>
            {formattedPercentage}
          </span>
        </div>
      );
    },
    meta: {
      dropdownLabel: "Price Difference"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => {
      if (row.original.sticky) {
        return (
          <DataTableStickyRowActions
            quote={row.original}
            tariffRates={tariffRates}
            quoteListStatus={quoteList?.status}
            showCapacityCharge={showCapacityCharge}
            updateColumnVisibility={updateColumnVisibility}
          />
        );
      }
      if (
        quoteList &&
        (row.original.type === QuoteType.CUSTOM ||
          row.original.type === QuoteType.BESPOKE)
      ) {
        return (
          <DataTableRowActions
            utilityType={row.original.utilityType}
            onSubmitForm={() => null}
            tariffRates={tariffRates}
            showCapacityCharge={showCapacityCharge}
            quoteListId={quoteList.id}
            quoteListStatus={quoteList.status}
            quoteStatus={row.original.status}
            customQuoteData={row.original}
          />
        );
      }

      return null;
    }
  }
];
