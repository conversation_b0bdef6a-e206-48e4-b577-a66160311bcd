"use client";

import type { Table } from "@tanstack/react-table";
import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import {
  getUniqueFilterOptions,
  getUniqueSupplierFilterOptions
} from "@watt/crm/utils/get-unique-filter-options";
import { XIcon } from "lucide-react";
import { type ChangeEvent, useRef, useState } from "react";
import { useDebounce } from "react-use";
import {
  CustomQuoteModal,
  type CustomQuoteModalProps
} from "../form/custom-quote-modal";

type ColumnFilter = {
  id: string;
  title: string;
  unfilteredList?: boolean;
};

type DataTableToolbarProps<TData> = CustomQuoteModalProps & {
  table: Table<TData>;
  currentSupplierId?: string | null;
};

export function DataTableToolbar<TData>({
  table,
  currentSupplierId,
  utilityType,
  tariffRates,
  showCapacityCharge,
  quoteListId,
  quoteListStatus,
  onSubmitForm
}: DataTableToolbarProps<TData>) {
  const [debouncedSearchValue, setDebouncedSearchValue] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);

  useDebounce(() => table.setGlobalFilter(debouncedSearchValue), 500, [
    debouncedSearchValue
  ]);

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearchValue(event.target.value);
  };

  const resetFilters = () => {
    table.resetColumnFilters();
    table.resetGlobalFilter();
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
    setDebouncedSearchValue("");
  };

  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  const filterableColumns: ColumnFilter[] = [
    { id: "type", title: "Quote Source" },
    { id: "contractType", title: "Product" },
    { id: "duration", title: "Term" }
  ];

  return (
    <div className="ml-10 flex flex-wrap items-center justify-between gap-2 xl:ml-14">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          ref={searchInputRef}
          value={debouncedSearchValue}
        />
        <DataTableFacetedFilter
          column={table.getColumn("supplier")}
          title="Supplier"
          options={getUniqueSupplierFilterOptions(
            "supplier",
            table,
            true,
            currentSupplierId
          )}
        />
        {filterableColumns.map(column => (
          <DataTableFacetedFilter
            key={column.id}
            column={table.getColumn(column.id)}
            title={column.title}
            options={getUniqueFilterOptions({
              columnId: column.id,
              table,
              unfilteredList: column.unfilteredList
            })}
          />
        ))}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <XIcon className="ml-2 size-4" />
          </Button>
        )}
      </div>
      <div className="flex space-x-2">
        <DataTableViewOptions table={table} />
        <CustomQuoteModal
          utilityType={utilityType}
          tariffRates={tariffRates}
          showCapacityCharge={showCapacityCharge}
          quoteListId={quoteListId}
          quoteListStatus={quoteListStatus}
          onSubmitForm={onSubmitForm}
        />
      </div>
    </div>
  );
}
