import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import type { QuoteList_And_Quotes } from "@watt/api/src/router";
import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import { QuoteStatus, QuoteType, UtilityType } from "@watt/db/src/enums";
import { QuoteResultsDataTable } from "./quote-results-data-table";

// Mock dependencies
jest.mock("next/image", () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => (
    <img src={src} alt={alt} />
  )
}));

jest.mock("@watt/crm/utils/copy", () => ({
  copyToClipboard: jest.fn()
}));

jest.mock("./quote-email-form", () => ({
  QuoteEmailForm: () => (
    <div data-testid="quote-email-form">Quote Email Form</div>
  )
}));

// Create a mock data table toolbar that provides select all functionality
jest.mock("./quote-results-data-table-toolbar", () => ({
  DataTableToolbar: ({
    table
  }: { table: { toggleAllPageRowsSelected: () => void } }) => (
    <div data-testid="data-table-toolbar">
      <button
        type="button"
        data-testid="select-all"
        onClick={() => table.toggleAllPageRowsSelected()}
      >
        Select All
      </button>
    </div>
  )
}));

// Helper to create a large number of quotes for performance testing
function createMockQuotes(
  count: number
): FindUniqueQuoteListSelectQuotesGetPayload[] {
  return Array.from(
    { length: count },
    (_, i) =>
      ({
        id: `quote-${i}`,
        sticky: false,
        status: QuoteStatus.GENERATED,
        type: QuoteType.UD,
        duration: 12,
        endDate: new Date(),
        provider: {
          udcoreId: `supplier-${i}`,
          displayName: `Supplier ${i}`,
          logoFileName: `supplier${i}`
        },
        electricQuote: {
          unitRate: 10.5 + i * 0.1,
          standingCharge: 25.0,
          annualPrice: 1200 + i * 10,
          contractType: "Fixed",
          unitRateUplift: 0.1,
          isBespokeUplift: false,
          standingChargeUplift: null,
          nightUnitRate: 8.5,
          weekendUnitRate: 9.0,
          capacityChargeKva: null,
          priceDifference: i % 2 === 0 ? -100 : 50
        },
        // biome-ignore lint/suspicious/noExplicitAny: <types are not as important in a test>
        gasQuote: {} as unknown as any,
        utilityType: UtilityType.ELECTRICITY
      }) satisfies FindUniqueQuoteListSelectQuotesGetPayload
  );
}

describe("QuoteResultsDataTable Performance", () => {
  const createMockData = (quoteCount: number): QuoteList_And_Quotes =>
    ({
      quoteList: {
        id: "quote-list-1",
        status: QuoteStatus.GENERATED,
        utilityType: UtilityType.ELECTRICITY,
        contractStartDate: new Date(),
        upliftRate: 0.1,
        createdAt: new Date(),
        isCustomQuotesOnly: false,
        siteMeterId: "meter-1",
        siteMeter: {
          id: "meter-1",
          createdAt: new Date(),
          updatedAt: new Date(),
          companySiteId: "site-1",
          utilityType: UtilityType.ELECTRICITY,
          isSmartMeter: false,
          isDeEnergised: false,
          isLinkedToExistingMeter: false,
          companySite: {
            id: "site-1",
            company: {
              id: "company-1"
            }
          },
          electricSiteMeter: {
            siteMeterId: "meter-1",
            hasDayRate: true,
            hasNightRate: true,
            hasWeekendRate: true,
            mpanValue: "1234567890123",
            mpan: {
              value: "1234567890123",
              profileClass: "01",
              mtc: "001",
              lineLossFactor: "1.0"
            }
          },
          gasSiteMeter: null
        },
        currentProvider: {
          displayName: "Current Provider",
          udcoreId: "current",
          logoFileName: "current"
        },
        electricityUsage: {
          dayUsage: 10000,
          nightUsage: 5000,
          weekendUsage: 3000,
          totalUsage: 18000,
          capacityFigureKva: null
        },
        electricitySupplier: {
          id: "current-supplier",
          unitRate: 12.0,
          nightUnitRate: 10.0,
          weekendUnitRate: 10.5,
          capacityChargeKva: null,
          annualPrice: 1400,
          standingCharge: 27.0
        },
        gasUsage: null,
        gasSupplier: null,
        quotes: createMockQuotes(quoteCount),
        suppliersSupportingVerbalContract: ["supplier1"]
      },
      maxDecimalPlaces: 2,
      tariffRates: {
        day: true,
        night: true,
        weekend: true
      },
      additionalMeterData: null,
      bespokeSupplierUplifts: []
    }) satisfies QuoteList_And_Quotes;

  const defaultProps = {
    quoteListId: "quote-list-1",
    saveChanges: jest.fn(),
    closeModal: jest.fn(),
    showCapacityCharge: false,
    handleCreateContract: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("handles select all functionality without significant lag with 50 quotes", async () => {
    const user = userEvent.setup();
    const mockData = createMockData(50);

    render(<QuoteResultsDataTable {...defaultProps} data={mockData} />);

    const selectAllButton = screen.getByTestId("select-all");

    // Measure performance of select all
    const startTime = performance.now();
    await user.click(selectAllButton);
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Should complete quickly (under 200ms for 50 quotes)
    expect(duration).toBeLessThan(200);

    // Verify the table is still responsive
    expect(screen.getByRole("table")).toBeInTheDocument();
  });

  it("handles select all functionality without significant lag with 100 quotes", async () => {
    const user = userEvent.setup();
    const mockData = createMockData(100);

    render(
      <QuoteResultsDataTable
        {...defaultProps}
        data={mockData as QuoteList_And_Quotes}
      />
    );

    const selectAllButton = screen.getByTestId("select-all");

    // Measure performance of select all
    const startTime = performance.now();
    await user.click(selectAllButton);
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Should complete reasonably fast (under 300ms for 100 quotes)
    expect(duration).toBeLessThan(300);

    // Verify the table is still responsive
    expect(screen.getByRole("table")).toBeInTheDocument();
  });

  it("maintains performance on re-renders", async () => {
    const mockData = createMockData(50);

    const { rerender } = render(
      <QuoteResultsDataTable
        {...defaultProps}
        data={mockData as QuoteList_And_Quotes}
      />
    );

    const startTime = performance.now();

    // Force multiple re-renders
    for (let i = 0; i < 5; i++) {
      rerender(
        <QuoteResultsDataTable
          {...defaultProps}
          data={mockData as QuoteList_And_Quotes}
        />
      );
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    // Multiple re-renders should still be fast due to memoization
    expect(duration).toBeLessThan(500);
  });

  it("verifies memoization prevents unnecessary row re-renders", () => {
    const mockData = createMockData(10);
    let renderCount = 0;

    // Mock console.log to count renders
    const originalLog = console.log;
    console.log = jest.fn(message => {
      if (message === "ROW_RENDER") {
        renderCount++;
      }
    });

    const { rerender } = render(
      <QuoteResultsDataTable
        {...defaultProps}
        data={mockData as QuoteList_And_Quotes}
      />
    );

    const initialRenderCount = renderCount;

    // Re-render with same props
    rerender(
      <QuoteResultsDataTable
        {...defaultProps}
        data={mockData as QuoteList_And_Quotes}
      />
    );

    // Rows should not re-render if props haven't changed
    expect(renderCount).toBe(initialRenderCount);

    // Restore console.log
    console.log = originalLog;
  });
});
