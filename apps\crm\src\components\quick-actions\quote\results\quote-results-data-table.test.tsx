import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import type { QuoteList_And_Quotes } from "@watt/api/src/router";
import { QuoteStatus, QuoteType, UtilityType } from "@watt/db/src/enums";
import { QuoteResultsDataTable } from "./quote-results-data-table";

// Mock dependencies
jest.mock("next/image", () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => (
    <img src={src} alt={alt} />
  )
}));

jest.mock("@watt/crm/utils/copy", () => ({
  copyToClipboard: jest.fn()
}));

jest.mock("./quote-email-form", () => ({
  QuoteEmailForm: () => (
    <div data-testid="quote-email-form">Quote Email Form</div>
  )
}));

jest.mock("./quote-results-data-table-toolbar", () => ({
  DataTableToolbar: ({
    table
  }: { table: { toggleAllRowsSelected: () => void } }) => (
    <div data-testid="data-table-toolbar">
      <button
        type="button"
        data-testid="select-all"
        onClick={() => table.toggleAllRowsSelected()}
      >
        Select All
      </button>
    </div>
  )
}));

const mockQuoteData: QuoteList_And_Quotes = {
  quoteList: {
    id: "quote-list-1",
    status: QuoteStatus.GENERATED,
    utilityType: UtilityType.ELECTRICITY,
    contractStartDate: new Date(),
    upliftRate: 0.1,
    createdAt: new Date(),
    isCustomQuotesOnly: false,
    siteMeterId: "meter-1",
    siteMeter: {
      id: "meter-1",
      createdAt: new Date(),
      updatedAt: new Date(),
      companySiteId: "site-1",
      utilityType: UtilityType.ELECTRICITY,
      isSmartMeter: false,
      isDeEnergised: false,
      isLinkedToExistingMeter: false,
      companySite: {
        id: "site-1",
        company: {
          id: "company-1"
        }
      },
      electricSiteMeter: {
        siteMeterId: "meter-1",
        hasDayRate: true,
        hasNightRate: true,
        hasWeekendRate: true,
        mpanValue: "1234567890123",
        mpan: {
          value: "1234567890123",
          profileClass: "01",
          mtc: "001",
          lineLossFactor: "1.0"
        }
      },
      gasSiteMeter: null
    },
    currentProvider: {
      displayName: "Current Provider",
      udcoreId: "current",
      logoFileName: "current"
    },
    electricityUsage: {
      dayUsage: 10000,
      nightUsage: 5000,
      weekendUsage: 3000,
      totalUsage: 18000,
      capacityFigureKva: null
    },
    electricitySupplier: {
      id: "current-supplier",
      unitRate: 12.0,
      nightUnitRate: 10.0,
      weekendUnitRate: 10.5,
      capacityChargeKva: null,
      annualPrice: 1400,
      standingCharge: 27.0
    },
    gasUsage: null,
    gasSupplier: null,
    quotes: [
      {
        id: "quote-1",
        sticky: false,
        status: QuoteStatus.GENERATED,
        type: QuoteType.UD,
        duration: 12,
        endDate: new Date(),
        provider: {
          udcoreId: "supplier1",
          displayName: "Supplier 1",
          logoFileName: "supplier1"
        },
        electricQuote: {
          unitRate: 10.5,
          nightUnitRate: 8.5,
          weekendUnitRate: 9.0,
          capacityChargeKva: null,
          standingCharge: 25.0,
          annualPrice: 1200,
          contractType: "Fixed",
          unitRateUplift: 0.1,
          isBespokeUplift: false,
          standingChargeUplift: null,
          priceDifference: -100
        },
        // biome-ignore lint/suspicious/noExplicitAny: <types are not as important in a test>
        gasQuote: {} as unknown as any,
        utilityType: UtilityType.ELECTRICITY
      },
      {
        id: "quote-2",
        sticky: false,
        status: QuoteStatus.GENERATED,
        type: QuoteType.UD,
        duration: 24,
        endDate: new Date(),
        provider: {
          udcoreId: "supplier2",
          displayName: "Supplier 2",
          logoFileName: "supplier2"
        },
        electricQuote: {
          unitRate: 11.0,
          nightUnitRate: 9.0,
          weekendUnitRate: 9.5,
          capacityChargeKva: null,
          standingCharge: 26.0,
          annualPrice: 1300,
          contractType: "Variable",
          unitRateUplift: 0.15,
          isBespokeUplift: false,
          standingChargeUplift: null,
          priceDifference: 0
        },
        // biome-ignore lint/suspicious/noExplicitAny: <types are not as important in a test>
        gasQuote: {} as unknown as any,
        utilityType: UtilityType.ELECTRICITY
      },
      {
        id: "current-supplier",
        sticky: true,
        status: QuoteStatus.GENERATED,
        type: QuoteType.UD,
        duration: 12,
        endDate: new Date(),
        provider: {
          udcoreId: "current",
          displayName: "Current Supplier",
          logoFileName: "current"
        },
        electricQuote: {
          unitRate: 12.0,
          nightUnitRate: 10.0,
          weekendUnitRate: 10.5,
          capacityChargeKva: null,
          standingCharge: 27.0,
          annualPrice: 1400,
          contractType: "Fixed",
          unitRateUplift: 0,
          isBespokeUplift: false,
          standingChargeUplift: null,
          priceDifference: 0
        },
        // biome-ignore lint/suspicious/noExplicitAny: <types are not as important in a test>
        gasQuote: {} as unknown as any,
        utilityType: UtilityType.ELECTRICITY
      }
    ],
    suppliersSupportingVerbalContract: ["supplier1"]
  },
  maxDecimalPlaces: 2,
  tariffRates: {
    day: true,
    night: true,
    weekend: true
  },
  additionalMeterData: null,
  bespokeSupplierUplifts: []
} satisfies QuoteList_And_Quotes;

const defaultProps = {
  data: mockQuoteData,
  quoteListId: "quote-list-1",
  saveChanges: jest.fn(),
  closeModal: jest.fn(),
  showCapacityCharge: false,
  handleCreateContract: jest.fn()
};

describe("QuoteResultsDataTable", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the data table with quotes", () => {
    render(<QuoteResultsDataTable {...defaultProps} />);

    // Check that the table is rendered
    expect(screen.getByRole("table")).toBeInTheDocument();

    // Check that non-sticky quotes are displayed
    expect(screen.getByText("Supplier 1")).toBeInTheDocument();
    expect(screen.getByText("Supplier 2")).toBeInTheDocument();

    // Check that the sticky header is shown
    expect(screen.getByText("Current Supply")).toBeInTheDocument();
  });

  it("handles empty quotes list", () => {
    const emptyProps = {
      ...defaultProps,
      data: {
        ...mockQuoteData,
        quoteList: {
          ...mockQuoteData.quoteList!,
          quotes: []
        }
      }
    };

    render(<QuoteResultsDataTable {...emptyProps} />);

    expect(screen.getByText("No quotes found")).toBeInTheDocument();
    expect(
      screen.getByText(
        "There are no quotes generated for this meter. Get started by updating the search or create a custom quote."
      )
    ).toBeInTheDocument();
  });

  it("returns null when quote list is missing", () => {
    const { container } = render(
      <QuoteResultsDataTable
        {...defaultProps}
        data={{
          quoteList: null,
          maxDecimalPlaces: mockQuoteData.maxDecimalPlaces,
          tariffRates: mockQuoteData.tariffRates,
          additionalMeterData: null,
          bespokeSupplierUplifts: []
        }}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it("handles select all functionality without lag", async () => {
    const user = userEvent.setup();
    const { rerender } = render(<QuoteResultsDataTable {...defaultProps} />);

    const selectAllButton = screen.getByTestId("select-all");

    // Measure performance of select all
    const startTime = performance.now();
    await user.click(selectAllButton);
    const endTime = performance.now();

    // Should complete quickly (under 100ms for a small dataset)
    expect(endTime - startTime).toBeLessThan(100);

    // Force a re-render to ensure memoization is working
    rerender(<QuoteResultsDataTable {...defaultProps} />);

    // The component should not cause excessive re-renders
    await waitFor(() => {
      expect(screen.getByRole("table")).toBeInTheDocument();
    });
  });

  it("filters out sticky rows from regular table rows", () => {
    render(<QuoteResultsDataTable {...defaultProps} />);

    // Check that Current Supplier is in the sticky header section
    const currentSupplyHeader = screen.getByText("Current Supply");
    expect(currentSupplyHeader).toBeInTheDocument();

    // Verify non-sticky quotes are in the main table body
    const tableRows = screen.getAllByRole("row");
    // Should have header rows + sticky row + 2 data rows
    expect(tableRows.length).toBeGreaterThan(3);
  });

  it("displays correct column visibility based on tariff rates", () => {
    const { rerender } = render(<QuoteResultsDataTable {...defaultProps} />);

    // Check that night and weekend rates are visible when tariff rates include them
    expect(screen.getByText("Night Rate")).toBeInTheDocument();
    expect(screen.getByText("Weekend Rate")).toBeInTheDocument();

    // Update props to hide certain rates
    const updatedProps = {
      ...defaultProps,
      data: {
        ...mockQuoteData,
        tariffRates: {
          day: true,
          night: false,
          weekend: false
        }
      }
    };

    rerender(<QuoteResultsDataTable {...updatedProps} />);

    // Night and weekend rates should be hidden based on implementation
    // Note: This depends on how column visibility is handled in the actual component
  });

  it("shows capacity charge column when showCapacityCharge is true", () => {
    const propsWithCapacity = {
      ...defaultProps,
      showCapacityCharge: true
    };

    render(<QuoteResultsDataTable {...propsWithCapacity} />);

    expect(screen.getByText("Capacity Charge")).toBeInTheDocument();
  });

  it("calculates and displays price differences correctly", () => {
    render(<QuoteResultsDataTable {...defaultProps} />);

    // The component should show price difference column when current price exists
    expect(screen.getByText("Price Difference")).toBeInTheDocument();
  });

  it("memoizes rows to prevent unnecessary re-renders", () => {
    const { rerender } = render(<QuoteResultsDataTable {...defaultProps} />);

    // Get initial render count (this is a simplified test)
    const initialTableRows = screen.getAllByRole("row");

    // Trigger a re-render with the same props
    rerender(<QuoteResultsDataTable {...defaultProps} />);

    // The number of rows should remain the same
    const afterRerenderRows = screen.getAllByRole("row");
    expect(afterRerenderRows.length).toBe(initialTableRows.length);
  });

  it("handles quote email form integration", () => {
    render(<QuoteResultsDataTable {...defaultProps} />);

    // Check that the email form is rendered
    expect(screen.getByTestId("quote-email-form")).toBeInTheDocument();
    expect(screen.getByText("Recipient Details")).toBeInTheDocument();
  });
});
