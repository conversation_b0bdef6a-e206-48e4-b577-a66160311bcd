"use client";

import {
  type ColumnFiltersState,
  type Row,
  type RowSelectionState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import type { QuoteList_And_Quotes } from "@watt/api/src/router";
import type { FindUniqueQuoteListSelectQuotesGetPayload } from "@watt/api/src/types/quote/quote-queries";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { EmptyStatePanel } from "@watt/crm/app/account/quotes/components/empty-state";
import { DataTablePagination } from "@watt/crm/components/data-table/data-table-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@watt/crm/components/ui/table";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@watt/crm/components/ui/tooltip";
import { getCommonPinningStyles } from "@watt/crm/utils/common-pinning-styles";
import { QuoteStatus } from "@watt/db/src/enums";
import type { ContractType } from "@watt/db/src/enums";
import { InfoIcon, PenIcon } from "lucide-react";
import Image from "next/image";
import { memo, useCallback, useMemo, useState } from "react";
import { QuoteEmailForm } from "./quote-email-form";
import { quoteResultsColumns } from "./quote-results-columns";
import { DataTableToolbar } from "./quote-results-data-table-toolbar";

type DataTableProps = {
  data: QuoteList_And_Quotes;
  quoteListId: string;
  saveChanges: () => void;
  closeModal: () => void;
  showCapacityCharge: boolean;
  handleCreateContract: (
    contractType: ContractType,
    quoteId: string,
    contactId: string
  ) => void;
};

function disableSendQuoteReason(status: QuoteStatus) {
  const disallowedStatuses: Record<
    string,
    {
      message: string;
      disable: boolean;
    }
  > = {
    [QuoteStatus.BOUNCE]: {
      message: "The previous emails have bounced.",
      disable: false
    },
    [QuoteStatus.UNSUBSCRIBE]: {
      message: "The customer has unsubscribed from quote emails.",
      disable: false
    },
    [QuoteStatus.BLOCKED]: {
      message: "The customer has blocked quote emails.",
      disable: false
    },
    [QuoteStatus.GROUP_UNSUBSCRIBE]: {
      message: "The customer has unsubscribed from quote emails.",
      disable: false
    },
    [QuoteStatus.EXPIRED]: {
      message:
        "The quote has expired, generate a new quote list to send quotes to the customer.",
      disable: true
    },
    [QuoteStatus.ACCEPTED]: {
      message:
        "One of the quotes has already been accepted. You can either create a new quote in the quote table or update the search.",
      disable: true
    }
  };
  return disallowedStatuses[status];
}

type MemoizedTableRowProps = {
  row: Row<FindUniqueQuoteListSelectQuotesGetPayload>;
  rowIndex: number;
  isSelected: boolean;
};

const MemoizedTableRow = memo(function MemoizedTableRow({
  row,
  rowIndex,
  isSelected
}: MemoizedTableRowProps) {
  const cells = row.getVisibleCells();

  const backgroundColor =
    rowIndex % 2 === 0 ? "hsl(var(--muted))" : "hsl(var(--background))";

  return (
    <TableRow
      className={cn(
        "h-12 border-0 transition-all duration-300 hover:bg-muted-foreground/30",
        rowIndex % 2 === 0 && "bg-muted"
      )}
      data-state={isSelected && "selected"}
    >
      {cells.map(cell => (
        <TableCell
          key={cell.id}
          className="text-center *:justify-center"
          style={{
            ...getCommonPinningStyles({
              column: cell.column,
              backgroundColor
            })
          }}
        >
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
});

export function QuoteResultsDataTable({
  data,
  quoteListId,
  showCapacityCharge,
  saveChanges,
  closeModal,
  handleCreateContract
}: DataTableProps) {
  const { quoteList, maxDecimalPlaces, tariffRates } = data;

  const { quotes, currentSupplier, utilityQuote } = useMemo(() => {
    const quotes = quoteList?.quotes ?? [];
    const currentSupplier = quotes.find(quote => quote.sticky);
    const utilityQuote =
      currentSupplier?.electricQuote ?? currentSupplier?.gasQuote;
    return { quotes, currentSupplier, utilityQuote };
  }, [quoteList]);

  const companyId = quoteList?.siteMeter.companySite.company.id;
  const companySiteId = quoteList?.siteMeter.companySite.id;
  const currentPrice = utilityQuote?.annualPrice ?? 0;

  const defaultColumnVisibility = useMemo(
    () => ({
      id: false,
      unitRate:
        tariffRates.day ||
        quotes.some(
          quote => quote.electricQuote?.unitRate || quote.gasQuote?.unitRate
        ),
      nightUnitRate:
        tariffRates.night ||
        quotes.some(quote => quote.electricQuote?.nightUnitRate),
      weekendUnitRate:
        tariffRates.weekend ||
        quotes.some(quote => quote.electricQuote?.weekendUnitRate),
      priceDifference:
        currentPrice > 0 &&
        quotes.some(
          quote =>
            quote.electricQuote?.annualPrice || quote.gasQuote?.annualPrice
        ),
      capacityChargeKva: showCapacityCharge
    }),
    [tariffRates, quotes, currentPrice, showCapacityCharge]
  );

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    defaultColumnVisibility
  );
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");

  const updateColumnVisibility = useCallback(
    (columnKey: string, isVisible: boolean) => {
      setColumnVisibility(prev => ({
        ...prev,
        [columnKey]: isVisible
      }));
    },
    []
  );

  const columns = useMemo(
    () =>
      quoteResultsColumns(
        maxDecimalPlaces,
        currentPrice,
        tariffRates,
        quoteList,
        showCapacityCharge,
        updateColumnVisibility
      ),
    [
      maxDecimalPlaces,
      currentPrice,
      tariffRates,
      quoteList,
      showCapacityCharge,
      updateColumnVisibility
    ]
  );

  const table = useReactTable({
    data: quotes,
    columns,
    initialState: {
      pagination: {
        pageSize: 100
      }
    },
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      columnPinning: { right: ["actions"] },
      globalFilter
    },
    enableRowSelection: row => !row.original.sticky,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });

  const selectedQuotes = useMemo(
    () =>
      Object.keys(rowSelection)
        .map(id => table.getRow(id))
        .filter(row => row && !row.original.sticky)
        .map(row => row.original),
    [table, rowSelection]
  );

  const quoteIds = useMemo(
    () => quotes.filter(quote => !quote.sticky).map(quote => quote.id),
    [quotes]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <do not remove>
  const { stickyRows, nonStickyRows } = useMemo(() => {
    const allRows = table.getRowModel().rows;
    const coreRows = table.getCoreRowModel().rows;
    return {
      nonStickyRows: allRows.filter(row => !row.original.sticky),
      stickyRows: coreRows.filter(row => row.original.sticky)
    };
  }, [table, data, sorting, columnFilters, globalFilter]);

  if (!quoteList || !companyId || !companySiteId) {
    return null;
  }

  return (
    <div className="space-y-4">
      <DataTableToolbar
        table={table}
        currentSupplierId={currentSupplier?.provider.udcoreId}
        utilityType={quoteList.siteMeter.utilityType}
        tariffRates={tariffRates}
        showCapacityCharge={showCapacityCharge}
        quoteListId={quoteListId}
        quoteListStatus={quoteList.status}
        onSubmitForm={() => null}
      />
      <div className="rounded-md">
        <Table outerClassName="max-h-[866px] overflow-hidden hover:overflow-auto [scrollbar-gutter:stable]">
          <TableHeader className="sticky top-0 z-10 h-16 bg-background [&_tr]:border-b-0">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead
                      key={header.id}
                      className="py-4 text-center *:justify-center"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
            <TableStickyHeadRow
              heading="Current Supply"
              totalColSpan={
                table.getAllColumns().filter(column => column.getIsVisible())
                  .length
              }
              rows={stickyRows}
            />
          </TableHeader>
          <TableBody>
            {nonStickyRows.length > 0 ? (
              nonStickyRows.map((row, rowIndex) => (
                <MemoizedTableRow
                  key={row.id}
                  row={row}
                  rowIndex={rowIndex}
                  isSelected={row.getIsSelected()}
                />
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <EmptyStatePanel
                    title="No quotes found"
                    description="There are no quotes generated for this meter. Get started by updating the search or create a custom quote."
                  />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
      <div className="space-y-6">
        <h2 className="border-b py-6 font-bold text-xl">Recipient Details</h2>
        <QuoteEmailForm
          selectedQuotes={selectedQuotes}
          allQuoteIds={quoteIds}
          quoteListId={quoteListId}
          companyId={companyId}
          siteId={companySiteId}
          verbalSupportedSuppliers={quoteList.suppliersSupportingVerbalContract}
          saveChanges={saveChanges}
          closeModal={closeModal}
          handleCreateContract={handleCreateContract}
          noSendReason={disableSendQuoteReason(quoteList.status)}
        />
      </div>
    </div>
  );
}

type TableStickyHeadRowProps<R> = {
  heading: string;
  totalColSpan: number;
  rows: Row<R>[];
};

function TableStickyHeadRow<R>({
  heading,
  totalColSpan,
  rows
}: TableStickyHeadRowProps<R>) {
  const [openTooltip, setOpenTooltip] = useState(false);

  if (!rows.length) {
    return null;
  }

  return (
    <>
      <TableRow>
        <TableHead colSpan={totalColSpan} className="h-auto text-primary">
          <TooltipProvider>
            <Tooltip open={openTooltip} onOpenChange={setOpenTooltip}>
              <TooltipTrigger asChild onClick={() => setOpenTooltip(true)}>
                <div className="flex flex-row items-center justify-center gap-2">
                  <span className="font-semibold text-base italic">
                    {heading}
                  </span>
                  <InfoIcon className="size-4 text-muted-foreground" />
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-[50em]">
                <div className="flex flex-row gap-4 p-2 font-normal">
                  <Image
                    src="/img/bill-tie.png"
                    alt="Bill"
                    width={200}
                    height={200}
                    className="size-28 shrink-0"
                    priority
                  />
                  <div className="flex flex-col gap-2 text-left text-sm">
                    <p className="whitespace-normal break-words">
                      You can include a detailed comparison by providing the
                      current supplier details. Click the{" "}
                      <span className="font-medium">
                        <PenIcon className="inline size-3" />
                        <span className="sr-only fixed">Edit</span>
                      </span>{" "}
                      icon at the end of the row to edit the current supplier.
                    </p>
                    <p className="whitespace-normal break-words">
                      Once updated, the comparison will show the price
                      difference and percentage.{" "}
                      <svg className="mr-1 inline-block size-2 fill-current text-secondary">
                        <title>Green</title>
                        <circle cx="50%" cy="50%" r="50%" />
                      </svg>
                      <span className="text-secondary">Green</span> indicates
                      our offer is cheaper annually.{" "}
                      <svg className="mr-1 inline-block size-2 fill-current text-destructive">
                        <title>Red</title>
                        <circle cx="50%" cy="50%" r="50%" />
                      </svg>
                      <span className="text-destructive">Red</span> indicates
                      our offer is more expensive.
                    </p>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TableHead>
      </TableRow>
      {rows.map(row => (
        <TableRow
          key={row.id}
          className="sticky top-16 z-10 h-12 border-0 text-primary-foreground"
        >
          {row.getVisibleCells().map(cell => (
            <TableCell
              key={cell.id}
              className="text-center *:justify-center"
              style={{
                ...getCommonPinningStyles({
                  column: cell.column,
                  backgroundColor: "var(--primary-light)"
                })
              }}
            >
              {flexRender(cell.column.columnDef.cell, {
                ...cell.getContext()
              })}
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );
}
