import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { statuses } from "@watt/crm/common-data/contracts/data";
import { Badge } from "@watt/crm/components/ui/badge";
import type { ContractStatus } from "@watt/db/src/enums";

const statusStyles: Record<ContractStatus, string> = {
  GENERATED: "bg-purple-200 text-purple-700 hover:bg-purple-300",
  SENT: "bg-green-200 text-green-700 hover:bg-green-300",
  SIGNED: "bg-yellow-200 text-yellow-700 hover:bg-yellow-300",
  PROCESSING: "bg-sky-blue-200 text-sky-blue-700 hover:bg-sky-blue-300"
} as const;

function getStatusStyles(status: ContractStatus) {
  return statusStyles[status] ?? "bg-gray-200 text-gray-700 hover:bg-gray-300";
}

export function ContractStatusBadge({ status }: { status: ContractStatus }) {
  const statusObj = statuses.find(s => s.value === status);

  if (!statusObj) {
    return <div>N/A</div>;
  }

  return (
    <Badge
      variant="default"
      className={cn(
        "flex h-6 w-20 justify-center rounded-md",
        getStatusStyles(status)
      )}
    >
      {statusObj.label}
    </Badge>
  );
}
