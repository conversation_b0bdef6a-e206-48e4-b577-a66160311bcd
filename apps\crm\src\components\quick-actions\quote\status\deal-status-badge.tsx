import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { statuses } from "@watt/crm/common-data/deals/data";
import { Badge } from "@watt/crm/components/ui/badge";
import type { DealStatus } from "@watt/db/src/enums";

const statusStyles: Record<DealStatus, string> = {
  DEAL_MADE: "bg-purple-200 text-purple-700 hover:bg-purple-300"
} as const;

function getStatusStyles(status: DealStatus) {
  return statusStyles[status] ?? "bg-gray-200 text-gray-700 hover:bg-gray-300";
}

export function DealStatusBadge({ status }: { status: DealStatus }) {
  const statusObj = statuses.find(s => s.value === status);

  if (!statusObj) {
    return <div>N/A</div>;
  }

  return (
    <Badge
      variant="default"
      className={cn(
        "flex h-6 w-20 justify-center rounded-md",
        getStatusStyles(status)
      )}
    >
      {statusObj.label}
    </Badge>
  );
}
