import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { statuses } from "@watt/crm/common-data/quotes/data";
import { Badge } from "@watt/crm/components/ui/badge";
import type { QuoteStatus } from "@watt/db/src/enums";

const statusStyles: Record<QuoteStatus, string> = {
  GENERATED: "bg-purple-200 text-purple-700 hover:bg-purple-300",
  SENDING: "bg-sky-blue-200 text-sky-blue hover:bg-sky-blue-300",
  PROCESSED: "bg-blue-200 text-blue-700 hover:bg-blue-300",
  DELIVERED: "bg-green-200 text-green-700 hover:bg-green-300",
  ACCEPTED: "bg-green-200 text-green-700 hover:bg-green-300",
  OPEN: "bg-emerald-200 text-emerald-700 hover:bg-emerald-300",
  CLICK: "bg-yellow-200 text-yellow-700 hover:bg-yellow-300",
  DEFERRED: "bg-yellow-200 text-yellow-700 hover:bg-yellow-300",
  PENDING: "bg-orange-200 text-orange-700 hover:bg-orange-300",
  DROPPED: "bg-red-200 text-red-700 hover:bg-red-300",
  BOUNCE: "bg-red-200 text-red-700 hover:bg-red-300",
  BLOCKED: "bg-gray-200 text-gray-700 hover:bg-gray-300",
  EXPIRED: "bg-gray-200 text-gray-700 hover:bg-gray-300",
  SPAMREPORT: "bg-red-200 text-red-700 hover:bg-red-300",
  UNSUBSCRIBE: "bg-amber-200 text-amber-700 hover:bg-amber-300",
  GROUP_UNSUBSCRIBE: "bg-amber-200 text-amber-700 hover:bg-amber-300",
  GROUP_RESUBSCRIBE: "bg-amber-200 text-amber-700 hover:bg-amber-300",
  ERROR: "bg-red-200 text-red-700 hover:bg-red-300"
} as const;

function getStatusStyles(status: QuoteStatus) {
  return statusStyles[status] ?? "bg-gray-200 text-gray-700 hover:bg-gray-300";
}

export function QuoteStatusBadge({ status }: { status: QuoteStatus }) {
  const statusObj = statuses.find(s => s.value === status);

  if (!statusObj) {
    return <div>N/A</div>;
  }

  return (
    <Badge variant="default" className={cn(getStatusStyles(status))}>
      {statusObj.label}
    </Badge>
  );
}
