"use client";

import { CheckSiteMeterDetailsDialog } from "@watt/crm/components/site/check-site-meter-details-dialog";
import { EditSiteMeterDialog } from "@watt/crm/components/site/edit-site-meter-dialog";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";

export type EditSiteMeterModalQueryParams = {
  modal: "edit-meter-details" | "check-meter-details";
  siteMeterId: string;
};

export function SiteMeterProvider() {
  const { queryParams, removeQueryParams } =
    useQueryParams<EditSiteMeterModalQueryParams>();

  const handleModalClose = () => {
    removeQueryParams([], { newParams: true, mode: "push" });
  };

  if (!queryParams.siteMeterId) {
    return null;
  }

  return (
    <>
      <EditSiteMeterDialog
        siteMeterId={queryParams.siteMeterId}
        isOpen={queryParams.modal === "edit-meter-details"}
        onClose={handleModalClose}
        onSubmit={handleModalClose}
      />
      <CheckSiteMeterDetailsDialog
        siteMeterId={queryParams.siteMeterId}
        isOpen={queryParams.modal === "check-meter-details"}
        onClose={handleModalClose}
        onSubmit={handleModalClose}
      />
    </>
  );
}
