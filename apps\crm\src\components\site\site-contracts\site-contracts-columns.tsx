"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { AllSiteContracts } from "@watt/api/src/router/contract";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { DataTableRowActions } from "@watt/crm/app/account/contracts/components/data-table-row-actions";
import { DataTableColumnHeader } from "@watt/crm/components/data-table/data-table-column-header";
import { textFilter } from "@watt/crm/components/data-table/data-table-filter-functions";
import { ContractStatusBadge } from "@watt/crm/components/quick-actions/quote/status/contract-status-badge";
import type { ContractStatus } from "@watt/db/src/enums";

type ContractQuote = AllSiteContracts[number]["quote"];
type UtilityQuote = ContractQuote["electricQuote"] | ContractQuote["gasQuote"];

function extractQuoteValue<T>(
  quote: ContractQuote,
  extractor: (utilityQuote: UtilityQuote) => T | undefined
): T | undefined {
  return quote.electricQuote
    ? extractor(quote.electricQuote)
    : quote.gasQuote
      ? extractor(quote.gasQuote)
      : undefined;
}

export const siteContractsColumns: ColumnDef<AllSiteContracts[number]>[] = [
  {
    accessorKey: "startDate",
    accessorFn: row =>
      row.startDate ? formatDate(row.startDate, dateFormats.DD_MM_YYYY) : null,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Start Date" />
    ),
    cell: ({ row }) => <span>{row.getValue("startDate") ?? "N/A"}</span>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Start Date"
    }
  },
  {
    accessorKey: "endDate",
    accessorFn: row =>
      row.endDate ? formatDate(row.endDate, dateFormats.DD_MM_YYYY) : null,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="End Date" />
    ),
    cell: ({ row }) => <span>{row.getValue("endDate") ?? "N/A"}</span>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "End Date"
    }
  },
  {
    accessorKey: "term",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Term" />
    ),
    cell: ({ getValue }) => {
      return <div>{getValue<string>()}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Term"
    }
  },
  {
    accessorKey: "supplier",
    accessorFn: contract => contract.quote.provider.displayName,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Supplier" />
    ),
    cell: ({ getValue, row }) => {
      const name = getValue() as string;
      const { logoFileName, udcoreId } = row.original.quote.provider;

      return (
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={name}
          className="h-auto w-[60px] object-scale-down"
          responsive
        />
      );
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Supplier"
    }
  },
  {
    accessorKey: "unitRateUplift",
    accessorFn: contract =>
      extractQuoteValue(contract.quote, q => q?.unitRateUplift),
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Uplift" />
    ),
    cell: ({ getValue }) => {
      return <div>{getValue<string>()}</div>;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Uplift"
    }
  },
  {
    accessorKey: "createdAt",
    accessorFn: row =>
      row.createdAt
        ? formatDate(row.createdAt, dateFormats.DD_MM_YYYY_HH_MM)
        : null,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Signed Date" />
    ),
    cell: ({ row }) => <div>{row.getValue("createdAt")}</div>,
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Signed Date"
    }
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue<ContractStatus>("status");

      return <ContractStatusBadge status={status} />;
    },
    filterFn: textFilter,
    meta: {
      dropdownLabel: "Status"
    }
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <DataTableRowActions
        contractId={row.original.id}
        contractType={row.original.type}
      />
    ),
    filterFn: textFilter
  }
];
