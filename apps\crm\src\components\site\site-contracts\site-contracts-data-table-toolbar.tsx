"use client";

import type { Table } from "@tanstack/react-table";
import { X } from "lucide-react";
import React from "react";
import { useDebounce } from "react-use";

import { statuses } from "@watt/crm/common-data/contracts/data";
import { DataTableFacetedFilter } from "@watt/crm/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@watt/crm/components/data-table/data-table-view-options";
import { Button } from "@watt/crm/components/ui/button";
import { SearchInput } from "@watt/crm/components/ui/search-input";
import { getUniqueSupplierFilterOptions } from "@watt/crm/utils/get-unique-filter-options";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  children?: React.ReactNode;
}

export function SiteContractsDataTableToolbar<TData>({
  table,
  children
}: DataTableToolbarProps<TData>) {
  const [debouncedSearchValue, setDebouncedSearchValue] = React.useState("");
  const searchInputRef = React.useRef<HTMLInputElement>(null);
  const uniqueSuppliers = getUniqueSupplierFilterOptions(
    "supplier",
    table,
    true
  );

  useDebounce(
    () => {
      table.setGlobalFilter(debouncedSearchValue);
    },
    500, // You can adjust the debounce time as needed
    [debouncedSearchValue]
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearchValue(event.target.value);
  };

  const resetFilters = () => {
    table.resetColumnFilters();
    table.resetGlobalFilter();
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
    setDebouncedSearchValue("");
  };

  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  return (
    <div className="flex flex-wrap justify-between gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <SearchInput
          placeholder="Type to search..."
          onChange={handleSearchChange}
          className="h-9 w-[250px]"
          ref={searchInputRef}
        />
        {table.getColumn("supplier") && (
          <DataTableFacetedFilter
            column={table.getColumn("supplier")}
            title="Supplier"
            options={uniqueSuppliers}
          />
        )}
        {table.getColumn("status") && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={statuses}
          />
        )}
        {isFiltered && (
          <Button variant="ghost" onClick={resetFilters} size="sm">
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex gap-2">
        <DataTableViewOptions table={table} />
        {children}
      </div>
    </div>
  );
}
