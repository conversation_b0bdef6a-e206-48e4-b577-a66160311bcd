import type { AllSiteDeals } from "@watt/api/src/router/deal";
import type { DealModalQueryParams } from "@watt/crm/components/quick-actions/deal/deal-provider";
import { Button } from "@watt/crm/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@watt/crm/components/ui/dropdown-menu";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { FileInput, FileSymlink, History, MoreHorizontal } from "lucide-react";
export function DataTableRowActions({
  dealData
}: {
  dealData: AllSiteDeals[number];
}) {
  const { setQueryParams } = useQueryParams<DealModalQueryParams>();

  const handleModalOpen = (modalType: DealModalQueryParams["modal"]) => {
    setQueryParams(
      {
        modal: modalType,
        dealId: dealData.id,
        dealData: JSON.stringify(dealData) // TODO(Bidur): Temp solution to pass deal data to deal re-submission and cancellation modal
      },
      { mode: "push" }
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only fixed">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleModalOpen("submit-deal")}>
          <FileInput className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          Submit to Compliance
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleModalOpen("resubmit-deal")}>
          <FileSymlink className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          Resubmit to Compliance
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleModalOpen("view-cancelled-deal")}
        >
          <FileSymlink className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          View Cancelled Deal
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleModalOpen("view-timeline-deal")}>
          <History className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
          View Timeline
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
