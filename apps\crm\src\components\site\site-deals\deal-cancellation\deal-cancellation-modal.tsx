"use client";

import type { AllSiteDeals } from "@watt/api/src/router/deal";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@watt/crm/components/ui/alert";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@watt/crm/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { Clock, File } from "lucide-react";
import { useMemo } from "react";

type DealCancellationModalProps = {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  dealData: AllSiteDeals[number];
};

export function DealCancellationModal({
  openModal,
  setOpenModal,
  dealData
}: DealCancellationModalProps) {
  const currentProviderComponent = useMemo(() => {
    const { logoFileName, udcoreId } =
      dealData.contract.quote.quoteList.currentProvider;

    return (
      <div className="flex items-end gap-2">
        <p className="text-muted-foreground text-xs">From</p>
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={udcoreId ?? "current provider"}
          className="h-auto w-[60px] object-scale-down"
          responsive
        />
      </div>
    );
  }, [dealData]);

  const newProviderComponent = useMemo(() => {
    const { logoFileName, udcoreId } = dealData.contract.quote.provider;

    return (
      <div className="flex items-end gap-2">
        <p className="text-muted-foreground text-xs">To</p>
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={udcoreId ?? "new provider"}
          className="h-auto w-[60px] object-scale-down"
          responsive
        />
      </div>
    );
  }, [dealData]);

  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto p-10 pt-16">
        <div className="flex gap-6">
          <DialogHeader>
            <DialogTitle className="font-medium text-2xl">
              Deal Cancellation
            </DialogTitle>
            <DialogDescription>
              View the details and reason for this deal cancellation.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-1 items-start justify-between">
            <div className="flex flex-row gap-2">
              {currentProviderComponent}
              {newProviderComponent}
            </div>
            <p className="text-muted-foreground">
              Deal ID #{dealData.id.slice(0, 6)}
            </p>
          </div>
        </div>
        <div className="flex justify-between py-1">
          <p className="flex items-center gap-2 text-muted-foreground text-xs">
            <Clock className="size-3" />
            <span>Original Submission</span>
            <span>15 Nov 2024, 14:30</span>
          </p>
          <p className="flex items-center gap-2 text-muted-foreground text-xs">
            <File className="size-3" />
            <span>Cancelled by CO</span>
            <Avatar className="size-5 hover:cursor-pointer">
              <AvatarImage src="/img/logo.svg" alt="company logo" />
              <AvatarFallback>CO</AvatarFallback>
            </Avatar>
            <span>15 Nov 2024, 15:42</span>
          </p>
        </div>
        <div className="space-y-8">
          <Alert
            variant="warn"
            className="rounded-none border-0 border-yellow-500 border-l-4 text-black"
          >
            <AlertTitle>Failed Credit Check</AlertTitle>
            <AlertDescription className="mt-2 font-medium text-destructive text-xs">
              Reason: Credit assessment does not meet supplier criteria
            </AlertDescription>
            <AlertDescription className="mt-2 text-xs">
              Please explore alternative supplier options for this customer.
            </AlertDescription>
          </Alert>
          <div className="space-y-2">
            <h3>Compliance Cancellation Notes</h3>
            <Textarea
              value="Deal cancelled due to failed credit assessment. Customer's credit score does not meet the minimum requirement. Please review alternative suppliers with more flexible credit criteria and submit a new deal accordingly."
              className="min-h-[100px]"
              disabled
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
