import {
  type DealResubmission,
  DealResubmissionSchema
} from "@watt/api/src/types/deal";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@watt/crm/components/ui/alert";
import { But<PERSON> } from "@watt/crm/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/crm/components/ui/form";
import {
  RadioGroup,
  RadioGroupItem
} from "@watt/crm/components/ui/radio-group";
import { Textarea } from "@watt/crm/components/ui/textarea";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useZodForm } from "@watt/crm/hooks/use-zod-form";
import { FileInput } from "lucide-react";
import { FileSelectDropdown } from "../deal-submission/file-select-dropdown";

type DealResubmissionFormProps = {
  onSubmit: () => void;
};

export function DealResubmissionForm({ onSubmit }: DealResubmissionFormProps) {
  const form = useZodForm({
    schema: DealResubmissionSchema,
    mode: "onChange",
    defaultValues: {
      companyName: "",
      companyNumber: "",
      rejectionNotes:
        "LOA not found for this customer, please make a new one or attach your existing file to this resubmission. Missing sole trader details: DOB and address history (minimum 2 addresses required for trading period <3 years)"
    }
  });

  async function handleSubmit(data: DealResubmission) {
    try {
      toast({
        title: "Success",
        description: "Deal resubmission submitted successfully"
      });
      onSubmit();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit deal resubmission",
        variant: "destructive"
      });
    }
  }

  return (
    <FormWrapper form={form} handleSubmit={handleSubmit}>
      <div className="space-y-8">
        <Alert
          variant="warn"
          className="rounded-none border-0 border-yellow-500 border-l-4 text-black"
        >
          <AlertTitle>LOA Not Found</AlertTitle>
          <AlertDescription className="mt-2 font-medium text-destructive text-xs">
            Reason: Unable to locate LOA in submitted files or company systems
          </AlertDescription>
          <AlertDescription className="mt-2 text-xs">
            Please link the LOA file or provide the reference for compliance to
            locate the LOA
          </AlertDescription>
        </Alert>
        <Alert
          variant="warn"
          className="rounded-none border-0 border-yellow-500 border-l-4 text-black"
        >
          <AlertTitle>Written Contract - Missing Information</AlertTitle>
          <AlertDescription className="mt-2 font-medium text-destructive text-xs">
            Reason: Required contract details missing from submitted document
          </AlertDescription>
          <AlertDescription className="mt-2 text-xs">
            Please link the updated contract file
          </AlertDescription>
        </Alert>
        <div className="space-y-8">
          <FormField
            control={form.control}
            name="rejectionNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Compliance Rejection Notes*</FormLabel>
                <FormControl>
                  <Textarea {...field} className="min-h-[100px]" disabled />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="loaStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Required Document*</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-2"
                  >
                    <FormLabel>LOA</FormLabel>
                    <FormItem className="flex items-start space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem
                          value="WILL_ATTACH"
                          className="mt-0.5"
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        <FileSelectDropdown
                          label="Written LOA, I will attach the file in this submission."
                          value={form.watch("loaDocumentIds")}
                          onFileSelect={fileIds => {
                            form.setValue(
                              "loaDocumentIds",
                              fileIds as string[]
                            );
                          }}
                          allowMultiple
                        />
                        {form.formState.errors.loaDocumentIds && (
                          <FormMessage>
                            {form.formState.errors.loaDocumentIds?.message}
                          </FormMessage>
                        )}
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="IN_COMPANY_INBOX" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Written LOA, Compliance can find it in the company
                        inbox.
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="RECORDING_IN_SYSTEM" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Verbal LOA, Compliance can find recording in the system.
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="contractDocumentIds"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FileSelectDropdown
                    label="Contract"
                    value={field.value}
                    onFileSelect={field.onChange}
                    allowMultiple
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="resubmissionNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Resubmission Notes*</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Please provide information where to find supporting document..."
                    className="min-h-[100px]"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <Button
        type="submit"
        variant="secondary"
        width="lg"
        className="mt-8 ml-auto flex"
      >
        Confirm and Submit
        <FileInput className="ml-2 size-4" />
      </Button>
    </FormWrapper>
  );
}
