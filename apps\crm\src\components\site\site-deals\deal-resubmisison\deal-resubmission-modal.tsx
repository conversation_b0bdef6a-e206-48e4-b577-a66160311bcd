"use client";

import type { AllSiteDeals } from "@watt/api/src/router/deal";
import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@watt/crm/components/ui/dialog";
import { useMemo } from "react";
import { DealResubmissionContainer } from "./form/deal-resubmission-container";

type DealResubmissionModalProps = {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  dealData: AllSiteDeals[number];
};

export function DealResubmissionModal({
  openModal,
  setOpenModal,
  dealData
}: DealResubmissionModalProps) {
  const currentProviderComponent = useMemo(() => {
    const { logoFileName, udcoreId } =
      dealData.contract.quote.quoteList.currentProvider;

    return (
      <div className="flex items-end gap-2">
        <p className="text-muted-foreground text-xs">From</p>
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={udcoreId ?? "current provider"}
          className="h-auto w-[60px] object-scale-down"
          responsive
        />
      </div>
    );
  }, [dealData]);

  const newProviderComponent = useMemo(() => {
    const { logoFileName, udcoreId } = dealData.contract.quote.provider;

    return (
      <div className="flex items-end gap-2">
        <p className="text-muted-foreground text-xs">To</p>
        <ProviderLogo
          logoFileName={logoFileName}
          displayName={udcoreId ?? "new provider"}
          className="h-auto w-[60px] object-scale-down"
          responsive
        />
      </div>
    );
  }, [dealData]);

  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto p-10 pt-16">
        <div className="flex gap-6">
          <DialogHeader>
            <DialogTitle className="font-medium text-2xl">
              Deal Resubmission
            </DialogTitle>
            <DialogDescription>
              Resubmit this deal to Electralink for processing. Review the
              details before proceeding.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-1 items-start justify-between">
            <div className="flex flex-row gap-2">
              {currentProviderComponent}
              {newProviderComponent}
            </div>
            <p className="text-muted-foreground">
              Deal ID #{dealData.id.slice(0, 6)}
            </p>
          </div>
        </div>
        <DealResubmissionContainer
          onSubmit={() => setOpenModal(false)}
          dealId={dealData.id}
        />
      </DialogContent>
    </Dialog>
  );
}
