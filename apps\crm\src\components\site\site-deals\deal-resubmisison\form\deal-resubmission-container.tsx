import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@watt/crm/components/ui/avatar";
import { Skeleton } from "@watt/crm/components/ui/skeleton";
import { Clock, File } from "lucide-react";
import { DealResubmissionForm } from "../deal-resubmission-form";

export type DealResubmissionContainerProps = {
  dealId: string;
  onSubmit: () => void;
};

export function DealResubmissionContainer({
  dealId,
  onSubmit
}: DealResubmissionContainerProps) {
  return (
    <>
      <div className="flex justify-between py-1">
        <p className="flex items-center gap-2 text-muted-foreground text-xs">
          <Clock className="size-3" />
          <span>Original Submission</span>
          <span>15 Nov 2024, 14:30</span>
        </p>
        <p className="flex items-center gap-2 text-muted-foreground text-xs">
          <File className="size-3" />
          <span>Rejected by CO</span>
          <Avatar className="size-5 hover:cursor-pointer">
            <AvatarImage src="/img/logo.svg" alt="company logo" />
            <AvatarFallback>CO</AvatarFallback>
          </Avatar>
          <span>15 Nov 2024, 15:42</span>
        </p>
      </div>
      <DealResubmissionForm onSubmit={onSubmit} />
    </>
  );
}

function DealResubmissionSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-[300px] w-full" />
      <Skeleton className="ml-auto h-10 w-52" />
    </div>
  );
}
