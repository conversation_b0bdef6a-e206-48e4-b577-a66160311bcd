import type { DealSubmission } from "@watt/api/src/types/deal";
import {
  Alert,
  AlertDescription,
  AlertTitle
} from "@watt/crm/components/ui/alert";
import { Zap } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { DealDetailsSection } from "./deal-details-section";

export function ReviewQuoteDetails() {
  const { getValues } = useFormContext<DealSubmission>();
  const values = getValues();

  return (
    <div className="space-y-6">
      <div className="text-muted-foreground text-xs">
        Please review all deal details carefully before submission
        <ul className="list-inside list-disc">
          <li>Quote Details, Supplier, Term</li>
          <li>Rates and Charges</li>
          <li>Go Live Date, End Date</li>
        </ul>
      </div>
      <Alert
        variant="warn"
        className="rounded-none border-0 border-yellow-500 border-l-4 text-black"
      >
        <Zap className="size-4 stroke-black" />
        <AlertTitle>Heads up!</AlertTitle>
        <AlertDescription className="mt-2 text-xs">
          Incorrect details? Cancel this submission and create a new quote.
          Changes cannot be made after submission.
        </AlertDescription>
      </Alert>

      <DealDetailsSection {...values} />
    </div>
  );
}
