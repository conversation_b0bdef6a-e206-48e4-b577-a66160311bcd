import { TRPCClientError } from "@trpc/client";
import type { DealSubmission } from "@watt/api/src/types/deal";
import { log } from "@watt/common/src/utils/axiom-logger";
import type { DealModalQueryParams } from "@watt/crm/components/quick-actions/deal/deal-provider";
import { Button } from "@watt/crm/components/ui/button";
import { toast } from "@watt/crm/components/ui/use-toast";
import { useQueryParams } from "@watt/crm/hooks/use-query-params";
import { trpcClient } from "@watt/crm/utils/api";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { useWizard } from "react-use-wizard";
import { RequoteModal } from "../../requote-modal";

export type DealSubmissionFields =
  | keyof DealSubmission
  | `contact.${keyof DealSubmission["contact"]}`;

export type WizardNavigationProps = {
  triggerFields: DealSubmissionFields[];
  isSubmitting?: boolean;
};

export function WizardNavigation({
  triggerFields,
  isSubmitting
}: WizardNavigationProps) {
  const { isFirstStep, isLastStep, previousStep, nextStep } = useWizard();
  const { trigger, getValues } = useFormContext<DealSubmission>();
  const values = getValues();
  const { removeQueryParams } = useQueryParams<DealModalQueryParams>();
  const [requoteDialogIsOpen, setRequoteDialogOpen] = useState<boolean>(false);
  const cancelDealMutation = trpcClient.deal.cancelDeal.useMutation();

  const handleNext = async () => {
    if (!isLastStep) {
      const isValid = await trigger(triggerFields);
      if (isValid) {
        nextStep();
      }
    }
  };

  const handleRequoteSubmit = async () => {
    try {
      const dealId = values.dealId;
      if (!dealId) {
        throw new Error("Deal ID is required");
      }

      await cancelDealMutation.mutateAsync({ dealId });
      setRequoteDialogOpen(false);
      removeQueryParams([], { newParams: true, mode: "push" });
      toast({
        title: "Deal Successfully Cancelled",
        description:
          "The deal has been successfully cancelled and updated to 'Cancelled by SA'.",
        variant: "success"
      });
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while cancelling the deal. Please check the form and try again."
          : error.message;
      toast({
        title: "Deal Cancellation Failed",
        description,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="flex justify-between pt-4">
      {isFirstStep ? (
        <Button
          variant="outline-destructive"
          className="hover:bg-destructive hover:text-white"
          type="button"
          onClick={() => setRequoteDialogOpen(true)}
        >
          Requote
        </Button>
      ) : (
        <Button variant="outline" onClick={previousStep} type="button">
          Back
        </Button>
      )}
      <Button
        variant="secondary"
        onClick={handleNext}
        type={isLastStep ? "submit" : "button"}
        disabled={isSubmitting}
      >
        {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {isLastStep ? <>Submit</> : <>Continue</>}
      </Button>
      <RequoteModal
        modalOpen={requoteDialogIsOpen}
        setModalOpen={setRequoteDialogOpen}
        onSubmit={handleRequoteSubmit}
        isSubmitting={cancelDealMutation.isPending}
      />
    </div>
  );
}
