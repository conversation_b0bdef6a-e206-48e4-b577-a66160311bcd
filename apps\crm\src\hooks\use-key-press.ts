import { useCallback, useEffect, useReducer } from "react";

type State = { [key: string]: boolean };
type Action = { type: "down" | "up"; key: string };

function keyPressReducer(state: State, action: Action): State {
  switch (action.type) {
    case "down":
      return { ...state, [action.key]: true };
    case "up":
      return { ...state, [action.key]: false };
    default:
      return state;
  }
}

export function useKeyPress(targetKeys: string[]): State {
  const [keysPressed, dispatch] = useReducer(keyPressReducer, {});

  const downHandler = useCallback(
    ({ key }: KeyboardEvent) => {
      if (targetKeys.includes(key) && !keysPressed[key]) {
        dispatch({ type: "down", key });
      }
    },
    [targetKeys, keysPressed]
  );

  const upHandler = useCallback(
    ({ key }: KeyboardEvent) => {
      if (targetKeys.includes(key) && keysPressed[key]) {
        dispatch({ type: "up", key });
      }
    },
    [targetKeys, keysPressed]
  );

  useEffect(() => {
    window.addEventListener("keydown", downHandler);
    window.addEventListener("keyup", upHandler);
    return () => {
      window.removeEventListener("keydown", downHandler);
      window.removeEventListener("keyup", upHandler);
    };
  }, [downHandler, upHandler]);

  return keysPressed;
}
