import { useEffect } from "react";

type UsePreventUnloadProps = {
  shouldPreventUnload: boolean;
};

/**
 * A React hook that prevents the user from accidentally leaving/closing the page when there are unsaved changes.
 * When activated, it will show a browser-native confirmation dialog.
 *
 * @example
 * ```tsx
 * function MyForm() {
 *   const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
 *
 *   usePreventUnload({ shouldPreventUnload: hasUnsavedChanges });
 *
 *   return <form>...</form>;
 * }
 * ```
 *
 * @param props.shouldPreventUnload - When true, prevents page unload and shows a confirmation dialog
 */
export function usePreventUnload({
  shouldPreventUnload
}: UsePreventUnloadProps) {
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (shouldPreventUnload) {
        e.preventDefault();

        return null;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [shouldPreventUnload]);
}
