"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useMemo } from "react";
import type { z } from "zod";

// See for more detail https://github.com/vercel/next.js/discussions/47583
export type QueryParamsOptions = {
  mode?: "push" | "replace";
  newParams?: boolean;
};

export function useQueryParams<T extends object>() {
  const searchParams = useSearchParams();
  const queryParams = Object.fromEntries(searchParams.entries()) as Partial<T>;
  let urlSearchParams = new URLSearchParams(searchParams.toString());

  function setQueryParams(params: Partial<T>, options?: QueryParamsOptions) {
    if (options?.newParams) {
      urlSearchParams = new URLSearchParams();
    }

    for (const [key, value] of Object.entries(params)) {
      if (!value === undefined || value === null || value === "") {
        urlSearchParams.delete(key);
        continue;
      }
      if (typeof value === "boolean") {
        urlSearchParams.set(key, value ? "true" : "false");
      } else {
        urlSearchParams.set(key, String(value));
      }
    }

    const search = urlSearchParams.toString();
    const query = search ? `?${search}` : window.location.pathname;
    const method = options?.mode === "push" ? "pushState" : "replaceState";

    window.history[method](null, "", query);
  }

  function removeQueryParams(keys: (keyof T)[], options?: QueryParamsOptions) {
    if (options?.newParams) {
      urlSearchParams = new URLSearchParams();
    } else {
      for (const key of keys) {
        urlSearchParams.delete(key as string);
      }
    }

    const search = urlSearchParams.toString();
    const query = search ? `?${search}` : window.location.pathname;
    const method = options?.mode === "push" ? "pushState" : "replaceState";

    window.history[method](null, "", query);
  }

  return { queryParams, setQueryParams, removeQueryParams };
}

type QueryParamsSchema<
  TSchema extends z.ZodType<Record<string, unknown>, z.ZodTypeDef>
> = {
  queryParams: z.infer<TSchema>;
  setQueryParams: (params: Partial<z.infer<TSchema>>) => void;
};

export function useQueryParamsSchema<
  TSchema extends z.ZodType<Record<string, unknown>, z.ZodTypeDef>
>(schema: TSchema): QueryParamsSchema<TSchema> {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const queryParams = useMemo(() => {
    const parsedQueryParams = schema.safeParse(
      Object.fromEntries(searchParams.entries())
    );
    return parsedQueryParams.success
      ? parsedQueryParams.data
      : ({} as z.infer<TSchema>);
  }, [searchParams, schema]);

  const setQueryParams = useCallback(
    (params: Partial<z.infer<TSchema>>) => {
      const urlSearchParams = new URLSearchParams(searchParams.toString());
      for (const [key, value] of Object.entries(params)) {
        if (value === undefined || value === null) {
          urlSearchParams.delete(key);
          continue;
        }
        if (Array.isArray(value)) {
          urlSearchParams.set(key, value.join(","));
        } else {
          urlSearchParams.set(key, value.toString());
        }
      }

      const search = urlSearchParams.toString();
      const query = search ? `?${search}` : "";
      router.push(`${pathname}${query}`);
    },
    [pathname, router, searchParams]
  );

  return { queryParams, setQueryParams };
}
