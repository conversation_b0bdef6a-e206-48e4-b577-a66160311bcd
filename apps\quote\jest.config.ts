import type { Config } from "jest";
import nextJest from "next/jest";
import { pathsToModuleNameMapper } from "ts-jest";

import { compilerOptions } from "./tsconfig.json";

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: "./"
});

const config: Config = {
  preset: "ts-jest",
  testEnvironment: "jsdom",
  rootDir: "./",
  coverageDirectory: "<rootDir>/.coverage",
  collectCoverage: true,
  testPathIgnorePatterns: ["<rootDir>/node_modules/"],
  moduleFileExtensions: ["ts", "tsx", "js", "jsx"],
  coverageReporters: ["json", "lcov", "html", "text-summary"],
  projects: [
    {
      displayName: "Quote App - client",
      testEnvironment: "jest-environment-jsdom",
      transform: {
        "^.+\\.(ts|tsx)$": [
          "ts-jest",
          {
            tsconfig: {
              jsx: "react-jsx"
            }
          }
        ]
      },
      moduleDirectories: ["node_modules", "<rootDir>"],
      moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
        prefix: "<rootDir>/"
      }),
      testMatch: [
        "<rootDir>/**/*.client.spec.[jt]s?(x)",
        "<rootDir>/**/!(*.client|*.server).spec.[jt]s?(x)"
      ],
      testPathIgnorePatterns: ["<rootDir>/dist/"],
      setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"]
    },
    {
      displayName: "Quote App - server",
      testEnvironment: "node",
      transform: {
        "^.+\\.(ts|tsx)$": [
          "ts-jest",
          {
            tsconfig: {
              jsx: "react-jsx"
            }
          }
        ]
      },
      moduleDirectories: ["node_modules", "<rootDir>"],
      moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
        prefix: "<rootDir>/"
      }),
      testMatch: ["<rootDir>/**/*.server.spec.[jt]s?(x)"],
      testPathIgnorePatterns: ["<rootDir>/dist/"],
      setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"]
    }
  ]
};

export default createJestConfig(config);
