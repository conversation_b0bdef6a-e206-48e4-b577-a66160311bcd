import fs from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";
import { withAxiom } from "next-axiom";

/**
 * Checks if the required files exist in the public directory.
 * If these don't exist the application will throw an error.
 * We would rather see this error at build time than runtime.
 * @param {string[]} fileNames files in the public directory that are required
 */
function checkRequiredFiles(fileNames) {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);

  for (const fileName of fileNames) {
    const filePath = path.join(__dirname, "public", fileName);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Required file not found: ${filePath}`);
    }
  }
}

checkRequiredFiles(["pdf.worker.min.js"]);

/** @type {import("next").NextConfig} */
let nextConfig = {
  productionBrowserSourceMaps: process.env.ENABLE_SOURCE_MAPS === "true",
  reactStrictMode: true,
  // Enables hot reloading for local packages without a build step
  transpilePackages: ["@watt/api", "@watt/db", "@watt/events", "@watt/common"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "avatars.githubusercontent.com"
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com"
      }
    ]
  },
  logging: {
    fetches: {
      fullUrl: true
    }
  },
  serverExternalPackages: [
    // These deps need to be added directly to the crm
    // for them to be packaged in final build
    "@sparticuz/chromium",
    "puppeteer-core",
    "pdfjs-dist"
  ],
  distDir: process.env.NODE_ENV === "development" ? ".next/dev" : ".next/build",
  experimental: {
    ppr: true,
    authInterrupts: true,
    optimizePackageImports: [
      "@watt/api",
      "@watt/common",
      "@watt/db",
      "@watt/emails",
      "@watt/events",
      "@watt/external-apis",
      "@watt/pdfs",
      "@watt/redis"
    ],
    serverSourceMaps: process.env.ENABLE_SOURCE_MAPS === "true",
    externalDir: true // < for workspaces
  },
  // We run the type check and linting in the CI pipeline. Not during CD build.
  eslint: {
    ignoreDuringBuilds: true
  },
  typescript: {
    ignoreBuildErrors: true
  }
  // compiler: {
  //   removeConsole: {
  //     exclude: ["log", "info", "warn", "debug", "time", "timeEnd", "trace"],
  //   },
  // }
};

// TODO (Stephen): Setup bundle analyzer
if (process.env.ANALYZE === "true") {
  const withBundleAnalyzer = await import("@next/bundle-analyzer").then(
    mod => mod.default
  );

  nextConfig = withBundleAnalyzer({
    enabled: true
  })(nextConfig);
}

export default withAxiom(nextConfig);
