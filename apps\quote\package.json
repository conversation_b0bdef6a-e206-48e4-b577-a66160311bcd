{"name": "@watt/quote", "version": "0.1.0", "private": true, "description": "Quote App", "keywords": [], "license": "SEE LICENSE IN LICENSE.md", "author": "Watt Utilities", "type": "module", "scripts": {"build": "next build", "debug": "NODE_OPTIONS='--inspect' next dev", "dev": "next dev --turbo -p 3001", "dev:inspect": "node --inspect-brk $(which next) dev", "lint": "dotenv -v SKIP_ENV_VALIDATION=1 next lint", "lint:fix": "pnpm lint --fix", "preview": "next build && next start", "start": "next start -p 3001", "test": "jest --passWithNoTests", "test:cicd": "vercel env pull .env.vercel; dotenv -e ../../.env.vercel -- jest --passWithNoTests --testNamePattern '@cicd'; rm .env.vercel", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "catalog:", "@mermaid-js/mermaid-cli": "catalog:", "@next-safe-action/adapter-react-hook-form": "^1.0.12", "@novu/headless": "^2.0.0", "@novu/js": "^2.6.1", "@novu/node": "2.0.1", "@novu/react": "2.5.0", "@prisma/client": "catalog:", "@radix-ui/react-accessible-icon": "catalog:", "@radix-ui/react-accordion": "catalog:", "@radix-ui/react-alert-dialog": "catalog:", "@radix-ui/react-aspect-ratio": "catalog:", "@radix-ui/react-avatar": "catalog:", "@radix-ui/react-checkbox": "catalog:", "@radix-ui/react-collapsible": "catalog:", "@radix-ui/react-context-menu": "catalog:", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-focus-scope": "catalog:", "@radix-ui/react-hover-card": "catalog:", "@radix-ui/react-label": "catalog:", "@radix-ui/react-menubar": "catalog:", "@radix-ui/react-navigation-menu": "catalog:", "@radix-ui/react-popover": "catalog:", "@radix-ui/react-progress": "catalog:", "@radix-ui/react-radio-group": "catalog:", "@radix-ui/react-scroll-area": "catalog:", "@radix-ui/react-select": "catalog:", "@radix-ui/react-separator": "catalog:", "@radix-ui/react-slider": "catalog:", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-switch": "catalog:", "@radix-ui/react-tabs": "catalog:", "@radix-ui/react-toast": "catalog:", "@radix-ui/react-toggle": "catalog:", "@radix-ui/react-toggle-group": "catalog:", "@radix-ui/react-tooltip": "catalog:", "@radix-ui/react-visually-hidden": "^1.1.0", "@sparticuz/chromium": "catalog:", "@sparticuz/chromium-min": "catalog:", "@statelyai/inspect": "^0.4.0", "@supabase/ssr": "catalog:", "@supabase/supabase-js": "catalog:", "@tanstack/react-query": "catalog:", "@tanstack/react-query-devtools": "catalog:", "@tanstack/react-query-next-experimental": "catalog:", "@tanstack/react-table": "catalog:", "@tanstack/react-virtual": "catalog:", "@trpc/client": "catalog:", "@trpc/next": "catalog:", "@trpc/react-query": "catalog:", "@trpc/server": "catalog:", "@vercel/analytics": "catalog:", "@vercel/og": "catalog:", "@watt/api": "workspace:*", "@watt/common": "workspace:*", "@watt/db": "workspace:*", "@watt/emails": "workspace:*", "@watt/events": "workspace:*", "@watt/external-apis": "workspace:*", "@watt/notifications": "workspace:*", "@watt/pdfs": "workspace:^", "@watt/redis": "workspace:*", "@xstate/react": "^5.0.1", "bcrypt": "catalog:", "class-variance-authority": "catalog:", "cmdk": "catalog:", "d3-scale-chromatic": "catalog:", "date-fns": "catalog:", "effect": "^3.12.1", "embla-carousel-react": "catalog:", "encoding": "catalog:", "file-saver": "catalog:", "html-entities": "catalog:", "immer": "catalog:", "input-otp": "^1.4.2", "jotai": "catalog:", "jszip": "catalog:", "jwt-decode": "catalog:", "keyv": "catalog:", "lodash": "catalog:", "lucide-react": "catalog:", "meyda": "catalog:", "mime": "^4.0.6", "next": "catalog:", "next-axiom": "catalog:", "next-safe-action": "^7.10.2", "next-themes": "catalog:", "nextjs-toploader": "^3.7.15", "null-loader": "catalog:", "pdfjs-dist": "catalog:", "postcss": "catalog:", "posthog-js": "^1.184.1", "posthog-node": "^4.2.1", "prisma-erd-generator": "catalog:", "puppeteer": "catalog:", "puppeteer-core": "catalog:", "raw-loader": "catalog:", "react": "catalog:react19", "react-confetti": "^6.4.0", "react-day-picker": "catalog:", "react-dom": "catalog:react19", "react-dropzone": "^14.3.5", "react-error-boundary": "^4.1.2", "react-hook-form": "catalog:", "react-pdf": "catalog:", "react-resizable": "catalog:", "react-resizable-panels": "catalog:", "react-scan": "^0.0.54", "react-use": "catalog:", "react-use-wizard": "^2.3.0", "react-wrap-balancer": "catalog:", "recharts": "catalog:", "rehype-raw": "catalog:", "rehype-react": "catalog:", "rehype-sanitize": "catalog:", "remark-parse": "catalog:", "remark-rehype": "catalog:", "sharp": "catalog:", "sonner": "catalog:", "superjson": "catalog:", "tailwindcss-animate": "^1.0.7", "ts-pattern": "catalog:", "unified": "catalog:", "use-sound": "^4.0.3", "uuid": "catalog:", "vaul": "catalog:", "xstate": "^5.19.1", "zod": "catalog:", "zod-to-json-schema": "^3.23.3", "zustand": "catalog:", "zustand-computed": "catalog:"}, "devDependencies": {"@next/bundle-analyzer": "catalog:", "@next/eslint-plugin-next": "catalog:", "@types/bcrypt": "catalog:", "@types/d3-scale-chromatic": "catalog:", "@types/file-saver": "catalog:", "@types/lodash": "catalog:", "@types/meyda": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:react19", "@types/react-color": "catalog:", "@types/react-dom": "catalog:react19", "@types/react-resizable": "catalog:", "@types/uuid": "catalog:", "@types/yoga-layout": "catalog:", "@watt/typescript": "workspace:*", "@wixc3/react-board": "catalog:", "autoprefixer": "catalog:", "dotenv-cli": "catalog:", "esbuild": "catalog:", "prisma": "catalog:", "rehype": "catalog:", "rehype-autolink-headings": "catalog:", "rehype-slug": "catalog:", "remark": "catalog:", "remark-code-import": "catalog:", "remark-gfm": "catalog:", "shiki": "catalog:", "unist-builder": "catalog:", "unist-util-visit": "catalog:"}}