"use client";

import { AuthError } from "@supabase/supabase-js";
import { TRPCClientError } from "@trpc/client";
import type { Address_Find_Many } from "@watt/api/src/router/address";
import {
  type CreateCompanyAndContactInput,
  CreateCompanyAndContactInputSchema
} from "@watt/api/src/types/pcw/company";
import type { ContactAddressFormData } from "@watt/api/src/types/people";
import { isValidBusinessReference } from "@watt/common/src/regex/company-number";
import { isValidPostcode } from "@watt/common/src/regex/postcode";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { BusinessType } from "@watt/db/src/enums";
import type { BusinessTargetResult } from "@watt/external-apis/src/libs/experian/business-targeter";
import { DatePickerInlineInput } from "@watt/quote/components/date-picker-inline-input";
import { DynamicAddressFormList } from "@watt/quote/components/dynamic-address/dynamic-address-form-list";
import {
  AddressSelection,
  CompanySelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/quote/components/lookup";
import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardActions } from "@watt/quote/components/quote-wizard/quote-wizard-actions";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { QuoteWizardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-title";
import { Button } from "@watt/quote/components/ui/button";
import { Checkbox } from "@watt/quote/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/quote/components/ui/form";
import { Input } from "@watt/quote/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/quote/components/ui/select";
import { toast } from "@watt/quote/components/ui/use-toast";
import { Verification } from "@watt/quote/components/verification";
import { routes } from "@watt/quote/config/routes";
import { useAddressSearch } from "@watt/quote/hooks/use-address-search";
import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import { useZodForm } from "@watt/quote/hooks/use-zod-form";
import { trpcClient } from "@watt/quote/utils/api";
import { createClientComponentClient } from "@watt/quote/utils/supabase/client";
import { ChevronRightIcon, Loader2 } from "lucide-react";
import {
  startTransition,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";
import { useDebounce } from "react-use";
import {
  type EmailVerificationRef,
  EmailVerificationWrapper
} from "./email-verification-wrapper";

type CompanyFormData = CreateCompanyAndContactInput;

type QueryParams = {
  businessSearch: string;
  selectedBusinessRef: string;
  businessAddressSearch: string;
  selectedBusinessAddressId: string;
  businessType: string;
  contactForename: string;
  contactSurname: string;
  contactEmail: string;
  contactPhone: string;
  contactPosition: string;
  contactDateOfBirth: string;
  emailVerified: string;
};

type CompanyResultGroups<T = BusinessTargetResult[]> = {
  active: T;
  disolved: T;
};

function groupSearchResults(
  results: BusinessTargetResult[]
): CompanyResultGroups {
  return {
    active: results.filter(company => company.businessStatus !== "D"),
    disolved: results.filter(company => company.businessStatus === "D")
  };
}

export function CompanyForm() {
  const businessNumberContainerRef = useRef<HTMLDivElement>(null);
  const businessAddressContainerRef = useRef<HTMLDivElement>(null);
  const emailVerificationRef = useRef<EmailVerificationRef>(null);

  const [isBusinessModalOpen, setIsBusinessModalOpen] = useState(false);
  const [isBusinessAddressModalOpen, setIsBusinessAddressModalOpen] =
    useState(false);

  const [contactAddressList, setContactAddressList] = useState<
    ContactAddressFormData[]
  >([]);

  const { queryParams, setQueryParams } =
    useQueryParams<Partial<QueryParams>>();

  const [companySearchResults, setCompanySearchResults] =
    useState<CompanyResultGroups>({
      active: [],
      disolved: []
    });
  const [businessAddressData, setBusinessAddressData] = useState<
    Address_Find_Many | undefined
  >();

  const { fetchAddress, isFetchingAddress } = useAddressSearch();
  const businessTargetMutation = trpcClient.pcw.businessTargetGet.useMutation();
  const companyContactMutation =
    trpcClient.pcw.companyCreateCompanyAndContact.useMutation();

  const form = useZodForm({
    schema: CreateCompanyAndContactInputSchema,
    mode: "onChange",
    defaultValues: {
      company: {
        businessNumber: queryParams.selectedBusinessRef ?? "",
        businessName: "",
        businessType: undefined,
        businessAddressId: ""
      },
      contact: {
        forename: queryParams.contactForename ?? "",
        surname: queryParams.contactSurname ?? "",
        email: queryParams.contactEmail ?? "",
        phoneNumber: queryParams.contactPhone ?? "",
        position: queryParams.contactPosition ?? "",
        dateOfBirth: queryParams.contactDateOfBirth ?? "",
        addresses: []
      },
      agreements: {
        isAuthorized: false,
        letterOfAuthority: false,
        termsAndConditions: false
      }
    }
  });

  function fullResetBusinessFormData() {
    form.resetField("company.businessNumber");
    form.resetField("company.businessType");
    form.resetField("company.businessName");
    setQueryParams({
      businessSearch: "",
      selectedBusinessRef: "",
      businessType: ""
    });
    setCompanySearchResults({
      active: [],
      disolved: []
    });
  }

  useDebounce(
    async function lookupBusinessRefOnSearchInput() {
      try {
        if (!queryParams.businessSearch) {
          fullResetBusinessFormData();
          return;
        }

        const isBusinessRef = isValidBusinessReference(
          queryParams.businessSearch
        );
        const isPostcode = isValidPostcode(queryParams.businessSearch);

        const postcode = isPostcode ? queryParams.businessSearch : undefined;
        const businessRef = isBusinessRef
          ? queryParams.businessSearch
          : undefined;

        // If businessSearch query is a postcode then name must also be set to the postcode to get results
        const name = postcode ?? queryParams.businessSearch;

        const result = await businessTargetMutation.mutateAsync({
          ...(businessRef ? { businessRef } : { name }),
          ...(isPostcode ? { postcode } : {})
        });

        setCompanySearchResults(groupSearchResults(result));

        if (queryParams.selectedBusinessRef) {
          const selectedCompany = result.find(
            company => company.businessRef === queryParams.selectedBusinessRef
          );
          if (!selectedCompany) {
            return;
          }

          form.setValue(
            "company.businessNumber",
            queryParams.selectedBusinessRef
          );
          form.setValue("company.businessName", selectedCompany.commercialName);
        }
      } catch (e) {
        if (e instanceof TRPCClientError) {
          const message = e.message;
          // Extract the error code from the data if available
          const errorData = e.data as { code?: string; [key: string]: unknown };
          const code = errorData?.code || "INTERNAL_SERVER_ERROR";

          switch (code) {
            case "NOT_FOUND":
              toast({
                title: "Business Not Found",
                description: message,
                variant: "destructive"
              });
              break;

            case "BAD_REQUEST":
              toast({
                title: "Invalid Search",
                description: message,
                variant: "destructive"
              });
              break;

            default:
              toast({
                title: "Search Failed",
                description:
                  message || "Unable to search for business. Please try again.",
                variant: "destructive"
              });
          }
        } else {
          toast({
            title: "Connection Error",
            description:
              "Unable to search for business. Please check your connection.",
            variant: "destructive"
          });
        }
      }
    },
    500,
    [queryParams.businessSearch, queryParams.selectedBusinessRef, form]
  );

  const lookupBusinessAddressOnSearchInput = useCallback(async () => {
    try {
      if (!queryParams.businessAddressSearch) {
        return;
      }

      const postcodeIsValid = isValidPostcode(
        queryParams.businessAddressSearch
      );

      if (!postcodeIsValid) {
        return;
      }

      const result = await fetchAddress(queryParams.businessAddressSearch);
      setBusinessAddressData(result);

      if (queryParams.selectedBusinessAddressId) {
        form.setValue(
          "company.businessAddressId",
          queryParams.selectedBusinessAddressId
        );
      }
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
    }
  }, [
    fetchAddress,
    queryParams.businessAddressSearch,
    queryParams.selectedBusinessAddressId,
    form
  ]);

  useDebounce(lookupBusinessAddressOnSearchInput, 1000, [
    queryParams.businessAddressSearch
  ]);

  const selectedCompany = useMemo(
    () =>
      companySearchResults?.active.find(
        company =>
          company.businessRef ===
          queryParams.selectedBusinessRef?.toLocaleUpperCase()
      ),
    [queryParams.selectedBusinessRef, companySearchResults?.active]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(
    function updateBusinessType() {
      if (!queryParams.selectedBusinessRef || !companySearchResults.active) {
        return;
      }

      const businessType = selectedCompany?.businessType;
      if (!businessType) {
        return;
      }

      setQueryParams({ businessType });
      form.setValue("company.businessType", businessType);
      form.setValue("company.businessName", selectedCompany.commercialName);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      queryParams.selectedBusinessRef,
      companySearchResults.active,
      selectedCompany,
      form
    ]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function defaultSearchToSelectedReferenceOnMount() {
    if (queryParams.businessSearch || !queryParams.selectedBusinessRef) {
      return;
    }

    setQueryParams({
      businessSearch: queryParams.selectedBusinessRef
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleBusinessAddressSelect = (addressId: string) => {
    const selectedAddressObj = businessAddressData?.find(
      address => address.id === addressId
    );

    if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
      toast({
        title: "Invalid business address",
        description:
          "Selected business address does not have a valid address or postcode",
        variant: "destructive"
      });
      return;
    }

    setQueryParams({
      selectedBusinessAddressId: addressId
    });
    form.setValue("company.businessAddressId", addressId);
    setIsBusinessAddressModalOpen(false);
  };

  const handleBusinessEntitySelect = (businessRefRaw: string) => {
    const businessRef = businessRefRaw.toLocaleUpperCase();
    const selectedCompany = companySearchResults.active.find(
      company => company.businessRef === businessRef
    );
    if (!selectedCompany) {
      return;
    }

    // Batch updates in one React state update
    startTransition(() => {
      setQueryParams({
        selectedBusinessRef: businessRef
      });
      form.setValue("company.businessNumber", businessRef);
      form.setValue("company.businessName", selectedCompany.commercialName);
      setIsBusinessModalOpen(false);
    });
  };

  const handleBusinessAddressSearchInputChange = (
    businessAddressSearch: string
  ) => {
    setQueryParams({
      businessAddressSearch,
      selectedBusinessAddressId: ""
    });
    form.setValue("company.businessAddressId", "");
  };

  const handleFormSubmit = async (data: CompanyFormData) => {
    // Check email verification FIRST, before any other validation
    if (queryParams.emailVerified !== "true") {
      form.setError("contact.email", {
        type: "manual",
        message: "Please verify your email address before continuing"
      });
      form.setFocus("contact.email");
      return;
    }

    try {
      const result = await companyContactMutation.mutateAsync(data);

      // If a new profile was created, refresh the session to get updated JWT
      if (result.profileCreated) {
        const supabase = createClientComponentClient();
        const { error } = await supabase.auth.refreshSession();
        if (error) {
          console.error(
            "Failed to refresh session after profile creation:",
            error
          );
        }
      }

      window.location.href = `${routes.site}?companyId=${result.companyId}&companyAddressId=${result.companyAddressId}&contactId=${result.contactId}`;
    } catch (e) {
      if (e instanceof TRPCClientError) {
        const message = e.message;
        const errorData = e.data as {
          code?: string;
          zodError?: unknown;
          [key: string]: unknown;
        };
        const code = errorData?.code || "INTERNAL_SERVER_ERROR";
        log.error("Company creation failed:", {
          code,
          message,
          data: errorData
        });

        // Handle Zod validation errors
        if (errorData?.zodError) {
          toast({
            title: "Validation Error",
            description: "Please check your information and try again.",
            variant: "destructive"
          });
          return;
        }

        // Handle specific error codes
        switch (code) {
          case "NOT_FOUND":
            toast({
              title: "Not Found",
              description: message,
              variant: "destructive"
            });
            break;

          case "BAD_REQUEST":
            toast({
              title: "Invalid Information",
              description: message,
              variant: "destructive"
            });
            break;

          case "UNAUTHORIZED":
            toast({
              title: "Authentication Required",
              description: "Please sign in to continue.",
              variant: "destructive"
            });
            // Optionally redirect to login
            setTimeout(() => {
              window.location.href = "/";
            }, 2000);
            break;

          case "PRECONDITION_FAILED":
            toast({
              title: "Cannot Complete Action",
              description: message,
              variant: "destructive"
            });
            break;

          default:
            toast({
              title: "Something went wrong",
              description: message || "Please try again later.",
              variant: "destructive"
            });
        }
      } else {
        // Handle non-TRPC errors (network issues, etc)
        const errorMessage = e instanceof Error ? e.message : String(e);
        log.error("Unexpected error:", { error: errorMessage });
        toast({
          title: "Connection Error",
          description: "Please check your internet connection and try again.",
          variant: "destructive"
        });
      }
    }
  };

  const watchedBusinessType = form.watch("company.businessType");

  // Email verification handlers
  const handleEmailChange = (value: string) => {
    setQueryParams({
      contactEmail: value,
      emailVerified: ""
    });
    form.setValue("contact.email", value);
    form.clearErrors("contact.email");
  };

  const handleSendVerificationCode = useCallback(
    async (email: string) => {
      // Clear verification errors
      const currentError = form.formState.errors.contact?.email;
      if (
        currentError?.message ===
        "Please verify your email address before continuing"
      ) {
        form.clearErrors("contact.email");
      }

      const isEmailValid = await form.trigger("contact.email");
      if (!isEmailValid) {
        throw new Error("Invalid email");
      }

      const supabase = createClientComponentClient();
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: true,
          data: { role: "QUOTE_APP" }
        }
      });

      if (error) {
        // Handle specific auth errors for sending OTP
        if (error instanceof AuthError) {
          switch (error.code) {
            case "over_email_send_rate_limit":
              throw new Error(
                "Too many requests. Please wait before requesting a new verification code."
              );
            case "email_not_confirmed":
              throw new Error(
                "Email address not confirmed. Please check your email."
              );
            case "invalid_credentials":
              throw new Error("Invalid email address format.");
            default:
              throw new Error(
                error.message || "Failed to send verification code"
              );
          }
        }
        throw error;
      }

      toast({
        title: "Verification code sent",
        description: "Please check your email for the verification code."
      });
    },
    [form]
  );

  const handleVerifyCode = useCallback(
    async (email: string, code: string) => {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: code,
        type: "email"
      });

      if (error) {
        const { title, description } = (() => {
          if (error instanceof AuthError) {
            // Handle specific auth error codes
            switch (error.code) {
              case "otp_expired":
                return {
                  title: "Verification code expired",
                  description: "Please request a new verification code."
                };
              case "otp_disabled":
                return {
                  title: "OTP verification disabled",
                  description: "Please contact support for assistance."
                };
              case "otp_not_found":
                return {
                  title: "Invalid verification code",
                  description: "Please check the code and try again."
                };
              default:
                return {
                  title: "Verification failed",
                  description:
                    error.message || "Please check the code and try again."
                };
            }
          }

          // Fallback for non-AuthError errors
          return {
            title: "Verification failed",
            description: "Please check the code and try again."
          };
        })();

        toast({
          title,
          description,
          variant: "destructive"
        });

        return false;
      }

      if (data.session) {
        // Check if user has the correct role for quote app
        const userRole = data.session.user?.user_metadata?.role;

        // Only allow QUOTE_APP role or users without a role (new signups)
        if (userRole && userRole !== "QUOTE_APP") {
          // Sign out the non-quote-app user
          await supabase.auth.signOut();

          toast({
            title: "Access Denied",
            description:
              "This email is associated with a staff account. Please use a different email address.",
            variant: "destructive"
          });

          // Reset the email field
          setQueryParams({
            emailVerified: "",
            contactEmail: ""
          });
          form.setValue("contact.email", "");

          // Reset verification component state
          emailVerificationRef.current?.reset();

          return false;
        }

        setQueryParams({ emailVerified: "true" });
        toast({
          title: "Email verified",
          description: "Your email has been successfully verified."
        });
        return true;
      }

      return false;
    },
    [form, setQueryParams]
  );

  const handleVerificationError = (error: Error) => {
    // Extract title from error message if it follows pattern "Title: Description"
    const errorMessage = error.message;
    const colonIndex = errorMessage.indexOf(". ");

    const title =
      colonIndex > -1 && colonIndex < 50
        ? errorMessage.substring(0, colonIndex)
        : "Error";

    const description =
      colonIndex > -1 && colonIndex < 50
        ? errorMessage.substring(colonIndex + 2)
        : errorMessage;

    toast({
      title,
      description,
      variant: "destructive"
    });
  };

  const handleVerifiedChange = (verified: boolean) => {
    setQueryParams({
      emailVerified: verified ? "true" : ""
    });
  };

  const handleEmailValidation = async () => {
    const currentError = form.formState.errors.contact?.email;
    if (
      currentError?.message ===
      "Please verify your email address before continuing"
    ) {
      form.clearErrors("contact.email");
    }
    return await form.trigger("contact.email");
  };

  const handleClearEmailErrors = () => {
    form.clearErrors("contact.email");
  };

  const handleChangeEmail = async () => {
    // Sign out the user when they want to change email
    const supabase = createClientComponentClient();
    await supabase.auth.signOut();
  };

  return (
    <FormWrapper form={form} handleSubmit={handleFormSubmit} className="grow">
      <QuoteWizard>
        <QuoteWizardContent className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-[2fr_2fr_2fr]">
          <QuoteWizardItem>
            <QuoteWizardTitle>Company</QuoteWizardTitle>
            <QuoteWizardItem>
              <FormField
                control={form.control}
                name="company.businessNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Business Name *
                    </FormLabel>
                    <FormControl>
                      <div
                        ref={businessNumberContainerRef}
                        className="flex w-full flex-col"
                      >
                        <LookUp
                          open={isBusinessModalOpen}
                          onOpenChange={setIsBusinessModalOpen}
                        >
                          <LookUpTrigger
                            fieldValue={field.value}
                            isLoading={businessTargetMutation.isPending}
                            className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                          >
                            {selectedCompany ? (
                              <div className="max-w-96">
                                <CompanySelection
                                  {...selectedCompany}
                                  isTriggerDisplay
                                />
                              </div>
                            ) : (
                              <span className="font-normal">
                                Select a company, charity or sole trader
                              </span>
                            )}
                          </LookUpTrigger>
                          <LookUpContent
                            placeholder="Search by name or number"
                            searchInput={queryParams.businessSearch}
                            onSearchInputChange={businessSearch => {
                              setQueryParams({
                                businessSearch
                              });
                            }}
                            isLoading={businessTargetMutation.isPending}
                            container={businessNumberContainerRef.current}
                          >
                            <LookUpGroup className="p-0">
                              {companySearchResults?.active.map(company => (
                                <LookUpItem
                                  key={company.businessRef}
                                  value={company.businessRef}
                                  disabled={company.businessStatus !== "A"}
                                  onSelect={handleBusinessEntitySelect}
                                >
                                  <CompanySelection {...company} />
                                </LookUpItem>
                              ))}
                            </LookUpGroup>
                          </LookUpContent>
                        </LookUp>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="company.businessType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Business Type *
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(data: BusinessType) => {
                          setQueryParams({
                            businessType: data
                          });
                          form.setValue("company.businessType", data);
                        }}
                        value={field.value}
                      >
                        <SelectTrigger
                          className={cn(
                            "h-12 px-4 text-base shadow-sm hover:bg-muted",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          <SelectValue placeholder="Select the business type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={BusinessType.LTD}>
                            Limited Company
                          </SelectItem>
                          <SelectItem value={BusinessType.CHARITY}>
                            Charity
                          </SelectItem>
                          <SelectItem value={BusinessType.SOLE_TRADER}>
                            Sole Trader
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="company.businessAddressId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Business Address *
                    </FormLabel>
                    <FormControl>
                      <div
                        ref={businessAddressContainerRef}
                        className="flex w-full flex-col"
                      >
                        <LookUp
                          open={isBusinessAddressModalOpen}
                          onOpenChange={setIsBusinessAddressModalOpen}
                        >
                          <LookUpTrigger
                            fieldValue={field.value ?? ""}
                            isLoading={isFetchingAddress}
                            className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                          >
                            {field.value && businessAddressData ? (
                              <div className="max-w-96">
                                <AddressSelection
                                  isTriggerDisplay
                                  address={(() => {
                                    const foundAddress =
                                      businessAddressData.find(
                                        addr => addr.id === field.value
                                      );
                                    if (foundAddress) {
                                      return foundAddress;
                                    }

                                    const firstAddress = businessAddressData[0];
                                    if (!firstAddress) {
                                      throw new Error(
                                        "No business addresses available"
                                      );
                                    }
                                    return firstAddress;
                                  })()}
                                />
                              </div>
                            ) : (
                              <span className="font-normal">
                                Select a business address...
                              </span>
                            )}
                          </LookUpTrigger>
                          <LookUpContent
                            placeholder="Search by postcode, mpan or mprn number"
                            searchInput={queryParams.businessAddressSearch}
                            onSearchInputChange={
                              handleBusinessAddressSearchInputChange
                            }
                            isLoading={isFetchingAddress}
                            container={businessAddressContainerRef.current}
                          >
                            <LookUpGroup className="p-0">
                              {businessAddressData?.map(address => (
                                <LookUpItem
                                  key={address.id}
                                  value={address.id}
                                  onSelect={handleBusinessAddressSelect}
                                >
                                  <AddressSelection address={address} />
                                </LookUpItem>
                              ))}
                            </LookUpGroup>
                          </LookUpContent>
                        </LookUp>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {watchedBusinessType === BusinessType.SOLE_TRADER && (
                <FormField
                  control={form.control}
                  name="contact.addresses"
                  render={() => (
                    <FormItem>
                      <FormLabel className="font-normal text-base text-muted-foreground">
                        Addresses *
                      </FormLabel>
                      <FormControl>
                        <DynamicAddressFormList
                          contactAddresses={contactAddressList}
                          updateContactAddresses={setContactAddressList}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </QuoteWizardItem>
          </QuoteWizardItem>
          <QuoteWizardItem>
            <QuoteWizardTitle>Contact</QuoteWizardTitle>
            <QuoteWizardItem>
              <FormField
                control={form.control}
                name="contact.forename"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Forename *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          "h-12 px-4 text-base shadow-sm hover:bg-muted",
                          !field.value && "text-muted-foreground"
                        )}
                        placeholder="Enter Forename"
                        onChange={e => {
                          const value = e.target.value;
                          setQueryParams({
                            contactForename: value
                          });
                          form.setValue("contact.forename", value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contact.surname"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Surname *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          "h-12 px-4 text-base shadow-sm hover:bg-muted",
                          !field.value && "text-muted-foreground"
                        )}
                        placeholder="Enter Surname"
                        onChange={e => {
                          const value = e.target.value;
                          setQueryParams({
                            contactSurname: value
                          });
                          form.setValue("contact.surname", value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contact.email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Email *
                    </FormLabel>
                    <FormControl>
                      <Verification
                        value={field.value}
                        onChange={handleEmailChange}
                        onSend={handleSendVerificationCode}
                        onVerify={handleVerifyCode}
                        onError={handleVerificationError}
                        isVerified={queryParams.emailVerified === "true"}
                        onVerifiedChange={handleVerifiedChange}
                        resendDelay={10000}
                      >
                        <EmailVerificationWrapper
                          ref={emailVerificationRef}
                          onValidate={handleEmailValidation}
                          onClearErrors={handleClearEmailErrors}
                          onChangeEmail={handleChangeEmail}
                        />
                      </Verification>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contact.phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Phone Number *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          "h-12 px-4 text-base shadow-sm hover:bg-muted",
                          !field.value && "text-muted-foreground"
                        )}
                        placeholder="Enter Phone Number"
                        onChange={e => {
                          const value = e.target.value;
                          setQueryParams({
                            contactPhone: value
                          });
                          form.setValue("contact.phoneNumber", value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact.position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-base text-muted-foreground">
                      Position *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          "h-12 px-4 text-base shadow-sm hover:bg-muted",
                          !field.value && "text-muted-foreground"
                        )}
                        placeholder="Enter Position"
                        onChange={e => {
                          const value = e.target.value;
                          setQueryParams({
                            contactPosition: value
                          });
                          form.setValue("contact.position", value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {watchedBusinessType === BusinessType.SOLE_TRADER && (
                <FormField
                  control={form.control}
                  name="contact.dateOfBirth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-normal text-base text-muted-foreground">
                        Date of Birth *
                      </FormLabel>
                      <FormControl>
                        <DatePickerInlineInput
                          {...field}
                          placeholder="dd/mm/yyyy"
                          onChange={value => {
                            setQueryParams({
                              contactDateOfBirth: value
                            });
                            form.setValue("contact.dateOfBirth", value);
                          }}
                          calendarProps={{
                            toDate: new Date(),
                            captionLayout: "dropdown-buttons",
                            fromYear: 1920,
                            toYear: new Date().getFullYear(),
                            disabled: { after: new Date() }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </QuoteWizardItem>
          </QuoteWizardItem>

          <QuoteWizardItem>
            <QuoteWizardTitle>Agreements</QuoteWizardTitle>
            <QuoteWizardItem>
              <FormField
                control={form.control}
                name="agreements.isAuthorized"
                render={({ field }) => (
                  <FormItem className="space-x-2">
                    <FormControl suppressHydrationWarning>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="[&_svg]:size-3"
                      />
                    </FormControl>
                    <FormLabel className="font-normal text-base">
                      I confirm that I am authorized to manage this company's
                      account and consent to a credit check being carried out on
                      the company.
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="agreements.letterOfAuthority"
                render={({ field }) => (
                  <FormItem className="space-x-2" suppressHydrationWarning>
                    <FormControl suppressHydrationWarning>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="[&_svg]:size-3"
                      />
                    </FormControl>
                    <FormLabel className="font-normal text-base">
                      I agree with signing the{" "}
                      <a
                        href="https://app.watt.co.uk/assets/pdf/watt_loa.pdf"
                        className="text-secondary"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Letter of Authority
                      </a>{" "}
                      to allow Watt.co.uk to act on my behalf within the
                      industry and with my current supplier. The LOA is valid
                      for 30 days of signing, unless a contract is signed at
                      which point the LOA length will reflect the length of the
                      contract.
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="agreements.termsAndConditions"
                render={({ field }) => (
                  <FormItem className="space-x-2" suppressHydrationWarning>
                    <FormControl suppressHydrationWarning>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="[&_svg]:size-3"
                      />
                    </FormControl>
                    <FormLabel className="font-normal text-base">
                      I agree with the{" "}
                      <a
                        href="https://app.watt.co.uk/assets/pdf/watt-terms-and-conditions.pdf"
                        className="text-secondary"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Terms and Conditions
                      </a>{" "}
                      of{" "}
                      <a
                        href="https://watt.co.uk"
                        className="text-secondary"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Watt.co.uk
                      </a>
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuoteWizardItem>
          </QuoteWizardItem>
        </QuoteWizardContent>
        <QuoteWizardActions>
          <Button
            type="submit"
            variant="secondary"
            className="text-base"
            disabled={companyContactMutation.isPending}
          >
            {companyContactMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Next <ChevronRightIcon className="ml-1 size-5" />
          </Button>
        </QuoteWizardActions>
      </QuoteWizard>
    </FormWrapper>
  );
}
