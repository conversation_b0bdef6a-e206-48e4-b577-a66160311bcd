"use client";

import { But<PERSON> } from "@watt/quote/components/ui/button";
import {
  VerificationActions,
  VerificationCancel,
  VerificationError,
  VerificationField,
  VerificationInput,
  VerificationOTP,
  VerificationOTPField,
  VerificationResend,
  VerificationStatus,
  VerificationTrigger,
  VerificationValue,
  useVerification
} from "@watt/quote/components/verification";
import { Check as CheckIcon } from "lucide-react";
import type React from "react";
import { useImperativeHandle } from "react";

export interface EmailVerificationRef {
  reset: () => void;
}

interface EmailVerificationWrapperProps {
  onValidate: () => Promise<boolean>;
  onClearErrors: () => void;
  onChangeEmail?: () => Promise<void>;
  ref?: React.Ref<EmailVerificationRef>;
}

export function EmailVerificationWrapper({
  // TODO onValidate is not called
  onValidate,
  onClearErrors,
  onChangeEmail,
  ref
}: EmailVerificationWrapperProps) {
  const { actions } = useVerification();

  useImperativeHandle(
    ref,
    () => ({
      reset: () => actions.reset()
    }),
    [actions]
  );

  return (
    <>
      {/* Input state - shown when not verified and not showing OTP */}
      <VerificationField>
        <VerificationInput
          placeholder="Enter Email"
          className="h-12 px-4 text-base shadow-sm hover:bg-muted"
        />
        <VerificationTrigger className="h-12">Verify</VerificationTrigger>
      </VerificationField>

      {/* Verified state */}
      <VerificationStatus>
        <div className="relative">
          <VerificationValue
            className="peer h-12 px-4 pe-9 text-base text-muted-foreground shadow-sm"
            placeholder="Enter Email"
          />
          <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-green-600">
            <CheckIcon size={16} aria-hidden="true" />
          </div>
        </div>
        <Button
          type="button"
          variant="link"
          className="h-auto p-0 text-sm"
          onClick={async () => {
            if (onChangeEmail) {
              await onChangeEmail();
            }
            actions.change();
          }}
        >
          Change Email
        </Button>
      </VerificationStatus>

      {/* OTP input state */}
      <VerificationOTPField>
        <VerificationOTP />
        <VerificationActions>
          <VerificationCancel />
          <VerificationResend />
        </VerificationActions>
      </VerificationOTPField>

      <VerificationError />
    </>
  );
}
