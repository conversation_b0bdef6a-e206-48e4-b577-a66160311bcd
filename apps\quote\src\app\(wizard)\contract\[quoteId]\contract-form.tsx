"use client";

import { TRPCClientError } from "@trpc/client";
import { Provider<PERSON>ogo } from "@watt/common/src/components/provider-logo";
import { log } from "@watt/common/src/utils/axiom-logger";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatCurrency } from "@watt/common/src/utils/format-currency";
import { QuoteRateItem } from "@watt/quote/components/quote-rate-item";
import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardActions } from "@watt/quote/components/quote-wizard/quote-wizard-actions";
import { QuoteWizardCard } from "@watt/quote/components/quote-wizard/quote-wizard-card";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { QuoteWizardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-title";
import { Button } from "@watt/quote/components/ui/button";
import { Checkbox } from "@watt/quote/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/quote/components/ui/form";
import { Input } from "@watt/quote/components/ui/input";
import { Textarea } from "@watt/quote/components/ui/textarea";
import { toast } from "@watt/quote/components/ui/use-toast";
import { useZodForm } from "@watt/quote/hooks/use-zod-form";
import { trpcClient } from "@watt/quote/utils/api";
import {
  calculateAnnualPrice,
  extractDurationYears,
  getProductPlan
} from "@watt/quote/utils/quote-utils";
import {
  ChevronLeftIcon,
  DownloadIcon,
  FileTextIcon,
  Loader2Icon
} from "lucide-react";
import { Calligraffitti } from "next/font/google";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo } from "react";
import { ContractFormSchema } from "./form";
import { SkeletonLoader } from "./skeleton-loader";

const signatureFont = Calligraffitti({
  subsets: ["latin"],
  weight: ["400"]
});

type ContractFormProps = {
  quoteId: string;
  contactId: string;
};

export function ContractForm({ quoteId, contactId }: ContractFormProps) {
  const router = useRouter();

  const form = useZodForm({
    schema: ContractFormSchema,
    mode: "onChange",
    defaultValues: {
      bankName: "",
      accountNumber: "",
      accountHolderName: "",
      sortCode: "",
      signature: "",
      comments: "",
      hasDownloadedContract: false,
      isAuthorized: false,
      commissionAcknowledged: false
    }
  });

  const signContract = trpcClient.pcw.signContract.useMutation();
  const generatePrefilledContract =
    trpcClient.pcw.generatePrefilledContract.useMutation();

  const { data, isLoading } = trpcClient.pcw.quoteGetQuoteById.useQuery({
    id: quoteId
  });

  const quote = data?.quote;
  const isSigned = data?.deal?.signedAt;
  const maxDecimalPlaces = data?.maxDecimalPlaces;
  const electricQuote = quote?.electricQuote;
  const gasQuote = quote?.gasQuote;

  const contractFilename = `${quoteId}.pdf`;

  const handleSubmit = useCallback(async () => {
    try {
      const values = form.getValues();

      await signContract.mutateAsync({
        quoteId,
        contactId,
        bankName: values.bankName,
        accountNumber: values.accountNumber,
        sortCode: values.sortCode,
        signature: values.signature,
        comments: values.comments,
        accountHolderName: values.accountHolderName
      });

      router.push(`/signed/${quoteId}?contactId=${contactId}`);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while signing the contract"
          : error.message;
      toast({
        title: "Unable to sign contract",
        description,
        variant: "destructive"
      });
    }
  }, [signContract, quoteId, contactId, form, router]);

  const handleDownload = useCallback(async () => {
    try {
      const result = await generatePrefilledContract.mutateAsync({
        quoteId,
        contactId
      });

      if (result.signedUrl) {
        const response = await fetch(result.signedUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = contractFilename ?? "contract.pdf";
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        form.setValue("hasDownloadedContract", true);
        form.clearErrors("hasDownloadedContract");
      }
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while downloading the prefilled contract"
          : error.message;
      toast({
        title: "Unable to download prefilled contract",
        description,
        variant: "destructive"
      });
    }
  }, [generatePrefilledContract, quoteId, contactId, contractFilename, form]);

  const onBack = useCallback(() => {
    router.back();
  }, [router]);

  const [annualPrice, durationYears, productPlan] = useMemo(() => {
    return !quote
      ? [0, 0, ""]
      : [
          calculateAnnualPrice(quote),
          extractDurationYears(quote),
          getProductPlan(quote)
        ];
  }, [quote]);

  useEffect(() => {
    if (isSigned) {
      router.push(`/signed/${quoteId}?contactId=${contactId}`);
    }
  }, [isSigned, quoteId, contactId, router]);

  useEffect(() => {
    if (!isLoading && !quote) {
      router.push("/company");
    }
  }, [isLoading, quote, router]);

  if (isLoading || isSigned || !quote) {
    return <SkeletonLoader />;
  }

  return (
    <FormWrapper form={form} handleSubmit={handleSubmit} className="grow">
      <QuoteWizard>
        <QuoteWizardContent>
          <div className="flex flex-col gap-12 sm:gap-16 lg:flex-row [&_*]:placeholder:italic">
            <QuoteWizardItem className="flex-1 gap-8">
              <QuoteWizardItem>
                <QuoteWizardTitle>Contract Summary</QuoteWizardTitle>
                <QuoteWizardCard className="gap-y-8">
                  <QuoteWizardItem className="flex-row items-center justify-between">
                    <ProviderLogo
                      logoFileName={quote.provider.logoFileName}
                      displayName={quote.provider.displayName}
                      width={168}
                      height={84}
                    />
                    <div className="flex flex-col items-end gap-1.5 sm:gap-3">
                      <span className="font-semibold text-xl sm:text-2xl">
                        {formatCurrency(annualPrice)}
                        <span className="text-sm">/year</span>
                      </span>
                      <span className="font-medium text-xs sm:text-sm">
                        {formatCurrency(annualPrice / 12)}
                        /month
                      </span>
                    </div>
                  </QuoteWizardItem>
                  <div className="space-y-3 divide-y rounded-lg border bg-muted p-3 [&_div:not(:first-child)]:pt-3">
                    <QuoteRateItem
                      label="Contract length"
                      value={`${durationYears}`}
                      unit={durationYears === 1 ? "year" : "years"}
                    />
                    <QuoteRateItem
                      label="Day unit rate"
                      value={(
                        electricQuote?.unitRate || gasQuote?.unitRate
                      )?.toFixed(maxDecimalPlaces)}
                      unit="p/unit"
                    />
                    <QuoteRateItem
                      label="Night unit rate"
                      value={
                        electricQuote?.nightUnitRate
                          ? Number(electricQuote.nightUnitRate).toFixed(
                              maxDecimalPlaces
                            )
                          : undefined
                      }
                      unit="p/unit"
                    />
                    <QuoteRateItem
                      label="Weekend unit rate"
                      value={
                        electricQuote?.weekendUnitRate
                          ? Number(electricQuote.weekendUnitRate).toFixed(
                              maxDecimalPlaces
                            )
                          : undefined
                      }
                      unit="p/unit"
                    />
                    <QuoteRateItem
                      label="Standing charge"
                      value={(
                        electricQuote?.standingCharge ||
                        gasQuote?.standingCharge
                      )?.toFixed(maxDecimalPlaces)}
                      unit="p/day"
                    />
                    <QuoteRateItem label="Product plan" value={productPlan} />
                  </div>
                </QuoteWizardCard>
              </QuoteWizardItem>
              <FormField
                control={form.control}
                name="hasDownloadedContract"
                render={() => (
                  <div className="flex flex-col gap-4">
                    <QuoteWizardCard className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
                      <div className="flex flex-col gap-4">
                        <div className="flex items-start gap-2 sm:items-center">
                          <FileTextIcon className="size-5 shrink-0" />
                          <span className="-mt-1 sm:-mt-0 font-semibold">
                            Please read your generated contract
                          </span>
                        </div>
                        <p className="text-muted-foreground text-sm">
                          You can download the contract and read it offline.
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="outline-secondary"
                        className="w-full text-base sm:w-auto [&_svg]:mr-2 [&_svg]:size-4"
                        onClick={handleDownload}
                        disabled={generatePrefilledContract.isPending}
                      >
                        {generatePrefilledContract.isPending ? (
                          <Loader2Icon className="animate-spin" />
                        ) : (
                          <DownloadIcon />
                        )}
                        Download
                      </Button>
                    </QuoteWizardCard>
                    <FormMessage />
                  </div>
                )}
              />
            </QuoteWizardItem>
            <QuoteWizardItem className="flex-1">
              <QuoteWizardTitle>Contract Signing</QuoteWizardTitle>
              <QuoteWizardCard>
                <QuoteWizardItem>
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-normal text-base text-muted-foreground">
                          Bank name *
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Mybank"
                            className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="accountNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-normal text-base text-muted-foreground">
                          Account number *
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="********"
                            className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                            maxLength={8}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="accountHolderName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-normal text-base text-muted-foreground">
                          Account holder name *
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="John Smith"
                            className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="sortCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-normal text-base text-muted-foreground">
                          Sort code *
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="123456"
                            className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                            maxLength={6}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="signature"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-normal text-base text-muted-foreground">
                          Your signature *
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="John Smith"
                            className={cn(
                              signatureFont.className,
                              "h-12 px-4 text-2xl shadow-sm hover:bg-muted"
                            )}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="comments"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-normal text-base text-muted-foreground">
                          Comments
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="e.g. Supplier meter reading"
                            className="px-4 text-base shadow-sm hover:bg-muted"
                          />
                        </FormControl>
                        <p className="text-muted-foreground text-sm">
                          {field.value?.length ?? 0}/256 characters
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="isAuthorized"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              className="[&_svg]:size-3"
                            />
                          </FormControl>
                          <FormLabel className="font-normal text-base">
                            I confirm that I am authorised to sign this
                            contract.
                          </FormLabel>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="commissionAcknowledged"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex gap-2">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              className="[&_svg]:size-3"
                            />
                          </FormControl>
                          <FormLabel className="-mt-1 font-normal text-base">
                            I understand that from the signed price, Watt.co.uk
                            will receive a commission of no more than 1p/unit.
                          </FormLabel>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    variant="secondary"
                    className="w-full text-base"
                    disabled={signContract.isPending}
                  >
                    {signContract.isPending && (
                      <Loader2Icon className="mr-2 size-4 animate-spin" />
                    )}
                    Sign contract
                  </Button>
                </QuoteWizardItem>
              </QuoteWizardCard>
            </QuoteWizardItem>
          </div>
        </QuoteWizardContent>
        <QuoteWizardActions className="justify-between">
          <Button
            type="button"
            variant="ghost"
            className="text-base"
            onClick={onBack}
          >
            <ChevronLeftIcon className="mr-1 size-5" />
            Back
          </Button>
        </QuoteWizardActions>
      </QuoteWizard>
    </FormWrapper>
  );
}
