import { SignContractBaseSchema } from "@watt/api/src/types/contract";
import { z } from "zod";

export const ContractFormSchema = SignContractBaseSchema.extend({
  hasDownloadedContract: z.boolean().refine(val => val === true, {
    message: "Please download your contract before proceeding"
  }),
  isAuthorized: z.boolean().refine(val => val === true, {
    message: "Required"
  }),
  commissionAcknowledged: z.boolean().refine(val => val === true, {
    message: "Required"
  })
});
