"use client";

import { QuoteW<PERSON>rd } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { Skeleton } from "@watt/quote/components/ui/skeleton";

export function SkeletonLoader() {
  return (
    <QuoteWizard>
      <QuoteWizardContent>
        <div className="flex flex-col gap-12 sm:gap-16 lg:flex-row">
          <div className="flex-1 space-y-6">
            <Skeleton className="h-9 w-36 rounded-lg" />
            <Skeleton className="h-72 w-full rounded-lg" />
            <Skeleton className="h-28 w-full rounded-lg" />
          </div>
          <div className="flex-1 space-y-6">
            <Skeleton className="h-9 w-36 rounded-lg" />
            <Skeleton className="h-screen w-full rounded-lg" />
          </div>
        </div>
      </QuoteWizardContent>
    </QuoteWizard>
  );
}
