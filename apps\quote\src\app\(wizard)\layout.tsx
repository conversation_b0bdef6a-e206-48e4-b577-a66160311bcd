import { QuoteWizardHeader } from "@watt/quote/components/quote-wizard/quote-wizard-header";
import type { Metadata } from "next";
import type { PropsWithChildren } from "react";
import { QuoteWizardNav } from "./wiz-nav";

export const metadata: Metadata = {
  title: "Quote Wizard",
  description: "Get utility quotes in minutes"
};

export default function QuoteWizardLayout({ children }: PropsWithChildren) {
  return (
    <div className="min-h-screen bg-zinc-100">
      <QuoteWizardHeader />
      <QuoteWizardNav />
      {children}
    </div>
  );
}
