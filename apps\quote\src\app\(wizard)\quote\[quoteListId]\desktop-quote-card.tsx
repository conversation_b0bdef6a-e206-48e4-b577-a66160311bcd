"use client";

import { Provider<PERSON>ogo } from "@watt/common/src/components/provider-logo";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatCurrency } from "@watt/common/src/utils/format-currency";
import { Badge } from "@watt/quote/components/ui/badge";
import { Button } from "@watt/quote/components/ui/button";
import { Loader2Icon, MousePointerClickIcon, StarIcon } from "lucide-react";
import { memo, useMemo } from "react";
import { QuoteRateItem } from "../../../../components/quote-rate-item";
import {
  type PcwQuotes,
  calculateAnnualPrice,
  extractDurationYears,
  getProductPlan
} from "../../../../utils/quote-utils";

type DesktopQuoteCardProps = {
  quote: PcwQuotes;
  maxDecimalPlaces: number;
  isBestPrice: boolean;
  isSelected: boolean;
  isLoading: boolean;
  isDisabled: boolean;
  onSignUp: () => void;
};

export const DesktopQuoteCard = memo(
  ({
    quote,
    maxDecimalPlaces,
    isBestPrice,
    isSelected,
    isLoading,
    isDisabled,
    onSignUp
  }: DesktopQuoteCardProps) => {
    const { electricQuote, gasQuote } = quote;

    const { annualPrice, durationYears, productPlan } = useMemo(
      () => ({
        annualPrice: calculateAnnualPrice(quote),
        durationYears: extractDurationYears(quote),
        productPlan: getProductPlan(quote)
      }),
      [quote]
    );

    return (
      <div
        className={cn(
          "relative hidden h-auto min-h-auto items-center justify-between gap-8 rounded-lg border p-6 shadow-sm transition-colors duration-200 lg:flex",
          isSelected
            ? "border-secondary bg-secondary/5"
            : "border-border bg-background"
        )}
      >
        <div className="flex items-center gap-4">
          <div className="w-full max-w-52 shrink-0">
            <ProviderLogo
              logoFileName={quote.provider.logoFileName}
              displayName={quote.provider.displayName}
              width={224}
              height={112}
              className="h-auto max-h-28 object-scale-down"
            />
          </div>
        </div>
        <div className="flex h-full w-full grow items-center gap-6">
          <div className="flex h-full w-full grow items-center justify-between gap-4 rounded-lg bg-muted p-4">
            <div className="grid w-full grid-cols-1 gap-2 xl:grid-cols-2">
              <div className="flex w-full flex-col justify-start gap-y-2">
                <QuoteRateItem
                  label="Day unit rate"
                  value={(
                    electricQuote?.unitRate || gasQuote?.unitRate
                  )?.toFixed(maxDecimalPlaces)}
                  unit="p/unit"
                  variant="paragraph"
                  labelWidth="w-32"
                />
                <QuoteRateItem
                  label="Night unit rate"
                  value={
                    electricQuote?.nightUnitRate
                      ? Number(electricQuote.nightUnitRate).toFixed(
                          maxDecimalPlaces
                        )
                      : undefined
                  }
                  unit="p/unit"
                  variant="paragraph"
                  labelWidth="w-32"
                />
                <QuoteRateItem
                  label="Weekend unit rate"
                  value={
                    electricQuote?.weekendUnitRate
                      ? Number(electricQuote.weekendUnitRate).toFixed(
                          maxDecimalPlaces
                        )
                      : undefined
                  }
                  unit="p/unit"
                  variant="paragraph"
                  labelWidth="w-32"
                />
              </div>
              <div className="flex w-full flex-col justify-start gap-y-2">
                <QuoteRateItem
                  label="Standing charge"
                  value={(
                    electricQuote?.standingCharge || gasQuote?.standingCharge
                  )?.toFixed(maxDecimalPlaces)}
                  unit="p/day"
                  variant="paragraph"
                  labelWidth="w-32"
                />
                <QuoteRateItem
                  label="Monthly estimate"
                  value={annualPrice / 12}
                  unit="/month"
                  formatter={formatCurrency}
                  variant="paragraph"
                  labelWidth="w-32"
                />
                <QuoteRateItem
                  label="Product plan"
                  value={productPlan}
                  variant="paragraph"
                  labelWidth="w-32"
                />
              </div>
            </div>
            <div className="flex h-full shrink-0 flex-col items-end justify-between gap-4">
              <div className="flex">
                <Badge variant="secondary">
                  {durationYears} {durationYears === 1 ? "year" : "years"}
                </Badge>
              </div>
              <span className="font-semibold text-2xl md:text-3xl">
                {formatCurrency(annualPrice)}
                <span className="text-sm">/year</span>
              </span>
            </div>
          </div>

          <Button
            onClick={onSignUp}
            variant="secondary"
            size="sm"
            width="md"
            disabled={isDisabled || isLoading}
            className="[&_svg]:mr-1.5 [&_svg]:size-4"
          >
            {isLoading ? (
              <Loader2Icon className="animate-spin" />
            ) : (
              <MousePointerClickIcon />
            )}
            Sign up
          </Button>
        </div>
        {isBestPrice && (
          <div className="-top-3.5 absolute right-6 flex h-7 items-center gap-1.5 rounded-lg bg-primary px-3">
            <StarIcon className="size-4 shrink-0 fill-yellow-400 text-yellow-400" />
            <span className="font-medium text-primary-foreground text-sm">
              Best Price
            </span>
          </div>
        )}
      </div>
    );
  }
);
