import { EmptyState } from "@watt/quote/components/empty-state/empty-state";
import { BarChart3 } from "lucide-react";

type EmptyStatePanelProps = {
  title: string;
  description: string;
  children?: React.ReactNode;
};

export function EmptyStatePanel({
  title,
  description,
  children
}: EmptyStatePanelProps) {
  return (
    <EmptyState.Root
      title={title}
      description={description}
      icon={
        <EmptyState.Icon>
          <BarChart3 className="h-8 w-8 text-muted-foreground" />
        </EmptyState.Icon>
      }
    >
      {children}
    </EmptyState.Root>
  );
}
