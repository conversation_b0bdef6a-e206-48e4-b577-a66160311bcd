"use client";

import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatCurrency } from "@watt/common/src/utils/format-currency";
import { Badge } from "@watt/quote/components/ui/badge";
import { Button } from "@watt/quote/components/ui/button";
import {
  ChevronDownIcon,
  Loader2Icon,
  MousePointerClickIcon,
  StarIcon
} from "lucide-react";
import { memo, useMemo } from "react";
import { QuoteRateItem } from "../../../../components/quote-rate-item";
import {
  type PcwQuotes,
  calculateAnnualPrice,
  extractDurationYears,
  getProductPlan
} from "../../../../utils/quote-utils";

type MobileQuoteCardProps = {
  quote: PcwQuotes;
  maxDecimalPlaces: number;
  isExpanded: boolean;
  isBestPrice: boolean;
  isSelected: boolean;
  isLoading: boolean;
  isDisabled: boolean;
  onToggleDetails: (quoteId: string) => void;
  onSignUp: () => void;
};

export const MobileQuoteCard = memo(
  ({
    quote,
    maxDecimalPlaces,
    isExpanded,
    isBestPrice,
    isSelected,
    isLoading,
    isDisabled,
    onToggleDetails,
    onSignUp
  }: MobileQuoteCardProps) => {
    const { electricQuote, gasQuote } = quote;

    const { annualPrice, durationYears, productPlan } = useMemo(
      () => ({
        annualPrice: calculateAnnualPrice(quote),
        durationYears: extractDurationYears(quote),
        productPlan: getProductPlan(quote)
      }),
      [quote]
    );

    return (
      <div className={cn("space-y-2 lg:hidden", isBestPrice && "pt-3.5")}>
        <div
          className={cn(
            "relative w-full rounded-lg border p-5 shadow-sm transition-colors duration-200",
            isSelected
              ? "border-secondary bg-secondary/5"
              : "border-border bg-background",
            !isExpanded && "pb-6"
          )}
        >
          <div className="h-full w-full space-y-5">
            <div className="flex items-center justify-between">
              <div className="ml-auto">
                <Badge variant="secondary" className="font-medium">
                  {durationYears} {durationYears === 1 ? "year" : "years"}
                </Badge>
              </div>
            </div>

            <div className="flex items-center justify-between gap-4">
              <div className="flex flex-1 items-center">
                <ProviderLogo
                  logoFileName={quote.provider.logoFileName}
                  displayName={quote.provider.displayName}
                  width={120}
                  height={40}
                  className="h-auto w-full max-w-32 object-scale-down"
                />
              </div>
              <div className="flex flex-1 justify-end">
                <span className="font-semibold text-xl md:text-2xl">
                  {formatCurrency(annualPrice)}
                  <span className="text-sm">/year</span>
                </span>
              </div>
            </div>

            {isExpanded && (
              <div className="space-y-4">
                <div className="mx-auto space-y-3 rounded-md bg-muted/30 p-4 text-sm">
                  <QuoteRateItem
                    label="Day unit rate"
                    value={(
                      electricQuote?.unitRate || gasQuote?.unitRate
                    )?.toFixed(maxDecimalPlaces)}
                    unit="p/unit"
                  />
                  <QuoteRateItem
                    label="Night unit rate"
                    value={
                      electricQuote?.nightUnitRate
                        ? Number(electricQuote.nightUnitRate).toFixed(
                            maxDecimalPlaces
                          )
                        : undefined
                    }
                    unit="p/unit"
                  />
                  <QuoteRateItem
                    label="Weekend unit rate"
                    value={
                      electricQuote?.weekendUnitRate
                        ? Number(electricQuote.weekendUnitRate).toFixed(
                            maxDecimalPlaces
                          )
                        : undefined
                    }
                    unit="p/unit"
                  />
                  <QuoteRateItem
                    label="Standing charge"
                    value={(
                      electricQuote?.standingCharge || gasQuote?.standingCharge
                    )?.toFixed(maxDecimalPlaces)}
                    unit="p/day"
                  />
                  <QuoteRateItem
                    label="Monthly estimate"
                    value={annualPrice / 12}
                    unit="/month"
                    formatter={formatCurrency}
                  />
                  <QuoteRateItem label="Product plan" value={productPlan} />
                </div>
                <Button
                  onClick={onSignUp}
                  variant="secondary"
                  size="sm"
                  className="w-full [&_svg]:mr-1.5 [&_svg]:size-4"
                  disabled={isDisabled || isLoading}
                >
                  {isLoading ? (
                    <Loader2Icon className="animate-spin" />
                  ) : (
                    <MousePointerClickIcon />
                  )}
                  Sign me up
                </Button>
              </div>
            )}
          </div>

          {isBestPrice && (
            <div className="-top-3.5 absolute left-4 flex h-7 items-center gap-1.5 rounded-full bg-primary px-3">
              <StarIcon className="size-4 shrink-0 fill-yellow-400 text-yellow-400" />
              <span className="font-medium text-primary-foreground text-sm">
                Best Price
              </span>
            </div>
          )}
        </div>

        <Button
          type="button"
          size="sm"
          className="h-8 w-full font-medium text-xs"
          onClick={() => {
            onToggleDetails(quote.id);
          }}
        >
          {isExpanded ? "Hide" : "View"} Details
          <ChevronDownIcon
            className={cn("ml-1 size-3.5", isExpanded && "rotate-180")}
          />
        </Button>
      </div>
    );
  }
);
