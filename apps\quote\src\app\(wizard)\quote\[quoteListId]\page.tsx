import { Skeleton } from "@watt/quote/components/ui/skeleton";
import { HydrateClient, tRPCServerApi } from "@watt/quote/trpc/server";
import { Suspense } from "react";
import { QuoteDetails } from "./quote-details";

export default async function QuoteResults(props: {
  params: Promise<{ quoteListId: string }>;
  searchParams: Promise<{ contactId: string }>;
}) {
  const params = await props.params;
  const searchParams = await props.searchParams;

  void tRPCServerApi.pcw.quoteGetQuotesByQuoteListId.prefetch({
    quoteListId: params.quoteListId
  });

  return (
    <HydrateClient>
      <Suspense
        fallback={
          <div className="container space-y-4 py-10">
            <div className="flex gap-4">
              <Skeleton className="h-9 w-36 rounded-lg" />
              <Skeleton className="h-9 w-36 rounded-lg" />
            </div>
            <div className="flex flex-col gap-4">
              <Skeleton className="h-40 w-full rounded-lg" />
              <Skeleton className="h-40 w-full rounded-lg" />
              <Skeleton className="h-40 w-full rounded-lg" />
              <Skeleton className="h-40 w-full rounded-lg" />
            </div>
          </div>
        }
      >
        <QuoteDetails
          quoteListId={params.quoteListId}
          contactId={searchParams.contactId}
        />
      </Suspense>
    </HydrateClient>
  );
}
