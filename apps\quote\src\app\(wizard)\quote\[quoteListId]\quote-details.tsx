"use client";

import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardActions } from "@watt/quote/components/quote-wizard/quote-wizard-actions";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { Button } from "@watt/quote/components/ui/button";
import { ChevronLeftIcon } from "lucide-react";
import { QuoteResultsDataTable } from "./quote-results-data-table";
import { QuoteUpdateForm } from "./quote-update-form";

import { UtilityType } from "@prisma/client";
import { routes } from "@watt/quote/config/routes";
import { trpcClient } from "@watt/quote/utils/api";
import { useRouter } from "next/navigation";

type QuoteDetailsProps = {
  quoteListId: string;
  contactId: string;
};

export function QuoteDetails({ quoteListId, contactId }: QuoteDetailsProps) {
  const router = useRouter();

  const [data] = trpcClient.pcw.quoteGetQuotesByQuoteListId.useSuspenseQuery({
    quoteListId
  });

  if (!data.quoteList) {
    router.push(routes.company);
    return;
  }

  const {
    id,
    contractStartDate,
    utilityType,
    siteMeter,
    electricityUsage,
    gasUsage
  } = data.quoteList;

  const meterIdentifier =
    utilityType === UtilityType.ELECTRICITY
      ? siteMeter?.electricSiteMeter?.mpanValue
      : siteMeter?.gasSiteMeter?.mprnValue;

  const utilityUsage = electricityUsage?.totalUsage ?? gasUsage?.totalUsage;

  if (!meterIdentifier || !siteMeter || !utilityUsage) {
    router.push(routes.company);
    return;
  }

  const handleUpdateSearchSubmit = (newQuoteListId: string) => {
    if (newQuoteListId !== quoteListId) {
      router.replace(`/quote/${newQuoteListId}?contactId=${contactId}`);
    }
  };

  return (
    <QuoteWizard>
      <QuoteWizardContent className="!gap-4">
        <QuoteUpdateForm
          quoteListId={id}
          meterIdentifier={meterIdentifier}
          siteMeterId={siteMeter.id}
          contactId={contactId}
          initialContractStartDate={contractStartDate}
          initialTotalUsage={utilityUsage}
          onSubmit={handleUpdateSearchSubmit}
        />
        <QuoteResultsDataTable data={data} contactId={contactId} />
      </QuoteWizardContent>
      <QuoteWizardActions>
        <Button
          type="button"
          variant="ghost"
          onClick={() => router.back()}
          className="mr-auto text-base"
        >
          <ChevronLeftIcon className="mr-1 size-5" />
          Back
        </Button>
      </QuoteWizardActions>
    </QuoteWizard>
  );
}
