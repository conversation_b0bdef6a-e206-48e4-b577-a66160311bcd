"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { textFilter } from "@watt/quote/components/data-table/data-table-filter-functions";
import {
  type PcwQuotes,
  extractDurationYears
} from "../../../../utils/quote-utils";

export const quoteResultsColumns: ColumnDef<PcwQuotes>[] = [
  {
    accessorKey: "id",
    header: "ID"
  },
  {
    accessorKey: "duration",
    accessorFn: quote => extractDurationYears(quote),
    header: "Contract Length",
    filterFn: textFilter
  },
  {
    accessorKey: "supplier",
    accessorFn: quote => quote.provider.udcoreId,
    header: "Supplier Name",
    filterFn: textFilter
  }
];
