import type { Table } from "@tanstack/react-table";
import {
  getUniqueFilterOptions,
  getUniqueSupplierFilterOptions
} from "@watt/quote/utils/get-unique-filter-options";

import { DataTableFacetedFilter } from "@watt/quote/components/data-table/data-table-faceted-filter";
import { Button } from "@watt/quote/components/ui/button";
import { CalendarIcon, PackageIcon, X } from "lucide-react";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
}

type ColumnFilter = {
  id: string;
  title: string;
  unfilteredList?: boolean;
};

export function DataTableToolbar<TData>({
  table
}: DataTableToolbarProps<TData>) {
  const resetFilters = () => {
    table.resetColumnFilters();
    table.resetGlobalFilter();
  };

  const isFiltered =
    table.getPreFilteredRowModel().rows.length >
    table.getFilteredRowModel().rows.length;

  return (
    <div className="flex flex-wrap items-center gap-2">
      <DataTableFacetedFilter
        column={table.getColumn("duration")}
        title="Contract Length"
        icon={<CalendarIcon className="mr-2 h-4 w-4" />}
        options={getUniqueFilterOptions({
          columnId: "duration",
          table
        }).map(option => {
          const durationNumber = Number(option.label);
          return {
            ...option,
            label: `${durationNumber} ${
              durationNumber === 1 ? "year" : "years"
            }`
          };
        })}
      />
      <DataTableFacetedFilter
        column={table.getColumn("supplier")}
        title="Supplier Name"
        icon={<PackageIcon className="mr-2 h-4 w-4" />}
        options={getUniqueSupplierFilterOptions("supplier", table, true)}
      />
      {isFiltered && (
        <Button variant="ghost" onClick={resetFilters} size="sm">
          Reset
          <X className="ml-2 h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
