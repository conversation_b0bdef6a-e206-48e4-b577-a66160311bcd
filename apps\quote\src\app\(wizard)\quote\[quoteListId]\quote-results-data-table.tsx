"use client";

import {
  type ColumnFiltersState,
  type RowSelectionState,
  type SortingState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from "@tanstack/react-table";
import { TRPCClientError } from "@trpc/client";
import type { QuoteGetQuotesByQuoteListId } from "@watt/api/src/router";
import {
  Table,
  TableBody,
  TableCell,
  TableRow
} from "@watt/quote/components/ui/table";
import { toast } from "@watt/quote/components/ui/use-toast";
import { trpcClient } from "@watt/quote/utils/api";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { findCheapestQuote } from "../../../../utils/quote-utils";
import type { PcwQuotes } from "../../../../utils/quote-utils";
import { DesktopQuoteCard } from "./desktop-quote-card";
import { EmptyStatePanel } from "./empty-state";
import { MobileQuoteCard } from "./mobile-quote-card";
import { quoteResultsColumns as columns } from "./quote-results-columns";
import { DataTableToolbar } from "./quote-results-data-table-toolbar";

export type QuoteCardProps = {
  quote: PcwQuotes;
  maxDecimalPlaces: number;
  isBestPrice: boolean;
  isSelected: boolean;
  isLoading: boolean;
  isDisabled: boolean;
  onSignUp: () => void;
};

type DataTableProps = {
  data: QuoteGetQuotesByQuoteListId;
  contactId: string;
};

export function QuoteResultsDataTable({ data, contactId }: DataTableProps) {
  const router = useRouter();

  const { quoteList, maxDecimalPlaces } = data;

  const [clickedQuoteId, setClickedQuoteId] = useState<string | null>(null);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [expandedQuotes, setExpandedQuotes] = useState<Record<string, boolean>>(
    {}
  );

  const signUpQuoteMutation = trpcClient.pcw.quoteSignUpQuote.useMutation();

  const quotes = useMemo(() => quoteList?.quotes ?? [], [quoteList]);

  const table = useReactTable({
    data: quotes,
    columns,
    enableRowSelection: true,
    enableMultiRowSelection: false,
    initialState: {
      pagination: {
        pageSize: 100
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters
    },
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });

  const toggleQuoteDetails = (quoteId: string) => {
    setExpandedQuotes(prev => ({
      ...prev,
      [quoteId]: !prev[quoteId]
    }));
  };

  const cheapestQuote = useMemo(() => findCheapestQuote(quotes), [quotes]);

  if (!quoteList) {
    return null;
  }

  // Render quote cards
  const renderQuoteCards = () => {
    return table.getRowModel().rows.map(row => {
      const quote = row.original;
      const isExpanded = expandedQuotes[quote.id] || false;
      const isBestPrice = !!(cheapestQuote && quote?.id === cheapestQuote.id);
      const isSelected = row.getIsSelected();

      const handleSignUp = async () => {
        try {
          setClickedQuoteId(quote.id);
          await signUpQuoteMutation.mutateAsync({
            quoteId: quote.id,
            contactId
          });
          router.push(`/contract/${quote.id}?contactId=${contactId}`);
        } catch (e) {
          setClickedQuoteId(null);
          const error = e as Error;
          const description =
            error instanceof TRPCClientError && !!error.data.zodError
              ? "Error signing up for quote. Please try again."
              : error.message;
          toast({
            title: "Unable to sign up for quote",
            description,
            variant: "destructive"
          });
        }
      };

      const quoteCardProps: QuoteCardProps = {
        quote,
        maxDecimalPlaces,
        isBestPrice,
        isSelected,
        isLoading: clickedQuoteId === quote.id,
        isDisabled: clickedQuoteId !== null,
        onSignUp: handleSignUp
      };

      return (
        <div key={row.id}>
          <DesktopQuoteCard {...quoteCardProps} />
          <MobileQuoteCard
            {...quoteCardProps}
            isExpanded={isExpanded}
            onToggleDetails={toggleQuoteDetails}
          />
        </div>
      );
    });
  };

  return (
    <div className="space-y-2">
      <DataTableToolbar table={table} />
      <div className="rounded-md">
        <Table>
          <TableBody>
            {table.getRowModel().rows.length > 0 ? (
              <TableRow className="hover:bg-transparent">
                <TableCell colSpan={columns.length} className="px-0">
                  <div className="flex flex-col gap-4 py-4">
                    {renderQuoteCards()}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <EmptyStatePanel
                    title="No quotes found"
                    description="There are no quotes generated for this meter. Please update the search and try again."
                  />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
