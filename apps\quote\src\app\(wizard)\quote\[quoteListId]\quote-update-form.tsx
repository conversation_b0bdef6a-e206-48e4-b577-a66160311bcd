"use client";

import { PcwUpdateQuoteInputSchema } from "@watt/api/src/types/pcw/quote";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  convertLocalDateToUTCString,
  convertUTCStringToLocalDate
} from "@watt/common/src/utils/format-date";
import { dateFormats, formatDate } from "@watt/common/src/utils/format-date";
import { DatePickerInput } from "@watt/quote/components/date-picker-input";
import { SuffixInput } from "@watt/quote/components/suffix-input";
import { Button } from "@watt/quote/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/quote/components/ui/form";
import { toast } from "@watt/quote/components/ui/use-toast";
import { useZodForm } from "@watt/quote/hooks/use-zod-form";
import { trpcClient } from "@watt/quote/utils/api";
import { Loader2 } from "lucide-react";

type QuoteUpdateFormProps = {
  quoteListId: string;
  meterIdentifier: string;
  siteMeterId: string;
  contactId: string;
  initialContractStartDate: Date;
  initialTotalUsage: number;
  onSubmit: (quoteListId: string) => void;
};

export function QuoteUpdateForm({
  quoteListId,
  meterIdentifier,
  siteMeterId,
  contactId,
  initialContractStartDate,
  initialTotalUsage,
  onSubmit
}: QuoteUpdateFormProps) {
  const updateQuotesMutation = trpcClient.pcw.quoteUpdateQuotes.useMutation();

  const form = useZodForm({
    schema: PcwUpdateQuoteInputSchema,
    mode: "onChange",
    defaultValues: {
      quoteListId,
      siteMeterId,
      contactId,
      meterIdentifier,
      contractStartDate: formatDate(
        initialContractStartDate,
        dateFormats.YYYY_MM_DD_HYPHEN
      ),
      totalUsage: initialTotalUsage
    }
  });

  const handleUpdateSearch = async () => {
    try {
      const quoteListId = await updateQuotesMutation.mutateAsync(
        form.getValues()
      );
      onSubmit(quoteListId);
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      toast({
        title: "Unable to update quotes",
        description:
          error.message || "An error occurred while updating the quote",
        variant: "destructive"
      });
    }
  };

  return (
    <div>
      <FormWrapper
        form={form}
        handleSubmit={handleUpdateSearch}
        className="flex grow flex-col flex-wrap justify-between gap-4 overflow-auto py-2 md:flex-row md:items-end"
      >
        <div className="flex flex-col items-center gap-4 md:flex-row md:*:w-56">
          <FormField
            control={form.control}
            name="contractStartDate"
            render={({ field }) => (
              <FormItem className="flex w-full flex-col">
                <FormLabel className="font-normal text-base text-muted-foreground">
                  Start date of new contract *
                </FormLabel>
                <FormControl>
                  <DatePickerInput
                    placeholder="Select the start date"
                    className="h-10"
                    date={
                      field.value
                        ? convertUTCStringToLocalDate(field.value)
                        : undefined
                    }
                    setDate={date => {
                      if (!date) {
                        return;
                      }
                      form.setValue(
                        "contractStartDate",
                        convertLocalDateToUTCString(date),
                        {
                          shouldDirty: true
                        }
                      );
                    }}
                    calendarProps={{
                      fromDate: new Date()
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="totalUsage"
            render={({ field }) => {
              return (
                <FormItem className="flex w-full flex-col">
                  <FormLabel className="font-normal text-base text-muted-foreground">
                    Estimated annual usage *
                  </FormLabel>
                  <FormControl>
                    <SuffixInput
                      {...field}
                      type="number"
                      suffix="kWh/year"
                      step="1000"
                      placeholder="Enter current usage"
                      className="h-10 text-sm"
                      value={field.value === 0 ? "" : field.value}
                      onChange={e => {
                        const value = e.target.value;
                        if (value === "") {
                          form.setValue("totalUsage", 0, { shouldDirty: true });
                        } else {
                          const numValue = Number(value);
                          if (!Number.isNaN(numValue)) {
                            form.setValue("totalUsage", numValue, {
                              shouldDirty: true
                            });
                          }
                        }
                      }}
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
        </div>
        {/* TODO(Bidur): Duplicate error message for mobile. Find a better solution */}
        {Object.keys(form.formState.errors).length > 0 && (
          <span className="text-destructive text-sm md:hidden">
            Please enter valid values for all fields
          </span>
        )}
        <Button
          type="submit"
          variant="secondary"
          disabled={updateQuotesMutation.isPending || !form.formState.isDirty}
        >
          {updateQuotesMutation.isPending && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          Update Search
        </Button>
      </FormWrapper>
      {Object.keys(form.formState.errors).length > 0 && (
        <span className="hidden text-destructive text-sm md:block">
          Please enter valid values for all fields
        </span>
      )}
    </div>
  );
}
