"use client";

import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import { redirect } from "next/navigation";
import { SiteForm } from "./site-form";

export default function MeterPage() {
  const { queryParams } = useQueryParams<{
    companyId: string | undefined;
    companyAddressId: string | undefined;
    contactId: string | undefined;
  }>();

  if (
    !queryParams.companyId ||
    !queryParams.companyAddressId ||
    !queryParams.contactId
  ) {
    return redirect("/company");
  }

  return (
    <SiteForm
      companyId={queryParams.companyId}
      companyAddressId={queryParams.companyAddressId}
      contactId={queryParams.contactId}
    />
  );
}
