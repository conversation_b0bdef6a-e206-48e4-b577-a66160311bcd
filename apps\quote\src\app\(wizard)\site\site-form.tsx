"use client";

import { TRPCClientError } from "@trpc/client";
import type { Address_Find_Many } from "@watt/api/src/router/address";
import { isShortMpan } from "@watt/common/src/mpan/mpan";
import { isValidMPRN } from "@watt/common/src/mprn/mprn";
import { isValidPostcode } from "@watt/common/src/regex/postcode";
import { isValidUuid } from "@watt/common/src/regex/uuid";
import { log } from "@watt/common/src/utils/axiom-logger";
import type { UtilityType } from "@watt/db/src/enums";
import { trpcClient } from "@watt/quote/utils/api";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDebounce } from "react-use";

import { SiteInputSchema } from "@watt/api/src/types/pcw/site";
import {
  AddressSelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "@watt/quote/components/lookup";
import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardActions } from "@watt/quote/components/quote-wizard/quote-wizard-actions";
import { QuoteWizardCard } from "@watt/quote/components/quote-wizard/quote-wizard-card";
import { QuoteWizardCardContent } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-content";
import { QuoteWizardCardDescription } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-description";
import { QuoteWizardCardHeader } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-header";
import { QuoteWizardCardItem } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-item";
import { QuoteWizardCardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-title";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { QuoteWizardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-title";
import { Button } from "@watt/quote/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormWrapper
} from "@watt/quote/components/ui/form";
import { Label } from "@watt/quote/components/ui/label";
import {
  RadioGroup,
  RadioGroupItem
} from "@watt/quote/components/ui/radio-group";
import { toast } from "@watt/quote/components/ui/use-toast";
import { UtilitySelector } from "@watt/quote/components/utility-selector";
import { routes } from "@watt/quote/config/routes";
import { useAddressSearch } from "@watt/quote/hooks/use-address-search";
import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import { useZodForm } from "@watt/quote/hooks/use-zod-form";
import { ChevronLeftIcon, ChevronRightIcon, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

type QueryParams = {
  siteAddressSearch: string;
  selectedSiteAddressId: string;
  businessAddressId: string;
  utilityType: string;
};

interface SiteFormProps {
  companyId: string;
  companyAddressId: string;
  contactId: string;
}

// Define an enum for site address type, similar to the one in the quote wizard
enum SiteAddressType {
  SameAsBusiness = "same-as-business",
  DifferentAddress = "different-address"
}

export function SiteForm({
  companyId,
  companyAddressId,
  contactId
}: SiteFormProps) {
  const router = useRouter();

  const { queryParams, setQueryParams } =
    useQueryParams<Partial<QueryParams>>();

  const addressContainerRef = useRef<HTMLDivElement>(null);
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
  const [addressType, setAddressType] = useState<SiteAddressType>(
    queryParams.selectedSiteAddressId &&
      queryParams.selectedSiteAddressId !== companyAddressId
      ? SiteAddressType.DifferentAddress
      : SiteAddressType.SameAsBusiness
  );
  const [addressData, setAddressData] = useState<
    Address_Find_Many | undefined
  >();

  const { fetchAddress, isFetchingAddress } = useAddressSearch();

  const siteSubmitSiteInformationMutation =
    trpcClient.pcw.siteSubmitSiteInformation.useMutation();

  const onBack = useCallback(() => {
    router.back();
  }, [router]);

  const form = useZodForm({
    schema: SiteInputSchema,
    mode: "onChange",
    defaultValues: {
      companyId,
      contactId,
      utilityType: queryParams.utilityType as UtilityType
    }
  });

  // (Bidur): Instead of setting the queryParams value as the form's default value we set the form value through this useEffect
  // This gives us the ability to reset the input to undefined when the user clears the input field otherwise the input field resets back to the default value that was initially set
  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function updateDynamicInputFields() {
    if (queryParams.utilityType) {
      form.setValue("utilityType", queryParams.utilityType as UtilityType);
    }

    if (queryParams.selectedSiteAddressId) {
      form.setValue("siteAddressId", queryParams.selectedSiteAddressId);
    } else {
      form.setValue("siteAddressId", companyAddressId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fullResetAddressFormData = useCallback(() => {
    form.resetField("siteAddressId");
    form.resetField("sitePostcode");

    setQueryParams({
      siteAddressSearch: "",
      selectedSiteAddressId: ""
    });

    if (!queryParams.selectedSiteAddressId) {
      setAddressData(undefined);
    }
  }, [form, setQueryParams, queryParams.selectedSiteAddressId]);

  const lookupAddressOnSearchInput = useCallback(async () => {
    try {
      if (!queryParams.siteAddressSearch) {
        fullResetAddressFormData();
        return;
      }

      const postcodeIsValid = isValidPostcode(queryParams.siteAddressSearch);
      const mpanIsValid = isShortMpan(queryParams.siteAddressSearch);
      const mprnIsValid = isValidMPRN(queryParams.siteAddressSearch);
      const idIsValid = isValidUuid(queryParams.siteAddressSearch);

      if (!postcodeIsValid && !mpanIsValid && !mprnIsValid && !idIsValid) {
        return;
      }

      const result = await fetchAddress(queryParams.siteAddressSearch);
      setAddressData(result);

      if (queryParams.selectedSiteAddressId) {
        const selectedAddressObj = result?.find(
          address => address.id === queryParams.selectedSiteAddressId
        );

        if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
          fullResetAddressFormData();

          toast({
            title: "Invalid address in the URL",
            description:
              "Selected address does not have a valid address or postcode",
            variant: "destructive"
          });
          return;
        }
        form.setValue("siteAddressId", queryParams.selectedSiteAddressId);
        form.setValue("sitePostcode", selectedAddressObj.postcode);
      }
    } catch (e) {
      const error = e as Error;
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error fetching address. Please check the input and try again."
          : error.message;
      toast({
        title: "Unable to get address",
        description,
        variant: "destructive"
      });
    }
  }, [
    fetchAddress,
    queryParams.siteAddressSearch,
    form,
    fullResetAddressFormData,
    queryParams.selectedSiteAddressId
  ]);

  useDebounce(lookupAddressOnSearchInput, 1000, [
    queryParams.siteAddressSearch
  ]);

  const selectedAddress = useMemo(
    () =>
      addressData?.find(
        address => address.id === queryParams.selectedSiteAddressId
      ) ?? addressData?.[0],
    [queryParams.selectedSiteAddressId, addressData]
  );

  const handleAddressSelect = (addressId: string) => {
    const selectedAddressObj = addressData?.find(
      address => address.id === addressId
    );

    if (!selectedAddressObj?.displayName || !selectedAddressObj.postcode) {
      toast({
        title: "Invalid address",
        description:
          "Selected address does not have a valid address or postcode",
        variant: "destructive"
      });
      return;
    }

    setQueryParams({ selectedSiteAddressId: addressId });
    form.setValue("siteAddressId", addressId);
    form.setValue("sitePostcode", selectedAddressObj.postcode);

    setIsAddressModalOpen(false);
  };

  const handleUtilityTypeChange = (utilityType: UtilityType) => {
    setQueryParams({ utilityType });
    form.setValue("utilityType", utilityType);
  };

  const handleAddressSearchInputChange = (siteAddressSearch: string) => {
    setQueryParams({ siteAddressSearch, selectedSiteAddressId: "" });
    form.setValue("siteAddressId", "");
  };

  const syncBusinessAndSiteAddress = useCallback(async () => {
    const addressResult = await fetchAddress(companyAddressId);
    if (addressResult && addressResult.length > 0) {
      const selectedBusinessAddressData = addressResult[0];
      if (
        !selectedBusinessAddressData?.displayName ||
        !selectedBusinessAddressData.postcode
      ) {
        return;
      }

      setAddressData(addressResult);
      setQueryParams({
        selectedSiteAddressId: companyAddressId
      });
      form.setValue("siteAddressId", companyAddressId);
      form.setValue("sitePostcode", selectedBusinessAddressData.postcode);
    }
  }, [companyAddressId, form, fetchAddress, setQueryParams]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (
      (!queryParams.selectedSiteAddressId ||
        companyAddressId === queryParams.selectedSiteAddressId) &&
      !queryParams.siteAddressSearch // if the siteAddressSearch is defined then lookupAddressOnSearchInput will be triggered to update the addressData
    ) {
      syncBusinessAndSiteAddress();
    }
  }, []);

  const handleSiteAddressTypeChange = (value: SiteAddressType) => {
    setAddressType(value);

    if (value === SiteAddressType.SameAsBusiness) {
      syncBusinessAndSiteAddress();
    } else {
      fullResetAddressFormData();
    }
  };

  const handleFormSubmit = async () => {
    try {
      const result = await siteSubmitSiteInformationMutation.mutateAsync(
        form.getValues()
      );

      window.location.href = `${routes.usage}?companyReg=${result.companyReg}&siteAddressId=${result.siteAddressId}&utilityType=${result.utilityType}&contactId=${contactId}`;
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while getting the new quotes. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to get quotes",
        description,
        variant: "destructive"
      });
    }
  };

  const questionCount =
    addressType === SiteAddressType.DifferentAddress ? "3" : "2";

  return (
    <FormWrapper form={form} handleSubmit={handleFormSubmit} className="grow">
      <QuoteWizard>
        <QuoteWizardContent>
          <QuoteWizardItem>
            <QuoteWizardTitle>My Site Details</QuoteWizardTitle>
            <QuoteWizardCard>
              <QuoteWizardCardItem>
                <QuoteWizardCardHeader>
                  <QuoteWizardCardDescription>
                    Question 1 of {questionCount}
                  </QuoteWizardCardDescription>
                  <QuoteWizardCardTitle>
                    Is your site address the same as your business address?
                  </QuoteWizardCardTitle>
                </QuoteWizardCardHeader>
                <QuoteWizardCardContent>
                  {companyAddressId && (
                    <RadioGroup
                      className="gap-4"
                      value={addressType}
                      onValueChange={(value: SiteAddressType) =>
                        handleSiteAddressTypeChange(value)
                      }
                    >
                      <div className="flex gap-2">
                        <RadioGroupItem
                          id={SiteAddressType.SameAsBusiness}
                          value={SiteAddressType.SameAsBusiness}
                          className="size-5 [&_svg]:size-4"
                        />
                        <Label
                          htmlFor={SiteAddressType.SameAsBusiness}
                          className="-mt-0.5 font-medium text-base"
                        >
                          Yes
                        </Label>
                      </div>
                      <div className="flex gap-2">
                        <RadioGroupItem
                          id={SiteAddressType.DifferentAddress}
                          value={SiteAddressType.DifferentAddress}
                          className="size-5 [&_svg]:size-4"
                        />
                        <Label
                          htmlFor={SiteAddressType.DifferentAddress}
                          className="-mt-0.5 font-medium text-base"
                        >
                          No, I would like to enter a different address
                        </Label>
                      </div>
                    </RadioGroup>
                  )}
                </QuoteWizardCardContent>
              </QuoteWizardCardItem>

              {addressType === SiteAddressType.DifferentAddress && (
                <QuoteWizardCardItem>
                  <QuoteWizardCardHeader>
                    <QuoteWizardCardDescription>
                      Question 2 of {questionCount}
                    </QuoteWizardCardDescription>
                    <QuoteWizardCardTitle>
                      What's your site address?
                    </QuoteWizardCardTitle>
                  </QuoteWizardCardHeader>
                  <QuoteWizardCardContent>
                    <FormField
                      control={form.control}
                      name="siteAddressId"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Site Address *</FormLabel>
                          <FormControl>
                            <div
                              ref={addressContainerRef}
                              className="flex w-full flex-col"
                            >
                              <LookUp
                                open={isAddressModalOpen}
                                onOpenChange={setIsAddressModalOpen}
                              >
                                <LookUpTrigger
                                  fieldValue={field.value ?? ""}
                                  isLoading={isFetchingAddress}
                                  className="h-12 px-4 text-base shadow-sm hover:bg-muted"
                                >
                                  {selectedAddress ? (
                                    <AddressSelection
                                      address={selectedAddress}
                                    />
                                  ) : (
                                    <span className="font-normal">
                                      Select a site...
                                    </span>
                                  )}
                                </LookUpTrigger>
                                <LookUpContent
                                  placeholder="Search by postcode, mpan or mprn number"
                                  searchInput={queryParams.siteAddressSearch}
                                  onSearchInputChange={
                                    handleAddressSearchInputChange
                                  }
                                  isLoading={isFetchingAddress}
                                  container={addressContainerRef.current}
                                >
                                  <LookUpGroup className="p-0">
                                    {addressData?.map(address => (
                                      <LookUpItem
                                        key={address.id}
                                        value={address.id}
                                        onSelect={handleAddressSelect}
                                      >
                                        <AddressSelection address={address} />
                                      </LookUpItem>
                                    ))}
                                  </LookUpGroup>
                                </LookUpContent>
                              </LookUp>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </QuoteWizardCardContent>
                </QuoteWizardCardItem>
              )}

              <QuoteWizardCardItem>
                <QuoteWizardCardHeader>
                  <QuoteWizardCardDescription>
                    Question {questionCount} of {questionCount}
                  </QuoteWizardCardDescription>
                  <QuoteWizardCardTitle>
                    What utilities do you need for this address?
                  </QuoteWizardCardTitle>
                  {selectedAddress && (
                    <QuoteWizardCardDescription>
                      {selectedAddress.displayName}
                    </QuoteWizardCardDescription>
                  )}
                </QuoteWizardCardHeader>
                <QuoteWizardCardContent>
                  <FormField
                    control={form.control}
                    name="utilityType"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <UtilitySelector
                            value={field.value}
                            onChange={handleUtilityTypeChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </QuoteWizardCardContent>
              </QuoteWizardCardItem>
            </QuoteWizardCard>
          </QuoteWizardItem>
        </QuoteWizardContent>
        <QuoteWizardActions className="justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            disabled={siteSubmitSiteInformationMutation.isPending}
            className="text-base"
          >
            <ChevronLeftIcon className="mr-1 size-5" />
            Back
          </Button>
          <Button
            type="submit"
            variant="secondary"
            className="text-base"
            disabled={siteSubmitSiteInformationMutation.isPending}
          >
            {siteSubmitSiteInformationMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Next <ChevronRightIcon className="ml-1 size-5" />
          </Button>
        </QuoteWizardActions>
      </QuoteWizard>
    </FormWrapper>
  );
}
