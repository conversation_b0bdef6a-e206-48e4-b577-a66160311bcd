"use client";

import { UtilityType } from "@watt/db/src/enums";
import { EditableMPANField } from "@watt/quote/components/mpxn/editable-mpan-field";
import { EditableMPRNField } from "@watt/quote/components/mpxn/editable-mprn-field";
import { MPANField } from "@watt/quote/components/mpxn/mpan-field";
import { MPRNField } from "@watt/quote/components/mpxn/mprn-field";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { Button } from "@watt/quote/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/quote/components/ui/form";
import { Skeleton } from "@watt/quote/components/ui/skeleton";
import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import { trpcClient } from "@watt/quote/utils/api";
import { startTransition, useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import type { UsageFormProps, UsageFormQueryParams } from "./usage-form";

export function MeterSection({
  companyReg,
  siteAddressId,
  utilityType
}: UsageFormProps) {
  const form = useFormContext();

  const useCustomMeter = form.watch("useCustomMeter");
  const customMeterIdentifier = form.watch("customMeterIdentifier");

  const { queryParams, setQueryParams } =
    useQueryParams<Partial<UsageFormQueryParams>>();

  const [meterNumbersList, setMeterNumbersList] = useState<string[] | null>(
    null
  );

  const companySiteMeterListMutation =
    trpcClient.pcw.siteMeterFindCompanySiteMeters.useMutation();

  const { meterIdentifier } = queryParams;
  const isElectricityQuote = utilityType === UtilityType.ELECTRICITY;

  useEffect(() => void updateMeterNumbersList(), []);

  const resetUsageAndSupplier = useCallback(() => {
    form.resetField("totalUsage");
    form.resetField("currentSupplier");

    setQueryParams({
      totalUsage: "",
      currentSupplier: ""
    });
  }, [form, setQueryParams]);

  const handleMeterIdentifierChange = useCallback(
    (value: string) => {
      if (meterIdentifier === value) {
        return;
      }

      form.setValue("meterIdentifier", value);
      setQueryParams({ meterIdentifier: value });

      startTransition(resetUsageAndSupplier);
    },
    [form, meterIdentifier, setQueryParams, resetUsageAndSupplier]
  );

  const handleMeterChange = useCallback(
    (composedMeter: string | null) => {
      if (useCustomMeter) {
        form.setValue("customMeterIdentifier", composedMeter);
        return;
      }

      if (composedMeter === null) {
        form.resetField("meterIdentifier");
        setQueryParams({ meterIdentifier: "" });
        return;
      }

      form.clearErrors("meterIdentifier");
      form.setValue("meterIdentifier", composedMeter);

      setQueryParams({ meterIdentifier: composedMeter });
    },
    [form, useCustomMeter, setQueryParams]
  );

  const handleCustomMeterToggle = useCallback(() => {
    const currentState = !!useCustomMeter;

    if (currentState) {
      if (meterNumbersList?.length === 1) {
        const meter = meterNumbersList[0];

        if (meter) {
          form.setValue("meterIdentifier", meter);
          setQueryParams({
            meterIdentifier: meter
          });
        }
      }
    } else {
      form.resetField("meterIdentifier");
      setQueryParams({ meterIdentifier: "" });
    }

    form.setValue("useCustomMeter", !currentState);
  }, [form, useCustomMeter, meterNumbersList, setQueryParams]);

  const updateMeterNumbersList = useCallback(async () => {
    const meterNumbers = await companySiteMeterListMutation.mutateAsync({
      addressId: siteAddressId,
      companyReg,
      utilityType: utilityType as UtilityType
    });

    setMeterNumbersList(meterNumbers);

    if (useCustomMeter || !meterNumbers.length) {
      return;
    }

    const firstMeter = meterNumbers[0];

    if (!firstMeter) {
      return;
    }

    form.setValue("meterIdentifier", firstMeter);
    setQueryParams({ meterIdentifier: firstMeter });
  }, [
    companySiteMeterListMutation,
    siteAddressId,
    utilityType,
    useCustomMeter,
    companyReg,
    form,
    setQueryParams
  ]);

  return (
    <QuoteWizardItem className="lg:flex lg:flex-1 lg:py-8 xl:py-12">
      <FormField
        control={form.control}
        name="meterIdentifier"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="space-y-1">
              <p className="font-medium text-base">
                {useCustomMeter ? "Enter" : "Select"} Meter *
              </p>
              <p className="font-normal text-muted-foreground text-sm">
                Please select or provide your meter number
              </p>
            </FormLabel>
            <FormControl>
              <div className="grid w-full grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-1">
                {!useCustomMeter ? (
                  meterNumbersList && meterNumbersList.length > 0 ? (
                    meterNumbersList.map(meterNo =>
                      isElectricityQuote ? (
                        <MPANField
                          key={meterNo}
                          mpan={meterNo}
                          onSelect={handleMeterIdentifierChange}
                          isSelected={field.value === meterNo}
                        />
                      ) : (
                        <MPRNField
                          key={meterNo}
                          mprn={meterNo}
                          onSelect={handleMeterIdentifierChange}
                          isSelected={field.value === meterNo}
                        />
                      )
                    )
                  ) : meterNumbersList !== null ? (
                    <div className="text-muted-foreground text-sm">
                      No meters found for this utility at this address
                    </div>
                  ) : (
                    <Skeleton className="h-[114px] w-full" />
                  )
                ) : isElectricityQuote ? (
                  <EditableMPANField
                    value={customMeterIdentifier ?? ""}
                    onChange={handleMeterChange}
                  />
                ) : (
                  <EditableMPRNField
                    value={customMeterIdentifier ?? ""}
                    onChange={handleMeterChange}
                  />
                )}
              </div>
            </FormControl>
            <Button
              type="button"
              variant="link-secondary"
              onClick={handleCustomMeterToggle}
              className="cursor-pointer p-0 font-medium text-sm underline"
            >
              {useCustomMeter ? "Show detected meters" : "I don't see my meter"}
            </Button>
            <FormMessage />
          </FormItem>
        )}
      />
    </QuoteWizardItem>
  );
}
