"use client";

import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import { redirect } from "next/navigation";
import { UsageForm, type UsageFormProps } from "./usage-form";

export default function UsagePage() {
  const { queryParams } = useQueryParams<Partial<UsageFormProps>>();

  if (
    !queryParams.companyReg ||
    !queryParams.siteAddressId ||
    !queryParams.utilityType ||
    !queryParams.contactId
  ) {
    return redirect("/company");
  }

  return (
    <UsageForm
      companyReg={queryParams.companyReg}
      siteAddressId={queryParams.siteAddressId}
      utilityType={queryParams.utilityType}
      contactId={queryParams.contactId}
    />
  );
}
