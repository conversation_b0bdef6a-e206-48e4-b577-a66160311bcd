"use client";

import { ProviderLogo } from "@watt/common/src/components/provider-logo";
import {
  convertLocalDateToUTCString,
  convertUTCStringToLocalDate
} from "@watt/common/src/utils/format-date";
import { providersSoldByUs } from "@watt/db/src/constants/providers/providers-sold-by-us";
import { DatePickerInput } from "@watt/quote/components/date-picker-input";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { QuoteWizardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-title";
import { SuffixInput } from "@watt/quote/components/suffix-input";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/quote/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/quote/components/ui/select";
import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import Image from "next/image";
import { type ChangeEvent, memo, useCallback, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import type { UsageFormQueryParams } from "./usage-form";

type SupplySectionProps = {
  isUsageDataPending: boolean;
};

export const SupplySection = memo(
  ({ isUsageDataPending }: SupplySectionProps) => {
    const form = useFormContext();

    const { queryParams, setQueryParams } =
      useQueryParams<Partial<UsageFormQueryParams>>();

    const selectedProvider = useMemo(
      () =>
        providersSoldByUs.find(
          provider =>
            provider.udcoreId === queryParams.currentSupplier &&
            queryParams.currentSupplier !== "Other"
        ),
      [queryParams.currentSupplier]
    );

    const handleUsageChange = useCallback(
      ({ target: { value } }: ChangeEvent<HTMLInputElement>) => {
        setQueryParams({
          totalUsage: value
        });

        if (!value) {
          form.resetField("totalUsage");
          return;
        }

        const floatValue = Number.parseFloat(value);

        if (!Number.isNaN(floatValue)) {
          form.setValue("totalUsage", floatValue);
        }
      },
      [form, setQueryParams]
    );

    return (
      <QuoteWizardItem className="relative lg:flex-1 lg:border-x lg:bg-muted lg:px-8 lg:py-8 xl:px-16 xl:py-12">
        <div className="pt-6 lg:pb-6">
          <QuoteWizardTitle>Contract and usage</QuoteWizardTitle>
        </div>
        <div className="flex h-28 items-center justify-center overflow-hidden">
          <div className="relative flex h-full w-full items-center justify-center">
            {selectedProvider ? (
              <div className="flex h-16 items-center justify-center">
                <ProviderLogo
                  logoFileName={selectedProvider.logoFileName}
                  displayName={selectedProvider.displayName}
                  width={120}
                  height={40}
                  className="w-auto object-contain"
                />
              </div>
            ) : (
              <div className="flex h-28 items-center justify-center">
                <Image
                  src="/img/bill-tie.png"
                  alt="Bill Tie"
                  width={120}
                  height={40}
                  className="w-auto object-contain"
                  priority
                />
              </div>
            )}
          </div>
        </div>
        <FormField
          control={form.control}
          name="currentSupplier"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel className="font-normal text-base text-muted-foreground">
                Your current supplier *
              </FormLabel>
              <FormControl>
                <Select
                  value={field.value}
                  onValueChange={value => {
                    setQueryParams({ currentSupplier: value });
                    form.setValue("currentSupplier", value);
                  }}
                >
                  <SelectTrigger className="h-12 px-4 text-base shadow-sm hover:bg-muted">
                    <SelectValue placeholder="Select your supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {providersSoldByUs.map(provider => (
                      <SelectItem
                        value={provider.udcoreId}
                        key={provider.udcoreId}
                        className="text-base"
                      >
                        {provider.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contractStartDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-normal text-base text-muted-foreground">
                Start date of new contract *
              </FormLabel>
              <FormControl className="w-full">
                <DatePickerInput
                  className="h-12 px-4 text-base shadow-sm placeholder:italic hover:bg-muted"
                  date={
                    field.value
                      ? convertUTCStringToLocalDate(field.value)
                      : undefined
                  }
                  setDate={date => {
                    if (!date) {
                      return;
                    }

                    const utcStringDate = convertLocalDateToUTCString(date);

                    form.setValue("contractStartDate", utcStringDate);
                    setQueryParams({
                      contractStartDate: utcStringDate
                    });
                  }}
                  placeholder="Select the start date"
                  calendarProps={{
                    fromDate: new Date(),
                    disabled: { before: new Date() }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="totalUsage"
          render={() => {
            const totalUsage = form.getValues().totalUsage;
            return (
              <FormItem>
                <FormLabel className="font-normal text-base text-muted-foreground">
                  Estimated annual usage *
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <SuffixInput
                      suffix="kWh"
                      value={totalUsage === undefined ? "" : totalUsage}
                      onWheel={e => e.currentTarget.blur()}
                      disabled={isUsageDataPending}
                      placeholder="0 - 9,999"
                      className="h-12 px-4 text-base shadow-sm placeholder:italic hover:bg-muted"
                      onChange={handleUsageChange}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </QuoteWizardItem>
    );
  }
);
