"use client";

import { TRPCClientError } from "@trpc/client";
import { UsageInputSchema } from "@watt/api/src/types/pcw/usage";
import { isShortMpan } from "@watt/common/src/mpan/mpan";
import { isValidMPRN } from "@watt/common/src/mprn/mprn";
import { log } from "@watt/common/src/utils/axiom-logger";
import { providersSoldByUs } from "@watt/db/src/constants/providers/providers-sold-by-us";
import { UtilityType } from "@watt/db/src/enums";
import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardActions } from "@watt/quote/components/quote-wizard/quote-wizard-actions";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { Button } from "@watt/quote/components/ui/button";
import { FormWrapper } from "@watt/quote/components/ui/form";
import { toast } from "@watt/quote/components/ui/use-toast";
import { routes } from "@watt/quote/config/routes";
import { useQueryParams } from "@watt/quote/hooks/use-query-params";
import { useZodForm } from "@watt/quote/hooks/use-zod-form";
import { trpcClient } from "@watt/quote/utils/api";
import { ChevronLeftIcon, ChevronRightIcon, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { memo, useCallback, useEffect } from "react";
import { useDebounce } from "react-use";
import { MeterSection } from "./meter-section";
import { SupplySection } from "./supply-section";

export type UsageFormQueryParams = {
  utilityType: string;
  meterIdentifier: string;
  contractStartDate: string;
  currentSupplier: string;
  manualConsumptionEntry: string;
  totalUsage: string;
};

export type UsageFormProps = {
  companyReg: string;
  siteAddressId: string;
  utilityType: string;
  contactId: string;
};

export const UsageForm = memo((props: UsageFormProps) => {
  const { companyReg, siteAddressId, utilityType, contactId } = props;

  const router = useRouter();

  const { queryParams, setQueryParams } =
    useQueryParams<Partial<UsageFormQueryParams>>();

  const usageGetUsageDataMutation =
    trpcClient.pcw.usageGetUsageData.useMutation();
  const usageSubmitUsageInformationMutation =
    trpcClient.pcw.usageSubmitUsageInformation.useMutation();

  const form = useZodForm({
    schema: UsageInputSchema,
    mode: "onChange",
    defaultValues: {
      companyReg,
      siteAddressId,
      contactId,
      useCustomMeter: false,
      customMeterIdentifier: null,
      utilityType: utilityType as UtilityType,
      meterIdentifier: queryParams.meterIdentifier ?? "",
      contractStartDate: queryParams.contractStartDate ?? undefined
    }
  });

  const useCustomMeter = form.watch("useCustomMeter");
  const customMeterIdentifier = form.watch("customMeterIdentifier");

  const { meterIdentifier } = queryParams;
  const isElectricityQuote = utilityType === UtilityType.ELECTRICITY;

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(function updateDynamicInputFields() {
    if (queryParams.currentSupplier) {
      form.setValue("currentSupplier", queryParams.currentSupplier);
    }

    if (queryParams.totalUsage) {
      form.setValue("totalUsage", Number(queryParams.totalUsage));
    }
  }, []);

  useEffect(() => {
    console.log("customMeterIdentifier", customMeterIdentifier);
  }, [customMeterIdentifier]);

  const onBack = useCallback(() => router.back(), [router]);

  const resetUsageAndSupplier = useCallback(() => {
    form.resetField("totalUsage");
    form.resetField("currentSupplier");

    setQueryParams({
      totalUsage: "",
      currentSupplier: ""
    });
  }, [form, setQueryParams]);

  const handleFormSubmit = async () => {
    try {
      const result = await usageSubmitUsageInformationMutation.mutateAsync(
        form.getValues()
      );

      window.location.href = `${routes.quote}/${result.quoteListId}?contactId=${contactId}`;
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while getting the new quotes. Please check the form and try again."
          : error.message;
      toast({
        title: "Unable to get quotes",
        description,
        variant: "destructive"
      });
    }
  };

  useDebounce(
    async function lookupAnnualUsage() {
      const meterToCheck = useCustomMeter
        ? customMeterIdentifier
        : meterIdentifier;

      if (
        !meterToCheck ||
        !queryParams.utilityType ||
        (queryParams.totalUsage && queryParams.currentSupplier)
      ) {
        return;
      }

      try {
        const isValidMeter = isElectricityQuote
          ? isShortMpan(meterToCheck)
          : isValidMPRN(meterToCheck);

        if (!isValidMeter) {
          return;
        }

        const usageData = await usageGetUsageDataMutation.mutateAsync({
          meterIdentifier: meterToCheck,
          utilityType: queryParams.utilityType as UtilityType
        });

        if (!usageData) {
          return resetUsageAndSupplier();
        }

        if (!queryParams.totalUsage) {
          usageData.totalUsage &&
            form.setValue("totalUsage", usageData.totalUsage);
        }

        const matchingProvider = providersSoldByUs.find(
          provider =>
            provider.reccoId === usageData.supplier || // For gas
            provider.mpids.some(mpid => mpid === usageData.supplier) || // For electricity
            provider.udcoreId === usageData.supplier
        );

        if (!queryParams.currentSupplier) {
          const newCurrentSupplier = matchingProvider
            ? matchingProvider.udcoreId
            : "Other";

          form.setValue("currentSupplier", newCurrentSupplier);
          setQueryParams({
            currentSupplier: newCurrentSupplier
          });
        }
      } catch (e) {
        const error = e as Error;
        log.error(error.message);
        toast({
          title: "Unable to retrieve annual usage",
          description:
            "Error occurred while retrieving annual usage. Please check the meter identifier and utility type and try again.",
          variant: "destructive"
        });
      }
    },
    500,
    [meterIdentifier, customMeterIdentifier]
  );

  const isUsageDataPending = usageGetUsageDataMutation.isPending;
  const isSubmitting = usageSubmitUsageInformationMutation.isPending;

  return (
    <FormWrapper form={form} handleSubmit={handleFormSubmit} className="grow">
      <QuoteWizard>
        <QuoteWizardContent>
          <div className="lg:flex lg:gap-8 lg:rounded-lg lg:border lg:bg-white lg:px-8 lg:shadow-sm xl:gap-16 xl:px-16">
            <MeterSection {...props} />
            <SupplySection isUsageDataPending={isUsageDataPending} />
          </div>
        </QuoteWizardContent>
        <QuoteWizardActions className="justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            disabled={isSubmitting}
            className="text-base"
          >
            <ChevronLeftIcon className="mr-1 size-5" />
            Back
          </Button>
          <Button
            type="submit"
            variant="secondary"
            className="text-base"
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
            Get Quotes <ChevronRightIcon className="ml-1 size-5" />
          </Button>
        </QuoteWizardActions>
      </QuoteWizard>
    </FormWrapper>
  );
});
