"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { QuoteWizardNavStepIndicator } from "@watt/quote/components/quote-wizard/quote-wizard-nav/quote-wizard-nav-step-indicator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/quote/components/ui/popover";
import { ChevronDownIcon } from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo, useState } from "react";

export type QuoteWizardNavStep = {
  id: number;
  label: string;
  href: string;
};

export const QUOTE_WIZARD_NAV_STEPS: QuoteWizardNavStep[] = [
  {
    id: 1,
    label: "Company Information",
    href: "/company"
  },
  {
    id: 2,
    label: "Site Information",
    href: "/site"
  },
  {
    id: 3,
    label: "Utility Usage",
    href: "/usage"
  },
  {
    id: 4,
    label: "Utility Quotes",
    href: "/quote"
  },
  {
    id: 5,
    label: "Sign Contract",
    href: "/contract"
  }
] as const;

export function QuoteWizardNav() {
  const pathname = usePathname();

  const [isOpen, setIsOpen] = useState(false);

  const currentStep = useMemo(() => {
    const found = QUOTE_WIZARD_NAV_STEPS.find(step =>
      pathname.includes(step.href)
    );
    if (found) {
      return found;
    }

    const firstStep = QUOTE_WIZARD_NAV_STEPS[0];
    if (!firstStep) {
      throw new Error("No wizard steps defined");
    }
    return firstStep;
  }, [pathname]);

  return (
    <nav className="flex h-14 shrink-0 items-center justify-between border-b bg-background">
      <div className="container mx-auto hidden w-full items-center justify-between gap-8 px-6 lg:flex">
        {QUOTE_WIZARD_NAV_STEPS.map(step => {
          const isCurrent = step.id === currentStep.id;
          const isCompleted = step.id <= currentStep.id;

          return (
            <button key={step.id} type="button">
              <QuoteWizardNavStepIndicator
                step={step}
                isCurrent={isCurrent}
                isCompleted={isCompleted}
              />
            </button>
          );
        })}
      </div>
      <div className="relative flex h-full w-full lg:hidden">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <button
              type="button"
              className="flex h-full w-full items-center justify-between bg-background px-6 text-left outline-none ring-0 hover:bg-muted focus:ring-0 focus-visible:ring-0 active:ring-0"
            >
              <QuoteWizardNavStepIndicator
                step={currentStep}
                isCurrent={true}
                isCompleted={false}
              />
              <ChevronDownIcon
                className={cn("size-5 transition", isOpen && "rotate-180")}
              />
            </button>
          </PopoverTrigger>
          <PopoverContent className="-mt-1 w-[var(--radix-popover-trigger-width)] rounded-none p-0 shadow-sm">
            <div className="divide-y">
              {QUOTE_WIZARD_NAV_STEPS.map(
                step =>
                  step.id !== currentStep.id && (
                    <button
                      key={step.id}
                      type="button"
                      className="flex h-14 w-full items-center px-6 hover:bg-muted"
                    >
                      <QuoteWizardNavStepIndicator
                        key={step.id}
                        step={step}
                        isCurrent={false}
                        isCompleted={step.id < currentStep.id}
                      />
                    </button>
                  )
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </nav>
  );
}
