@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --muted: 0 0% 98%;
    --muted-foreground: 0 0% 29%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --primary: 199 82% 15%;
    --primary-foreground: 210 40% 98%;

    --secondary: 76 71% 43%;
    --secondary-foreground: 210 40% 98%;
    --secondary-light: 77 54% 89%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;

    --green-theme: #92bc20;
    --red-theme: #f23e2e;
    --primary-light: #8698a2;
    --dark-theme: #073348;
    --dark-theme-2: #1b3c53;

    --badge-purple: #d8b6fa;
    --badge-light-blue: #b4d3ff;
    --badge-dark-blue: #1d3557;
    --badge-red: #9c1313;
    --badge-lime-green: #d5e8a3;
    --badge-green: #93bc20;
    --badge-dark-grey: #4b4b4b;

    --badge-light-green: #c7e8a3;
    --badge-success: #93bc20;
    --badge-orange: #ffc107;
    --badge-yellow: #fff3cd;
    --badge-light-yellow: #fffbea;
    --badge-dark-red: #dc3545;
    --badge-spam: #e74c3c;
    --badge-unsubscribe: #5e4338;
    --badge-group-unsubscribe: #6c757d;
    --badge-group-resubscribe: #93bc20;
    --badge-error: #dc3545;
  }

  .dark {
    --background: 220 10% 12%;
    --foreground: 210 40% 98%;

    --muted: 220 8% 15%;
    --muted-foreground: 215 20.2% 65.1%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --card: 220 10% 12%;
    --card-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 76 71% 27.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-light: 77, 54%, 89%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --ring: 217.2 32.6% 17.5%;

    --badge-orange: #cf9c05;
    --badge-yellow: #fde7a1;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* react-day-picker - style applyed to Calendar component layout for the dropdown buttons UI (TAS-1622) */
.rdp [aria-hidden="true"] {
  @apply hidden;
}

.rdp-vhidden {
  @apply hidden;
}

select::-webkit-scrollbar {
  display: none;
}
/* END */

/* Hide NOVU message */
.nv-inboxContent > div:last-child {
  display: none;
}

/* I hate this */
.font-sans-default {
  font-family: sans-serif !important;
}
