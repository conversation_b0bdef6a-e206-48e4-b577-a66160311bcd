import { env } from "@watt/common/src/config/env";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { TailwindIndicator } from "@watt/quote/components/tailwind-indicator";
import { Toaster } from "@watt/quote/components/ui/toaster";
import { TooltipProvider } from "@watt/quote/components/ui/tooltip";
import { fontSans } from "@watt/quote/lib/fonts";
import type { Metadata } from "next";
import { headers } from "next/headers";
import NextTopLoader from "nextjs-toploader";
import { Suspense } from "react";
import { TRPCReactProvider } from "../trpc/trpc-react-provider";
import "./globals.css";
import { ReactScan } from "./react-scan";
import { StagewiseToolbarWrapper } from "./stagewise-toolbar";

export const metadata: Metadata = {
  title: "Quote App",
  description: "Quote generation application"
};

export default async function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const header = await headers();
  const isDevelopment = env.NODE_ENV === "development";

  const stagewiseConfig = {
    plugins: []
  };

  return (
    <html lang="en">
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          fontSans.variable
        )}
      >
        <Suspense fallback={null}>
          <ReactScan options={{ enabled: false, log: true }} />
        </Suspense>
        <TRPCReactProvider
          readonlyHeaders={header}
          showDevTools={isDevelopment}
        >
          <NextTopLoader color="#92bc20" showSpinner={false} />
          <TooltipProvider delayDuration={0}>{children}</TooltipProvider>
          <TailwindIndicator />
          <Toaster />
          {isDevelopment && (
            <StagewiseToolbarWrapper config={stagewiseConfig} />
          )}
        </TRPCReactProvider>
      </body>
    </html>
  );
}
