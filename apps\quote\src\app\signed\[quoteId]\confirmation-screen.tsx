"use client";

import { UtilityType } from "@prisma/client";
import { TRPCClientError } from "@trpc/client";
import { log } from "@watt/common/src/utils/axiom-logger";
import { formatDate } from "@watt/common/src/utils/format-date";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardCard } from "@watt/quote/components/quote-wizard/quote-wizard-card";
import { QuoteWizardCardDescription } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-description";
import { QuoteWizardCardHeader } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-header";
import { QuoteWizardCardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-card/quote-wizard-card-title";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { QuoteWizardTitle } from "@watt/quote/components/quote-wizard/quote-wizard-title";
import { Badge } from "@watt/quote/components/ui/badge";
import { Button } from "@watt/quote/components/ui/button";
import { toast } from "@watt/quote/components/ui/use-toast";
import { routes } from "@watt/quote/config/routes";
import { trpcClient } from "@watt/quote/utils/api";
import { type PcwQuotes, getProductPlan } from "@watt/quote/utils/quote-utils";
import { format } from "date-fns";
import {
  CalendarRangeIcon,
  ChevronRightIcon,
  CircleCheckBigIcon,
  Loader2Icon
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo } from "react";
import Confetti from "react-confetti";
import { useWindowSize } from "react-use";
import { SkeletonLoader } from "./skeleton-loader";
import { UtilityIcon } from "./utility-icon";

type ConfirmationScreenProps = {
  quoteId: string;
  contactId: string;
};

export function ConfirmationScreen({
  quoteId,
  contactId
}: ConfirmationScreenProps) {
  const { width, height } = useWindowSize();

  const availableWidth = useMemo(() => {
    return typeof window !== "undefined"
      ? document?.documentElement?.clientWidth
      : width - 16;
  }, [width]);

  const router = useRouter();

  const downloadSignedContract =
    trpcClient.pcw.downloadSignedContract.useMutation();

  const { data, isLoading } = trpcClient.pcw.getConfirmationDetails.useQuery({
    quoteId
  });

  const quote = data?.quote;
  const deal = data?.deal;

  const isSigned = !!deal?.signedAt;

  const siteMeter = quote?.quoteList?.siteMeter;
  const siteAddress = siteMeter?.companySite?.entityAddress;

  const productPlan = useMemo(
    () => (!quote ? "" : getProductPlan(quote as unknown as PcwQuotes)),
    [quote]
  );

  const meterNumber = useMemo(
    () =>
      siteMeter?.electricSiteMeter?.mpan?.value ??
      siteMeter?.gasSiteMeter?.mprn?.value,
    [siteMeter]
  );

  async function downloadContract() {
    try {
      const result = await downloadSignedContract.mutateAsync({
        quoteId
      });

      if (result.signedUrl) {
        const response = await fetch(result.signedUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${siteAddress?.displayName}-${humanize(quote?.utilityType)}.pdf`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
      }
    } catch (e) {
      const error = e as Error;
      log.error(error.message);
      const description =
        error instanceof TRPCClientError && !!error.data.zodError
          ? "Error occurred while downloading the signed contract"
          : error.message;
      toast({
        title: "Unable to download signed contract",
        description,
        variant: "destructive"
      });
    }
  }

  function completeNextQuote() {
    const companyReg =
      quote?.quoteList?.siteMeter?.companySite?.company?.registrationNumber ??
      "";
    const siteAddressId = siteAddress?.id ?? "";
    const utilityType =
      quote?.utilityType === UtilityType.GAS
        ? UtilityType.ELECTRICITY
        : UtilityType.GAS;
    router.push(
      `${routes.usage}?companyReg=${companyReg}&siteAddressId=${siteAddressId}&utilityType=${utilityType}&contactId=${contactId}`
    );
  }

  function goToSiteManager() {
    alert("TODO: Build and link to site manager");
  }

  useEffect(() => {
    if (!isLoading && (!quote || !isSigned)) {
      router.push("/company");
    }
  }, [isLoading, quote, isSigned, router]);

  if (isLoading || !quote || !isSigned) {
    return <SkeletonLoader />;
  }

  return (
    <>
      <QuoteWizard className="grow">
        <QuoteWizardContent>
          <QuoteWizardItem className="mx-auto w-full max-w-3xl items-center gap-8">
            <QuoteWizardItem className="flex flex-col items-center">
              <CircleCheckBigIcon className="size-10" />
              <QuoteWizardTitle className="font-medium">
                Contract Signed!
              </QuoteWizardTitle>
              <p className="text-center">
                Your {quote.utilityType.toLowerCase()} contract for{" "}
                <span className="font-medium text-secondary">
                  {siteAddress?.displayName}
                </span>{" "}
                has been processed.
              </p>
            </QuoteWizardItem>
            <QuoteWizardItem className="mx-auto w-full max-w-xl gap-6">
              <QuoteWizardCard className="gap-6">
                <QuoteWizardCardHeader>
                  <div className="flex items-center justify-between">
                    <QuoteWizardCardTitle>
                      Contract Summary
                    </QuoteWizardCardTitle>
                    <Badge>Signed</Badge>
                  </div>
                  <QuoteWizardCardDescription>
                    Signed date: {formatDate(deal?.signedAt)}
                  </QuoteWizardCardDescription>
                </QuoteWizardCardHeader>
                <QuoteWizardCardHeader className="gap-0.5">
                  <span className="font-medium">Site Address</span>
                  <span className="text-muted-foreground text-sm">
                    {siteAddress?.displayName}
                  </span>
                </QuoteWizardCardHeader>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
                  <div className="flex items-center gap-3">
                    <UtilityIcon utilityType={quote.utilityType} />
                    <div className="flex flex-col gap-1">
                      <span className="font-medium text-sm">
                        {humanize(quote.utilityType)}
                      </span>
                      <span className="text-muted-foreground text-xs">
                        <span className="font-medium">{meterNumber}</span>
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center rounded-full bg-zinc-400/15 p-1.5">
                      <CalendarRangeIcon className="size-4 text-zinc-500" />
                    </div>
                    <div className="flex flex-col gap-1">
                      <span className="font-medium text-sm">{productPlan}</span>
                      <span className="text-muted-foreground text-xs">
                        <span className="font-medium">
                          {format(
                            quote.quoteList.contractStartDate,
                            "dd/MM/yyyy"
                          )}{" "}
                          - {format(quote.endDate, "dd/MM/yyyy")}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="secondary"
                  className="text-base"
                  onClick={downloadContract}
                  disabled={downloadSignedContract.isPending}
                >
                  {downloadSignedContract.isPending && (
                    <Loader2Icon className="mr-2 size-4 animate-spin" />
                  )}
                  Download Contract
                </Button>
              </QuoteWizardCard>
              <QuoteWizardItem>
                {data?.showNextAction && (
                  <>
                    <QuoteWizardCardTitle>
                      What&apos;s next?
                    </QuoteWizardCardTitle>
                    <QuoteWizardCard className="gap-6">
                      <QuoteWizardCardTitle>
                        Continue with{" "}
                        {quote.utilityType === UtilityType.GAS
                          ? "Electricity"
                          : "Gas"}{" "}
                        Quote
                      </QuoteWizardCardTitle>
                      <div className="flex items-center gap-3">
                        <UtilityIcon utilityType={quote.utilityType} reversed />
                        <p className="font-medium text-sm">
                          Complete{" "}
                          {quote.utilityType === UtilityType.GAS
                            ? "electricity"
                            : "gas"}{" "}
                          quote for your site
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="secondary"
                        className="mt-1 text-base"
                        onClick={completeNextQuote}
                      >
                        Continue
                      </Button>
                    </QuoteWizardCard>
                  </>
                )}
                <QuoteWizardCard className="gap-6 border-secondary">
                  <QuoteWizardCardHeader>
                    <QuoteWizardCardTitle>
                      View All Sites & Quotes
                    </QuoteWizardCardTitle>
                    <QuoteWizardCardDescription>
                      View all your sites, utilities, and contracts in one place
                    </QuoteWizardCardDescription>
                  </QuoteWizardCardHeader>
                  <Button
                    type="button"
                    variant="outline-secondary"
                    className="text-base"
                    onClick={goToSiteManager}
                  >
                    Go to Site Manager
                    <ChevronRightIcon className="ml-2 size-4" />
                  </Button>
                </QuoteWizardCard>
              </QuoteWizardItem>
            </QuoteWizardItem>
          </QuoteWizardItem>
        </QuoteWizardContent>
      </QuoteWizard>
      <Confetti
        width={availableWidth}
        height={height}
        gravity={0.5}
        recycle={false}
      />
    </>
  );
}
