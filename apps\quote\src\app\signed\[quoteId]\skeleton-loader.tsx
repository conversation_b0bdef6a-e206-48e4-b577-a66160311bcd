"use client";

import { QuoteWizard } from "@watt/quote/components/quote-wizard/quote-wizard";
import { QuoteWizardContent } from "@watt/quote/components/quote-wizard/quote-wizard-content";
import { QuoteWizardItem } from "@watt/quote/components/quote-wizard/quote-wizard-item";
import { Skeleton } from "@watt/quote/components/ui/skeleton";

export function SkeletonLoader() {
  return (
    <QuoteWizard className="grow py-4">
      <QuoteWizardContent>
        <QuoteWizardItem className="mx-auto w-full max-w-3xl items-center gap-16">
          <QuoteWizardItem className="flex w-full flex-col items-center">
            <Skeleton className="size-12 rounded-full" />
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-8 w-full" />
          </QuoteWizardItem>
          <QuoteWizardItem className="mx-auto w-full max-w-lg gap-12">
            <Skeleton className="h-80 w-full rounded-lg" />
            <div className="flex flex-col gap-6">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-40 w-full rounded-lg" />
              <Skeleton className="h-40 w-full rounded-lg" />
            </div>
          </QuoteWizardItem>
        </QuoteWizardItem>
      </QuoteWizardContent>
    </QuoteWizard>
  );
}
