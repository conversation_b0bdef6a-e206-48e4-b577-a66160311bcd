"use client";

import { UtilityType } from "@prisma/client";
import { FlameIcon, LightbulbIcon } from "lucide-react";

export function UtilityIcon({
  utilityType,
  reversed = false
}: { utilityType: UtilityType; reversed?: boolean }) {
  const actualUtilityType = reversed
    ? utilityType === UtilityType.ELECTRICITY
      ? UtilityType.GAS
      : UtilityType.ELECTRICITY
    : utilityType;

  return actualUtilityType === UtilityType.ELECTRICITY ? (
    <div className="flex items-center justify-center rounded-full bg-yellow-400/15 p-1.5">
      <LightbulbIcon className="size-4 text-yellow-500" />
    </div>
  ) : (
    <div className="flex items-center justify-center rounded-full bg-rose-400/15 p-1.5">
      <FlameIcon className="size-4 text-rose-500" />
    </div>
  );
}
