import type { Address_Find_Many } from "@watt/api/src/router";
import { useRef, useState } from "react";

import {
  AddressSelection,
  LookUp,
  LookUpContent,
  LookUpGroup,
  LookUpItem,
  LookUpTrigger
} from "./lookup";

type AddressInputProps = {
  value: string;
  onSubmit: (id: string) => void;
  handleAddressSelected?: (address: Address_Find_Many[0] | null) => void;
  // @deprecated use handleAddressSelected instead provides access to the full address object
  handleAddressSelect?: (value: string) => void;
  addressData: Address_Find_Many | undefined;
  selectedAddress: Address_Find_Many[0] | undefined;
  handleAddressSearchInputChange: (addressSearch: string) => void;
  addressSearchInput: string | undefined;
  isLoading?: boolean;
  placeholder?: string;
  hideMetersInfo?: boolean;
};

export function AddressInput({
  value,
  isLoading,
  addressSearchInput,
  addressData,
  selectedAddress,
  hideMetersInfo,
  handleAddressSearchInputChange,
  handleAddressSelect,
  handleAddressSelected,
  onSubmit,
  placeholder = ""
}: AddressInputProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const containerRef = useRef<HTMLInputElement | null>(null);

  const onSelect = (value: string) => {
    if (handleAddressSelected) {
      handleAddressSelected(addressData?.find(a => a.id === value) ?? null);
    } else {
      handleAddressSelect?.(value);
    }
    setIsModalOpen(false);
  };

  return (
    <div ref={containerRef} className="flex flex-col overflow-x-hidden">
      <LookUp open={isModalOpen} onOpenChange={setIsModalOpen}>
        <LookUpTrigger
          fieldValue={value}
          isLoading={isLoading}
          className="h-12 px-4 text-base shadow-sm hover:bg-muted"
        >
          {selectedAddress ? (
            <div className="max-w-96">
              <AddressSelection
                isTriggerDisplay
                address={selectedAddress}
                hideMetersInfo={hideMetersInfo}
              />
            </div>
          ) : (
            <span className="font-normal">
              {placeholder || "Select an address..."}
            </span>
          )}
        </LookUpTrigger>
        <LookUpContent
          placeholder="Search by postcode"
          searchInput={addressSearchInput}
          onSearchInputChange={handleAddressSearchInputChange}
          isLoading={isLoading}
          container={containerRef.current}
        >
          <LookUpGroup className="p-0">
            {addressData?.map(address => (
              <LookUpItem
                key={address.id}
                value={address.id}
                onSelect={onSelect}
              >
                <AddressSelection
                  address={address}
                  hideMetersInfo={hideMetersInfo}
                />
              </LookUpItem>
            ))}
          </LookUpGroup>
        </LookUpContent>
      </LookUp>
    </div>
  );
}
