import type { Address_Find_Many } from "@watt/api/src/router";
import type { CreateCompanyAndContactInput } from "@watt/api/src/types/pcw/company";
import type { ContactAddressFormData } from "@watt/api/src/types/people";
import { DatePickerInlineInput } from "@watt/quote/components/date-picker-inline-input";
import { Button } from "@watt/quote/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/quote/components/ui/form";
import { Label } from "@watt/quote/components/ui/label";
import { Switch } from "@watt/quote/components/ui/switch";
import { toast } from "@watt/quote/components/ui/use-toast";
import { useAddressSearch } from "@watt/quote/hooks/use-address-search";
import { useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";
import { useDebounce } from "react-use";
import { v4 as uuidv4 } from "uuid";
import { AddressInput } from "../address-input";

type ContactAddressFormProps = {
  onSubmitForm: (data: ContactAddressFormData) => void;
  onCancel: () => void;
  existingAddress?: ContactAddressFormData;
  index: number;
};

export function ContactAddressForm({
  onSubmitForm,
  onCancel,
  existingAddress,
  index
}: ContactAddressFormProps) {
  const form = useFormContext<CreateCompanyAndContactInput>();
  const path = `contact.addresses.${index}` as const;
  const isCurrentAddress = form.watch(`${path}.isCurrent`);
  const [addressData, setAddressData] = useState<
    Address_Find_Many | undefined
  >();
  const [addressSearchInput, setAddressSearchInput] = useState(
    existingAddress?.address.id || ""
  );
  const [selectedAddressId, setSelectedAddressId] = useState(
    existingAddress?.address.id || ""
  );

  // Create an immutable copy of existingAddress at component mount
  const initialAddress = useRef(
    existingAddress ? { ...existingAddress } : undefined
  );

  const { fetchAddress, isFetchingAddress } = useAddressSearch();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(() => {
    const formData = form.getValues(path);

    // On handleCancelForm the formData is set to undefined. This messes with the form validation so we set the address.id to an empty string to reinitialise the form
    // TODO: Find a better way to handle this
    if (!formData) {
      form.setValue(`${path}.address.id`, "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useDebounce(
    async function lookupAddressOnSearchInput() {
      try {
        if (!addressSearchInput) {
          setAddressData(undefined);
          return;
        }

        const result = await fetchAddress(addressSearchInput);
        if (result) {
          setAddressData(result);
        }
      } catch (e) {
        const error = e as Error;
        toast({
          title: "Unable to get address",
          description: error.message,
          variant: "destructive"
        });
      }
    },
    1000,
    [addressSearchInput]
  );

  const handleAddressSelect = (address: Address_Find_Many[0] | null) => {
    // If the address is new, set a localId for it
    if (!form.getValues(`${path}.localId`)) {
      form.setValue(`${path}.localId`, uuidv4());
    }

    setSelectedAddressId(address?.id ?? "");
    form.setValue(`${path}.address.id`, address?.id ?? "", {
      shouldDirty: true
    });
    form.setValue(`${path}.address.postcode`, address?.postcode ?? "");
    form.setValue(`${path}.address.displayName`, address?.displayName ?? "");
  };

  const handleSave = async () => {
    // Check for duplicate addresses with the same movedInDate, excluding the current address being edited
    const addresses = form.getValues("contact.addresses");
    const conflictingAddresses = addresses.filter(
      address =>
        address.address.id === selectedAddressId &&
        address.movedInDate === form.getValues(`${path}.movedInDate`) &&
        address.localId !== form.getValues(`${path}.localId`)
    );

    if (conflictingAddresses.length > 0) {
      form.setError(`${path}.address.id`, {
        message:
          "This address with the same moved in date is already linked to the contact. Please select a different address or edit the existing entry."
      });
      return;
    }

    const isCurrent = form.getValues(`${path}.isCurrent`);
    const movedOutDate = form.getValues(`${path}.movedOutDate`);

    // Reset movedOutDate if isCurrent is true - this allows the user to toggle the switch in UI without having to clear the movedOutDate field
    if (isCurrent && movedOutDate) {
      form.setValue(`${path}.movedOutDate`, "", { shouldDirty: true });
    }

    const isValid = await form.trigger(path);
    console.log({ isValid });
    if (!isValid) {
      return;
    }

    const formData = form.getValues(path);
    onSubmitForm(formData);
  };

  const onNewAddressSubmit = (id: string) => {
    setAddressSearchInput(id);
  };

  const handleCancelForm = () => {
    // If this is a new address (not editing), remove it from form
    if (!initialAddress.current) {
      const addresses = form.getValues("contact.addresses");
      form.setValue(
        "contact.addresses",
        addresses.filter(
          addr => addr.localId !== form.getValues(`${path}.localId`)
        )
      );
    }

    // Reset existing address data if it was edited
    if (initialAddress.current) {
      form.setValue(`${path}.address.id`, initialAddress.current.address.id);
      form.setValue(
        `${path}.address.postcode`,
        initialAddress.current.address.postcode
      );
      form.setValue(
        `${path}.address.displayName`,
        initialAddress.current.address.displayName
      );
      form.setValue(`${path}.isCurrent`, initialAddress.current.isCurrent);
      form.setValue(`${path}.movedInDate`, initialAddress.current.movedInDate);
      form.setValue(
        `${path}.movedOutDate`,
        initialAddress.current.movedOutDate
      );
    }

    form.clearErrors(path);
    onCancel();
  };

  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name={`${path}.address.id`}
        render={() => (
          <FormItem>
            <Label>Address *</Label>
            <FormControl>
              <AddressInput
                value={selectedAddressId}
                isLoading={isFetchingAddress}
                addressData={addressData}
                selectedAddress={addressData?.find(
                  a => a.id === selectedAddressId
                )}
                addressSearchInput={addressSearchInput}
                handleAddressSearchInputChange={setAddressSearchInput}
                handleAddressSelected={handleAddressSelect}
                onSubmit={onNewAddressSubmit}
                placeholder="Select an address..."
                hideMetersInfo
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name={`${path}.isCurrent`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Is Current Address</FormLabel>
            <div className="space-y-4 rounded-md border p-4">
              <div className="flex items-center justify-between gap-2">
                <div className="space-y-0.5">
                  <FormLabel className="text-xs">
                    Turn on Toggle if this is the current address
                  </FormLabel>
                  <FormDescription className="text-xs">
                    Alternatively, leave it off if this is not the current
                    address.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    className="data-[state=checked]:bg-secondary"
                    onCheckedChange={value => {
                      form.setValue(`${path}.isCurrent`, value, {
                        shouldDirty: true
                      });
                    }}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name={`${path}.movedInDate`}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <Label>Moved In Date *</Label>
            <DatePickerInlineInput
              {...field}
              placeholder="dd/mm/yyyy"
              calendarProps={{
                toDate: new Date(),
                captionLayout: "dropdown-buttons",
                fromYear: 1920,
                toYear: new Date().getFullYear(),
                disabled: { after: new Date() }
              }}
            />
            <FormMessage />
          </FormItem>
        )}
      />

      {!isCurrentAddress && (
        <FormField
          control={form.control}
          name={`${path}.movedOutDate`}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <Label>Moved Out Date {isCurrentAddress ? "" : "*"}</Label>
              <DatePickerInlineInput
                {...field}
                placeholder="dd/mm/yyyy"
                calendarProps={{
                  toDate: new Date(),
                  captionLayout: "dropdown-buttons",
                  fromYear: 1920,
                  toYear: new Date().getFullYear(),
                  disabled: { after: new Date() }
                }}
              />
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <Button
        disabled={isFetchingAddress}
        type="button"
        variant="outline-secondary"
        className="w-full"
        onClick={handleSave}
      >
        Save Address
      </Button>
      <Button
        type="button"
        variant="outline"
        className="w-full"
        onClick={handleCancelForm}
        disabled={isFetchingAddress}
      >
        Cancel
      </Button>
    </div>
  );
}
