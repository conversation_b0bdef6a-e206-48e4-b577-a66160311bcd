import type { Table } from "@tanstack/react-table";
import {
  Chevron<PERSON>eft,
  ChevronRight,
  Chevrons<PERSON>eft,
  ChevronsRight
} from "lucide-react";

import { <PERSON><PERSON> } from "@watt/quote/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@watt/quote/components/ui/select";

/**
 * Represents a row of data in the table, extending the generic TData type.
 *
 * @template TData The base type for the row data.
 *
 * @property {boolean} [sticky] Optional property indicating if the row should be sticky.
 *   When true, the row will remain fixed in position regardless of scrolling or filtering.
 *   Sticky rows are not factored into the total row count or any other calculations.
 *   Defaults to undefined.
 */
export type RowData<TData> = TData & {
  sticky?: boolean;
};

interface DataTablePaginationProps<TData> {
  table: Table<RowData<TData>>;
  rowsPerPageOptions?: number[];
  setOriginalPageSize?: (pageSize: number) => void;
}

export function DataTablePagination<TData>({
  table,
  rowsPerPageOptions = [10, 20, 30, 40, 50, 100],
  setOriginalPageSize = undefined
}: DataTablePaginationProps<TData>) {
  const excludedSelectedRows = table
    .getFilteredSelectedRowModel()
    .rows.filter(row => !row.original.sticky);
  const excludedFilteredRows = table
    .getFilteredRowModel()
    .rows.filter(row => !row.original.sticky);
  const excludedTotalRows = table
    .getPreFilteredRowModel()
    .rows.filter(row => !row.original.sticky);

  const selectedRowCount = excludedSelectedRows.length;
  const filteredRowCount = excludedFilteredRows.length;
  const totalRowCount = excludedTotalRows.length;
  const hiddenRowCount = totalRowCount - filteredRowCount;

  return (
    <div className="flex items-center justify-between px-2">
      <div className="flex flex-row gap-2">
        {table.options.enableRowSelection && (
          <span className="text-muted-foreground text-sm">
            {selectedRowCount} of {filteredRowCount} row(s) selected.
          </span>
        )}
        {hiddenRowCount > 0 && (
          <span className="text-muted-foreground text-sm">
            {hiddenRowCount} row(s) hidden by filters.
          </span>
        )}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="font-medium text-sm">Rows per page</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={value => {
              const newSize = Number(value);
              table.setPageSize(newSize);
              setOriginalPageSize?.(newSize);
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {rowsPerPageOptions.map(pageSize => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center font-medium text-sm">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only fixed">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only fixed">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only fixed">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only fixed">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
