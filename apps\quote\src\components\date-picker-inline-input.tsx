import { isValid, parse } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import {
  type ChangeEvent,
  type ComponentProps,
  useEffect,
  useId,
  useState
} from "react";
import type { ControllerRenderProps } from "react-hook-form";

import { Button } from "@watt/quote/components/ui/button";
import { Calendar } from "@watt/quote/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/quote/components/ui/popover";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { formatDate } from "@watt/common/src/utils/format-date";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { Input } from "./ui/input";

type DatePickerInlineInputProps = ControllerRenderProps & {
  placeholder?: string;
  calendarProps?: ComponentProps<typeof Calendar>;
};

const dateFormat = dateFormats.DD_MM_YYYY;

export const DatePickerInlineInput: React.FC<DatePickerInlineInputProps> = ({
  ref,
  placeholder,
  calendarProps,
  value,
  onChange,
  disabled
}) => {
  const [inputValue, setInputValue] = useState("");
  const [month, setMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const inputId = useId();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <fix later>
  useEffect(() => {
    if (value) {
      const parsedDate = parse(value, dateFormat, new Date());

      setInputValue(value);
      setMonth(parsedDate);
      setSelectedDate(parsedDate);
    } else {
      // Reset state when value is undefined/empty
      setInputValue("");
      setSelectedDate(undefined);
      setMonth(new Date());
    }
  }, []);

  const setDate = (date: Date | undefined) => {
    if (!date) {
      setInputValue("");
      setSelectedDate(undefined);
      onChange("");
    } else {
      setSelectedDate(date);
      setMonth(date);
      const dateStr = formatDate(date, dateFormat);
      setInputValue(dateStr);
      onChange(dateStr);
    }
  };

  const handleInputChange = (ev: ChangeEvent<HTMLInputElement>) => {
    const { value } = ev.target;
    setInputValue(value);

    const parsedDate = parse(value, dateFormat, new Date());

    if (!isValid(parsedDate)) {
      onChange(value);
      return;
    }

    setSelectedDate(parsedDate);
    setMonth(parsedDate);
    onChange(formatDate(parsedDate, dateFormat));
  };

  const onCalendarMonthChange = (selectedMonthDate: Date) => {
    if (!selectedMonthDate) {
      return;
    }
    const existingDate = parse(value || new Date(), dateFormat, new Date());

    if (!isValid(existingDate)) {
      setDate(selectedMonthDate);
      return;
    }

    // update the year or month but keep the existing day
    let updatedDate = new Date(
      selectedMonthDate.getFullYear(),
      selectedMonthDate.getMonth(),
      existingDate.getDate()
    );

    // check if the date rolled over to the next month (invalid day for new month, like changing from 31 to 30 days months)
    if (updatedDate.getMonth() !== selectedMonthDate.getMonth()) {
      // if invalid, set the date to the last day of the selected month
      updatedDate = new Date(
        selectedMonthDate.getFullYear(),
        selectedMonthDate.getMonth() + 1,
        0 // the 0th day of the next month is the last day of the current month
      );
    }

    setDate(updatedDate);
  };

  return (
    <Popover>
      <div className="relative">
        <Input
          ref={ref}
          id={inputId}
          value={inputValue}
          placeholder={placeholder || "Pick a date"}
          onChange={handleInputChange}
          className={cn(
            "h-12 text-base",
            !inputValue && "text-muted-foreground"
          )}
          disabled={disabled}
        />
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="-translate-y-1/2 absolute top-1/2 right-2 size-9 bg-background"
            disabled={disabled}
          >
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </Button>
        </PopoverTrigger>
      </div>

      <PopoverContent className="w-auto p-0">
        <Calendar
          {...calendarProps}
          mode="single"
          selected={selectedDate}
          onSelect={setDate}
          month={month}
          onMonthChange={onCalendarMonthChange}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
};

DatePickerInlineInput.displayName = "DatePickerInlineInput";
