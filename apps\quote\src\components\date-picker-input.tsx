import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { type ComponentProps, useRef } from "react";

import { Button } from "@watt/quote/components/ui/button";
import { Calendar } from "@watt/quote/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/quote/components/ui/popover";

type DatePickerInputProps = {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  calendarProps?: ComponentProps<typeof Calendar>;
  disabled?: boolean;
};

export function DatePickerInput({
  date,
  setDate,
  placeholder,
  className,
  calendarProps,
  disabled
}: DatePickerInputProps) {
  const popoverContainerRef = useRef<HTMLDivElement>(null);
  return (
    <div ref={popoverContainerRef} className="flex w-full flex-col">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(className, "justify-between font-normal", {
              "opacity-50": disabled
            })}
            disabled={disabled}
          >
            {date ? (
              format(date, "PPP")
            ) : (
              <span className="text-muted-foreground italic">
                {placeholder || "Pick a date"}
              </span>
            )}
            <CalendarIcon className="ml-2 h-4 w-4 text-muted-foreground" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0"
          container={popoverContainerRef.current}
        >
          <Calendar
            {...calendarProps}
            mode="single"
            selected={date}
            onSelect={setDate}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
