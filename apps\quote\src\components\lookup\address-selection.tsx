import type { Address_Find_Many } from "@watt/api/src/router/address";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { Flame, Lightbulb } from "lucide-react";

type Address = Address_Find_Many[0];

type AddressSelectionProps = {
  address: Address;
  hideMetersInfo?: boolean;
  isTriggerDisplay?: boolean;
};

export function AddressSelection({
  address,
  hideMetersInfo,
  isTriggerDisplay
}: AddressSelectionProps) {
  return (
    <div className="flex w-full items-center justify-between gap-2 hover:cursor-pointer">
      <div
        className={cn(
          "text-sm",
          isTriggerDisplay && "min-w-0 overflow-hidden truncate"
        )}
      >
        <span className={cn(isTriggerDisplay ? "" : undefined)}>
          {getAddressDisplayName({
            displayName: address.displayName ?? null,
            postcode: address.postcode ?? ""
          })}
        </span>
      </div>
      <div className="flex">
        {!hideMetersInfo && address.siteMeters && (
          <>
            {address.siteMeters[1] > 0 && <Lightbulb className="h-4 w-4" />}
            {address.siteMeters[2] > 0 && <Flame className="h-4 w-4" />}
          </>
        )}
      </div>
    </div>
  );
}
