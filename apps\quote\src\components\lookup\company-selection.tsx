import { getBusinessType } from "@watt/db/src/maps/business-type-map";
import type { BusinessTargetResult } from "@watt/external-apis/src/libs/experian/business-targeter";
import {
  Ban,
  Building2,
  Hash,
  HelpingHand,
  MapPin,
  UserRound
} from "lucide-react";

import { BusinessType } from "@prisma/client";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { Badge } from "@watt/quote/components/ui/badge";

type Company = Pick<
  BusinessTargetResult,
  | "businessType"
  | "commercialName"
  | "businessRef"
  | "postcode"
  | "businessStatus"
>;

type CompanySelectionProps = Company & {
  isTriggerDisplay?: boolean;
};

// Local icon mapping for business types
const businessTypeIconMap = {
  [BusinessType.LTD]: Building2,
  [BusinessType.CHARITY]: HelpingHand,
  [BusinessType.SOLE_TRADER]: UserRound,
  [BusinessType.PLC]: Building2,
  [BusinessType.LLP]: Building2
};

export function CompanySelection({
  isTriggerDisplay,
  ...company
}: CompanySelectionProps) {
  const businessType = getBusinessType(company.businessType);
  const BusinessTypeIcon = businessTypeIconMap[company.businessType];

  return (
    <div className="flex w-full flex-row justify-between gap-2">
      <div
        className={cn(
          "flex flex-row items-center gap-2",
          isTriggerDisplay && "min-w-0 overflow-hidden"
        )}
      >
        <BusinessTypeIcon className="h-4 w-4 flex-shrink-0" />
        <span className={cn("text-sm", isTriggerDisplay && "truncate")}>
          {humanize(company.commercialName)}
        </span>
        <Badge variant="outline" className="gap-1 bg-muted">
          <Hash className="h-3 w-3" /> {company.businessRef}
        </Badge>
        {company.postcode && (
          <Badge variant="outline" className="gap-1 bg-muted">
            <MapPin className="h-3 w-3" /> {company.postcode}
          </Badge>
        )}
      </div>
      {company.businessStatus !== "A" && <Ban className="h-4 w-4" />}
    </div>
  );
}
