import { ChevronsUpDown, Loader2 } from "lucide-react";
import type { PropsWithChildren } from "react";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { But<PERSON> } from "@watt/quote/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandLoading
} from "@watt/quote/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/quote/components/ui/popover";
import type React from "react";

type SelectedResultProps = PropsWithChildren<{
  isLoading?: boolean;
}>;

type LookUpTriggerProps = React.ComponentProps<"button"> &
  PropsWithChildren<{
    fieldValue: string;
    isLoading?: boolean;
  }>;

type LookUpContentProps = React.ComponentProps<"div"> &
  PropsWithChildren<{
    placeholder: string;
    searchInput: string | undefined;
    onSearchInputChange: (value: string) => void;
    isLoading?: boolean;
    container?: HTMLElement | null;
    shouldFilter?: boolean;
  }>;

const SelectedResult = ({ children, isLoading }: SelectedResultProps) => (
  <span className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap">
    {isLoading ? (
      <div className="flex flex-row items-center justify-center gap-2 text-sm">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Loading...</span>
      </div>
    ) : (
      children
    )}
  </span>
);

const LookUpTrigger: React.FC<LookUpTriggerProps> = ({
  ref,
  className,
  children,
  fieldValue,
  isLoading,
  disabled
}) => (
  <PopoverTrigger asChild>
    <Button
      variant="outline"
      className={cn(
        "w-full justify-between",
        !fieldValue && "text-muted-foreground",
        className
      )}
      ref={ref}
      disabled={disabled}
    >
      <SelectedResult isLoading={isLoading}>{children}</SelectedResult>
      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
    </Button>
  </PopoverTrigger>
);
LookUpTrigger.displayName = "LookUpTrigger";

const LookUpContent: React.FC<LookUpContentProps> = ({
  ref,
  className,
  placeholder,
  searchInput,
  onSearchInputChange,
  isLoading,
  children,
  container,
  shouldFilter = false
}) => (
  <PopoverContent
    ref={ref}
    className={cn("w-[var(--radix-popover-trigger-width)] p-0", className)}
    container={container}
  >
    <Command shouldFilter={shouldFilter}>
      <CommandInput
        value={searchInput ?? ""}
        onValueChange={onSearchInputChange}
        placeholder={placeholder}
      />
      {isLoading ? (
        <CommandLoading>
          <div className="flex flex-row items-center justify-center gap-2 bg-muted py-4 text-sm">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading...</span>
          </div>
        </CommandLoading>
      ) : (
        <CommandEmpty>
          {searchInput ? "No results found." : "Please enter a valid input."}
        </CommandEmpty>
      )}
      <CommandList className="max-h-[400px] overflow-y-auto">
        {children}
      </CommandList>
    </Command>
  </PopoverContent>
);
LookUpContent.displayName = "LookUpContent";

const LookUp = Popover;
const LookUpGroup = CommandGroup;
const LookUpItem = CommandItem;

export { AddressSelection } from "./address-selection";
export { CompanySelection } from "./company-selection";
export { LookUp, LookUpContent, LookUpGroup, LookUpItem, LookUpTrigger };
