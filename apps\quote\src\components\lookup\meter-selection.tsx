import { UtilityType } from "@prisma/client";
import type { Address_Find_Many } from "@watt/api/src/router/address";
import { Flame, Lightbulb } from "lucide-react";

type MeterNumber = NonNullable<Address_Find_Many[0]["meterNumbers"]>[0];

export function MeterSelection(meter: MeterNumber) {
  return (
    <div className="flex w-full items-center justify-between gap-2 hover:cursor-pointer">
      <div className="text-xs">{meter.meterNumber}</div>
      <div className="flex">
        {meter.utilityType === UtilityType.ELECTRICITY ? (
          <Lightbulb className="h-4 w-4" />
        ) : (
          <Flame className="h-4 w-4" />
        )}
      </div>
    </div>
  );
}
