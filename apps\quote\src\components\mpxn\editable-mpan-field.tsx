"use client";

import {
  isShortMpan,
  parseMpan,
  parseShortMpan
} from "@watt/common/src/mpan/mpan";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { MPANInput } from "./mpan-input";

type EditableMPANFieldProps = {
  value?: string;
  onChange: (mpan: string | null) => void;
};

export function EditableMPANField({ value, onChange }: EditableMPANFieldProps) {
  const [values, setValues] = useState({
    profileClass: "",
    meterTimeSwitchCode: "",
    lineLossFactorClass: "",
    distributor: "",
    uniqueIdLeft: "",
    uniqueIdRight: "",
    checksum: ""
  });

  const inputRefs = {
    profileClass: useRef<HTMLInputElement>(null),
    meterTimeSwitchCode: useRef<HTMLInputElement>(null),
    lineLossFactorClass: useRef<HTMLInputElement>(null),
    distributor: useRef<HTMLInputElement>(null),
    uniqueIdLeft: useRef<HTMLInputElement>(null),
    uniqueIdRight: useRef<HTMLInputElement>(null),
    checksum: useRef<HTMLInputElement>(null)
  };

  const inputOrder = [
    "profileClass",
    "meterTimeSwitchCode",
    "lineLossFactorClass",
    "distributor",
    "uniqueIdLeft",
    "uniqueIdRight",
    "checksum"
  ] as const;

  const populateFieldsFromValue = useCallback((inputValue: string) => {
    if (!inputValue) {
      // Clear all fields if no value
      setValues({
        profileClass: "",
        meterTimeSwitchCode: "",
        lineLossFactorClass: "",
        distributor: "",
        uniqueIdLeft: "",
        uniqueIdRight: "",
        checksum: ""
      });
      return;
    }

    // Remove any spaces from the input value
    const cleanValue = inputValue.replace(/\s/g, "");

    // First, try to parse as a complete MPAN (either short or long format)
    const parsedMpan = parseMpan(cleanValue);

    if (!parsedMpan.error && parsedMpan.data) {
      // We have a valid MPAN, distribute it across the fields
      const mpanData = parsedMpan.data;

      // For long MPAN format
      if ("profileClass" in mpanData) {
        // This is a long MPAN format
        setValues({
          profileClass: mpanData.profileClass,
          meterTimeSwitchCode: mpanData.meterTimeSwitchCode,
          lineLossFactorClass: mpanData.lineLossFactorClass,
          distributor: mpanData.distributor,
          uniqueIdLeft: mpanData.uniqueIdentifier.slice(0, 4),
          uniqueIdRight: mpanData.uniqueIdentifier.slice(4, 8),
          checksum: mpanData.checksum
        });
        return;
      }

      // This is a short MPAN format
      setValues({
        profileClass: "",
        meterTimeSwitchCode: "",
        lineLossFactorClass: "",
        distributor: mpanData.distributor,
        uniqueIdLeft: mpanData.uniqueIdentifier.slice(0, 4),
        uniqueIdRight: mpanData.uniqueIdentifier.slice(4, 8),
        checksum: mpanData.checksum
      });
      return;
    }

    // If not a valid full MPAN, check if it's a bottom line (13 digits)
    if (isShortMpan(cleanValue)) {
      // This looks like a bottom line MPAN, parse it using the utility function
      const parsedShortMpan = parseShortMpan(cleanValue);

      // If parsing was successful
      if (!parsedShortMpan.error && parsedShortMpan.data) {
        const { distributor, uniqueIdentifier, checksum } =
          parsedShortMpan.data;

        setValues({
          profileClass: "",
          meterTimeSwitchCode: "",
          lineLossFactorClass: "",
          distributor,
          uniqueIdLeft: uniqueIdentifier.slice(0, 4),
          uniqueIdRight: uniqueIdentifier.slice(4, 8),
          checksum
        });
        return;
      }
    }

    // If we get here, it's not a recognized MPAN format
    // Clear all fields as the value is not parseable
    setValues({
      profileClass: "",
      meterTimeSwitchCode: "",
      lineLossFactorClass: "",
      distributor: "",
      uniqueIdLeft: "",
      uniqueIdRight: "",
      checksum: ""
    });
  }, []);

  const handleInputChange = (field: keyof typeof values, value: string) => {
    setValues(prev => ({ ...prev, [field]: value }));

    const currentIndex = inputOrder.indexOf(field);
    const nextField = inputOrder[currentIndex + 1];

    if (nextField && value.length === inputRefs[field].current?.maxLength) {
      inputRefs[nextField].current?.focus();
    }
  };

  const handleKeyDown = (
    field: keyof typeof values,
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // If backspace is pressed and the field is empty
    if (event.key === "Backspace" && values[field] === "") {
      const currentIndex = inputOrder.indexOf(field);
      const prevField = inputOrder[currentIndex - 1];

      // If there's a previous field, focus on it
      if (prevField) {
        event.preventDefault();
        inputRefs[prevField].current?.focus();
      }
    }
  };

  const handlePaste = (
    field: keyof typeof values,
    event: React.ClipboardEvent<HTMLInputElement>
  ) => {
    // Prevent the default paste behavior
    event.preventDefault();

    // Get the pasted text and remove any spaces
    const pastedText = event.clipboardData.getData("text").replace(/\s/g, "");

    // First, try to parse as a complete MPAN (either short or long format)
    const parsedMpan = parseMpan(pastedText);

    if (!parsedMpan.error && parsedMpan.data) {
      // We have a valid MPAN, distribute it across the fields
      const mpanData = parsedMpan.data;

      // For long MPAN format
      if ("profileClass" in mpanData) {
        // This is a long MPAN format
        setValues({
          profileClass: mpanData.profileClass,
          meterTimeSwitchCode: mpanData.meterTimeSwitchCode,
          lineLossFactorClass: mpanData.lineLossFactorClass,
          distributor: mpanData.distributor,
          uniqueIdLeft: mpanData.uniqueIdentifier.slice(0, 4),
          uniqueIdRight: mpanData.uniqueIdentifier.slice(4, 8),
          checksum: mpanData.checksum
        });

        // Focus on the last field
        inputRefs.checksum.current?.focus();
        return;
      }

      // This is a short MPAN format
      setValues({
        profileClass: "",
        meterTimeSwitchCode: "",
        lineLossFactorClass: "",
        distributor: mpanData.distributor,
        uniqueIdLeft: mpanData.uniqueIdentifier.slice(0, 4),
        uniqueIdRight: mpanData.uniqueIdentifier.slice(4, 8),
        checksum: mpanData.checksum
      });

      // Focus on the last field
      inputRefs.checksum.current?.focus();
      return;
    }

    // If not a valid full MPAN, check if it's a bottom line (13 digits)
    if (isShortMpan(pastedText)) {
      // This looks like a bottom line MPAN, parse it using the utility function
      const parsedShortMpan = parseShortMpan(pastedText);

      // If parsing was successful
      if (!parsedShortMpan.error && parsedShortMpan.data) {
        const { distributor, uniqueIdentifier, checksum } =
          parsedShortMpan.data;

        // Update the values starting from the current field
        if (
          field === "distributor" ||
          field === "uniqueIdLeft" ||
          field === "uniqueIdRight" ||
          field === "checksum"
        ) {
          setValues(prev => ({
            ...prev,
            distributor:
              field === "distributor" ? distributor : prev.distributor,
            uniqueIdLeft:
              field === "distributor" || field === "uniqueIdLeft"
                ? uniqueIdentifier.slice(0, 4)
                : prev.uniqueIdLeft,
            uniqueIdRight:
              field === "distributor" ||
              field === "uniqueIdLeft" ||
              field === "uniqueIdRight"
                ? uniqueIdentifier.slice(4, 8)
                : prev.uniqueIdRight,
            checksum:
              field === "distributor" ||
              field === "uniqueIdLeft" ||
              field === "uniqueIdRight" ||
              field === "checksum"
                ? checksum
                : prev.checksum
          }));

          // Focus on the last field
          inputRefs.checksum.current?.focus();
          return;
        }
      }
    }

    // If we get here, it's not a recognized MPAN format
    // Try to intelligently fill in fields based on the current field and pasted text length
    const currentIndex = inputOrder.indexOf(field);

    // If we're pasting into a field and the text is longer than the field's max length,
    // try to distribute it across this and subsequent fields
    const maxLength = inputRefs[field].current?.maxLength || 0;
    if (pastedText.length > maxLength) {
      let remainingText = pastedText;
      let currentFieldIndex = currentIndex;

      // Distribute the pasted text across fields
      while (
        remainingText.length > 0 &&
        currentFieldIndex < inputOrder.length
      ) {
        const currentField = inputOrder[currentFieldIndex];
        if (!currentField) {
          break; // Safety check
        }

        const currentMaxLength =
          inputRefs[currentField].current?.maxLength || 0;
        const valueForThisField = remainingText.slice(0, currentMaxLength);

        setValues(prev => ({
          ...prev,
          [currentField]: valueForThisField
        }));

        remainingText = remainingText.slice(valueForThisField.length);
        currentFieldIndex++;
      }

      // Focus the next empty field or the last field if all are filled
      if (currentFieldIndex < inputOrder.length) {
        const nextField = inputOrder[currentFieldIndex];
        if (nextField) {
          inputRefs[nextField].current?.focus();
        }
      } else {
        const lastField = inputOrder[inputOrder.length - 1];
        if (lastField) {
          inputRefs[lastField].current?.focus();
        }
      }
    } else {
      // Just paste into the current field
      handleInputChange(field, pastedText);
    }
  };

  const composedMPAN = useMemo(() => {
    return `${values.distributor}${values.uniqueIdLeft}${values.uniqueIdRight}${values.checksum}`;
  }, [values]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (composedMPAN.length === 0) {
      onChange(null);
      return;
    }

    onChange(composedMPAN);
  }, [composedMPAN]);

  // Handle value prop changes
  useEffect(() => {
    if (value !== undefined) {
      populateFieldsFromValue(value);
    }
  }, [value]);

  useEffect(() => {
    // Focus the input when the component mounts
    inputRefs.profileClass.current?.focus();
  }, [inputRefs.profileClass.current?.focus]);

  return (
    <div
      className={
        "flex items-center gap-4 rounded-md border bg-background px-4 py-3 shadow-sm sm:gap-6 sm:px-6 sm:py-4"
      }
    >
      <div className="flex shrink-0 items-center justify-center py-3 sm:py-4">
        <span className="font-bold text-4xl leading-none sm:text-5xl">S</span>
      </div>
      <div className="relative flex h-full grow flex-col justify-between gap-1 sm:gap-2">
        <div className="flex items-center justify-between gap-1 sm:gap-2">
          <MPANInput
            ref={inputRefs.profileClass}
            value={values.profileClass}
            onChange={v => handleInputChange("profileClass", v)}
            onKeyDown={e => handleKeyDown("profileClass", e)}
            onPaste={e => handlePaste("profileClass", e)}
            minLength={2}
            maxLength={2}
            isEditable
          />
          <MPANInput
            ref={inputRefs.meterTimeSwitchCode}
            value={values.meterTimeSwitchCode}
            onChange={v => handleInputChange("meterTimeSwitchCode", v)}
            onKeyDown={e => handleKeyDown("meterTimeSwitchCode", e)}
            onPaste={e => handlePaste("meterTimeSwitchCode", e)}
            minLength={3}
            maxLength={3}
            isEditable
          />
          <MPANInput
            ref={inputRefs.lineLossFactorClass}
            value={values.lineLossFactorClass}
            onChange={v => handleInputChange("lineLossFactorClass", v)}
            onKeyDown={e => handleKeyDown("lineLossFactorClass", e)}
            onPaste={e => handlePaste("lineLossFactorClass", e)}
            minLength={3}
            maxLength={3}
            isEditable
          />
        </div>
        <div className="flex items-center justify-between gap-1 sm:gap-2">
          <MPANInput
            ref={inputRefs.distributor}
            value={values.distributor}
            onChange={v => handleInputChange("distributor", v)}
            onKeyDown={e => handleKeyDown("distributor", e)}
            onPaste={e => handlePaste("distributor", e)}
            minLength={2}
            maxLength={2}
            isEditable
          />
          <MPANInput
            ref={inputRefs.uniqueIdLeft}
            value={values.uniqueIdLeft}
            onChange={v => handleInputChange("uniqueIdLeft", v)}
            onKeyDown={e => handleKeyDown("uniqueIdLeft", e)}
            onPaste={e => handlePaste("uniqueIdLeft", e)}
            minLength={4}
            maxLength={4}
            isEditable
          />
          <MPANInput
            ref={inputRefs.uniqueIdRight}
            value={values.uniqueIdRight}
            onChange={v => handleInputChange("uniqueIdRight", v)}
            onKeyDown={e => handleKeyDown("uniqueIdRight", e)}
            onPaste={e => handlePaste("uniqueIdRight", e)}
            minLength={4}
            maxLength={4}
            isEditable
          />
          <MPANInput
            ref={inputRefs.checksum}
            value={values.checksum}
            onChange={v => handleInputChange("checksum", v)}
            onKeyDown={e => handleKeyDown("checksum", e)}
            onPaste={e => handlePaste("checksum", e)}
            minLength={3}
            maxLength={3}
            isEditable
          />
        </div>
      </div>
    </div>
  );
}
