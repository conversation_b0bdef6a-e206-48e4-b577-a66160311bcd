"use client";

import { useEffect, useRef } from "react";
import { MPRNInput } from "./mprn-input";

type EditableMPRNFieldProps = {
  value: string;
  onChange: (mprn: string | null) => void;
};

export function EditableMPRNField({ value, onChange }: EditableMPRNFieldProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Focus the input when the component mounts
    inputRef.current?.focus();
  }, []);

  return (
    <div
      className={
        "flex items-center gap-4 rounded-md border bg-background px-4 py-3 shadow-sm sm:gap-6 sm:px-6 sm:py-4"
      }
    >
      <div className="flex shrink-0 items-center justify-center py-3 sm:py-4">
        <span className="font-bold text-4xl leading-none sm:text-5xl">M</span>
      </div>
      <MPRNInput ref={inputRef} value={value} onChange={onChange} isEditable />
    </div>
  );
}
