"use client";

import { type MPANLong, parseMpan } from "@watt/common/src/mpan/mpan";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { MPANInput } from "./mpan-input";

type MPANFieldProps = {
  mpan: string;
  onSelect: (mpan: string) => void;
  isSelected: boolean;
};

export function MPANField({ mpan, onSelect, isSelected }: MPANFieldProps) {
  const parsedMpan = parseMpan(mpan);

  const { data: unkData, error } = parsedMpan;

  const {
    profileClass = "**",
    meterTimeSwitchCode = "***",
    lineLossFactorClass = "***",
    distributor = "**",
    uniqueIdentifier = "********",
    checksum = "***"
  } = unkData as MPANLong;

  const idPart1 = uniqueIdentifier.slice(0, 4);
  const idPart2 = uniqueIdentifier.slice(4, 8);

  return (
    <div className="space-y-2">
      <button
        type="button"
        onClick={() => onSelect(mpan)}
        className={cn(
          "flex items-center gap-4 rounded-md border bg-background px-4 py-3 shadow-sm sm:gap-6 sm:px-6 sm:py-4",
          isSelected && "border-secondary bg-secondary/10"
        )}
      >
        <div className="flex shrink-0 items-center justify-center py-3 sm:py-4">
          <span className="font-bold text-4xl leading-none sm:text-5xl">S</span>
        </div>
        <div className="relative flex h-full grow flex-col justify-between gap-1 sm:gap-2">
          <div className="flex items-center justify-between gap-1 sm:gap-2">
            <MPANInput
              value={profileClass}
              isSelected={isSelected}
              minLength={2}
              maxLength={2}
            />
            <MPANInput
              value={meterTimeSwitchCode}
              isSelected={isSelected}
              minLength={3}
              maxLength={3}
            />
            <MPANInput
              value={lineLossFactorClass}
              isSelected={isSelected}
              minLength={3}
              maxLength={3}
            />
          </div>
          <div className="flex items-center justify-between gap-1 sm:gap-2">
            <MPANInput
              value={distributor}
              isSelected={isSelected}
              minLength={2}
              maxLength={2}
            />
            <MPANInput
              value={idPart1}
              isSelected={isSelected}
              minLength={4}
              maxLength={4}
            />
            <MPANInput
              value={idPart2}
              isSelected={isSelected}
              minLength={4}
              maxLength={4}
            />
            <MPANInput
              value={checksum}
              isSelected={isSelected}
              minLength={3}
              maxLength={3}
            />
          </div>
        </div>
      </button>
      {error && (
        <p className="font-medium text-destructive text-sm">{error.message}</p>
      )}
    </div>
  );
}
