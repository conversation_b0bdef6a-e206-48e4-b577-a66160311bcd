"use client";

import { parseMprn } from "@watt/common/src/mprn/mprn";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { MPRNInput } from "./mprn-input";

type MPRNFieldProps = {
  mprn: string;
  onSelect: (mprn: string) => void;
  isSelected: boolean;
};

export function MPRNField({ mprn, onSelect, isSelected }: MPRNFieldProps) {
  const parsedMprn = parseMprn(mprn);

  const { data, error } = parsedMprn;

  return (
    <div className="space-y-2">
      <button
        type="button"
        onClick={() => onSelect(mprn)}
        className={cn(
          "flex w-full items-center gap-4 rounded-md border bg-background px-4 py-3 shadow-sm sm:gap-6 sm:px-6 sm:py-4",
          isSelected && "border-secondary bg-secondary/10"
        )}
      >
        <div className="flex shrink-0 items-center justify-center py-3 sm:py-4">
          <span className="font-bold text-4xl leading-none sm:text-5xl">M</span>
        </div>

        <MPRNInput value={data || ""} isSelected={isSelected} />
      </button>
      {error && (
        <p className="font-medium text-destructive text-sm">{error.message}</p>
      )}
    </div>
  );
}
