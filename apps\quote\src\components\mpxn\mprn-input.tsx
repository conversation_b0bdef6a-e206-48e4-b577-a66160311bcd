"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

type MPRNInputProps = Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  "onChange" | "value"
> & {
  value: string;
  isSelected?: boolean;
  isEditable?: boolean;
  onChange?: (value: string) => void;
  ref?: React.Ref<HTMLInputElement>;
};

export function MPRNInput({
  value,
  isSelected = false,
  isEditable = false,
  onChange,
  className,
  ref,
  ...props
}: MPRNInputProps) {
  return (
    <input
      {...props}
      ref={ref}
      readOnly={!isEditable}
      disabled={!isEditable}
      value={value}
      onChange={e => onChange?.(e.target.value)}
      className={cn(
        "w-full rounded-md border-2 bg-primary/5 px-0.5 py-1 text-center font-bold placeholder-primary/60 focus-visible:outline-none sm:px-1",
        isSelected && "border-primary/5",
        !isEditable && "pointer-events-none text-muted-foreground",
        className
      )}
    />
  );
}
