export function QuoteRateItem({
  label,
  value,
  unit,
  formatter = (val: number) => val.toFixed(2),
  variant = "default",
  labelWidth,
  showPlaceholder = false
}: {
  label: string;
  value: number | undefined | string;
  unit?: string;
  formatter?: (value: number) => string;
  variant?: "default" | "paragraph";
  labelWidth?: string;
  showPlaceholder?: boolean;
}) {
  if (value === undefined || value === null) {
    if (!showPlaceholder) {
      return null;
    }

    // Return placeholder content to maintain layout
    if (variant === "paragraph") {
      return (
        <div className="flex items-center">
          <span
            className={`whitespace-nowrap text-muted-foreground ${labelWidth || ""} flex-shrink-0`}
          >
            {label}:
          </span>
          <span className="ml-2 font-medium text-muted-foreground/50">-</span>
        </div>
      );
    }

    return (
      <div className="flex justify-between">
        <span
          className={`text-muted-foreground ${labelWidth || ""} flex-shrink-0`}
        >
          {label}
        </span>
        <span className="ml-2 font-medium text-muted-foreground/50">-</span>
      </div>
    );
  }

  if (variant === "paragraph") {
    return (
      <div className="flex items-center">
        <span
          className={`whitespace-nowrap text-muted-foreground ${labelWidth || ""} flex-shrink-0`}
        >
          {label}:
        </span>
        <span className="ml-2 font-medium">
          {typeof value === "number" ? formatter(value) : value}
          {unit && <span className="ml-1 text-muted-foreground">{unit}</span>}
        </span>
      </div>
    );
  }

  return (
    <div className="flex justify-between">
      <span
        className={`text-muted-foreground ${labelWidth || ""} flex-shrink-0`}
      >
        {label}
      </span>
      <span className="ml-2 font-medium">
        {typeof value === "number" ? formatter(value) : value}
        {unit && <span className="ml-1 text-muted-foreground">{unit}</span>}
      </span>
    </div>
  );
}
