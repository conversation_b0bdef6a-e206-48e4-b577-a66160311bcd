"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardActionsProps = HTMLAttributes<HTMLDivElement>;

export function QuoteWizardActions({
  children,
  className,
  ...props
}: QuoteWizardActionsProps) {
  return (
    <div
      {...props}
      className="fixed right-0 bottom-0 left-0 z-10 flex h-[4.5rem] w-full shrink-0 items-center border-t bg-background px-6"
    >
      <div
        className={cn(
          "container mx-auto flex w-full items-center justify-end gap-4",
          className
        )}
      >
        {children}
      </div>
    </div>
  );
}
