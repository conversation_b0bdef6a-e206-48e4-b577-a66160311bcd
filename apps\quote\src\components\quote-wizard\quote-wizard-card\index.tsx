"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardCardProps = HTMLAttributes<HTMLDivElement>;

export function QuoteWizardCard({
  children,
  className,
  ...props
}: QuoteWizardCardProps) {
  return (
    <div
      {...props}
      className={cn(
        "flex flex-col gap-12 rounded-lg border bg-white p-6 shadow-sm",
        className
      )}
    >
      {children}
    </div>
  );
}
