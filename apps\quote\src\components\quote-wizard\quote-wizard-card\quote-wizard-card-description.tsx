"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardCardDescriptionProps = HTMLAttributes<HTMLSpanElement>;

export function QuoteWizardCardDescription({
  children,
  className,
  ...props
}: QuoteWizardCardDescriptionProps) {
  return (
    <span {...props} className={cn("text-muted-foreground text-sm", className)}>
      {children}
    </span>
  );
}
