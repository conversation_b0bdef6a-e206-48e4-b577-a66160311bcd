"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardCardHeaderProps = HTMLAttributes<HTMLDivElement>;

export function QuoteWizardCardHeader({
  children,
  className,
  ...props
}: QuoteWizardCardHeaderProps) {
  return (
    <div {...props} className={cn("flex flex-col gap-2", className)}>
      {children}
    </div>
  );
}
