"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardCardItemProps = HTMLAttributes<HTMLDivElement>;

export function QuoteWizardCardItem({
  children,
  className,
  ...props
}: QuoteWizardCardItemProps) {
  return (
    <div {...props} className={cn("flex flex-col gap-6", className)}>
      {children}
    </div>
  );
}
