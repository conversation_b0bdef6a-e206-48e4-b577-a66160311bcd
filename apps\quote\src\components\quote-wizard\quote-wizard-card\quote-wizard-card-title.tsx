"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardCardTitleProps = HTMLAttributes<HTMLSpanElement>;

export function QuoteWizardCardTitle({
  children,
  className,
  ...props
}: QuoteWizardCardTitleProps) {
  return (
    <span
      {...props}
      className={cn("font-semibold text-lg lg:text-xl", className)}
    >
      {children}
    </span>
  );
}
