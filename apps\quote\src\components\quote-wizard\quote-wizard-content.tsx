"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardContentProps = HTMLAttributes<HTMLDivElement>;

export function QuoteWizardContent({
  children,
  className,
  ...props
}: QuoteWizardContentProps) {
  return (
    <div
      {...props}
      className={cn(
        "container mx-auto w-full gap-12 px-6 py-8 pb-[5.5rem] lg:gap-16 lg:pt-10 lg:pb-[5.5rem]",
        className
      )}
    >
      {children}
    </div>
  );
}
