"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardDescriptionProps = HTMLAttributes<HTMLSpanElement>;

export function QuoteWizardDescription({
  children,
  className,
  ...props
}: QuoteWizardDescriptionProps) {
  return (
    <span {...props} className={cn("text-muted-foreground text-sm", className)}>
      {children}
    </span>
  );
}
