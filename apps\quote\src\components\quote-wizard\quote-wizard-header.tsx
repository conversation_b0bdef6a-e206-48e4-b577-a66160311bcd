"use client";

import { WattLogoWithText } from "@watt/common/src/components/svgs/watt-logo-with-text.svg";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { MenuIcon, XIcon } from "lucide-react";
import { Open_Sans } from "next/font/google";
import { useState } from "react";

const openSans = Open_Sans({
  subsets: ["latin"],
  weight: ["500", "600"]
});

/** Navigation configuration constants */
const NAVIGATION_CONFIG = {
  PHONE_NUMBER: "0161 833 8661"
} as const;

/** Main navigation links configuration */
const MAIN_NAV_LINKS = [
  { href: "https://watt-quote.vercel.app/", label: "GET A QUOTE" },
  { href: "https://www.watt.co.uk/loa", label: "LOA" },
  { href: "https://www.watt.co.uk/news", label: "NEWS" },
  { href: "https://www.watt.co.uk/about-us", label: "ABOUT US" },
  { href: "https://www.watt.co.uk/contact", label: "CONTACT" }
] as const;

/** Business benefits dropdown links configuration */
const BUSINESS_BENEFITS_LINKS = [
  {
    href: "https://www.watt.co.uk/large-businesses/",
    label: "Best Utility Contracts"
  },
  {
    href: "https://www.watt.co.uk/large-businesses-our-tailored-services/",
    label: "Our Tailored Services"
  },
  {
    href: "https://www.watt.co.uk/large-businesses-corporate-customers/",
    label: "Corporate Customers"
  },
  {
    href: "https://www.watt.co.uk/pass-through-fixed-contracts/",
    label: "Pass Through & Fixed Contracts"
  },
  { href: "https://www.watt.co.uk/case-studies/", label: "Case Studies" },
  { href: "https://watt-quote.vercel.app/", label: "Enquire Today" }
] as const;

/** CSS class constants */
const STYLES = {
  HOVER_BG: "hover:bg-[#032530]",
  ACTIVE_BG: "bg-[#032530]",
  FOCUS_BG: "focus-visible:!bg-[#032530]",
  TRANSITION: "transition duration-300"
} as const;

/**
 * Renders a navigation link with consistent styling
 */
interface NavLinkProps {
  readonly href: string;
  readonly label: string;
  readonly className?: string;
  readonly target?: string;
  readonly rel?: string;
}

const NavLink = ({
  href,
  label,
  className,
  target = "_blank",
  rel = "noreferrer"
}: NavLinkProps) => (
  <a
    href={href}
    target={target}
    rel={rel}
    className={cn(
      "p-[13px_20px]",
      STYLES.TRANSITION,
      STYLES.HOVER_BG,
      className
    )}
  >
    {label}
  </a>
);

/**
 * Business benefits dropdown menu using CSS hover
 */
const BusinessBenefitsDropdown = () => (
  <div className="group relative">
    <button
      type="button"
      className={cn(
        "flex cursor-default items-center gap-[7px] p-[13px_20px] outline-none",
        STYLES.TRANSITION,
        STYLES.HOVER_BG,
        STYLES.FOCUS_BG,
        "group-hover:bg-[#032530]"
      )}
    >
      <span className="cursor-text select-text">BUSINESS BENEFITS</span>
      <ChevronDownIcon className="-mr-[1px] -mt-[1px] size-[14px] fill-white transition-transform duration-200 group-hover:rotate-180" />
    </button>

    <div
      className={cn(
        "-mt-1 invisible absolute top-full left-0 z-10 w-64 bg-primary opacity-0",
        "transition-all duration-300 ease-in-out",
        "group-hover:visible group-hover:translate-y-0 group-hover:opacity-100",
        "-translate-y-2",
        openSans.className
      )}
    >
      {BUSINESS_BENEFITS_LINKS.map(({ href, label }) => (
        <a
          key={href}
          href={href}
          target="_blank"
          rel="noreferrer"
          className={cn(
            "block p-[7px_28px] font-medium text-[13px] text-primary-foreground",
            STYLES.TRANSITION,
            STYLES.HOVER_BG
          )}
        >
          {label}
        </a>
      ))}
    </div>
  </div>
);

/**
 * Mobile navigation menu with CSS animations
 */
interface MobileNavigationProps {
  readonly isOpen: boolean;
  readonly isSubMenuOpen: boolean;
  readonly onSubMenuToggle: () => void;
}

const MobileNavigation = ({
  isOpen,
  isSubMenuOpen,
  onSubMenuToggle
}: MobileNavigationProps) => (
  <div
    className={cn(
      "grid w-full overflow-hidden transition-all duration-300 ease-in-out lg:hidden",
      isOpen ? "grid-rows-[1fr]" : "grid-rows-[0fr]"
    )}
  >
    <div className="overflow-hidden">
      <div className="mx-auto flex w-full max-w-[1302px] flex-col items-start text-white">
        {MAIN_NAV_LINKS.slice(0, 2).map(({ href, label }) => (
          <a
            key={href}
            href={href}
            target="_blank"
            rel="noreferrer"
            className={cn(
              "!font-sans-default w-full p-[19px_29px] font-[550] text-[13px] tracking-tight",
              STYLES.HOVER_BG
            )}
          >
            {label}
          </a>
        ))}

        <button
          type="button"
          className={cn(
            "!font-sans-default flex w-full items-center gap-1.5 p-[19px_29px] font-[550] text-[13px] tracking-tight",
            STYLES.HOVER_BG,
            isSubMenuOpen && STYLES.ACTIVE_BG
          )}
          onClick={onSubMenuToggle}
        >
          BUSINESS BENEFITS
          <ChevronDownIcon
            className={cn(
              "-mr-[1px] -mt-[1px] size-[14px] fill-white transition-transform duration-200",
              isSubMenuOpen && "rotate-180"
            )}
          />
        </button>

        <div
          className={cn(
            "grid w-full overflow-hidden transition-all duration-300 ease-in-out",
            isSubMenuOpen ? "grid-rows-[1fr]" : "grid-rows-[0fr]"
          )}
        >
          <div className="overflow-hidden">
            {BUSINESS_BENEFITS_LINKS.map(({ href, label }) => (
              <a
                key={href}
                href={href}
                target="_blank"
                rel="noreferrer"
                className={cn(
                  "!font-sans-default block w-full p-[19px_35px] font-medium text-[11.5px] tracking-tight",
                  STYLES.HOVER_BG
                )}
              >
                {label}
              </a>
            ))}
          </div>
        </div>

        {MAIN_NAV_LINKS.slice(2).map(({ href, label }) => (
          <a
            key={href}
            href={href}
            target="_blank"
            rel="noreferrer"
            className={cn(
              "!font-sans-default w-full p-[19px_29px] font-[550] text-[13px] tracking-tight",
              STYLES.HOVER_BG
            )}
          >
            {label}
          </a>
        ))}
      </div>
    </div>
  </div>
);

/**
 * Main header component with responsive navigation
 */
export const QuoteWizardHeader = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSubMenuOpen, setIsSubMenuOpen] = useState(false);

  const toggleMobileMenu = () => setIsMobileMenuOpen(prev => !prev);
  const toggleSubMenu = () => setIsSubMenuOpen(prev => !prev);

  return (
    <nav className="flex flex-col items-center justify-center bg-primary">
      <div className="mx-auto flex h-[81px] w-full max-w-[1302px] items-center justify-between gap-2 px-[15px] sm:gap-0 sm:px-[70px] md:h-[75px] md:px-[20px] lg:h-[81px] lg:justify-start lg:px-0">
        <div className="-mb-1 -ml-0.5 flex w-auto shrink items-center justify-center sm:ml-0 md:mb-0 lg:w-[15.894%]">
          <a href="https://www.watt.co.uk" target="_blank" rel="noreferrer">
            <WattLogoWithText className="h-auto w-[107px] transition duration-300 sm:w-[150px] sm:shrink-0 lg:hover:scale-110" />
          </a>
        </div>

        <div className="hidden w-[71.798%] items-center justify-center font-sans-default font-semibold text-[13px] text-white lg:flex">
          <div className="-ml-[19.5px] -mb-[1px] flex items-center justify-center">
            {MAIN_NAV_LINKS.slice(0, 2).map(({ href, label }) => (
              <NavLink key={href} href={href} label={label} />
            ))}

            <BusinessBenefitsDropdown />

            <NavLink
              href={MAIN_NAV_LINKS[2].href}
              label={MAIN_NAV_LINKS[2].label}
              className="-ml-[2px]"
            />

            {MAIN_NAV_LINKS.slice(3).map(({ href, label }) => (
              <NavLink key={href} href={href} label={label} />
            ))}
          </div>
        </div>

        <div className="-mb-1 -mr-1 flex w-auto shrink-0 items-center justify-center gap-1 sm:gap-6 md:mr-0 md:mb-0 lg:w-[11.972%]">
          <a
            href={`tel:${NAVIGATION_CONFIG.PHONE_NUMBER}`}
            className={cn(
              openSans.className,
              "lg:-ml-[33.5px] whitespace-nowrap pr-1 font-semibold text-[15px] text-white sm:pr-2 sm:text-[18px] lg:text-[15px]"
            )}
          >
            {NAVIGATION_CONFIG.PHONE_NUMBER}
          </a>

          <button
            type="button"
            className="mr-3 flex size-[32px] shrink-0 items-center justify-center rounded-[3px] bg-[#063044] text-white sm:mr-1 lg:hidden"
            onClick={toggleMobileMenu}
          >
            {isMobileMenuOpen ? (
              <XIcon strokeWidth={3} />
            ) : (
              <MenuIcon strokeWidth={3} />
            )}
          </button>
        </div>
      </div>

      <MobileNavigation
        isOpen={isMobileMenuOpen}
        isSubMenuOpen={isSubMenuOpen}
        onSubMenuToggle={toggleSubMenu}
      />
    </nav>
  );
};

/**
 * Chevron down icon component
 */
const ChevronDownIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" {...props}>
    <title>Chevron Down</title>
    <path d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z" />
  </svg>
);
