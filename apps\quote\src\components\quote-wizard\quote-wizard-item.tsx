"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardItemProps = HTMLAttributes<HTMLDivElement>;

export function QuoteWizardItem({
  children,
  className,
  ...props
}: QuoteWizardItemProps) {
  return (
    <section {...props} className={cn("flex flex-col gap-6", className)}>
      {children}
    </section>
  );
}
