"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/quote/components/ui/popover";
import { ChevronDownIcon } from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo, useState } from "react";
import { QuoteWizardNavStepIndicator } from "./quote-wizard-nav-step-indicator";

export type QuoteWizardNavStep = {
  id: number;
  label: string;
  href: string;
  isHidden?: boolean;
};

export const QUOTE_WIZARD_NAV_STEPS: QuoteWizardNavStep[] = [
  {
    id: 1,
    label: "Company Information",
    href: "/john/quote-wizard/company-information"
  },
  {
    id: 2,
    label: "Site Address",
    href: "/john/quote-wizard/site-address"
  },
  {
    id: 3,
    label: "Utility Usage",
    href: "/john/quote-wizard/utility-usage"
  },
  {
    id: 4,
    label: "Utility Quotes",
    href: "/john/quote-wizard/utility-quotes"
  },
  {
    id: 5,
    label: "Sign Contract",
    href: "/john/quote-wizard/sign-contract"
  },
  {
    id: 6,
    label: "Contract Signed",
    href: "/john/quote-wizard/contract-signed",
    isHidden: true
  }
] as const;

export function QuoteWizardNav() {
  const pathname = usePathname();

  const [isOpen, setIsOpen] = useState(false);

  const currentStep = useMemo(() => {
    const found = QUOTE_WIZARD_NAV_STEPS.find(step => pathname === step.href);
    if (found) {
      return found;
    }

    const firstStep = QUOTE_WIZARD_NAV_STEPS[0];
    if (!firstStep) {
      throw new Error("No wizard steps defined");
    }
    return firstStep;
  }, [pathname]);

  const navSteps = useMemo(() => {
    return QUOTE_WIZARD_NAV_STEPS.filter(step => !step.isHidden);
  }, []);

  function handleStepClick(step: QuoteWizardNavStep) {
    // Handle here if needed
  }

  if (currentStep.isHidden) {
    return null;
  }

  return (
    <nav className="flex h-14 shrink-0 items-center justify-between border-b bg-background">
      <div className="container mx-auto hidden w-full items-center justify-between gap-8 px-6 lg:flex">
        {navSteps.map(step => {
          const isCurrent = step.id === currentStep.id;
          const isCompleted = step.id <= currentStep.id;

          return (
            <button
              key={step.id}
              type="button"
              onClick={() => handleStepClick(step)}
            >
              <QuoteWizardNavStepIndicator
                step={step}
                isCurrent={isCurrent}
                isCompleted={isCompleted}
              />
            </button>
          );
        })}
      </div>
      <div className="relative flex h-full w-full lg:hidden">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <button
              type="button"
              className="flex h-full w-full items-center justify-between bg-background px-6 text-left outline-none ring-0 hover:bg-muted focus:ring-0 focus-visible:ring-0 active:ring-0"
            >
              <QuoteWizardNavStepIndicator
                step={currentStep}
                isCurrent={true}
                isCompleted={false}
              />
              <ChevronDownIcon
                className={cn("size-5 transition", isOpen && "rotate-180")}
              />
            </button>
          </PopoverTrigger>
          <PopoverContent className="-mt-1 w-[var(--radix-popover-trigger-width)] rounded-none p-0 shadow-sm">
            <div className="divide-y">
              {QUOTE_WIZARD_NAV_STEPS.map(
                step =>
                  step.id !== currentStep.id && (
                    <button
                      key={step.id}
                      type="button"
                      className="flex h-14 w-full items-center px-6 hover:bg-muted"
                      onClick={() => handleStepClick(step)}
                    >
                      <QuoteWizardNavStepIndicator
                        key={step.id}
                        step={step}
                        isCurrent={false}
                        isCompleted={step.id < currentStep.id}
                      />
                    </button>
                  )
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </nav>
  );
}
