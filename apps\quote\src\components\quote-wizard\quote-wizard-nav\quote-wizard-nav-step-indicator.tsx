"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import Link from "next/link";
import type { QuoteWizardNavStep } from ".";

type QuoteWizardNavStepIndicatorProps = {
  step: QuoteWizardNavStep;
  isCurrent: boolean;
  isCompleted: boolean;
};

export function QuoteWizardNavStepIndicator({
  step,
  isCurrent,
  isCompleted
}: QuoteWizardNavStepIndicatorProps) {
  return (
    <Link href={step.href} className="flex items-center gap-2 font-semibold">
      <div
        className={cn(
          "relative flex size-6 shrink-0 items-center justify-center rounded-full border-[1.5px] border-primary transition-colors",
          isCurrent || isCompleted
            ? "bg-primary text-background"
            : "text-primary"
        )}
      >
        <span className="text-sm">{step.id}</span>
      </div>
      <span className="text-primary">{step.label}</span>
    </Link>
  );
}
