"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type { HTMLAttributes } from "react";

type QuoteWizardTitleProps = HTMLAttributes<HTMLSpanElement>;

export function QuoteWizardTitle({
  children,
  className,
  ...props
}: QuoteWizardTitleProps) {
  return (
    <span {...props} className={cn("font-bold text-xl lg:text-2xl", className)}>
      {children}
    </span>
  );
}
