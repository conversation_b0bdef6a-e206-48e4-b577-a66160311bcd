import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";

export type SuffixInputProps = React.ComponentProps<"input"> & {
  suffix: string;
};

const SuffixInput: React.FC<SuffixInputProps> = ({
  ref,
  className,
  type,
  suffix,
  ...props
}) => {
  const localRef = React.useRef<HTMLInputElement>(null);

  return (
    <div className="relative">
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pr-12 text-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground hover:bg-accent focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={localRef}
        {...props}
      />
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
        <div className="pointer-events-none ml-2 flex items-center">
          <span className="pointer-events-none rounded border bg-muted px-1.5 font-medium font-mono text-[10px] opacity-100">
            {suffix}
          </span>
        </div>
      </div>
    </div>
  );
};

SuffixInput.displayName = "SuffixInput";

export { SuffixInput };
