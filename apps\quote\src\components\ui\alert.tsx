import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type VariantProps, cva } from "class-variance-authority";
import type * as React from "react";

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:top-4 [&>svg]:left-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
        warn: "border-badge-orange bg-badge-yellow text-foreground",
        "warn-secondary":
          "border-badge-orange bg-badge-light-yellow text-foreground"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

const Alert: React.FC<
  React.ComponentProps<"div"> & VariantProps<typeof alertVariants>
> = ({ ref, className, variant, ...props }) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
);
Alert.displayName = "Alert";

const AlertTitle: React.FC<React.ComponentProps<"h5">> = ({
  ref,
  className,
  ...props
}) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
);
AlertTitle.displayName = "AlertTitle";

const AlertDescription: React.FC<React.ComponentProps<"div">> = ({
  ref,
  className,
  ...props
}) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
);
AlertDescription.displayName = "AlertDescription";

export { Alert, AlertDescription, AlertTitle };
