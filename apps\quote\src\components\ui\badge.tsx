import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type VariantProps, cva } from "class-variance-authority";
import type * as React from "react";

import { Button } from "./button";

const badgeVariants = cva(
  "group/badge relative inline-flex min-w-max cursor-pointer items-center overflow-hidden rounded-full border font-semibold text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        foreground:
          "border-transparent bg-foreground text-background hover:bg-foreground/80",
        accent:
          "border-transparent bg-accent text-accent-foreground hover:bg-muted-foreground/80 hover:text-background",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        "outline-secondary":
          "border border-input border-secondary bg-background text-secondary transition-all duration-300 hover:border-secondary hover:bg-accent hover:bg-secondary/10 hover:text-accent-foreground hover:text-secondary"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

export interface BadgeProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof badgeVariants> {}

export interface BadgeTextProps extends React.ComponentProps<"span"> {}

export interface BadgeButtonProps extends React.ComponentProps<"button"> {}

const Badge: React.FC<BadgeProps> = ({ className, variant, ...props }) => (
  <div
    className={cn(badgeVariants({ variant }), className, "px-2.5 py-0.5")}
    {...props}
  />
);

const BadgeText: React.FC<BadgeTextProps> = ({ className, ...props }) => (
  <span
    className={cn(
      "relative overflow-hidden truncate whitespace-nowrap transition-all duration-300 ease-in-out group-hover/badge:w-[calc(100%-1rem)]",
      className
    )}
    {...props}
  />
);

const BadgeButton: React.FC<BadgeButtonProps> = ({ className, ...props }) => (
  <Button
    variant="none"
    size="none"
    className={cn(
      "absolute inset-y-0 right-0 flex translate-x-full transform items-center pr-2 transition-all duration-300 ease-in-out group-hover/badge:translate-x-0 group-active/badge:scale-110",
      className
    )}
    {...props}
  />
);

export { Badge, BadgeButton, BadgeText, badgeVariants };
