import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

const Card: React.FC<React.ComponentProps<"div">> = ({
  ref,
  className,
  ...props
}) => (
  <div
    ref={ref}
    className={cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      className
    )}
    {...props}
  />
);
Card.displayName = "Card";

const CardHeader: React.FC<React.ComponentProps<"div">> = ({
  ref,
  className,
  ...props
}) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
);
CardHeader.displayName = "CardHeader";

const CardTitle: React.FC<React.ComponentProps<"h3">> = ({
  ref,
  className,
  ...props
}) => (
  <h3
    ref={ref}
    className={cn(
      "font-semibold text-2xl leading-none tracking-tight",
      className
    )}
    {...props}
  />
);
CardTitle.displayName = "CardTitle";

const CardDescription: React.FC<React.ComponentProps<"p">> = ({
  ref,
  className,
  ...props
}) => (
  <p
    ref={ref}
    className={cn("text-muted-foreground text-sm", className)}
    {...props}
  />
);
CardDescription.displayName = "CardDescription";

const CardContent: React.FC<React.ComponentProps<"div">> = ({
  ref,
  className,
  ...props
}) => <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />;
CardContent.displayName = "CardContent";

const CardFooter: React.FC<React.ComponentProps<"div">> = ({
  ref,
  className,
  ...props
}) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
);
CardFooter.displayName = "CardFooter";

export {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
};
