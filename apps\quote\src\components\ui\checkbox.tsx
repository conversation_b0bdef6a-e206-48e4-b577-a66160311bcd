"use client";

import { Check } from "lucide-react";

import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

const Checkbox: React.FC<
  React.ComponentProps<typeof CheckboxPrimitive.Root> & {
    iconClassName?: string;
  }
> = ({ ref, className, iconClassName, ...props }) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center text-current")}
    >
      <Check className={cn("h-4 w-4", iconClassName)} />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
);
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
