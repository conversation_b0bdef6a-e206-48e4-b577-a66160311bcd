"use client";

import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Button } from "@watt/quote/components/ui/button";
import { Calendar } from "@watt/quote/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@watt/quote/components/ui/popover";

type DatePickerProps = {
  date?: Date;
  setDate: (date?: Date) => void;
} & React.ComponentProps<typeof PopoverContent>;

export const DatePicker: React.FC<DatePickerProps> = ({
  ref,
  date,
  setDate,
  ...props
}) => (
  <Popover {...props}>
    <PopoverTrigger asChild>
      <Button
        variant={"outline"}
        className={cn(
          "w-full justify-start text-left font-normal",
          !date && "text-muted-foreground"
        )}
      >
        <CalendarIcon className="mr-2 h-4 w-4" />
        {date ? format(date, "PPP") : <span>Pick a date</span>}
      </Button>
    </PopoverTrigger>
    <PopoverContent className="w-auto p-0" ref={ref}>
      <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
    </PopoverContent>
  </Popover>
);
