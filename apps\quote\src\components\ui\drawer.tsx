"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type VariantProps, cva } from "class-variance-authority";
import type React from "react";
import { Drawer as DrawerPrimitive } from "vaul";

const Drawer = ({
  shouldScaleBackground = true,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (
  <DrawerPrimitive.Root
    shouldScaleBackground={shouldScaleBackground}
    {...props}
  />
);
Drawer.displayName = "Drawer";

const DrawerTrigger = DrawerPrimitive.Trigger;

const DrawerPortal = DrawerPrimitive.Portal;

const DrawerClose = DrawerPrimitive.Close;

const DrawerOverlay: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Overlay>
> = ({ ref, className, ...props }) => (
  <DrawerPrimitive.Overlay
    ref={ref}
    className={cn("fixed inset-0 z-50", className)}
    {...props}
  />
);
DrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName;

const DrawerContent: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Content>
> = ({ ref, className, children, ...props }) => (
  <DrawerPortal>
    <DrawerOverlay className="bg-black/80" />
    <DrawerPrimitive.Content
      ref={ref}
      className={cn(
        "!select-auto fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background",
        className
      )}
      {...props}
    >
      <div className="mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted" />
      {children}
    </DrawerPrimitive.Content>
  </DrawerPortal>
);
DrawerContent.displayName = "DrawerContent";

const drawerContentVariants = cva(
  "fixed z-50 flex bg-background outline-none",
  {
    variants: {
      variant: {
        default: "right-0 bottom-0 left-0 h-[40%] flex-col rounded-t-[10px]",
        top: "top-0 right-0 left-0 h-[40%] flex-col rounded-b-[10px]",
        left: "top-0 bottom-0 left-0 w-[40%] flex-row rounded-r-[10px]",
        right: "top-0 right-0 bottom-0 w-[40%] flex-row rounded-l-[10px]"
      },
      scroll: {
        true: "p-6",
        false: ""
      }
    },
    defaultVariants: {
      variant: "default",
      scroll: true
    }
  }
);

const scrollBarVariants = cva("rounded-full bg-zinc-300", {
  variants: {
    variant: {
      default: "mx-auto h-1.5 w-12",
      top: "mx-auto h-1.5 w-12",
      left: "my-auto h-12 w-1.5",
      right: "my-auto h-12 w-1.5"
    }
  },
  defaultVariants: {
    variant: "default"
  }
});

const DrawerContentWithDirection: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Content> &
    VariantProps<typeof drawerContentVariants>
> = ({ ref, className, variant, scroll = true, children, ...props }) => (
  <DrawerPortal>
    <DrawerOverlay className="backdrop-blur-sm" />
    <DrawerPrimitive.Content
      ref={ref}
      className={cn(
        "!select-auto",
        drawerContentVariants({ variant, scroll, className })
      )}
      {...props}
    >
      {scroll && (
        <div className={cn(scrollBarVariants({ variant, className }))} />
      )}
      <div className="grid h-full w-full border-secondary border-l-4">
        {children}
      </div>
    </DrawerPrimitive.Content>
  </DrawerPortal>
);
DrawerContentWithDirection.displayName = "DrawerContentWithDirection";

const DrawerHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("grid gap-1.5 p-4 text-center sm:text-left", className)}
    {...props}
  />
);
DrawerHeader.displayName = "DrawerHeader";

const DrawerFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("mt-auto flex flex-col gap-2 p-4", className)}
    {...props}
  />
);
DrawerFooter.displayName = "DrawerFooter";

const DrawerTitle: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Title>
> = ({ ref, className, ...props }) => (
  <DrawerPrimitive.Title
    ref={ref}
    className={cn(
      "font-semibold text-lg leading-none tracking-tight",
      className
    )}
    {...props}
  />
);
DrawerTitle.displayName = DrawerPrimitive.Title.displayName;

const DrawerDescription: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Description>
> = ({ ref, className, ...props }) => (
  <DrawerPrimitive.Description
    ref={ref}
    className={cn("text-muted-foreground text-sm", className)}
    {...props}
  />
);
DrawerDescription.displayName = DrawerPrimitive.Description.displayName;

export {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerContentWithDirection,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerOverlay,
  DrawerPortal,
  DrawerTitle,
  DrawerTrigger
};
