import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage
} from "@watt/quote/components/ui/form";
import { Switch } from "@watt/quote/components/ui/switch";
import type { ControllerRenderProps } from "react-hook-form";
import { Skeleton } from "./skeleton";

type FormSwitchProps = {
  label: string;
  description: string;
  field: ControllerRenderProps; // Use the field directly from react-hook-form
};

export function FormSwitch({ label, description, field }: FormSwitchProps) {
  return (
    <FormItem className="flex flex-col">
      <FormLabel>{label}</FormLabel>
      <div className="flex items-center justify-between gap-2 rounded-md border p-4">
        <div className="space-y-0.5">
          <FormLabel className="text-xs">{label} Toggle</FormLabel>
          <FormDescription className="text-xs">{description}</FormDescription>
        </div>
        <FormControl>
          <Switch
            checked={field.value} // Use field.value for checked state
            onCheckedChange={field.onChange} // Use field.onChange directly
            className="data-[state=checked]:bg-secondary"
          />
        </FormControl>
        <FormMessage />
      </div>
    </FormItem>
  );
}

export function FormSwitchSkeleton() {
  return (
    <div className="flex flex-col space-y-2">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-3 w-48" />
      <div className="flex items-center justify-between gap-2 rounded-md border p-4">
        <Skeleton className="h-6 w-12 rounded-full" />
      </div>
    </div>
  );
}
