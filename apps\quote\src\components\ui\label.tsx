"use client";

import * as LabelPrimitive from "@radix-ui/react-label";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type VariantProps, cva } from "class-variance-authority";
import type React from "react";

const labelVariants = cva(
  "font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
);

const Label: React.FC<
  React.ComponentProps<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
> = ({ ref, className, ...props }) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
);
Label.displayName = LabelPrimitive.Root.displayName;

export { Label };
