"use client";

import { OTPInput as OTPInputComponent, type SlotProps } from "input-otp";
import { useId } from "react";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Label } from "./label";

interface OTPInputProps {
  value: string;
  onChange: (value: string) => void;
  onComplete?: (value: string) => void;
  maxLength?: number;
  label?: string;
  disabled?: boolean;
  className?: string;
}

function Slot(props: SlotProps) {
  return (
    <div
      className={cn(
        "flex h-12 flex-1 items-center justify-center rounded-md border border-input bg-background font-medium text-foreground shadow-xs transition-[color,box-shadow]",
        { "z-10 border-ring ring-[3px] ring-ring/50": props.isActive }
      )}
    >
      {props.char !== null && <div>{props.char}</div>}
    </div>
  );
}

export function OTPInput({
  value,
  onChange,
  onComplete,
  maxLength = 6,
  label,
  disabled = false,
  className
}: OTPInputProps) {
  const id = useId();

  return (
    <div className={cn("space-y-3", className)}>
      {label && <Label htmlFor={id}>{label}</Label>}
      <OTPInputComponent
        id={id}
        value={value}
        onChange={onChange}
        onComplete={onComplete}
        maxLength={maxLength}
        disabled={disabled}
        containerClassName="flex items-center gap-3 has-disabled:opacity-50"
        render={({ slots }) => (
          <div className="flex w-full gap-2">
            {slots.map((slot, idx) => (
              <Slot key={`otp-slot-${slot.char ?? "empty"}-${idx}`} {...slot} />
            ))}
          </div>
        )}
      />
    </div>
  );
}
