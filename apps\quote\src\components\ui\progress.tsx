"use client";

import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

const Progress: React.FC<
  React.ComponentProps<typeof ProgressPrimitive.Root> & {
    indicatorClassName?: string;
  }
> = ({ ref, className, indicatorClassName, value, ...props }) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(
        "h-full w-full flex-1 bg-primary transition-all",
        indicatorClassName
      )}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
);
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
