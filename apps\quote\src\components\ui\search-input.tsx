import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";

export type SearchInputProps = React.ComponentProps<"input">;

const SearchInput: React.FC<SearchInputProps> = ({
  ref,
  className,
  type,
  ...props
}) => {
  const localRef = React.useRef<HTMLInputElement>(null);

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "/") {
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement && activeElement.tagName.toLowerCase() === "input") {
          // If already focused on an input element, do nothing
          return;
        }
        e.preventDefault();
        localRef.current?.focus(); // Focus the search input
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  return (
    <div className="relative">
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pr-6 text-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground hover:bg-accent focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={localRef}
        {...props}
      />
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
        <kbd className="pointer-events-none absolute top-1.5 right-1.5 h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-medium font-mono text-[10px] opacity-100 sm:flex">
          <span className="text-xs">/</span>
        </kbd>
      </div>
    </div>
  );
};
SearchInput.displayName = "Input";

export { SearchInput };
