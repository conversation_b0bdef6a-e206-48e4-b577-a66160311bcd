import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

type TableProps = React.ComponentProps<"table"> & {
  outerClassName?: string;
};

const Table: React.FC<TableProps> = ({
  ref,
  className,
  outerClassName,
  ...props
}) => (
  <div className={cn("w-full overflow-auto", outerClassName)}>
    <table
      ref={ref}
      className={cn("w-full caption-bottom text-sm", className)}
      {...props}
    />
  </div>
);
Table.displayName = "Table";

const TableHeader: React.FC<React.ComponentProps<"thead">> = ({
  ref,
  className,
  ...props
}) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
);
TableHeader.displayName = "TableHeader";

const TableBody: React.FC<React.ComponentProps<"tbody">> = ({
  ref,
  className,
  ...props
}) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
);

const TableFooter: React.FC<React.ComponentProps<"tfoot">> = ({
  ref,
  className,
  ...props
}) => (
  <tfoot
    ref={ref}
    className={cn(
      "bg-primary font-medium text-primary-foreground [&>tr]:last:border-b-0",
      className
    )}
    {...props}
  />
);
TableFooter.displayName = "TableFooter";

const TableRow: React.FC<React.ComponentProps<"tr">> = ({
  ref,
  className,
  ...props
}) => (
  <tr
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
      className
    )}
    {...props}
  />
);
TableRow.displayName = "TableRow";

const TableHead: React.FC<React.ComponentProps<"th">> = ({
  ref,
  className,
  ...props
}) => (
  <th
    ref={ref}
    className={cn(
      "h-12 whitespace-nowrap px-4 py-4 text-left align-top text-muted-foreground [&:has([role=checkbox])]:pr-0",
      className
    )}
    {...props}
  />
);
TableHead.displayName = "TableHead";

type TableLoadingProps = {
  colSpan: number;
  rowClassName?: string;
  cellClassName?: string;
};

const TableLoading: React.FC<TableLoadingProps> = ({
  colSpan,
  rowClassName,
  cellClassName
}) => (
  <TableRow className={cn("border-none", rowClassName)}>
    <TableHead
      colSpan={colSpan}
      className={cn(
        "bg-secondary/10 text-center font-medium text-secondary",
        cellClassName
      )}
    >
      <span>Loading...</span>
    </TableHead>
  </TableRow>
);
TableLoading.displayName = "TableLoading";

const TableCell: React.FC<React.ComponentProps<"td">> = ({
  ref,
  className,
  ...props
}) => (
  <td
    ref={ref}
    className={cn(
      "px-4 py-2 align-middle [&:has([role=checkbox])]:pr-0",
      className
    )}
    {...props}
  />
);
TableCell.displayName = "TableCell";

const TableCaption: React.FC<React.ComponentProps<"caption">> = ({
  ref,
  className,
  ...props
}) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-muted-foreground text-sm", className)}
    {...props}
  />
);
TableCaption.displayName = "TableCaption";

export {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableLoading,
  TableRow
};
