"use client";

import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import type React from "react";

const CustomTabs = TabsPrimitive.Root;

function CustomTabsList({
  ref,
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  return (
    <TabsPrimitive.List
      ref={ref}
      className={cn(
        "inline-flex items-center border-b text-muted-foreground",
        className
      )}
      {...props}
    />
  );
}
CustomTabsList.displayName = TabsPrimitive.List.displayName;

function CustomTabsTrigger({
  ref,
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap border-b-2 px-3 py-1.5 font-semibold ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-green-600",
        "data-[state=inactive]:border-transparent",
        className
      )}
      {...props}
    />
  );
}
CustomTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

function CustomTabsContent({
  ref,
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      ref={ref}
      className={cn(
        "ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        className
      )}
      {...props}
    />
  );
}
CustomTabsContent.displayName = TabsPrimitive.Content.displayName;

export { CustomTabs, CustomTabsContent, CustomTabsList, CustomTabsTrigger };
