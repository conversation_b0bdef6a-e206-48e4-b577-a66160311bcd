"use client";

import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type VariantProps, cva } from "class-variance-authority";
import type React from "react";

const Tabs = TabsPrimitive.Root;

const tabsListVariants = cva("inline-flex items-center text-muted-foreground", {
  variants: {
    variant: {
      default: "h-10 justify-center rounded-md bg-muted p-1",
      outline: "border-b"
    }
  },
  defaultVariants: {
    variant: "default"
  }
});

const TabsList: React.FC<
  React.ComponentProps<typeof TabsPrimitive.List> &
    VariantProps<typeof tabsListVariants>
> = ({ ref, className, variant, ...props }) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(tabsListVariants({ variant }), className)}
    {...props}
  />
);
TabsList.displayName = TabsPrimitive.List.displayName;

const tabTriggerVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "rounded-sm font-medium text-sm data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
        outline:
          "border-b-2 font-semibold data-[state=active]:border-secondary data-[state=inactive]:border-transparent"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

const TabsTrigger: React.FC<
  React.ComponentProps<typeof TabsPrimitive.Trigger> &
    VariantProps<typeof tabTriggerVariants>
> = ({ ref, className, variant, ...props }) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(tabTriggerVariants({ variant }), className)}
    {...props}
  />
);
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const tabsContentVariants = cva(
  "ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
  {
    variants: {
      variant: {
        default: "py-2",
        outline: ""
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

const TabsContent: React.FC<
  React.ComponentProps<typeof TabsPrimitive.Content> &
    VariantProps<typeof tabsContentVariants>
> = ({ ref, className, variant, ...props }) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(tabsContentVariants({ variant }), className)}
    {...props}
  />
);
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsContent, TabsList, TabsTrigger };
