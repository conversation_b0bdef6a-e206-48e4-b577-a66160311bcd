import * as ToastPrimitives from "@radix-ui/react-toast";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { type VariantProps, cva } from "class-variance-authority";
import { Al<PERSON>Triangle, CheckCircle2, Info, X } from "lucide-react";
import type React from "react";

const ToastProvider = ToastPrimitives.Provider;

const ToastViewport: React.FC<
  React.ComponentProps<typeof ToastPrimitives.Viewport>
> = ({ ref, className, ...props }) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:top-auto sm:right-0 sm:bottom-0 sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
);
ToastViewport.displayName = ToastPrimitives.Viewport.displayName;

const toastVariants = cva(
  "group data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-4 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=closed]:animate-out data-[state=open]:animate-in data-[swipe=end]:animate-out data-[swipe=move]:transition-none",
  {
    variants: {
      variant: {
        success: "border-secondary border-t-4 bg-background",
        default: "border bg-background",
        destructive:
          "destructive group border-destructive border-t-4 bg-background"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
type ToastIconsProps = React.ComponentProps<any> & {
  // TODO Stephen: fix type
  variant: "default" | "success" | "destructive" | null | undefined;
};

const ToastIcons: React.FC<ToastIconsProps> = ({ variant, ...props }) => {
  switch (variant) {
    case "success":
      return <CheckCircle2 {...props} />;
    case "destructive":
      return <AlertTriangle {...props} />;
    default:
      return <Info {...props} />;
  }
};

const Toast: React.FC<
  React.ComponentProps<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
> = ({ ref, className, variant, ...props }) => (
  <ToastPrimitives.Root
    ref={ref}
    className={cn(toastVariants({ variant }), className)}
    {...props}
  />
);
Toast.displayName = ToastPrimitives.Root.displayName;

const ToastAction: React.FC<
  React.ComponentProps<typeof ToastPrimitives.Action>
> = ({ ref, className, ...props }) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 font-medium text-sm ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:focus:ring-destructive group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground",
      className
    )}
    {...props}
  />
);
ToastAction.displayName = ToastPrimitives.Action.displayName;

const ToastClose: React.FC<
  React.ComponentProps<typeof ToastPrimitives.Close>
> = ({ ref, className, ...props }) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute top-3 right-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600 group-[.destructive]:hover:text-red-50",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
);
ToastClose.displayName = ToastPrimitives.Close.displayName;

const ToastTitle: React.FC<
  React.ComponentProps<typeof ToastPrimitives.Title> &
    VariantProps<typeof toastVariants>
> = ({ ref, className, children, variant, ...props }) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("flex items-center gap-2 font-semibold text-sm", className)}
    {...props}
  >
    <ToastIcons variant={variant} className="h-4 w-4" />
    {children}
  </ToastPrimitives.Title>
);
ToastTitle.displayName = ToastPrimitives.Title.displayName;

const ToastDescription: React.FC<
  React.ComponentProps<typeof ToastPrimitives.Description>
> = ({ ref, className, ...props }) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90", className)}
    {...props}
  />
);
ToastDescription.displayName = ToastPrimitives.Description.displayName;

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;

type ToastActionElement = React.ReactElement<typeof ToastAction>;

export {
  Toast,
  ToastAction,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
  type ToastActionElement,
  type ToastProps
};
