"use client";

import type { UtilityType } from "@prisma/client";
import {
  UTILITY_METADATA,
  type UtilityMetadata
} from "@watt/common/src/constants/utility-icons";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Label } from "@watt/quote/components/ui/label";
import { useState } from "react";

// Filter to only show the utilities we want in the quote app
export const utilities = UTILITY_METADATA.filter(
  utility =>
    utility.id === "ELECTRICITY" ||
    utility.id === "GAS" ||
    utility.id === "WATER" ||
    utility.id === "TELECOM" ||
    utility.id === "INTERNET"
);

type UtilitySelectorProps = {
  value?: UtilityType;
  onChange?: (value: UtilityType) => void;
};

export function UtilitySelector({
  value,
  onChange
}: UtilitySelectorProps = {}) {
  const [selectedUtility, setSelectedUtility] = useState<
    UtilityType | undefined
  >(value);

  const handleSelect = (utilityId: UtilityType) => {
    setSelectedUtility(utilityId);
    onChange?.(utilityId);
  };

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-6">
      {utilities.map(utility => {
        const isSelected = selectedUtility === utility.id;

        return (
          <UtilitySelectorImpl
            key={utility.id}
            utility={utility}
            isSelected={isSelected}
            onSelect={handleSelect}
          />
        );
      })}
    </div>
  );
}

type UtilitySelectorImplProps = {
  utility: UtilityMetadata;
  isSelected: boolean;
  onSelect: (id: UtilityType) => void;
};

function UtilitySelectorImpl({
  utility,
  isSelected,
  onSelect
}: UtilitySelectorImplProps) {
  const Icon = utility.icon;

  return (
    <button
      type="button"
      role="radio"
      aria-checked={isSelected}
      onClick={() => !utility.disabled && onSelect(utility.id)}
      className={cn(
        "relative flex w-full flex-col items-start gap-4 rounded-md border p-4 shadow-xs outline-none",
        isSelected ? "border-primary/50" : "border-input",
        utility.disabled
          ? "[&_*]:!cursor-not-allowed cursor-not-allowed opacity-50"
          : "cursor-pointer"
      )}
    >
      <div className="flex w-full justify-between gap-2">
        <Icon
          className="size-4 shrink-0"
          style={{ color: utility.color }}
          aria-hidden="true"
        />
        <div
          className={cn(
            "flex size-4 items-center justify-center rounded-full border border-primary [&_svg]:size-3"
          )}
        >
          {isSelected && (
            <div className="size-3 shrink-0 rounded-full bg-primary" />
          )}
        </div>
      </div>
      <div className="flex flex-col items-start gap-2">
        <Label>{utility.name}</Label>
        <p className="text-muted-foreground text-sm">
          {utility.suppliers ? `${utility.suppliers} suppliers` : "coming soon"}
        </p>
      </div>
    </button>
  );
}
