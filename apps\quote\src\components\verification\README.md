# Verification Component System

A composable, reusable verification input system that supports email, phone, or any other verification flow using OTP (One-Time Password).

## Overview

This component system follows a composition pattern similar to Radix UI, where individual components work together through a shared context. Each component can be styled independently and arranged in any layout.

## Core Components

### `<Verification>`

The root component that provides context and manages state for all child components.

```tsx
<Verification
  value={email}
  onChange={setEmail}
  onSend={handleSendOtp}
  onVerify={handleVerifyOtp}
  isVerified={isEmailVerified}
  onVerifiedChange={setIsEmailVerified}
>
  {/* Child components */}
</Verification>
```

### `<VerificationField>`

Container for the input and trigger button. Only visible when not verified and not showing OTP.

### `<VerificationInput>`

The main input field that syncs with the verification state.

### `<VerificationTrigger>`

The button to initiate verification (send OTP).

### `<VerificationOTPField>`

Container for OTP-related components. Only visible when OTP is sent.

### `<VerificationOTP>`

The OTP input field with automatic verification on completion.

### `<VerificationActions>`

Container for action buttons (cancel, resend).

### `<VerificationCancel>`

Button to cancel OTP entry and return to input state.

### `<VerificationResend>`

Button to resend OTP with countdown timer.

### `<VerificationStatus>`

Container for verified state components. Only visible when verified.

### `<VerificationValue>`

Displays the verified value (read-only input).

### `<VerificationChange>`

Button to change the verified value.

### `<VerificationError>`

Displays error messages from the verification process.

## Integration with company-form.tsx

To integrate this system with the existing company-form.tsx:

```tsx
import {
  Verification,
  VerificationField,
  VerificationInput,
  VerificationTrigger,
  VerificationOTPField,
  VerificationOTP,
  VerificationActions,
  VerificationCancel,
  VerificationResend,
  VerificationStatus,
  VerificationValue,
  VerificationChange
} from "@watt/quote/components/verification";
import { Check as CheckIcon } from "lucide-react";

// In your form field:
<FormField
  control={form.control}
  name="contact.email"
  render={({ field }) => (
    <FormItem>
      <FormLabel className="font-normal text-base text-muted-foreground">
        Email *
      </FormLabel>
      <FormControl>
        <Verification
          value={field.value}
          onChange={(value) => {
            field.onChange(value);
            setQueryParams({
              contactEmail: value,
              emailVerified: isEmailVerified ? "true" : ""
            });
          }}
          onSend={async (email) => {
            const supabase = createClientComponentClient();
            const { error } = await supabase.auth.signInWithOtp({
              email,
              options: {
                shouldCreateUser: true,
                data: { role: "QUOTE_APP" }
              }
            });
            if (error) throw error;
          }}
          onVerify={async (email, code) => {
            const supabase = createClientComponentClient();
            const { data, error } = await supabase.auth.verifyOtp({
              email,
              token: code,
              type: "email"
            });
            if (error) throw error;
            return !!data.session;
          }}
          isVerified={isEmailVerified}
          onVerifiedChange={(verified) => {
            setIsEmailVerified(verified);
            setQueryParams({ emailVerified: verified ? "true" : "" });
          }}
          onError={(error) => {
            toast({
              title: "Verification failed",
              description: error.message,
              variant: "destructive"
            });
          }}
        >
          {/* Input State */}
          <VerificationField>
            <VerificationInput
              placeholder="Enter Email"
              type="email"
              className="h-12 px-4 text-base shadow-sm hover:bg-muted"
            />
            <VerificationTrigger className="h-12">
              Verify
            </VerificationTrigger>
          </VerificationField>

          {/* OTP State */}
          <VerificationOTPField>
            <VerificationOTP />
            <VerificationActions>
              <VerificationCancel />
              <VerificationResend />
            </VerificationActions>
          </VerificationOTPField>

          {/* Verified State */}
          <VerificationStatus>
            <div className="relative">
              <VerificationValue
                className="peer h-12 px-4 pe-9 text-base shadow-sm"
                disabled
              />
              <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-green-600">
                <CheckIcon size={16} aria-hidden="true" />
              </div>
            </div>
            <VerificationChange>Change Email</VerificationChange>
          </VerificationStatus>
        </Verification>
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Key Features

1. **Composable**: Each component can be arranged in any layout
2. **Type-safe**: Full TypeScript support with proper types
3. **Flexible**: Works with any verification type (email, phone, etc.)
4. **Form-compatible**: Integrates seamlessly with react-hook-form
5. **State management**: Handles all verification states automatically
6. **Error handling**: Built-in error display and handling
7. **Accessible**: Proper keyboard navigation and ARIA attributes

## Configuration Options

- `value`: The current value to be verified
- `onChange`: Callback when value changes
- `onSend`: Async function to send OTP
- `onVerify`: Async function to verify OTP (returns boolean)
- `isVerified`: External verified state
- `onVerifiedChange`: Callback when verified state changes
- `onError`: Error handler callback
- `resendDelay`: Delay before allowing resend (default: 10000ms)
- `otpLength`: Expected OTP length (default: 6)
- `disabled`: Disable all interactions

## Benefits over inline implementation

1. **Reusability**: Can be used for any verification flow
2. **Maintainability**: 150+ lines reduced to ~50 lines in usage
3. **Testability**: Each component can be tested independently
4. **Consistency**: Same verification UX across the app
5. **Flexibility**: Easy to customize layout and styling
