import { act, renderHook, waitFor } from "@testing-library/react";
import type React from "react";
import {
  createVerificationConfigTestData,
  createVerificationMocks
} from "../test-utils/builders";
import {
  type VerificationConfig,
  VerificationProvider,
  useVerification
} from "../verification-context";

describe("VerificationContext", () => {
  const createWrapper = (config: VerificationConfig) => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <VerificationProvider config={config}>{children}</VerificationProvider>
    );
    return wrapper;
  };

  // Helper to build config with proper types
  const buildConfig = (
    builder: ReturnType<typeof createVerificationConfigTestData>
  ) => {
    return builder.build() as VerificationConfig;
  };

  describe("Initial State", () => {
    it("should initialize with default values", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      expect(result.current.state).toMatchObject({
        value: "<EMAIL>",
        otpCode: "",
        isVerified: false,
        showOtp: false,
        isSending: false,
        isVerifying: false,
        canResend: false,
        timeUntilResend: 0
      });
    });

    it("should initialize with isVerified when provided", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("isVerified", true)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      expect(result.current.state.isVerified).toBe(true);
    });
  });

  describe("Value Updates", () => {
    it("should update value and call onChange", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      act(() => {
        result.current.actions.updateValue("<EMAIL>");
      });

      expect(result.current.state.value).toBe("<EMAIL>");
      expect(mocks.onChange).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should reset verification status when value changes", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("isVerified", true)
          .set("onVerifiedChange", mocks.onVerifiedChange)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      expect(result.current.state.isVerified).toBe(true);

      act(() => {
        result.current.actions.updateValue("<EMAIL>");
      });

      expect(result.current.state.isVerified).toBe(false);
      expect(mocks.onVerifiedChange).toHaveBeenCalledWith(false);
    });
  });

  describe("Send OTP", () => {
    it("should handle successful OTP send", async () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("resendDelay", 1000)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      await act(async () => {
        await result.current.actions.send();
      });

      expect(mocks.onSend).toHaveBeenCalledWith("<EMAIL>");
      expect(result.current.state.showOtp).toBe(true);
      expect(result.current.state.canResend).toBe(false);
      expect(result.current.state.timeUntilResend).toBe(1);
    });

    it("should handle OTP send failure", async () => {
      const error = new Error("Network error");
      const mocks = createVerificationMocks();
      mocks.onSend.mockRejectedValue(error);
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("onError", mocks.onError)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      await act(async () => {
        await result.current.actions.send();
      });

      expect(result.current.state.error).toBe("Network error");
      expect(result.current.state.showOtp).toBe(false);
      expect(mocks.onError).toHaveBeenCalledWith(error);
    });

    it("should not send if value is empty", async () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      await act(async () => {
        await result.current.actions.send();
      });

      expect(mocks.onSend).not.toHaveBeenCalled();
    });
  });

  describe("Verify OTP", () => {
    it("should handle successful verification", async () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("onVerifiedChange", mocks.onVerifiedChange)
          .set("otpLength", 6)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      // First, set OTP code
      act(() => {
        result.current.actions.updateOtpCode("123456");
      });

      // Then verify
      await act(async () => {
        await result.current.actions.verify();
      });

      expect(mocks.onVerify).toHaveBeenCalledWith("<EMAIL>", "123456");
      expect(result.current.state.isVerified).toBe(true);
      expect(result.current.state.showOtp).toBe(false);
      expect(mocks.onVerifiedChange).toHaveBeenCalledWith(true);
    });

    it("should handle verification failure", async () => {
      const mocks = createVerificationMocks();
      mocks.onVerify.mockResolvedValue(false);
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("onError", mocks.onError)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      act(() => {
        result.current.actions.updateOtpCode("123456");
      });

      await act(async () => {
        await result.current.actions.verify();
      });

      expect(result.current.state.isVerified).toBe(false);
      expect(result.current.state.error).toBe("Verification failed");
    });

    it("should not verify if OTP is wrong length", async () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("otpLength", 6)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      act(() => {
        result.current.actions.updateOtpCode("123"); // Too short
      });

      await act(async () => {
        await result.current.actions.verify();
      });

      expect(mocks.onVerify).not.toHaveBeenCalled();
    });
  });

  describe("Cancel and Change Actions", () => {
    it("should reset OTP state on cancel", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      // Set some state
      act(() => {
        result.current.actions.updateOtpCode("123456");
      });

      // Cancel
      act(() => {
        result.current.actions.cancel();
      });

      expect(result.current.state.showOtp).toBe(false);
      expect(result.current.state.otpCode).toBe("");
      expect(result.current.state.error).toBeUndefined();
    });

    it("should reset verified state on change", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
          .set("isVerified", true)
          .set("onVerifiedChange", mocks.onVerifiedChange)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      act(() => {
        result.current.actions.change();
      });

      expect(result.current.state.isVerified).toBe(false);
      expect(mocks.onVerifiedChange).toHaveBeenCalledWith(false);
    });
  });

  describe("Error Handling", () => {
    it("should clear error", () => {
      const mocks = createVerificationMocks();
      const config = buildConfig(
        createVerificationConfigTestData()
          .set("value", "<EMAIL>")
          .set("onChange", mocks.onChange)
          .set("onSend", mocks.onSend)
          .set("onVerify", mocks.onVerify)
      );

      const { result } = renderHook(() => useVerification(), {
        wrapper: createWrapper(config)
      });

      // Manually set error state (normally would come from failed send/verify)
      act(() => {
        result.current.actions.clearError();
      });

      expect(result.current.state.error).toBeUndefined();
    });
  });

  describe("Hook Usage", () => {
    it("should throw error when used outside provider", () => {
      // Suppress console.error for this test
      const originalError = console.error;
      console.error = jest.fn();

      expect(() => {
        renderHook(() => useVerification());
      }).toThrow("useVerification must be used within a VerificationProvider");

      console.error = originalError;
    });
  });
});
