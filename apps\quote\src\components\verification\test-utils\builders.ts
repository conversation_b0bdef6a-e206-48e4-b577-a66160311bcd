import { createGenericBuilder } from "@watt/common/src/utils/generic-fluent-builder";
import { z } from "zod";

// Schema for verification test data
export const VerificationTestDataSchema = z.object({
  email: z.string().email(),
  phoneNumber: z.string(),
  otpCode: z.string().length(6),
  isVerified: z.boolean(),
  errorMessage: z.string().optional(),
  resendDelay: z.number().optional()
});

export type VerificationTestData = z.infer<typeof VerificationTestDataSchema>;

// Builder for creating test data
export const createVerificationTestData = () =>
  createGenericBuilder(VerificationTestDataSchema);

// Schema for verification config test data
export const VerificationConfigTestSchema = z.object({
  value: z.string(),
  onChange: z.function(),
  onSend: z.function(),
  onVerify: z.function(),
  onError: z.function().optional(),
  onVerifiedChange: z.function().optional(),
  resendDelay: z.number().optional(),
  otpLength: z.number().optional(),
  disabled: z.boolean().optional(),
  isVerified: z.boolean().optional()
});

export type VerificationConfigTest = z.infer<
  typeof VerificationConfigTestSchema
>;

// Builder for creating config test data
export const createVerificationConfigTestData = () =>
  createGenericBuilder(VerificationConfigTestSchema);

// Helper to create properly typed mock functions
export const createVerificationMocks = () => ({
  onChange: jest.fn(),
  onSend: jest.fn().mockResolvedValue(undefined),
  onVerify: jest.fn().mockResolvedValue(true),
  onError: jest.fn(),
  onVerifiedChange: jest.fn()
});
