import { type RenderOptions, render } from "@testing-library/react";
import {
  Verification,
  type VerificationConfig
} from "@watt/quote/components/verification";
import React, { type ReactElement } from "react";

interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  verificationConfig?: Partial<VerificationConfig>;
}

// Default mock functions
const defaultMocks = {
  onChange: jest.fn(),
  onSend: jest.fn().mockResolvedValue(undefined),
  onVerify: jest.fn().mockResolvedValue(true),
  onError: jest.fn(),
  onVerifiedChange: jest.fn()
};

export function renderWithVerification(
  ui: ReactElement,
  options?: CustomRenderOptions
) {
  const { verificationConfig, ...renderOptions } = options || {};

  const defaultConfig: VerificationConfig = {
    value: "",
    onChange: defaultMocks.onChange,
    onSend: defaultMocks.onSend,
    onVerify: defaultMocks.onVerify,
    onError: defaultMocks.onError,
    onVerifiedChange: defaultMocks.onVerifiedChange,
    ...verificationConfig
  };

  const result = render(
    <Verification {...defaultConfig}>{ui}</Verification>,
    renderOptions
  );

  return {
    ...result,
    mockFunctions: {
      onChange: verificationConfig?.onChange || defaultMocks.onChange,
      onSend: verificationConfig?.onSend || defaultMocks.onSend,
      onVerify: verificationConfig?.onVerify || defaultMocks.onVerify,
      onError: verificationConfig?.onError || defaultMocks.onError,
      onVerifiedChange:
        verificationConfig?.onVerifiedChange || defaultMocks.onVerifiedChange
    }
  };
}

// Helper to flush promises when using fake timers
export const flushPromises = () =>
  new Promise(resolve => process.nextTick(resolve));

// Helper to advance timers and flush promises together
export async function advanceTimersAndFlush(ms: number) {
  jest.advanceTimersByTime(ms);
  await flushPromises();
}
