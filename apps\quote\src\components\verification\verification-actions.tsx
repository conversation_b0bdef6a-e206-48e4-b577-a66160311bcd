"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";
import type { ReactNode } from "react";

export interface VerificationActionsProps {
  children: ReactNode;
  className?: string;
  asChild?: boolean;
}

export function VerificationActions({
  children,
  className,
  asChild = false
}: VerificationActionsProps) {
  if (asChild) {
    return <>{children}</>;
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>{children}</div>
  );
}
