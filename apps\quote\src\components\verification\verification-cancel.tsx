"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { But<PERSON> } from "@watt/quote/components/ui/button";
import type React from "react";
import { useVerification } from "./verification-context";

export interface VerificationCancelProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Button>, "onClick"> {
  ref?: React.Ref<HTMLButtonElement>;
}

export function VerificationCancel({
  children = "Cancel",
  className,
  variant = "link",
  size = "sm",
  ref,
  ...props
}: VerificationCancelProps) {
  const { actions } = useVerification();

  return (
    <Button
      ref={ref}
      type="button"
      variant={variant}
      size={size}
      onClick={actions.cancel}
      className={cn("h-auto p-0", className)}
      {...props}
    >
      {children}
    </Button>
  );
}
