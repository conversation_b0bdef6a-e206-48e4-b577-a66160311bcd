"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { But<PERSON> } from "@watt/quote/components/ui/button";
import type React from "react";
import { useVerification } from "./verification-context";

export interface VerificationChangeProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Button>, "onClick"> {
  valueType?: string;
  ref?: React.Ref<HTMLButtonElement>;
}

export function VerificationChange({
  children,
  className,
  variant = "link",
  valueType = "value",
  ref,
  ...props
}: VerificationChangeProps) {
  const { actions } = useVerification();

  const defaultChildren = `Change ${valueType}`;

  return (
    <Button
      ref={ref}
      type="button"
      variant={variant}
      onClick={actions.change}
      className={cn("h-auto p-0 text-sm", className)}
      {...props}
    >
      {children ?? defaultChildren}
    </Button>
  );
}
