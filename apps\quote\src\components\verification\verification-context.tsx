"use client";

import React, {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

export interface VerificationConfig {
  value: string;
  onChange: (value: string) => void;
  onSend: (value: string) => Promise<void>;
  onVerify: (value: string, code: string) => Promise<boolean>;
  onError?: (error: Error) => void;
  resendDelay?: number;
  otpLength?: number;
  disabled?: boolean;
  isVerified?: boolean;
  onVerifiedChange?: (verified: boolean) => void;
}

interface VerificationState {
  value: string;
  otpCode: string;
  isVerified: boolean;
  showOtp: boolean;
  isSending: boolean;
  isVerifying: boolean;
  canResend: boolean;
  timeUntilResend: number;
  error?: string;
}

interface VerificationContextValue {
  state: VerificationState;
  config: VerificationConfig;
  actions: {
    updateValue: (value: string) => void;
    updateOtpCode: (code: string) => void;
    send: () => Promise<void>;
    verify: (code?: string) => Promise<void>;
    cancel: () => void;
    change: () => void;
    clearError: () => void;
    reset: () => void;
  };
}

const VerificationContext = createContext<VerificationContextValue | null>(
  null
);

interface VerificationProviderProps {
  children: ReactNode;
  config: VerificationConfig;
}

export function VerificationProvider({
  children,
  config
}: VerificationProviderProps) {
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null);
  const resendIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const resendDelay = config.resendDelay ?? 10000;

  const [state, setState] = useState<VerificationState>({
    value: config.value,
    otpCode: "",
    isVerified: config.isVerified ?? false,
    showOtp: false,
    isSending: false,
    isVerifying: false,
    canResend: false,
    timeUntilResend: 0,
    error: undefined
  });

  // Sync external value changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      value: config.value,
      isVerified: config.isVerified ?? false
    }));
  }, [config.value, config.isVerified]);

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (resendTimerRef.current) {
        clearTimeout(resendTimerRef.current);
      }
      if (resendIntervalRef.current) {
        clearInterval(resendIntervalRef.current);
      }
    };
  }, []);

  const updateValue = useCallback(
    (value: string) => {
      setState(prev => {
        const newState = { ...prev, value };

        // Reset verification if value changes and was verified
        if (prev.isVerified) {
          newState.isVerified = false;
          newState.showOtp = false;
          newState.otpCode = "";
        }

        return newState;
      });

      config.onChange(value);

      // Call onVerifiedChange after state update if needed
      if (state.isVerified) {
        config.onVerifiedChange?.(false);
      }
    },
    [config, state.isVerified]
  );

  const updateOtpCode = useCallback((otpCode: string) => {
    setState(prev => ({ ...prev, otpCode }));
  }, []);

  const send = useCallback(async () => {
    if (!state.value || state.isSending || config.disabled) {
      return;
    }

    setState(prev => ({ ...prev, isSending: true, error: undefined }));

    try {
      await config.onSend(state.value);

      setState(prev => ({
        ...prev,
        isSending: false,
        showOtp: true,
        canResend: false,
        timeUntilResend: Math.floor(resendDelay / 1000)
      }));

      // Set up resend timer
      if (resendTimerRef.current) {
        clearTimeout(resendTimerRef.current);
      }
      if (resendIntervalRef.current) {
        clearInterval(resendIntervalRef.current);
      }

      // Update countdown every second
      resendIntervalRef.current = setInterval(() => {
        setState(prev => {
          const newTime = Math.max(0, prev.timeUntilResend - 1);
          if (newTime === 0 && resendIntervalRef.current) {
            clearInterval(resendIntervalRef.current);
          }
          return { ...prev, timeUntilResend: newTime };
        });
      }, 1000);

      resendTimerRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, canResend: true }));
      }, resendDelay);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to send verification code";
      setState(prev => ({
        ...prev,
        isSending: false,
        error: errorMessage
      }));
      config.onError?.(
        error instanceof Error ? error : new Error(errorMessage)
      );
    }
  }, [state.value, state.isSending, config.disabled, config, resendDelay]);

  const verify = useCallback(
    async (code?: string) => {
      const otpToVerify = code ?? state.otpCode;
      const expectedLength = config.otpLength ?? 6;

      if (!otpToVerify || otpToVerify.length !== expectedLength) {
        return;
      }

      setState(prev => ({ ...prev, isVerifying: true, error: undefined }));

      try {
        const success = await config.onVerify(state.value, otpToVerify);

        if (success) {
          setState(prev => ({
            ...prev,
            isVerifying: false,
            isVerified: true,
            showOtp: false,
            otpCode: "",
            canResend: false
          }));

          // Clear timers
          if (resendTimerRef.current) {
            clearTimeout(resendTimerRef.current);
          }
          if (resendIntervalRef.current) {
            clearInterval(resendIntervalRef.current);
          }

          config.onVerifiedChange?.(true);
        } else {
          setState(prev => ({
            ...prev,
            isVerifying: false,
            error: "Verification failed",
            otpCode: ""
          }));
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Verification failed";
        setState(prev => ({
          ...prev,
          isVerifying: false,
          error: errorMessage,
          otpCode: ""
        }));
        config.onError?.(
          error instanceof Error ? error : new Error(errorMessage)
        );
      }
    },
    [state.otpCode, state.value, config]
  );

  const cancel = useCallback(() => {
    setState(prev => ({
      ...prev,
      showOtp: false,
      otpCode: "",
      canResend: false,
      timeUntilResend: 0,
      error: undefined
    }));

    // Clear timers
    if (resendTimerRef.current) {
      clearTimeout(resendTimerRef.current);
    }
    if (resendIntervalRef.current) {
      clearInterval(resendIntervalRef.current);
    }
  }, []);

  const change = useCallback(() => {
    setState(prev => ({
      ...prev,
      isVerified: false,
      showOtp: false,
      otpCode: "",
      error: undefined
    }));
    config.onVerifiedChange?.(false);
  }, [config]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: undefined }));
  }, []);

  const reset = useCallback(() => {
    setState({
      value: "",
      otpCode: "",
      isVerified: false,
      showOtp: false,
      isSending: false,
      isVerifying: false,
      canResend: false,
      timeUntilResend: 0,
      error: undefined
    });

    // Clear timers
    if (resendTimerRef.current) {
      clearTimeout(resendTimerRef.current);
    }
    if (resendIntervalRef.current) {
      clearInterval(resendIntervalRef.current);
    }

    config.onChange("");
    config.onVerifiedChange?.(false);
  }, [config]);

  const contextValue = useMemo<VerificationContextValue>(
    () => ({
      state,
      config,
      actions: {
        updateValue,
        updateOtpCode,
        send,
        verify,
        cancel,
        change,
        clearError,
        reset
      }
    }),
    [
      state,
      config,
      updateValue,
      updateOtpCode,
      send,
      verify,
      cancel,
      change,
      clearError,
      reset
    ]
  );

  return (
    <VerificationContext.Provider value={contextValue}>
      {children}
    </VerificationContext.Provider>
  );
}

export function useVerification() {
  const context = useContext(VerificationContext);
  if (!context) {
    throw new Error(
      "useVerification must be used within a VerificationProvider"
    );
  }
  return context;
}
