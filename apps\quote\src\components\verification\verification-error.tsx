"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";
import { useVerification } from "./verification-context";

export interface VerificationErrorProps {
  className?: string;
}

export function VerificationError({ className }: VerificationErrorProps) {
  const { state } = useVerification();

  if (!state.error) {
    return null;
  }

  return (
    <p className={cn("font-medium text-destructive text-sm", className)}>
      {state.error}
    </p>
  );
}
