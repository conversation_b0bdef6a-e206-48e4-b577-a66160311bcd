"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React, { type ReactNode } from "react";
import { useVerification } from "./verification-context";

export interface VerificationFieldProps {
  children: ReactNode;
  className?: string;
  asChild?: boolean;
}

export function VerificationField({
  children,
  className,
  asChild = false
}: VerificationFieldProps) {
  const { state } = useVerification();

  // Only show this component when not showing OTP and not verified
  if (state.showOtp || state.isVerified) {
    return null;
  }

  if (asChild) {
    return <>{children}</>;
  }

  return <div className={cn("flex w-full gap-2", className)}>{children}</div>;
}
