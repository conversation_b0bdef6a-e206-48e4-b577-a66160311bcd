"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Input } from "@watt/quote/components/ui/input";
import type React from "react";
import { forwardRef } from "react";
import { useVerification } from "./verification-context";

export interface VerificationInputProps
  extends Omit<
    React.ComponentPropsWithoutRef<typeof Input>,
    "value" | "onChange" | "ref"
  > {
  onValueChange?: (value: string) => void;
}

export const VerificationInput = forwardRef<
  HTMLInputElement,
  VerificationInputProps
>(({ className, onValueChange, disabled, ...props }, ref) => {
  const { state, config, actions } = useVerification();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    actions.updateValue(value);
    onValueChange?.(value);
    // Clear any existing errors when user types
    if (state.error) {
      actions.clearError();
    }
  };

  return (
    <Input
      ref={ref}
      value={state.value}
      onChange={handleChange}
      disabled={disabled || config.disabled || state.isSending}
      className={cn(
        "flex-1",
        !state.value && "text-muted-foreground",
        className
      )}
      {...props}
    />
  );
});

VerificationInput.displayName = "VerificationInput";
