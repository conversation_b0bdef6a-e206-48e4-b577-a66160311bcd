"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";
import type { ReactNode } from "react";
import { useVerification } from "./verification-context";

export interface VerificationOTPFieldProps {
  children: ReactNode;
  className?: string;
  asChild?: boolean;
}

export function VerificationOTPField({
  children,
  className,
  asChild = false
}: VerificationOTPFieldProps) {
  const { state } = useVerification();

  // Only show this component when showing OTP and not verified
  if (!state.showOtp || state.isVerified) {
    return null;
  }

  if (asChild) {
    return <>{children}</>;
  }

  return <div className={cn("space-y-3", className)}>{children}</div>;
}
