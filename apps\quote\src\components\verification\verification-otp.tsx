"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { OTPInput } from "@watt/quote/components/ui/otp-input";
import type React from "react";
import { useVerification } from "./verification-context";

export interface VerificationOTPProps {
  className?: string;
  onComplete?: (code: string) => void;
  ref?: React.Ref<HTMLDivElement>;
}

export function VerificationOTP({
  className,
  onComplete,
  ref
}: VerificationOTPProps) {
  const { state, config, actions } = useVerification();
  const otpLength = config.otpLength ?? 6;

  const handleChange = (value: string) => {
    actions.updateOtpCode(value);
    // Clear error when user starts typing
    if (state.error) {
      actions.clearError();
    }
  };

  const handleComplete = async (value: string) => {
    await actions.verify(value);
    onComplete?.(value);
  };

  return (
    <div ref={ref} className={cn("w-full", className)}>
      <OTPInput
        maxLength={otpLength}
        value={state.otpCode}
        onChange={handleChange}
        onComplete={handleComplete}
        disabled={state.isVerifying || config.disabled}
      />
    </div>
  );
}
