"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { But<PERSON> } from "@watt/quote/components/ui/button";
import { Loader2 } from "lucide-react";
import type React from "react";
import { useVerification } from "./verification-context";

export interface VerificationResendProps
  extends Omit<
    React.ComponentPropsWithoutRef<typeof Button>,
    "onClick" | "children"
  > {
  children?:
    | React.ReactNode
    | ((props: {
        canResend: boolean;
        timeLeft: number;
      }) => React.ReactNode);
  formatTime?: (seconds: number) => string;
  ref?: React.Ref<HTMLButtonElement>;
}

export function VerificationResend({
  children = "Resend OTP",
  className,
  variant = "link",
  size = "sm",
  formatTime,
  disabled,
  ref,
  ...props
}: VerificationResendProps) {
  const { state, actions } = useVerification();

  const handleClick = async () => {
    if (state.canResend) {
      await actions.send();
    }
  };

  const defaultFormatTime = (seconds: number) => `Resend in ${seconds}s`;
  const timeFormatter = formatTime ?? defaultFormatTime;

  const buttonContent = () => {
    if (state.isSending) {
      return (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Sending...
        </>
      );
    }

    if (typeof children === "function") {
      return children({
        canResend: state.canResend,
        timeLeft: state.timeUntilResend
      });
    }

    if (!state.canResend && state.timeUntilResend > 0) {
      return timeFormatter(state.timeUntilResend);
    }

    return children;
  };

  return (
    <Button
      ref={ref}
      type="button"
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled || !state.canResend || state.isSending}
      className={cn("h-auto p-0", className)}
      {...props}
    >
      {buttonContent()}
    </Button>
  );
}
