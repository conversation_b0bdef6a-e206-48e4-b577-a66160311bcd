"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React from "react";
import type { ReactNode } from "react";
import { useVerification } from "./verification-context";

export interface VerificationStatusProps {
  children: ReactNode;
  className?: string;
  asChild?: boolean;
}

export function VerificationStatus({
  children,
  className,
  asChild = false
}: VerificationStatusProps) {
  const { state } = useVerification();

  // Only show this component when verified
  if (!state.isVerified) {
    return null;
  }

  if (asChild) {
    return <>{children}</>;
  }

  return <div className={cn("space-y-2", className)}>{children}</div>;
}
