"use client";

import { But<PERSON> } from "@watt/quote/components/ui/button";
import { Loader2 } from "lucide-react";
import type React from "react";
import { useVerification } from "./verification-context";

export interface VerificationTriggerProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Button>, "onClick"> {
  loadingText?: string;
  ref?: React.Ref<HTMLButtonElement>;
}

export function VerificationTrigger({
  children = "Verify",
  loadingText = "Sending...",
  className,
  variant = "outline",
  disabled,
  ref,
  ...props
}: VerificationTriggerProps) {
  const { state, config, actions } = useVerification();

  const handleClick = async () => {
    await actions.send();
  };

  const isDisabled =
    disabled ||
    config.disabled ||
    state.isSending ||
    !state.value ||
    state.value.trim() === "";

  return (
    <Button
      ref={ref}
      type="button"
      variant={variant}
      onClick={handleClick}
      disabled={isDisabled}
      className={className}
      {...props}
    >
      {state.isSending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </Button>
  );
}
