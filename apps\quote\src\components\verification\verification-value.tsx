"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { Input } from "@watt/quote/components/ui/input";
import type React from "react";
import { useVerification } from "./verification-context";

export interface VerificationValueProps
  extends Omit<
    React.ComponentPropsWithoutRef<typeof Input>,
    "value" | "onChange"
  > {
  ref?: React.Ref<HTMLInputElement>;
}

export function VerificationValue({
  className,
  disabled = true,
  ref,
  ...props
}: VerificationValueProps) {
  const { state } = useVerification();

  return (
    <Input
      ref={ref}
      value={state.value}
      disabled={disabled}
      readOnly
      className={cn("text-muted-foreground", className)}
      {...props}
    />
  );
}
