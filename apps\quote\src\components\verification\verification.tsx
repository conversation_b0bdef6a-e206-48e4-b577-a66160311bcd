"use client";

import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import React, { type ReactNode } from "react";
import {
  type VerificationConfig,
  VerificationProvider
} from "./verification-context";

export interface VerificationProps extends VerificationConfig {
  children: ReactNode;
  className?: string;
}

export function Verification({
  children,
  className,
  ...config
}: VerificationProps) {
  return (
    <VerificationProvider config={config}>
      <div className={cn("w-full", className)}>{children}</div>
    </VerificationProvider>
  );
}
