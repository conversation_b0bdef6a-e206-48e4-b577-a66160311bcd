import { isShortMpan } from "@watt/common/src/mpan/mpan";
import { isValidMPRN } from "@watt/common/src/mprn/mprn";
import { isValidPostcode } from "@watt/common/src/regex/postcode";
import { isValidUuid } from "@watt/common/src/regex/uuid";
import { trpcClient } from "@watt/quote/utils/api";
import { useState } from "react";

type AddressSearchInput = {
  postcode?: string;
  mpan?: string;
  mprn?: string;
  id?: string;
};

export function useAddressSearch() {
  const utils = trpcClient.useUtils();
  const [isLoading, setIsLoading] = useState(false);

  const fetchAddress = async (searchInput: string) => {
    const postcodeIsValid = isValidPostcode(searchInput);
    const mpanIsValid = isShortMpan(searchInput);
    const mprnIsValid = isValidMPRN(searchInput);
    const idIsValid = isValidUuid(searchInput);

    if (!postcodeIsValid && !mpanIsValid && !mprnIsValid && !idIsValid) {
      return;
    }

    const input: AddressSearchInput = {
      ...(postcodeIsValid && { postcode: searchInput }),
      ...(mpanIsValid && { mpan: searchInput }),
      ...(mprnIsValid && { mprn: searchInput }),
      ...(idIsValid && { id: searchInput })
    };

    try {
      setIsLoading(true);

      return await utils.pcw.addressFindMany.fetch(input);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    fetchAddress,
    isFetchingAddress: isLoading
  };
}
