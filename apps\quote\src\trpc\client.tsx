"use client";

import { createTRPCClient, httpBatchLink, loggerLink } from "@trpc/client";
import type { AppRouter } from "@watt/api/src/routes";
import { transformer } from "@watt/api/src/transformer";
import { env } from "@watt/common/src/config/env";
import { ONE_MINUTE_MS } from "@watt/common/src/constants/time-durations";
import { log } from "@watt/common/src/utils/axiom-logger";
import { toast, toastMemoryState } from "@watt/quote/components/ui/use-toast";
import packageJson from "../../package.json";
import { TRPC_LOGS, tRPCBaseUrl } from "./config";

/**
 * This is the client-side tRPC client for non hook usage
 * via trpcClientProxy.books.query(...)
 */
export const trpcClientProxy = createTRPCClient<AppRouter>({
  links: [
    loggerLink<AppRouter>({
      enabled: opts =>
        (env.NODE_ENV === "development" ||
          (opts.direction === "down" && opts.result instanceof Error)) &&
        TRPC_LOGS
    }),
    httpBatchLink({
      transformer,
      url: `${tRPCBaseUrl()}/api/trpc`,
      fetch(url, options) {
        return fetch(url, options)
          .then(response => {
            const clientVersion = packageJson.version;
            const serverVersion = response.headers.get("X-Server-Version");

            if (
              clientVersion !== serverVersion &&
              !toastMemoryState.toasts.some(
                t =>
                  t.title ===
                  "You are running an old version of the app. Please refresh."
              )
            ) {
              toast({
                title:
                  "You are running an old version of the app. Please refresh.",
                description: `There is a new version of the app available. You are running ${clientVersion} and the server is running ${serverVersion}. Please refresh the page when you can to get the latest version.`,
                duration: ONE_MINUTE_MS,
                variant: "destructive"
              });
            }

            return response;
          })
          .catch(err => {
            log.error("tRPC fetch error", err);
            throw err;
          });
      },
      headers() {
        const headers = new Headers();
        headers.set("x-trpc-source", "quote-client");
        return headers;
      }
    })
  ]
});
