import "server-only";

import { createHydrationHelpers } from "@trpc/react-query/rsc";
import type { AppRouter } from "@watt/api/src/routes";
import { createTRPCContext } from "@watt/api/src/trpc";
import { createCaller } from "@watt/api/src/types";
import { headers } from "next/headers";
import { cache } from "react";
import { createQueryClient } from "./query-client";

/**
 * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when
 * handling a tRPC call from a React Server Component.
 */
const createContext = cache(async () => {
  const heads = new Headers(await headers());
  heads.set("x-trpc-source", "quote-rsc");

  return createTRPCContext({
    headers: heads,
    // t3 stack parses the session from here. I think with the use of cache this will be more performant
    // TODO switch to handling the session and parsing it in from here.
    session: null
  });
});

const getQueryClient = cache(createQueryClient);
const caller = createCaller(createContext);

export const { trpc: tRPCServerApi, HydrateClient } =
  createHydrationHelpers<AppRouter>(caller, getQueryClient);
