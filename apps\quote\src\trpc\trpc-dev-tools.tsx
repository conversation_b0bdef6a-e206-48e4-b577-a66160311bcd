import { TRPCLogo } from "@watt/common/src/components/svgs/tRPC-logo.svg";
import { isLocalEnvironment } from "@watt/common/src/utils/is-production-environment";
import { useState } from "react";

import { Button } from "@watt/quote/components/ui/button";

export function TRPCDevTools() {
  const [showMenu, setShowMenu] = useState(false);
  const [enableTRPCLogs] = useState(false);

  if (!isLocalEnvironment()) {
    return null;
  }

  return (
    <>
      <div className="fixed bottom-2 left-32 z-100">
        <Button
          variant="none"
          onClick={() => setShowMenu(!showMenu)}
          className="group/button"
        >
          <TRPCLogo className="h-[35px] w-[35px] text-[#398CCB] group-hover/button:text-[#1A4E7B]" />
        </Button>
      </div>
      {showMenu && (
        <div className="absolute right-0 bottom-0 left-0 z-[100000] bg-muted p-4">
          <div className="flex flex-row gap-2">
            <Button
              variant="default"
              onClick={() => {
                //
              }}
            >
              {enableTRPCLogs ? "Disable" : "Enable"} tRPC Logs
            </Button>
            <Button variant="default" onClick={() => setShowMenu(false)}>
              Close
            </Button>
          </div>
        </div>
      )}
    </>
  );
}
