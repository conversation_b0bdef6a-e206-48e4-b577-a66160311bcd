"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ReactQueryStreamedHydration } from "@tanstack/react-query-next-experimental";
import { httpBatchLink, loggerLink } from "@trpc/client";
import type { AppRouter } from "@watt/api/src/routes";
import { transformer } from "@watt/api/src/transformer";
import { env } from "@watt/common/src/config/env";
import { trpcClient } from "@watt/quote/utils/api";
import { useState } from "react";
import { TRPC_LOGS, tRPCBaseUrl } from "./config";
import { getQueryClient } from "./get-query-client";

type TRPCReactProviderProps = {
  readonlyHeaders?: Headers;
  showDevTools?: boolean;
  children: React.ReactNode;
};

/**
 * This is the client-side tRPC client for hook usage
 * via trpcClient.books.useQuery(...)
 * @param props
 * @returns
 */
export function TRPCReactProvider({
  children,
  readonlyHeaders,
  showDevTools
}: TRPCReactProviderProps) {
  const queryClient = getQueryClient();

  const [client] = useState(() =>
    trpcClient.createClient({
      links: [
        loggerLink<AppRouter>({
          enabled: opts =>
            (env.NODE_ENV === "development" ||
              (opts.direction === "down" && opts.result instanceof Error)) &&
            TRPC_LOGS
        }),
        httpBatchLink({
          transformer,
          url: `${tRPCBaseUrl()}/api/trpc`,
          headers() {
            // Stephen: We need to set the headers in development as the headers are not copied over for local development
            // Vercel does this automatically for production and if we try to do it in production it results in an error
            if (process.env.NODE_ENV === "development") {
              const headers = new Headers(readonlyHeaders);
              headers.set("x-trpc-source", "quote-react");
              return headers;
            }

            const headers = new Headers();
            if (readonlyHeaders) {
              for (const [key, value] of readonlyHeaders.entries()) {
                headers.append(key, value);
              }
            }

            headers.set("x-trpc-source", "quote-react");
            return headers;
          }
        })
      ]
    })
  );

  return (
    <>
      <trpcClient.Provider client={client} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <ReactQueryStreamedHydration transformer={transformer}>
            {children}
          </ReactQueryStreamedHydration>
          <ReactQueryDevtools
            initialIsOpen={false}
            buttonPosition="bottom-left"
          />
        </QueryClientProvider>
      </trpcClient.Provider>
      {/* Hide until we have a use case for it */}
      {/* {showDevTools && <TRPCDevTools />} */}
    </>
  );
}
