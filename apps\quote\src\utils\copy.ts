import { log } from "@watt/common/src/utils/axiom-logger";

import { toast } from "@watt/quote/components/ui/use-toast";

export async function copyToClipboard(text: string, description: string) {
  try {
    await navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: `The ${description} has been copied to your clipboard. Use CTRL+V to paste.`,
      variant: "success"
    });
  } catch (err) {
    log.error("calls/data-table-row-actions.copyToClipboard", { err });
    toast({
      title: "Error copying to clipboard!",
      description: `There was an error copying the ${description} to your clipboard.`,
      variant: "destructive"
    });
  }
}
