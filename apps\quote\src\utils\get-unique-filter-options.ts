import type { RowData, Table } from "@tanstack/react-table";
import { MPANSchemaShort } from "@watt/common/src/mpan/mpan";
import { MPRNSchema } from "@watt/common/src/mprn/mprn";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { z } from "zod";

type GetUniqueFilterOptionsProps<T> = {
  columnId: string;
  table: Table<T>;
  unfilteredList?: boolean;
  humanizeValues?: boolean;
};

export function getUniqueFilterOptions<T extends RowData>({
  columnId,
  table,
  unfilteredList,
  humanizeValues
}: GetUniqueFilterOptionsProps<T>) {
  const column = table.getColumn(columnId);
  if (!column) {
    return [];
  }

  const applicableValues = (
    unfilteredList
      ? [
          ...new Set(
            table.getCoreRowModel().flatRows.map(row => row.getValue(columnId))
          )
        ]
      : Array.from(column.getFacetedUniqueValues().keys())
  ) as string[];

  const selectedValues = (column.getFilterValue() || []) as string[];
  const combinedValues = new Set([...applicableValues, ...selectedValues]);

  const uniqueValues = Array.from(combinedValues).filter(
    value =>
      value !== undefined && value !== null && String(value).trim() !== ""
  );

  return uniqueValues
    .map(value => ({
      label: humanizeValues ? humanize(value) : String(value),
      value: value
    }))
    .sort((a, b) => {
      if (typeof a.value === "number" && typeof b.value === "number") {
        return a.value - b.value;
      }
      if (typeof a.value === "string" && typeof b.value === "string") {
        return a.value.localeCompare(b.value);
      }
      return 0;
    });
}

export function getUniqueSupplierFilterOptions<TData extends RowData>(
  columnId: string,
  table: Table<TData>,
  unfilteredList?: boolean,
  currentSupplierId?: string | null
) {
  const column = table.getColumn(columnId);
  if (!column) {
    return [];
  }

  const applicableValues = (
    unfilteredList
      ? [
          ...new Set(
            table.getCoreRowModel().flatRows.map(row => row.getValue(columnId))
          )
        ]
      : Array.from(column.getFacetedUniqueValues().keys())
  ) as string[];

  const selectedValues = (column.getFilterValue() || []) as string[];
  const combinedValues = new Set([...applicableValues, ...selectedValues]);
  const shouldRemoveSupplierId =
    table
      .getCoreRowModel()
      .flatRows.map(row => row.getValue(columnId))
      .filter(value => value === currentSupplierId).length === 1;

  const uniqueValues = Array.from(combinedValues).filter(
    value =>
      value !== undefined &&
      value !== null &&
      String(value).trim() !== "" &&
      (shouldRemoveSupplierId ? value !== currentSupplierId : true)
  );

  return uniqueValues
    .map(value => ({
      label: String(value),
      value: value
    }))
    .sort((a, b) => {
      if (typeof a.value === "number" && typeof b.value === "number") {
        return a.value - b.value;
      }
      if (typeof a.value === "string" && typeof b.value === "string") {
        return a.value.localeCompare(b.value);
      }
      return 0;
    });
}

export function getUniqueMpxnFilterOptions<TData extends RowData>(
  columnId: string,
  table: Table<TData>,
  unfilteredList?: boolean
) {
  const column = table.getColumn(columnId);

  if (!column) {
    return [];
  }

  const applicableValues = (
    unfilteredList
      ? [
          ...new Set(
            table.getCoreRowModel().flatRows.map(row => row.getValue(columnId))
          )
        ]
      : Array.from(column.getFacetedUniqueValues().keys())
  ).map(value => {
    const parseMpxn = z.union([MPANSchemaShort, MPRNSchema]).safeParse(value);
    return parseMpxn.success ? parseMpxn.data : "";
  });

  const selectedValues = (column.getFilterValue() || []) as string[];
  const combinedValues = new Set([...applicableValues, ...selectedValues]);

  const uniqueValues = Array.from(combinedValues).filter(
    value =>
      value !== undefined && value !== null && String(value).trim() !== ""
  );

  return uniqueValues
    .map(value => ({
      label: String(value),
      value: value
    }))
    .sort((a, b) => {
      if (typeof a.value === "number" && typeof b.value === "number") {
        return a.value - b.value;
      }
      if (typeof a.value === "string" && typeof b.value === "string") {
        return a.value.localeCompare(b.value);
      }
      return 0;
    });
}
