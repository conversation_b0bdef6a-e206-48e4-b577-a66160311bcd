import type { QuoteGetQuotesByQuoteListId } from "@watt/api/src/router";

type PcwQuoteList = NonNullable<QuoteGetQuotesByQuoteListId["quoteList"]>;
export type PcwQuotes = PcwQuoteList["quotes"][number];

/**
 * Calculates the total annual price for a quote
 */
export function calculateAnnualPrice(quote: PcwQuotes): number {
  return (
    (quote.electricQuote?.annualPrice ?? 0) + (quote.gasQuote?.annualPrice ?? 0)
  );
}

/**
 * Extracts the duration years from quote contract type
 */
export function extractDurationYears(quote: PcwQuotes): number {
  return Math.ceil(quote.duration / 12);
}

/**
 * Gets the product plan from the contract type
 */
export function getProductPlan(quote: PcwQuotes): string {
  const contractType =
    quote.electricQuote?.contractType || quote.gasQuote?.contractType;
  return contractType ?? "Standard";
}

/**
 * Finds the cheapest quote from a list of quotes
 */
export function findCheapestQuote(quotes: PcwQuotes[]): PcwQuotes | null {
  if (!quotes.length) {
    return null;
  }

  const firstQuote = quotes[0];
  if (!firstQuote) {
    return null;
  }

  return quotes.reduce((cheapest, current) => {
    const currentTotal = calculateAnnualPrice(current);
    const cheapestTotal = calculateAnnualPrice(cheapest);
    return currentTotal < cheapestTotal ? current : cheapest;
  }, firstQuote);
}
