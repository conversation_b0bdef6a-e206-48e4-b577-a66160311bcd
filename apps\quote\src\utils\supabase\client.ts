import { createBrowserClient } from "@supabase/ssr";
import { env } from "@watt/common/src/config/env";
import { getSupabaseCookieName } from "@watt/common/src/libs/supabase/cookie-config";

export const createClientComponentClient = () =>
  createBrowserClient(
    env.NEXT_PUBLIC_SUPABASE_URL!,
    env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookieOptions: {
        name: getSupabaseCookieName("quote")
      }
    }
  );
