import { User<PERSON><PERSON> } from "@prisma/client";
import { createMiddlewareAdapter } from "@watt/common/src/libs/supabase/cookie-adapters";
import { createSupabaseServerClientWithAdapter } from "@watt/common/src/libs/supabase/server";
import { log } from "@watt/common/src/utils/axiom-logger";
import { getSupabaseUser } from "@watt/db/src/supabase/get-user";
import { type NextRequest, NextResponse } from "next/server";

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request
  });

  const supabase = createSupabaseServerClientWithAdapter(
    // Create middleware adapter with response update callback
    createMiddlewareAdapter(request, response => {
      supabaseResponse = response;
    }),
    "quote"
  );

  const { user, profileData } = await getSupabaseUser(supabase);

  // If user is authenticated, they must be a QUOTE_APP user
  if (user && profileData?.role !== UserRole.QUOTE_APP) {
    log.warn("Staff user attempted to access Quote app", {
      email: user.email,
      userId: user.id,
      role: user.user_metadata?.role,
      path: request.nextUrl.pathname
    });

    // Check if this is an API request
    const isApiRequest = request.nextUrl.pathname.startsWith("/api/");
    const isTrpcRequest = request.nextUrl.pathname.includes("/trpc/");

    if (isApiRequest || isTrpcRequest) {
      // For API requests, sign out and return 401
      await supabase.auth.signOut({ scope: "local" });

      return new NextResponse(
        JSON.stringify({
          error: "Unauthorized",
          message: "Staff members cannot use the quote app"
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    // For page requests, sign out and redirect
    await supabase.auth.signOut({
      // Only sign out on this client
      scope: "local"
    });

    const homeUrl = new URL("/", request.url);
    homeUrl.searchParams.set("error", "staff_access_denied");
    return NextResponse.redirect(homeUrl);
  }

  return supabaseResponse;
}
