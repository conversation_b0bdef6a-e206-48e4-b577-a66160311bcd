import typographyPlugin from "@tailwindcss/typography";
import tailwindcssAnimate from "tailwindcss-animate";
import { fontFamily } from "tailwindcss/defaultTheme";

/** @type {import('tailwindcss').Config} */
export default {
  mode: "jit",
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{ts,tsx}",
    "./src/components/**/*.{ts,tsx}",
    "./src/app/**/*.{ts,tsx}"
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px"
      }
    },
    extend: {
      colors: {
        "green-theme": "hsl(var(--green-theme) / <alpha-value>)",
        "red-theme": "hsl(var(--red-theme) / <alpha-value>)",
        "dark-theme": "hsl(var(--dark-theme) / <alpha-value>)",
        "dark-theme-2": "hsl(var(--dark-theme-2) / <alpha-value>)",
        border: "hsl(var(--border) / <alpha-value>)",
        input: "hsl(var(--input) / <alpha-value>)",
        ring: "hsl(var(--ring) / <alpha-value>)",
        background: "hsl(var(--background) / <alpha-value>)",
        foreground: "hsl(var(--foreground) / <alpha-value>)",
        primary: {
          DEFAULT: "hsl(var(--primary) / <alpha-value>)",
          foreground: "hsl(var(--primary-foreground) / <alpha-value>)"
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary) / <alpha-value>)",
          foreground: "hsl(var(--secondary-foreground) / <alpha-value>)",
          light: "hsl(var(--secondary-light) / <alpha-value>)"
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive) / <alpha-value>)",
          foreground: "hsl(var(--destructive-foreground) / <alpha-value>)"
        },
        muted: {
          DEFAULT: "hsl(var(--muted) / <alpha-value>)",
          foreground: "hsl(var(--muted-foreground) / <alpha-value>)"
        },
        accent: {
          DEFAULT: "hsl(var(--accent) / <alpha-value>)",
          foreground: "hsl(var(--accent-foreground) / <alpha-value>)"
        },
        popover: {
          DEFAULT: "hsl(var(--popover) / <alpha-value>)",
          foreground: "hsl(var(--popover-foreground) / <alpha-value>)"
        },
        card: {
          DEFAULT: "hsl(var(--card) / <alpha-value>)",
          foreground: "hsl(var(--card-foreground) / <alpha-value>)"
        },
        "badge-purple": "var(--badge-purple)",
        "badge-light-blue": "var(--badge-light-blue)",
        "badge-dark-blue": "var(--badge-dark-blue)",
        "badge-red": "var(--badge-red)",
        "badge-lime-green": "var(--badge-lime-green)",
        "badge-green": "var(--badge-green)",
        "badge-dark-grey": "var(--badge-dark-grey)",
        "badge-light-green": "var(--badge-light-green)",
        "badge-success": "var(--badge-success)",
        "badge-orange": "var(--badge-orange)",
        "badge-yellow": "var(--badge-yellow)",
        "badge-light-yellow": "var(--badge-light-yellow)",
        "badge-dark-red": "var(--badge-dark-red)",
        "badge-spam": "var(--badge-spam)",
        "badge-unsubscribe": "var(--badge-unsubscribe)",
        "badge-group-unsubscribe": "var(--badge-group-unsubscribe)",
        "badge-group-resubscribe": "var(--badge-group-resubscribe)",
        "badge-error": "var(--badge-error)",
        "primary-light": "var(--primary-light)"
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)"
      },
      fontFamily: {
        sans: ["var(--font-sans)", ...fontFamily.sans]
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" }
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" }
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        spin: "spin 2s linear infinite",
        pulse: "pulse 2s cubic-bezier(0, 0, 0.2, 1) infinite"
      }
    }
  },
  // These classes are used in the app to dynamically change the badge color
  // Due to tailwind's JIT mode, we need to whitelist these classes
  // https://stackoverflow.com/questions/75440072/tailwindcss-styles-not-rendered-when-applied-dynamically-in-nextjs
  safelist: [
    "bg-badge-purple",
    "bg-badge-light-blue",
    "bg-badge-dark-blue",
    "bg-badge-red",
    "bg-badge-lime-green",
    "bg-badge-green",
    "bg-badge-dark-grey",
    "bg-badge-light-green",
    "bg-badge-success",
    "bg-badge-orange",
    "bg-badge-yellow",
    "bg-badge-light-yellow",
    "bg-badge-dark-red",
    "bg-badge-spam",
    "bg-badge-unsubscribe",
    "bg-badge-group-unsubscribe",
    "bg-badge-group-resubscribe",
    "bg-badge-error"
  ],
  variants: {
    extend: {
      borderColor: ["responsive", "hover", "focus", "group-hover"]
    },
    animation: ["responsive", "motion-safe", "motion-reduce"]
  },
  plugins: [tailwindcssAnimate, typographyPlugin]
};
