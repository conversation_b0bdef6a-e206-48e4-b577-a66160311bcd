{"$schema": "https://openapi.vercel.sh/vercel.json", "version": 2, "public": false, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,DELETE,PATCH,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}]}], "outputDirectory": ".next/build", "regions": ["lhr1"], "installCommand": "pnpm install", "buildCommand": "pnpm build", "github": {"autoJobCancelation": true}, "rewrites": [{"source": "/ingest/static/:path*", "destination": "https://eu-assets.i.posthog.com/static/:path*"}, {"source": "/ingest/:path*", "destination": "https://eu.i.posthog.com/:path*"}]}