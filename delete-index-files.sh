#!/bin/sh

# This script deletes all index.ts and index.tsx files in the project
# that are NOT ignored by git AND are identified as 'barrel files'.
# A barrel file is defined conservatively as one containing ONLY lines matching:
#   export * from ...;
#   export { ... } from ...;
#   export * as ... from ...;
# (after comments and blank lines are removed).
# Be cautious when running this script as it will permanently delete files.

DRY_RUN=false
if [ "$1" = "--dry-run" ]; then
  DRY_RUN=true
  echo "DRY RUN MODE: No files will be deleted."
fi

# Function to determine if a file is a barrel file
is_barrel_file() {
  local file_path="$1"
  local effective_lines_count=0
  local matching_barrel_lines_count=0
  local line_content
  local has_effective_content=false

  # awk script to:
  # 1. Handle block comments /* ... */ (in_comment flag)
  # 2. Remove // style comments
  # 3. Skip empty lines or lines that become empty after comment removal
  # Note: This awk script is reasonably robust for typical comment styles.
  line_content=$(awk '
        BEGIN { in_comment = 0 }
        # End of block comment processing
        in_comment && /\*\// {
            sub(/.*\\*\\//, ""); # Remove content before */ on this line
            in_comment = 0;
            if (length($0) == 0 || $0 ~ /^[[:space:]]*$/) next; # Skip if line becomes empty
        }
        in_comment { next } # Skip lines fully within a block comment

        # Start of block comment processing
        /\/\*/ {
            if (/\*\\//) { # Single line block comment /* ... */
                gsub(/\/\*.*\*\\//, ""); # Remove the block comment
            } else { # Start of a multi-line block comment
                sub(/\/\*.*/, ""); # Remove from /* to end of line
                in_comment = 1;
            }
            if (length($0) == 0 || $0 ~ /^[[:space:]]*$/) next; # Skip if line becomes empty
        }

        { gsub(/\/\/.*$/, "") } # Remove // comments to end of line
        /^[[:space:]]*$/ { next } # Skip empty lines or lines that became empty

        { print } # Print non-comment, non-empty lines
    ' "$file_path")

  if [ -z "$line_content" ]; then
    return 1 # Not a barrel if effectively empty
  fi

  echo "$line_content" | while IFS= read -r line || [ -n "$line" ]; do # Process each effective line
    has_effective_content=true
    effective_lines_count=$((effective_lines_count + 1))
    # Trim leading/trailing whitespace
    trimmed_line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

    # Check against allowed barrel patterns
    if echo "$trimmed_line" | grep -Eq '^export[[:space:]]+\*[[:space:]]+from[[:space:]]+.+;' ||
      echo "$trimmed_line" | grep -Eq '^export[[:space:]]+\{[^}]+\}[[:space:]]+from[[:space:]]+.+;' ||
      echo "$trimmed_line" | grep -Eq '^export[[:space:]]+\*[[:space:]]+as[[:space:]]+[a-zA-Z_][a-zA-Z0-9_]*[[:space:]]+from[[:space:]]+.+;'; then
      matching_barrel_lines_count=$((matching_barrel_lines_count + 1))
    else
      # If any line does not match, it's not a pure barrel file by this script's definition
      # echo "DEBUG: Non-barrel line in '$file_path': [$trimmed_line]" # Optional debug
      echo "NON_BARREL_LINE_FOUND" # Signal to stop processing this file
      return 1
    fi
  done | while IFS= read -r result; do # Capture "NON_BARREL_LINE_FOUND"
    if [ "$result" = "NON_BARREL_LINE_FOUND" ]; then
      return 1
    fi
  done
  # Check if the subshell pipeline indicated a non-barrel line
  # This is a bit tricky due to subshells. The above pipe to while loop is a workaround.
  # A more direct way if the above isn't perfect is to check a flag set inside the loop,
  # but that requires careful subshell management or temp files.
  # The `return 1` inside the loop should terminate that sub-process of `echo ... | while ...`
  # Let's re-evaluate the loop structure for returning early.

  # Simpler loop structure for returning:
  is_currently_barrel=true
  effective_lines_count=0
  echo "$line_content" | while IFS= read -r line || [ -n "$line" ]; do
    effective_lines_count=$((effective_lines_count + 1))
    trimmed_line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    if ! (echo "$trimmed_line" | grep -Eq '^export[[:space:]]+\*[[:space:]]+from[[:space:]]+.+;' ||
      echo "$trimmed_line" | grep -Eq '^export[[:space:]]+\{[^}]+\}[[:space:]]+from[[:space:]]+.+;' ||
      echo "$trimmed_line" | grep -Eq '^export[[:space:]]+\*[[:space:]]+as[[:space:]]+[a-zA-Z_][a-zA-Z0-9_]*[[:space:]]+from[[:space:]]+.+:'); then
      is_currently_barrel=false
      break # Exit the while loop immediately
    fi
  done

  if [ "$is_currently_barrel" = false ]; then
    return 1 # Not a barrel
  fi

  if [ "$effective_lines_count" -gt 0 ]; then # Must have had some content
    return 0                                  # It's a barrel
  else
    return 1 # No effective lines, not a barrel
  fi
}

process_file_type() {
  local file_extension="$1"
  echo "Searching for $file_extension files..."
  find . -type f -name "*.$file_extension" -print0 | while IFS= read -r -d $'\\0' file; do
    if ! git check-ignore -q "$file"; then
      if is_barrel_file "$file"; then
        if [ "$DRY_RUN" = true ]; then
          echo "WOULD DELETE (barrel, not git-ignored): $file"
        else
          echo "Deleting (barrel, not git-ignored): $file"
          rm "$file"
        fi
      else
        if [ "$DRY_RUN" = true ]; then
          # Only print if it's NOT a barrel in dry run if verbose, otherwise silent
          # echo "WOULD SKIP (not a barrel or git-ignored): $file"
          : # Be silent about non-barrels in dry-run unless explicitly requested
        else
          echo "Skipping (not a barrel or git-ignored): $file"
        fi
      fi
    else
      if [ "$DRY_RUN" = false ]; then # Only print skipped git-ignored if not dry run
        echo "Skipping (git-ignored): $file"
      fi
    fi
  done
}

process_file_type "ts"
process_file_type "tsx"

if [ "$DRY_RUN" = true ]; then
  echo "DRY RUN COMPLETE: No files were deleted."
else
  echo "Deletion of applicable barrel index.ts and index.tsx files complete."
fi
