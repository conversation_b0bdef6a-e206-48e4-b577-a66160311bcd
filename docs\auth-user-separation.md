# User Type Separation Between CRM and Quote Apps

## Overview

This document explains how we separate user types between the CRM and Quote apps while maintaining a single Supabase database and authentication system.

## Solution

We use the existing `UserRole` enum with a new `QUOTE_APP` role to differentiate between staff users (CRM access) and customer users (Quote app access).

### Key Components

1. **UserRole Enum**: Added `QUOTE_APP` to the existing UserRole enum in the Prisma schema
2. **JWT-based Role Checking**: Store user role in `user_metadata` during signup and check it in middleware
3. **SupabaseSafeSession**: Use JWT verification instead of REST API calls for better performance
4. **Middleware Protection**: Both apps check user roles and prevent unauthorized access

### Performance Optimizations

1. **No Additional Database Calls**: User role is stored in JWT and verified locally
2. **Avoid REST API Calls**: Use `SupabaseSafeSession` instead of `supabase.auth.getUser()`
3. **Conditional Profile Fetching**: Only fetch Profile for staff users, not Quote app customers

## Implementation

### 1. User Signup

When creating users, set the appropriate role in `user_metadata`:

```typescript
// Quote app customer signup
await supabase.auth.signUp({
  email: "<EMAIL>",
  password: "password123",
  options: {
    data: {
      role: UserRole.QUOTE_APP
    }
  }
});

// Staff user signup (done through admin interface)
await supabase.auth.admin.createUser({
  email: "<EMAIL>",
  password: "password123",
  user_metadata: {
    role: UserRole.SALES_AGENT // or other staff role
  }
});
```

### 2. Middleware Protection

#### CRM App (`apps/crm/src/utils/supabase/middleware.ts`)

- Uses `SupabaseSafeSession` to verify JWT without REST API call
- Checks if user has a staff role using `hasStaffAccess()`
- Redirects Quote app users to login with error message

#### Quote App (`apps/quote/utils/supabase/middleware.ts`)

- Allows unauthenticated access
- If authenticated, ensures user has `QUOTE_APP` role
- Redirects staff users with error message

### 3. TRPC Context

The TRPC context has been optimized to:

- Use `SupabaseSafeSession` to avoid REST API calls
- Only fetch Profile for staff users (not Quote app customers)
- Handle QUOTE_APP users in `enforceUserIsAuthed` middleware

## Security Considerations

1. **Role Storage**: User roles are stored in `user_metadata` which can only be set during signup or by admin
2. **JWT Verification**: All role checks use cryptographically verified JWT claims
3. **Automatic Signout**: Users attempting to access the wrong app are automatically signed out
4. **Clear Error Messages**: Users see appropriate error messages when access is denied

## Migration Path

For existing users:

1. Staff users already have appropriate roles in their Profile
2. Quote app customers should be created with `QUOTE_APP` role
3. Consider a migration script to update existing customer `user_metadata`

## Testing

Test scenarios:

1. Quote app customer tries to access CRM → Should be signed out and redirected
2. Staff user tries to access Quote app → Should be signed out and redirected
3. Unauthenticated user accesses Quote app → Should work normally
4. Staff user accesses CRM → Should work based on their role permissions
