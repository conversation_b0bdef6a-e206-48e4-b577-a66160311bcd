# Avoiding Barrel Exports for Better Performance

## TL;DR

**Stop using barrel exports (index.ts files with `export * from`) - they harm build performance, increase bundle size, and break tree-shaking.** Import directly from source files instead.

## The Problem with Barrel Exports

Barrel exports create several performance issues:

- **Increased bundle size** - Bundlers often include entire modules even when only one export is used
- **Slower build times** - Every import must traverse through multiple files
- **Tree-shaking failures** - Modern bundlers struggle to eliminate dead code through barrel exports
- **Circular dependency risks** - Barrel files increase the chance of circular imports
- **Poor developer experience** - Slower TypeScript compilation and IDE performance

## Current Issues in the Codebase

### ❌ Problematic Barrel Exports Found

```typescript
// packages/api/src/service/index.ts
export * from "./address";
export * from "./auth";
export * from "./companies-house";
export * from "./company-files";
// ... 15+ more exports

// packages/db/src/enums/index.ts
export * from "./address-type";
export * from "./callback-type";
export * from "./call-outcome";
// ... many more exports
```

### Impact Analysis

When importing from these barrels:

```typescript
// This imports ALL services, not just the one needed
import { getAddressById } from "@acme/api/service";
```

The bundler must:

1. Load the barrel file
2. Load ALL referenced files
3. Parse and analyze all exports
4. Attempt (often failing) to tree-shake unused code

## Migration Examples

### ❌ Old Way (Barrel Exports)

```typescript
// packages/api/src/service/index.ts
export * from "./address";
export * from "./auth";
export * from "./company";
export * from "./notes";

// Usage in app
import { getAddress, authenticate, getCompany } from "@acme/api/service";
```

### ✅ New Way (Direct Imports)

```typescript
// Remove the index.ts file entirely

// Usage in app - import directly
import { getAddress } from "@acme/api/service/address";
import { authenticate } from "@acme/api/service/auth";
import { getCompany } from "@acme/api/service/company";
```

## Performance Comparison

| Metric | Barrel Exports | Direct Imports |
|--------|----------------|----------------|
| **Bundle size** | +15-30% larger | Baseline |
| **Build time** | 2-3x slower | Baseline |
| **Tree-shaking** | Often fails | Works reliably |
| **TypeScript performance** | Slower compilation | Faster |
| **IDE responsiveness** | Sluggish imports | Instant |
| **Memory usage** | Higher | Lower |

## Best Practices

### 1. Delete All Barrel Files

```bash
# Find all barrel files
find . -name "index.ts" -o -name "index.js" | grep -v node_modules

# Remove them after updating imports
```

### 2. Use Path Aliases for Clean Imports

```json
// tsconfig.json
{
  "compilerOptions": {
    "paths": {
      "@/services/*": ["./packages/api/src/service/*"],
      "@/components/*": ["./apps/crm/src/components/*"]
    }
  }
}
```

### 3. Group Related Exports in Single Files

Instead of:

```typescript
// ❌ Multiple files with barrel
// types/user.ts
export type User = {...}
// types/profile.ts
export type Profile = {...}
// types/index.ts
export * from './user';
export * from './profile';
```

Do:

```typescript
// ✅ Single file with related types
// types/user-types.ts
export type User = {...}
export type Profile = {...}
export type UserWithProfile = User & { profile: Profile }
```

### 4. Configure ESLint to Prevent Barrel Exports

```json
// .eslintrc.json
{
  "rules": {
    "no-restricted-syntax": [
      "error",
      {
        "selector": "ExportAllDeclaration",
        "message": "Avoid barrel exports. Import directly from source files."
      }
    ]
  }
}
```

## Migration Checklist

- [ ] Identify all barrel export files using the find command
- [ ] Map out all imports from barrel files
- [ ] Update imports to use direct paths
- [ ] Delete barrel export files
- [ ] Configure build tools to use direct imports
- [ ] Add ESLint rule to prevent new barrel exports
- [ ] Update documentation and import conventions
- [ ] Monitor bundle size reduction

## Real-World Impact

Based on similar migrations:

- **Bundle size**: 20-40% reduction for affected chunks
- **Build time**: 30-50% faster incremental builds
- **TypeScript**: 2-3x faster type checking
- **Developer experience**: Near-instant IDE imports

## Common Objections and Solutions

### "But barrel exports look cleaner!"

Use path aliases and proper module organization:

```typescript
// Still clean, but performant
import { UserService, AuthService } from '@/services/user';
import { Button, Card, Modal } from '@/components/ui';
```

### "It's too much work to migrate!"

Use automated tools:

```bash
# Use a codemod or write a script to update imports
npx jscodeshift -t remove-barrel-exports.js ./src
```

## Conclusion

Barrel exports are a legacy pattern that made sense before modern bundlers. Today, they're a performance anti-pattern that should be avoided. The migration effort pays for itself quickly through faster builds and smaller bundles.
