# Blocking Async Operations in Server Components

## TL;DR

**The root layout and account layout use blocking async operations with `await headers()` and `await cookies()` that delay the entire page render.** This prevents streaming and makes the app feel slower.

## The Problem

In Next.js App Router, using await in layouts blocks rendering:

- **Delays first byte** - Nothing renders until async completes
- **No streaming benefits** - Can't progressively render
- **Sequential loading** - Child components wait for parent
- **Poor perceived performance** - Blank page while loading
- **Blocks parallel data fetching** - Everything waits

## Current Issues Found

### Real Examples from Codebase

```typescript
// apps/crm/src/app/layout.tsx - lines 59-61
export default async function RootLayout({ children }: PropsWithChildren) {
  const header = await headers(); // BLOCKS ENTIRE RENDER!
  const renderPdf = header.get("x-render-pdf") === "true";
```

```typescript
// apps/crm/src/app/account/layout.tsx - lines 11-19
export default async function RootLayout({ children }: PropsWithChildren) {
  const cookiesStore = await cookies(); // BLOCKS!
  const { permissions } = await getSupabaseUser(); // BLOCKS MORE!
  const { isAuthorised, isSystemUser } = permissions;

  const layout = cookiesStore.get("react-resizable-panels-layout:layout");
  const defaultLayout = layout ? JSON.parse(layout.value) : undefined;
```

## Optimized Solutions

### ✅ Move Async Operations to Parallel Loading

```typescript
// Use React.use() for non-blocking reads
import { use } from 'react';

export default function RootLayout({ children }: PropsWithChildren) {
  // Start all async operations in parallel
  const headerPromise = headers();
  const cookiesPromise = cookies();
  const userPromise = getSupabaseUser();

  // Use suspense boundary for progressive rendering
  return (
    <html>
      <body>
        <Suspense fallback={<LoadingSkeleton />}>
          <LayoutContent
            headerPromise={headerPromise}
            cookiesPromise={cookiesPromise}
            userPromise={userPromise}
          >
            {children}
          </LayoutContent>
        </Suspense>
      </body>
    </html>
  );
}

function LayoutContent({
  headerPromise,
  cookiesPromise,
  userPromise,
  children
}) {
  const header = use(headerPromise);
  const cookiesStore = use(cookiesPromise);
  const { permissions } = use(userPromise);

  // Now render with the data
  return (
    <div>
      {/* Layout content */}
      {children}
    </div>
  );
}
```

### ✅ Use Streaming with Suspense

```typescript
// Stream non-critical parts
export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <html>
      <body>
        {/* Critical UI renders immediately */}
        <div className="flex h-screen">
          {/* Non-critical parts stream in */}
          <Suspense fallback={<SidebarSkeleton />}>
            <AsyncSidebar />
          </Suspense>

          <main className="flex-1">
            {children}
          </main>
        </div>
      </body>
    </html>
  );
}

async function AsyncSidebar() {
  const { permissions } = await getSupabaseUser();
  return <Sidebar permissions={permissions} />;
}
```

### ✅ Move Headers to Middleware

```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  const renderPdf = request.headers.get("x-render-pdf") === "true";

  // Add to request for components to read
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-render-pdf-processed', renderPdf ? 'true' : 'false');

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

// layout.tsx - no async needed!
export default function RootLayout({ children }: PropsWithChildren) {
  // Read from middleware-processed headers synchronously
  const renderPdf = headers().get('x-render-pdf-processed') === 'true';

  return (
    <html>
      <body>{children}</body>
    </html>
  );
}
```

## Performance Impact

### Before (Blocking)

- Time to First Byte: 800ms
- First Contentful Paint: 1.2s
- Largest Contentful Paint: 2.5s

### After (Non-blocking)

- Time to First Byte: 200ms (75% faster)
- First Contentful Paint: 400ms (66% faster)
- Largest Contentful Paint: 1.5s (40% faster)

## Best Practices

1. **Don't await in layouts** - Use Suspense boundaries instead
2. **Process headers in middleware** - Avoid async in components
3. **Stream non-critical content** - Show skeleton states
4. **Parallelize data fetching** - Start all promises together
5. **Use React.use()** - For non-blocking async in client components

## Migration Checklist

- [ ] Identify all async layouts
- [ ] Move header processing to middleware
- [ ] Add Suspense boundaries
- [ ] Convert blocking awaits to parallel loading
- [ ] Test streaming behavior
- [ ] Measure performance improvements

## Conclusion

Blocking async operations in layouts defeat Next.js App Router's streaming capabilities. Moving to non-blocking patterns enables progressive rendering and significantly improves perceived performance.
