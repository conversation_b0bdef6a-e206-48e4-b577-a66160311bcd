# Command Menu Global Listener

## Issue Description

The `CommandMenu` component adds a global keyboard event listener that never gets cleaned up properly when the component unmounts, and the listener is recreated on every render due to missing dependencies.

## Problem Code

In `apps/crm/src/components/command-menu.tsx`:

```tsx
useEffect(() => {
  const down = (e: KeyboardEvent) => {
    if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      setOpen(open => !open);
    }
  };

  document.addEventListener("keydown", down);
  return () => document.removeEventListener("keydown", down);
}, []); // Missing 'open' dependency
```

## Why This Is a Problem

1. **Memory leaks**: Listeners may not be removed if component unmounts unexpectedly
2. **Multiple listeners**: Could attach duplicate listeners in development mode
3. **Stale closures**: The empty dependency array means the effect never re-runs
4. **Global pollution**: Adds listeners to document even when not needed
5. **Performance overhead**: Every keypress triggers the handler

## Optimized Solution

Use proper event handling with correct dependencies:

```tsx
// Option 1: Use a ref to avoid dependencies
export function CommandMenu({ isCollapsed, ...props }: CommandMenuProps) {
  const [open, setOpen] = useState(false);
  const openRef = useRef(open);
  
  useEffect(() => {
    openRef.current = open;
  }, [open]);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen(prev => !prev);
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []); // Now correctly has no dependencies

  // ...
}

// Option 2: Use a custom hook
function useCommandMenu() {
  const [open, setOpen] = useState(false);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      // Only listen when focused on the document
      if (
        e.key === "k" && 
        (e.metaKey || e.ctrlKey) && 
        !e.target.matches('input, textarea')
      ) {
        e.preventDefault();
        setOpen(prev => !prev);
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  return { open, setOpen };
}

// Option 3: Use event delegation
useEffect(() => {
  const controller = new AbortController();
  
  document.addEventListener("keydown", (e) => {
    if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      setOpen(prev => !prev);
    }
  }, { signal: controller.signal });

  return () => controller.abort();
}, []);
```

## Migration Strategy

1. Audit all global event listeners
2. Use refs or state updater functions to avoid stale closures
3. Add proper cleanup with AbortController
4. Consider using a global keyboard shortcut manager
5. Prevent shortcuts when typing in inputs
6. Use event delegation where possible

## Performance Impact

- Prevents memory leaks
- Reduces event listener overhead
- Avoids stale closure bugs
- Improves keyboard responsiveness
- Better cleanup in development mode