# Companies Table Client-Side Filtering

## Issue Description

The Companies DataTable component uses `manualFiltering: true` but still initializes all the client-side filtering infrastructure, creating unnecessary overhead and confusion about where filtering actually happens.

## Problem Code

In `apps/crm/src/app/account/companies/components/data-table.tsx`:

```tsx
const table = useReactTable({
  data: allItems,
  columns,
  state: {
    sorting,
    columnVisibility,
    columnFilters,
    rowSelection,
    globalFilter
  },
  // ... handlers
  getCoreRowModel: getCoreRowModel(),
  getFilteredRowModel: getFilteredRowModel(), // Not used with manualFiltering!
  getSortedRowModel: getSortedRowModel(),
  getFacetedRowModel: getFacetedRowModel(), // Not used!
  getFacetedUniqueValues: getFacetedUniqueValues(), // Not used!
  getFacetedMinMaxValues: getFacetedMinMaxValues(), // Not used!
  debugTable: false,
  manualFiltering: true // Server-side filtering enabled
});
```

## Why This Is a Problem

1. **Wasted initialization**: Client filtering functions initialized but never used
2. **Bundle size**: Includes unused TanStack table features
3. **Memory overhead**: Faceted models create data structures
4. **Confusing code**: Mixed signals about filtering location
5. **Performance cost**: Unnecessary computations on data changes

## Optimized Solution

Remove unused client-side filtering features:

```tsx
// Only include what's actually needed for server-side filtering
const table = useReactTable({
  data: allItems,
  columns,
  state: {
    sorting,
    columnVisibility,
    columnFilters,
    rowSelection,
    globalFilter
  },
  enableRowSelection: false,
  onGlobalFilterChange: setGlobalFilter,
  onRowSelectionChange: setRowSelection,
  onSortingChange: setSorting,
  onColumnFiltersChange: setColumnFilters,
  onColumnVisibilityChange: setColumnVisibility,
  getCoreRowModel: getCoreRowModel(),
  manualFiltering: true,
  manualSorting: true, // Also make sorting manual if server-side
  debugTable: false
});

// Or better: Create a server-side table hook
function useServerSideTable<TData>({
  data,
  columns,
  onFiltersChange,
  onSortingChange
}: ServerSideTableOptions<TData>) {
  return useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualFiltering: true,
    manualSorting: true,
    manualPagination: true,
    onColumnFiltersChange: onFiltersChange,
    onSortingChange: onSortingChange,
    // Only core features needed for display
  });
}

// Even better: Use React Query's select to transform data
const { data } = trpcClient.company.companyFilteredList.useInfiniteQuery(
  {
    searchFilters: {
      columnFilters: debouncedColumnFilters,
      globalFilter
    }
  },
  {
    select: (data) => ({
      items: data.pages.flatMap(page => page.items),
      totalCount: data.pages[0]?.meta?.totalRowCount ?? 0,
      hasMore: data.pages[data.pages.length - 1]?.nextCursor != null
    })
  }
);
```

## Migration Strategy

1. Remove unused row model imports
2. Remove unused table configuration
3. Create specialized server-side table utilities
4. Document that filtering happens server-side
5. Consider removing TanStack table if only using display features
6. Measure bundle size reduction

## Performance Impact

- Reduces initialization overhead
- Smaller bundle size (remove unused features)
- Clearer code intent
- Less memory usage
- Faster table creation