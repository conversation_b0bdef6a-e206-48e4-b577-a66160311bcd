# Console Log Removal Optimization

## TL;DR

Console.log statements in production expose sensitive data and impact performance. Using proper logging services and build-time removal ensures clean, secure production code.

## The Problem

```tsx
console.log("identifyUser", id, userProperties); // Exposes user data!
```

Issues:

1. **Security risk**: Sensitive data visible in browser console
2. **Performance impact**: String concatenation and output
3. **Unprofessional**: Debug logs in production
4. **Bundle size**: Unnecessary code in production

## Solutions

### Solution 1: Build-Time Removal (Recommended)

Configure Next.js to strip console statements:

```js
// next.config.js
const nextConfig = {
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'] // Keep errors and warnings
    } : false
  }
};
```

### Solution 2: Development-Only Logs

```tsx
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info');
}
```

### Solution 3: Proper Logging Service

```tsx
import { log } from '@watt/common/src/utils/axiom-logger';

// Structured logging with appropriate levels
log.debug('User identified', { userId: id });
log.info('Feature accessed', { feature: 'analytics' });
log.error('Operation failed', { error, context });
```

## Implementation Strategy

1. **Audit existing logs**: Find all console.* usage
2. **Categorize by purpose**:
   - Debug info → Remove or use log.debug
   - Errors → Keep as console.error or log.error
   - User data → Remove completely
3. **Replace with proper logging**
4. **Configure build process**
5. **Add ESLint rule**

## ESLint Configuration

```json
{
  "rules": {
    "no-console": ["error", {
      "allow": ["warn", "error"]
    }]
  }
}
```

## Benefits

1. **Security**: No data leaks in production
2. **Performance**: Smaller bundles, faster execution
3. **Professionalism**: Clean production environment
4. **Debugging**: Structured logs in development
