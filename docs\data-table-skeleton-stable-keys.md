# Data Table Skeleton Stable Keys Optimization

## TL;DR

The DataTableSkeleton component uses `crypto.randomUUID()` for React keys, causing unnecessary DOM operations and breaking reconciliation. Using stable, memoized skeleton data with index-based keys fixes this issue.

## The Problem

Current implementation has multiple issues:

1. `crypto.randomUUID()` generates new keys on every render
2. React treats elements as completely new, causing full unmount/remount
3. Breaks CSS transitions and animations
4. Creates memory pressure from constant UUID generation
5. Incorrect cellWidth access using `cellWidths[0]` for all cells

## Current Code

```tsx
// Problematic: New UUIDs on every render
{Array.from({ length: searchableColumnCount }).map(() => (
  <Skeleton key={crypto.randomUUID()} className="h-7 w-40 lg:w-60" />
))}

// Also problematic: Using cellWidths[0] for all cells
style={{
  width: cellWidths[0], // Should use cellWidths[j]
  minWidth: shrinkZero ? cellWidths[0] : "auto"
}}
```

## Optimized Solution

```tsx
import { useMemo } from "react";

export function DataTableSkeleton({...props}) {
  // Create stable skeleton data structures
  const searchSkeletons = useMemo(
    () => Array.from({ length: searchableColumnCount }, (_, i) => ({
      id: `search-${i}`,
      key: `search-skeleton-${i}`
    })),
    [searchableColumnCount]
  );

  const filterSkeletons = useMemo(
    () => Array.from({ length: filterableColumnCount }, (_, i) => ({
      id: `filter-${i}`,
      key: `filter-skeleton-${i}`
    })),
    [filterableColumnCount]
  );

  const rowSkeletons = useMemo(
    () => Array.from({ length: rowCount }, (_, i) => ({
      id: `row-${i}`,
      key: `row-skeleton-${i}`,
      cells: Array.from({ length: columnCount }, (_, j) => ({
        id: `cell-${i}-${j}`,
        key: `cell-skeleton-${i}-${j}`
      }))
    })),
    [rowCount, columnCount]
  );

  // Use stable keys in render
  {searchSkeletons.map(({ key }) => (
    <Skeleton key={key} className="h-7 w-40 lg:w-60" />
  ))}
}
```

## Performance Impact

### Before

- New UUIDs generated on every render
- Full DOM reconstruction: ~10-20ms per render
- Broken React reconciliation
- Memory allocation for UUID strings

### After

- Stable keys based on indices
- Proper React reconciliation: <1ms updates
- Smooth animations preserved
- Minimal memory allocation

## Additional Optimizations

1. **Memoize skeleton structures** to prevent array recreation
2. **Fix cellWidth indexing** to use proper column index
3. **Extract skeleton components** for better reusability
4. **Consider virtualization** for large row counts

## Implementation Benefits

1. **Predictable behavior**: Keys based on data structure
2. **Better debugging**: Meaningful keys in React DevTools
3. **Performance**: Eliminates unnecessary DOM operations
4. **Memory efficiency**: No constant string allocation
5. **Type safety**: Strongly typed skeleton structures
