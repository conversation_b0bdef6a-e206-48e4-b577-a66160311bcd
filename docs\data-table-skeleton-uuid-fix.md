# Data Table Skeleton UUID Generation Fix

## TL;DR

The DataTableSkeleton component generates new UUIDs on every render, breaking <PERSON><PERSON>'s reconciliation and causing unnecessary DOM updates. Using stable keys based on indices fixes this issue.

## The Problem

Using `uuid()` as React keys in render methods causes:

1. New keys on every render
2. <PERSON>act treats elements as completely new
3. Full unmount/remount of skeleton elements
4. Lost animations and transitions
5. Performance degradation

Current problematic code:

```tsx
{Array.from({ length: searchableColumnCount }).map(() => (
  <Skeleton key={uuid()} className="h-7 w-40 lg:w-60" />
))}
```

## The Solution

Use stable, index-based keys for skeleton elements:

```tsx
{Array.from({ length: searchableColumnCount }).map((_, index) => (
  <Skeleton key={`search-skeleton-${index}`} className="h-7 w-40 lg:w-60" />
))}
```

## Performance Impact

### Before

- Full DOM reconstruction on every render
- ~5-10ms per skeleton row
- Breaks CSS transitions
- Causes layout thrashing

### After

- Proper React reconciliation
- <1ms per update
- Smooth animations
- Minimal DOM updates

## Implementation

Replace all `uuid()` calls with descriptive, index-based keys:

- Search skeletons: `search-skeleton-${index}`
- Filter skeletons: `filter-skeleton-${index}`
- Column headers: `header-skeleton-${index}`
- Table cells: `cell-skeleton-${rowIndex}-${colIndex}`

## Additional Benefits

1. Predictable element persistence
2. Better debugging (meaningful keys)
3. Reduced memory allocation
4. Improved animation performance
