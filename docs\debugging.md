# Debugging performance issues in NextJS

<https://github.com/vercel/next.js/discussions/73412>

## Trace profiling and viewing in Jaeger

1. First remove the .next folder.

2. Use [Turbopack tracing](https://nextjs.org/docs/app/guides/local-development?utm_source=chatgpt.com#turbopack-tracing) to generate the traces.

```shell
NEXT_CPU_PROF=1 NEXT_TURBOPACK_TRACING=1 pnpm turbo dev
```

2. Ensure you have [Rust](https://www.rust-lang.org/tools/install) installed and the `cargo` command available and run [Jaeger](https://www.jaegertracing.io/docs/2.4/getting-started/)

```shell
docker run --rm --name jaeger \
  -p 16686:16686 \
  -p 4317:4317 \
  -p 4318:4318 \
  -p 5778:5778 \
  -p 9411:9411 \
  jaegertracing/jaeger:2.4.0
```

3. Load the app <http://localhost:3000/authentication/login>

4.Ingest the trace file into <PERSON><PERSON><PERSON>. If you are not in the right directory and the rust script can't find your file you will NOT get a jaeger url to click. So double check the cwd and path to the trace file.

```shell
git clone https://github.com/vercel/next.js.git ~/workdir/next.js
cd ~/workdir/next.js/scripts/send-trace-to-jaeger
cargo run ~/workdir/watt/.next/dev/trace

    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.35s
     Running `/Users/<USER>/code/next.js/target/debug/send-trace-to-jaeger trace`

Jaeger trace will be available on http://127.0.0.1:16686/trace/d98c390dcc1b3a5d
body = Ok("json: cannot unmarshal bool into Go struct field .Alias.tags of type string\n")
body = Ok("json: cannot unmarshal bool into Go struct field .Alias.tags of type string\n")
body = Ok("json: cannot unmarshal bool into Go struct field .Alias.tags of type string\n")
body = Ok("json: cannot unmarshal bool into Go struct field .Alias.tags of type string\n")
body = Ok("json: cannot unmarshal bool into Go struct field .Alias.tags of type string\n")
body = Ok("")

```
