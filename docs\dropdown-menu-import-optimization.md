# Dropdown Menu Import Optimization

**Issue:** Using `import * as DropdownMenuPrimitive` prevents tree-shaking and includes unused code.

**Location:** `/apps/crm/src/components/ui/dropdown-menu.tsx`

**Current Problem:**

```typescript
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
```

**Solution:** Use named imports:

```typescript
import {
  Root,
  Trigger,
  Group,
  Portal,
  Sub,
  RadioGroup,
  SubTrigger,
  SubContent,
  Content,
  Item,
  CheckboxItem,
  RadioItem,
  Label,
  Separator,
  ItemIndicator
} from "@radix-ui/react-dropdown-menu";
```

**Benefits:**

- Reduces bundle size by only importing what's used
- Enables tree-shaking
- Improves build performance
- Makes dependencies explicit
