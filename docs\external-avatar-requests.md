# External Avatar Requests

## Issue Description

The `TeamSwitcher` component makes external HTTP requests to vercel.sh for avatar images on every render. These requests are made for each team option and can slow down the UI, especially with poor network conditions.

## Problem Code

In `apps/crm/src/app/account/components/team-switcher.tsx`:

```tsx
<Avatar className="mr-2 h-5 w-5">
  <AvatarImage
    src={`https://avatar.vercel.sh/${selectedTeam.value}.png`}
    alt={selectedTeam.label}
  />
  <AvatarFallback>SC</AvatarFallback>
</Avatar>
```

And in the dropdown:

```tsx
{group.teams.map(team => (
  <CommandItem key={team.value}>
    <Avatar className="mr-2 h-5 w-5">
      <AvatarImage
        src={`https://avatar.vercel.sh/${team.value}.png`}
        alt={team.label}
      />
      <AvatarFallback>SC</AvatarFallback>
    </Avatar>
  </CommandItem>
))}
```

## Why This Is a Problem

1. **Network dependency**: UI performance depends on external service availability
2. **Render blocking**: Avatar loading can delay component rendering
3. **Repeated requests**: Same avatars fetched multiple times
4. **No caching strategy**: Images re-downloaded on every page load
5. **CORS issues**: External domains can introduce CORS-related delays

## Optimized Solution

Use local avatars or a proper image optimization strategy:

```tsx
// Option 1: Use local avatar generation
import { generateAvatar } from '@/utils/avatar';

<Avatar className="mr-2 h-5 w-5">
  <AvatarImage
    src={generateAvatar(team.value)}
    alt={team.label}
  />
  <AvatarFallback>{team.label.slice(0, 2).toUpperCase()}</AvatarFallback>
</Avatar>

// Option 2: Use Next.js Image with optimization
import Image from 'next/image';

<Avatar className="mr-2 h-5 w-5">
  <Image
    src={`/avatars/${team.value}.png`}
    alt={team.label}
    width={20}
    height={20}
    loading="lazy"
  />
</Avatar>

// Option 3: Use CSS-based avatars
<Avatar className="mr-2 h-5 w-5" style={{
  background: `hsl(${hashCode(team.value) % 360}, 70%, 60%)`
}}>
  <span>{team.label.charAt(0)}</span>
</Avatar>
```

## Migration Strategy

1. Audit all external image dependencies
2. Generate local avatars or use CSS-based solutions
3. If external images are needed, proxy through your own CDN
4. Implement proper caching headers
5. Use Next.js Image component for optimization
6. Add loading="lazy" for below-the-fold images

## Performance Impact

- Eliminates network requests for avatars
- Reduces Time to Interactive (TTI)
- Improves performance on slow networks
- Ensures consistent avatar display
- Reduces bandwidth usage