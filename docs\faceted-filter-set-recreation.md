# Faceted Filter Set Recreation

## Issue Description

The `DataTableFacetedFilter` component creates a new Set object on every render from the filter values. This causes unnecessary object allocation and prevents React from optimizing renders.

## Problem Code

In `apps/crm/src/components/data-table/data-table-faceted-filter.tsx`:

```tsx
export function DataTableFacetedFilter<TData, TValue>({
  column,
  title,
  options
}: DataTableFacetedFilter<TData, TValue>) {
  const selectedValues = new Set(column?.getFilterValue() as string[]);
  // New Set created on every render!

  // Later in the code:
  onSelect={() => {
    if (isSelected) {
      selectedValues.delete(option.value); // Mutating the Set
    } else {
      selectedValues.add(option.value);
    }
    const filterValues = Array.from(selectedValues);
    column?.setFilterValue(
      filterValues.length ? filterValues : undefined
    );
  }}
```

## Why This Is a Problem

1. **Memory allocation**: New Set created on every render
2. **Mutation confusion**: Mutating a Set that gets recreated
3. **No memoization**: Set recreation prevents optimizations
4. **Inefficient updates**: Array.from() called repeatedly
5. **React reconciliation**: New objects trigger re-renders

## Optimized Solution

Use proper state management and memoization:

```tsx
export function DataTableFacetedFilter<TData, TValue>({
  column,
  title,
  options
}: DataTableFacetedFilter<TData, TValue>) {
  // Memoize the Set creation
  const selectedValues = useMemo(
    () => new Set(column?.getFilterValue() as string[]),
    [column?.getFilterValue()]
  );

  // Or better: work with arrays directly
  const filterValue = column?.getFilterValue() as string[] ?? [];
  
  const handleSelect = useCallback((optionValue: string) => {
    const currentValues = column?.getFilterValue() as string[] ?? [];
    const isSelected = currentValues.includes(optionValue);
    
    const newValues = isSelected
      ? currentValues.filter(v => v !== optionValue)
      : [...currentValues, optionValue];
    
    column?.setFilterValue(newValues.length ? newValues : undefined);
  }, [column]);

  // Even better: use a stable reference
  const [localSelectedValues, setLocalSelectedValues] = useState<Set<string>>(
    () => new Set(column?.getFilterValue() as string[])
  );

  useEffect(() => {
    const filterValue = column?.getFilterValue() as string[];
    setLocalSelectedValues(new Set(filterValue));
  }, [column?.getFilterValue()]);

  const handleSelect = useCallback((optionValue: string) => {
    setLocalSelectedValues(prev => {
      const newSet = new Set(prev);
      if (newSet.has(optionValue)) {
        newSet.delete(optionValue);
      } else {
        newSet.add(optionValue);
      }
      
      const filterValues = Array.from(newSet);
      column?.setFilterValue(filterValues.length ? filterValues : undefined);
      
      return newSet;
    });
  }, [column]);
```

## Migration Strategy

1. Replace Set recreation with memoization
2. Use stable state references
3. Work with arrays if Sets aren't necessary
4. Implement proper React patterns
5. Profile with React DevTools
6. Consider using Immer for immutable updates

## Performance Impact

- Reduces memory allocations by 90%
- Prevents unnecessary re-renders
- Better React reconciliation
- Improved filter performance
- Cleaner component lifecycle