# Feature Toggles Runtime Environment Checks

## Issue Description

The feature toggles system calls environment check functions on every import, and these checks are used at runtime in components. This creates unnecessary overhead and prevents build-time optimization.

## Problem Code

In `apps/crm/src/feature-toggles.ts`:

```tsx
import {
  isDevEnvironment,
  isLocalEnvironment
} from "@watt/common/src/utils/is-production-environment";

const isLocalOrDev = isLocalEnvironment() || isDevEnvironment();

const enabled = isLocalOrDev;

export const featureToggles = {
  routes: {
    dashboard: enabled,
    notes: enabled,
    sites: enabled,
    people: enabled,
    // ... many more
  }
} as const;
```

And usage in `apps/crm/src/app/account/people/page.tsx`:

```tsx
export default function PeoplePage() {
  if (!featureToggles.routes.people) {
    notFound(); // Runtime check on every render
  }
  // ...
}
```

## Why This Is a Problem

1. **Runtime overhead**: Environment checks on every page load
2. **No tree shaking**: Dead code can't be eliminated at build time
3. **Bundle includes all code**: Even disabled features ship to production
4. **Runtime redirects**: Uses notFound() instead of build-time exclusion
5. **Performance cost**: Unnecessary checks in production

## Optimized Solution

Use build-time feature flags:

```tsx
// feature-toggles.ts - Use environment variables at build time
const FEATURES = {
  routes: {
    dashboard: process.env.NEXT_PUBLIC_FEATURE_DASHBOARD === 'true',
    notes: process.env.NEXT_PUBLIC_FEATURE_NOTES === 'true',
    sites: process.env.NEXT_PUBLIC_FEATURE_SITES === 'true',
    people: process.env.NEXT_PUBLIC_FEATURE_PEOPLE === 'true',
  }
} as const;

export const featureToggles = FEATURES;

// Even better: Use Next.js middleware for routing
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const DISABLED_ROUTES = process.env.NODE_ENV === 'production' ? [
  '/account/people',
  '/account/sites',
  '/account/notes'
] : [];

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  if (DISABLED_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.rewrite(new URL('/404', request.url));
  }
}

// Best: Use build-time route generation
// next.config.js
module.exports = {
  async redirects() {
    const isProduction = process.env.NODE_ENV === 'production';
    
    if (isProduction) {
      return [
        {
          source: '/account/people',
          destination: '/404',
          permanent: false,
        },
        // ... other disabled routes
      ];
    }
    
    return [];
  },
};

// For components: Use build-time conditionals
// In page component
export default function PeoplePage() {
  // No runtime check needed - route won't exist in production
  return <PeopleDataTable />;
}
```

## Migration Strategy

1. Move feature flags to environment variables
2. Use Next.js middleware for route protection
3. Implement build-time redirects in next.config.js
4. Remove runtime checks from components
5. Use conditional exports for feature modules
6. Measure bundle size reduction

## Performance Impact

- Eliminates runtime environment checks
- Enables dead code elimination
- Reduces bundle size for production
- Faster page loads
- No runtime redirects for disabled features