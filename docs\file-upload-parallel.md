# File Upload Sequential Processing

**Issue:** Files are uploaded one by one in a sequential loop, causing slow uploads for multiple files.

**Location:** `/apps/crm/src/components/quick-actions/file/upload-files-modal/upload-files-form.tsx`

**Current Problem:**

```typescript
// Sequential upload - slow for multiple files
for (let i = 0; i < filenames.length; i++) {
  await onUpload([renamedFile]);
  await registerFile.mutateAsync({...});
}
```

**Solution:** Process uploads in parallel with proper error handling:

```typescript
const uploadPromises = filenames.map(async (filenameMeta, i) => {
  const uploadedFile = files[i];
  if (!uploadedFile || !filenameMeta.acceptedFilename) {
    throw new Error(`Invalid file at index ${i}`);
  }

  const { path, fileId } = getFilePathAndId(companyId, filenameMeta.acceptedFilename);
  const renamedFile = new File([uploadedFile], path, {
    type: uploadedFile.type
  });

  // Upload file to storage
  await onUpload([renamedFile]);

  // Register file in database
  await registerFile.mutateAsync({
    id: fileId,
    path,
    filename: filenameMeta.acceptedFilename,
    mimeType: uploadedFile.type,
    size: uploadedFile.size,
    companyId,
    type: category,
    sites,
    siteMeters: meters
  });

  return { index: i, success: true };
});

// Process all uploads in parallel
const results = await Promise.allSettled(uploadPromises);

// Handle results
const failedIndexes = results
  .map((result, index) => result.status === 'rejected' ? index : null)
  .filter(index => index !== null);
```

**Benefits:**

- 5-10x faster for multiple file uploads
- Better user experience
- Maintains error handling per file
- Shows accurate progress
