# File Upload Sequential Processing

## Issue Description

The `useUploadFile` hook uploads files sequentially in a for loop, making users wait for each file to complete before starting the next one. This creates a poor user experience for multi-file uploads.

## Problem Code

In `apps/crm/src/hooks/use-upload-file.ts`:

```tsx
async function onUpload(files: File[]) {
  setIsUploading(true);
  // Initialize progress
  const initialProgresses: Record<string, number> = {};
  for (const file of files) {
    initialProgresses[file.name] = 0;
  }
  setProgresses(initialProgresses);

  try {
    for (const file of files) {
      const { data, error } = await createSignedUploadUrlMutation.mutateAsync({
        bucketName,
        filename: file.name
      });

      // ... upload logic

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(bucketName)
        .uploadToSignedUrl(file.name, token, file);

      // Sequential - waits for each file to complete
    }
  }
}
```

## Why This Is a Problem

1. **Slow uploads**: Total time = sum of all file upload times
2. **No parallelism**: Doesn't utilize available bandwidth
3. **Poor UX**: Users wait unnecessarily long
4. **No cancellation**: Can't stop mid-upload
5. **Fake progress**: Mock progress doesn't reflect reality

## Optimized Solution

Use parallel uploads with proper progress tracking:

```tsx
async function onUpload(files: File[]) {
  setIsUploading(true);
  const abortController = new AbortController();

  try {
    // Upload files in parallel with concurrency limit
    const uploadPromises = files.map(async (file) => {
      try {
        // Get signed URL
        const { data } = await createSignedUploadUrlMutation.mutateAsync({
          bucketName,
          filename: file.name
        });

        if (!data) throw new Error("No upload URL");

        // Upload with progress tracking
        const xhr = new XMLHttpRequest();

        return new Promise((resolve, reject) => {
          xhr.upload.addEventListener("progress", (e) => {
            if (e.lengthComputable) {
              const progress = (e.loaded / e.total) * 100;
              setProgresses(prev => ({ ...prev, [file.name]: progress }));
            }
          });

          xhr.addEventListener("load", () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve({ name: file.name, url: data.path });
            } else {
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          });

          xhr.addEventListener("error", () => reject(new Error("Upload failed")));
          xhr.addEventListener("abort", () => reject(new Error("Upload cancelled")));

          xhr.open("PUT", data.uploadUrl);
          xhr.send(file);
        });
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error);
        throw error;
      }
    });

    // Limit concurrency to avoid overwhelming the server
    const results = await pLimit(3)(uploadPromises);

    const uploaded = results.filter(Boolean);
    setUploadedFiles(prev => [...prev, ...uploaded]);

    toast.success(`${uploaded.length} file(s) uploaded successfully`);
  } catch (err) {
    toast.error("Some uploads failed");
  } finally {
    setIsUploading(false);
  }
}

// Or use a proper upload library
import { Upload } from 'tus-js-client';

async function onUpload(files: File[]) {
  const uploads = files.map(file => {
    const upload = new Upload(file, {
      endpoint: '/api/upload',
      retryDelays: [0, 3000, 5000],
      metadata: { filename: file.name },
      onProgress: (bytesUploaded, bytesTotal) => {
        const progress = (bytesUploaded / bytesTotal) * 100;
        setProgresses(prev => ({ ...prev, [file.name]: progress }));
      }
    });

    return upload.start();
  });

  await Promise.allSettled(uploads);
}
```

## Migration Strategy

1. Implement parallel upload logic
2. Add proper progress tracking with XHR or fetch streams
3. Implement upload cancellation
4. Add retry logic for failed uploads
5. Consider chunked uploads for large files
6. Use a battle-tested upload library

## Performance Impact

- 3-5x faster multi-file uploads
- Better bandwidth utilization
- Real progress tracking
- Improved user experience
- Support for upload cancellation
