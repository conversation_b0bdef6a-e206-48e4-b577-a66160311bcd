# FilesGrid React.memo Optimization

## TL;DR

The FilesGrid component re-renders unnecessarily when parent components update, even if the `companyId` prop hasn't changed. This causes expensive re-filtering of files on every parent render.

## The Problem

The current FilesGrid component performs complex filtering operations in a useMemo hook that processes nested arrays (files, sites, siteMeters). Without React.memo, the component re-renders whenever its parent re-renders, triggering:

1. Re-execution of the component function
2. Re-creation of event handlers
3. Re-evaluation of the useMemo dependency array
4. Potential re-fetching in useCompanyFilesSearch hook

## Current Code

```tsx
export function FilesGrid({ companyId }: FilesGridProps) {
  // Component implementation
}
```

## Optimized Code

```tsx
import { memo } from "react";

function FilesGridComponent({ companyId }: FilesGridProps) {
  // Same implementation
}

export const FilesGrid = memo(FilesGridComponent, (prevProps, nextProps) => {
  // Only re-render if companyId changes
  return prevProps.companyId === nextProps.companyId;
});
```

## Alternative: Move Filtering to Server

For better performance with large datasets, move the filtering logic to the server:

```tsx
const { files, isLoading } = useCompanyFilesSearch({
  companyId,
  searchQuery: filterData.query // Add search parameter
});

// Remove client-side filteredFiles useMemo
```

## Performance Impact

### Before

- Re-renders on every parent update
- O(n²) filtering on each render for n files with m sites/meters
- ~15-20ms per unnecessary render

### After

- Only re-renders when companyId changes
- Filtering only runs when files or query changes
- Eliminates 90%+ of re-renders

## Migration Steps

1. Wrap component in React.memo with comparison function
2. Test that file operations still work correctly
3. Monitor re-renders with React DevTools Profiler
4. Consider server-side filtering for datasets > 100 items

## Additional Optimizations

1. **Virtualization**: For > 50 files, implement react-window
2. **Debounced Search**: Add 300ms debounce to search input
3. **Lazy Loading**: Load files in batches of 20-50
4. **Server Filtering**: Move search logic to API endpoint
