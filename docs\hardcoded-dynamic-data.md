# Hardcoded Data in Client Components Preventing Static Optimization

## TL;DR

**Dashboard components are marked as "use client" with hardcoded static data, preventing static optimization.** These components re-render on every page load despite showing the same data.

## The Problem

Client components with static data cause:
- **Unnecessary hydration** - Static data requires client JS
- **Larger bundles** - Data included in JavaScript
- **Re-renders on navigation** - Static data re-processes
- **No server optimization** - Can't leverage RSC benefits
- **Wasted CPU cycles** - Computing same values repeatedly

## Current Issues Found

### Real Examples from Codebase

```typescript
// apps/crm/src/app/account/dashboard/page.tsx - lines 26-117
export default function DashboardPage() {
  // Hardcoded data defined in component!
  const dashboardStatsCardsData: DashboardStatCardProps[] = [
    {
      title: "My Pool",
      stats: [
        {
          title: "ASSIGNED TO ME",
          value: 2100, // Static!
          color: "bg-green-theme"
        },
        // ... more hardcoded values
      ]
    }
  ];

  // More hardcoded data
  const myKPICardData: MyKPICardProps = {
    dialAttempts: 0,
    connectedCalls: 0,
    avgCallTime: setSeconds(setMinutes(setHours(new Date(0), 0), 12), 45),
    missedCalls: 0,
    unansweredCalls: 0
  };
}

// apps/crm/src/app/account/dashboard/components/my-kpi-card.tsx
"use client"; // Forces client-side for static content!

export function MyKPICard(props: MyKPICardProps) {
  // Static data array created on every render!
  const data: KPIItemProps[] = [
    {
      title: "Dial Attempts",
      value: props.dialAttempts,
      bgColor: "bg-dark-theme",
      icon: PhoneOutgoing
    },
    // ... more items
  ];
}
```

## Optimized Solutions

### ✅ Move Static Data Outside Components

```typescript
// constants/dashboard-data.ts
export const DASHBOARD_STATS_DATA: DashboardStatCardProps[] = [
  {
    title: "My Pool",
    stats: [
      { title: "ASSIGNED TO ME", value: 2100, color: "bg-green-theme" },
      { title: "UNCONTACTED", value: 70, color: "bg-red-theme" },
      // ... more
    ]
  },
  // ... more cards
] as const; // Make immutable

export const DEFAULT_KPI_DATA: MyKPICardProps = {
  dialAttempts: 0,
  connectedCalls: 0,
  avgCallTime: new Date(0, 0, 0, 0, 12, 45),
  missedCalls: 0,
  unansweredCalls: 0
} as const;
```

### ✅ Server Components for Static Content

```typescript
// Make dashboard a server component
// app/account/dashboard/page.tsx
import { DASHBOARD_STATS_DATA } from '@/constants/dashboard-data';

export default function DashboardPage() {
  return (
    <div className="flex-col px-4 md:flex">
      <h1 className="font-bold text-xl tracking-tight">Dashboard</h1>
      
      {/* Static cards as server components */}
      <div className="grid gap-10">
        {DASHBOARD_STATS_DATA.map((card) => (
          <DashboardStatCard key={card.title} {...card} />
        ))}
      </div>
      
      {/* Dynamic content in client boundary */}
      <DynamicKPISection />
    </div>
  );
}

// Server component for static card
function DashboardStatCard({ title, stats }: DashboardStatCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {stats.map(stat => (
          <StatItem key={stat.title} {...stat} />
        ))}
      </CardContent>
    </Card>
  );
}
```

### ✅ Memoize Static Computations

```typescript
// For client components that must remain client-side
"use client";

// Move outside component or memoize
const KPI_CONFIG = [
  { title: "Dial Attempts", icon: PhoneOutgoing, bgColor: "bg-dark-theme" },
  { title: "Missed Calls", icon: PhoneIncoming, bgColor: "bg-red-theme" },
  // ... more
] as const;

export function MyKPICard(props: MyKPICardProps) {
  // Memoize data transformation
  const kpiData = useMemo(() => 
    KPI_CONFIG.map(config => ({
      ...config,
      value: props[config.key] ?? 0
    })),
    [props]
  );
  
  return (
    <Card>
      {kpiData.map(item => (
        <KPIItem key={item.title} {...item} />
      ))}
    </Card>
  );
}
```

### ✅ Static JSON Import

```typescript
// dashboard-data.json
{
  "statsCards": [
    {
      "title": "My Pool",
      "stats": [
        { "title": "ASSIGNED TO ME", "value": 2100, "color": "bg-green-theme" }
      ]
    }
  ]
}

// Import at build time
import dashboardData from './dashboard-data.json';

export default function DashboardPage() {
  // Data is inlined at build time
  return <DashboardView data={dashboardData} />;
}
```

## Performance Optimization Patterns

### 1. Separate Static and Dynamic

```typescript
// Static shell (server component)
export default function Dashboard() {
  return (
    <DashboardLayout>
      <StaticDashboardCards />
      <Suspense fallback={<KPISkeleton />}>
        <DynamicKPIData />
      </Suspense>
    </DashboardLayout>
  );
}

// Static content
function StaticDashboardCards() {
  return STATIC_CARDS.map(card => 
    <ServerCard key={card.id} {...card} />
  );
}

// Dynamic content
async function DynamicKPIData() {
  const kpis = await fetchUserKPIs();
  return <ClientKPICard data={kpis} />;
}
```

### 2. Configuration-Driven UI

```typescript
// Define UI configuration separately
const DASHBOARD_CONFIG = {
  sections: [
    {
      id: 'stats',
      type: 'grid',
      columns: 3,
      items: [/* ... */]
    },
    {
      id: 'kpi',
      type: 'card',
      dynamic: true
    }
  ]
} as const;

// Render based on configuration
export default function Dashboard() {
  return (
    <DashboardRenderer 
      config={DASHBOARD_CONFIG}
      dynamicData={fetchDynamicData}
    />
  );
}
```

## Performance Impact

### Before (Client Components with Static Data)
- Bundle size: +25KB for dashboard
- Hydration time: 200ms
- Re-renders on navigation: Yes
- Memory usage: Higher (data in JS heap)

### After (Server Components)
- Bundle size: +5KB (80% reduction)
- Hydration time: 50ms (75% faster)
- Re-renders on navigation: No
- Memory usage: Lower

## Best Practices

1. **Extract static data** - Move to constants/config files
2. **Use server components** - For static content
3. **Memoize computations** - If client component needed
4. **Split static/dynamic** - Use Suspense boundaries
5. **Configuration over code** - Define UI structure as data

## Common Anti-patterns to Fix

### 1. Inline Array Creation

```typescript
// ❌ Bad - New array every render
function Component() {
  const items = [
    { id: 1, name: 'Item 1' },
    { id: 2, name: 'Item 2' }
  ];
}

// ✅ Good - Static reference
const ITEMS = [
  { id: 1, name: 'Item 1' },
  { id: 2, name: 'Item 2' }
] as const;
```

### 2. Date Creation in Render

```typescript
// ❌ Bad - New date every render
const avgCallTime = setSeconds(setMinutes(setHours(new Date(0), 0), 12), 45);

// ✅ Good - Static value
const AVG_CALL_TIME = new Date('1970-01-01T00:12:45');
```

## Migration Checklist

- [ ] Identify components with hardcoded data
- [ ] Extract static data to constants
- [ ] Convert to server components where possible
- [ ] Add memoization for client components
- [ ] Split static and dynamic sections
- [ ] Remove unnecessary "use client"
- [ ] Test bundle size reduction

## Conclusion

Hardcoded static data in client components wastes resources and prevents optimization. Moving this data to server components or external constants can significantly reduce bundle size and improve performance.