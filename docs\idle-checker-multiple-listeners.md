# Idle Checker Multiple Event Listeners

## Issue Description

The `useIdle` hook attaches event listeners to 8 different DOM events and creates a BroadcastChannel on every component mount. With multiple components using this hook, it can lead to hundreds of duplicate event listeners.

## Problem Code

In `apps/crm/src/hooks/use-idle.ts`:

```tsx
const DEFAULT_EVENTS: (keyof DocumentEventMap)[] = [
  "mousemove",
  "mousedown", 
  "scroll",
  "wheel",
  "touchmove",
  "keydown",
  "touchstart",
  "focus"
];

const setup = useCallback(() => {
  if (!enabled) return;

  for (const event of events) {
    document.addEventListener(event, throttledSetLastActivity, {
      passive: true
    });
  }

  broadcastChannel.current = new BroadcastChannel("idle-checker");
  broadcastChannel.current.onmessage = handleBroadcastMessage;
}, [events, throttledSetLastActivity, enabled, handleBroadcastMessage]);
```

## Why This Is a Problem

1. **Duplicate listeners**: Each component instance adds 8 event listeners
2. **Memory overhead**: BroadcastChannel created per component
3. **Event handling cost**: Every user interaction triggers multiple handlers
4. **Battery drain**: Constant event processing on mobile devices
5. **Memory leaks**: Complex cleanup logic prone to errors

## Optimized Solution

Use a singleton pattern or context provider:

```tsx
// Create a singleton idle manager
class IdleManager {
  private static instance: IdleManager;
  private listeners = new Set<(lastActivity: number) => void>();
  private lastActivity = Date.now();
  private broadcastChannel: BroadcastChannel;
  private eventsAttached = false;

  private constructor() {
    this.broadcastChannel = new BroadcastChannel("idle-checker");
    this.broadcastChannel.onmessage = this.handleBroadcast;
  }

  static getInstance() {
    if (!IdleManager.instance) {
      IdleManager.instance = new IdleManager();
    }
    return IdleManager.instance;
  }

  subscribe(callback: (lastActivity: number) => void) {
    this.listeners.add(callback);
    
    if (!this.eventsAttached && this.listeners.size > 0) {
      this.attachEvents();
    }

    return () => {
      this.listeners.delete(callback);
      if (this.listeners.size === 0) {
        this.detachEvents();
      }
    };
  }

  private attachEvents = () => {
    if (this.eventsAttached) return;
    
    // Single throttled handler for all events
    const handler = throttle(this.updateActivity, 1000);
    
    EVENTS.forEach(event => {
      document.addEventListener(event, handler, { passive: true });
    });
    
    this.eventsAttached = true;
  };

  private updateActivity = () => {
    this.lastActivity = Date.now();
    this.notifyListeners();
  };
}

// Simplified hook
export function useIdle({ onIdle, timeout }: UseIdleOptions) {
  const manager = useMemo(() => IdleManager.getInstance(), []);
  
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const checkIdle = (lastActivity: number) => {
      clearTimeout(timeoutId);
      
      const timeUntilIdle = timeout - (Date.now() - lastActivity);
      
      if (timeUntilIdle <= 0) {
        onIdle();
      } else {
        timeoutId = setTimeout(() => onIdle(), timeUntilIdle);
      }
    };

    return manager.subscribe(checkIdle);
  }, [manager, onIdle, timeout]);
}
```

## Migration Strategy

1. Create a centralized idle detection service
2. Use React Context for idle state management
3. Implement singleton pattern for event listeners
4. Reduce number of tracked events
5. Use event delegation where possible
6. Consider using Intersection Observer for visibility

## Performance Impact

- Reduces event listeners from 8n to 8 (where n is component count)
- Single BroadcastChannel instance
- Lower memory usage
- Better battery life on mobile
- Cleaner component lifecycle