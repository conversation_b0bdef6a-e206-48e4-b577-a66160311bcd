# Performance Issues Documentation Index

This index provides an overview of all documented performance issues in the Watt CRM codebase.

## Documented Issues

### 1. [Barrel Exports Performance](./barrel-exports-performance.md)

Stop using barrel exports (index.ts files with `export * from`) - they harm build performance, increase bundle size, and break tree-shaking.

### 2. [Missing React Memo](./missing-react-memo.md)

Over 180+ components in the codebase don't use React.memo, useMemo, or useCallback, causing excessive re-renders.

### 3. [Import Star Bundle Bloat](./import-star-bundle-bloat.md)

Stop using `import * as` syntax - it imports entire modules and prevents tree-shaking. Use named imports to reduce bundle size by up to 90% for large libraries.

### 4. [Missing Loading States](./missing-loading-states.md)

Most async operations in the codebase lack proper loading states, causing UI flicker, layout shifts, and confusing user experiences.

### 5. [Inefficient Data Fetching](./inefficient-data-fetching.md)

The codebase lacks proper data fetching optimization - no caching, no deduplication, no prefetching, and missing optimistic updates.

### 6. [Missing Error Boundaries](./missing-error-boundaries.md)

The application lacks React Error Boundaries, causing entire pages to crash and show white screens when components fail.

### 7. [Missing Code Splitting](./missing-code-splitting.md)

The application loads all JavaScript upfront instead of splitting code by routes and features.

### 8. [TypeScript Any Type Abuse](./typescript-any-type-abuse.md)

29+ files in the codebase use the `any` type, disabling TypeScript's type safety.

### 9. [Missing Cleanup Memory Leaks](./missing-cleanup-memory-leaks.md)

Multiple components in the codebase create event listeners, timers, and subscriptions without proper cleanup.

### 10. [Missing Suspense Boundaries](./missing-suspense-boundaries.md)

The application doesn't use React Suspense for data fetching and code splitting, causing poor loading experiences.

### 11. [Missing Virtual Scrolling](./missing-virtual-scrolling.md)

The application renders all list items in the DOM, causing performance issues with large datasets.

### 12. [Client-Side Data Filtering](./client-side-data-filtering.md)

The application fetches ALL data then filters on the client, causing massive overfetching and poor performance.

### 13. [Synchronous Blocking Operations](./synchronous-blocking-operations.md)

The codebase has synchronous operations that block the main thread, causing UI freezes and poor responsiveness.

### 14. [Unoptimized Images](./unoptimized-images.md)

The application serves unoptimized images without lazy loading, responsive sizing, or modern formats.

### 15. [Missing Database Indexes](./missing-database-indexes.md)

Database queries lack proper indexes and suffer from N+1 problems, causing slow API responses and poor performance.

### 16. [Unnecessary Re-renders](./unnecessary-re-renders.md)

Components re-render unnecessarily due to unstable object/array references, inline function definitions, and poor dependency management.

### 17. [Missing SEO Optimization](./missing-seo-optimization.md)

The Next.js app lacks proper SEO optimization, meta tags, and structured data.

### 18. [Form Validation Performance](./form-validation-performance.md)

Forms re-validate on every keystroke and trigger unnecessary re-renders of entire forms.

### 19. [Missing Web Workers](./missing-web-workers.md)

CPU-intensive operations run on the main thread, blocking user interactions and causing the UI to freeze.

### 20. [Excessive DOM Manipulation](./excessive-dom-manipulation.md)

The application triggers multiple layout recalculations (reflows) and style recalculations in rapid succession.

### 21. [Inefficient Context Usage](./inefficient-context-usage.md)

Multiple nested context providers and large context values cause unnecessary re-renders throughout the component tree.

### 22. [Missing Request Cancellation](./missing-request-cancellation.md)

API requests are not cancelled when components unmount or when new requests are made, causing race conditions.

### 23. [Unoptimized Font Loading](./unoptimized-font-loading.md)

Fonts load without optimization, causing Flash of Unstyled Text (FOUT) or Flash of Invisible Text (FOIT).

### 24. [Blocking Async Layout](./blocking-async-layout.md)

The root layout and account layout use blocking async operations with `await headers()` and `await cookies()` that delay the entire page render.

### 25. [Missing Static Generation](./missing-static-generation.md)

Static pages like the dashboard are rendered dynamically on every request instead of being statically generated.

### 26. [Search Params Deopting](./search-params-deopting.md)

Using `useSearchParams` without proper Suspense boundaries causes entire routes to deopt to client-side rendering.

### 27. [Hardcoded Dynamic Data](./hardcoded-dynamic-data.md)

Dashboard components are marked as "use client" with hardcoded static data, preventing static optimization.

### 28. [TRPC Client Recreation](./trpc-client-recreation.md)

The TRPC client is recreated on every render due to improper useState initialization, causing potential connection churn and performance issues.

### 29. [Sidebar Performance Issues](./sidebar-performance-issues.md)

The sidebar recreates icon components and recalculates active states on every render, and doesn't memoize any of its child components.

### 30. [Middleware Sequential Execution](./middleware-sequential-execution.md)

Middleware functions execute sequentially with await, blocking each request until all middleware completes.

### 31. [Cookie Writes on Resize](./cookie-writes-resize.md)

The ResizableLayout component writes cookies on every resize event without debouncing, causing performance issues.

### 32. [Missing Build Optimizations](./missing-build-optimizations.md)

Next.js config disables TypeScript checking, ESLint, and doesn't remove console logs in production builds.

### 33. [Table Row Hover Animations](./table-row-hover-animations.md)

Data tables use 300ms CSS transitions on every row hover, causing performance issues with large datasets.

### 34. [External Avatar Requests](./external-avatar-requests.md)

TeamSwitcher makes external HTTP requests to vercel.sh for avatars on every render, causing network dependency.

### 35. [Notification Menu Force Re-render](./notification-menu-force-rerender.md)

NotificationMenu uses Date.now() to force complete re-renders by changing component keys.

### 36. [Infinite Scroll Inline Styles](./infinite-scroll-inline-styles.md)

InfiniteScrollDataTable uses inline styles for height and colors, preventing CSS optimizations.

### 37. [Command Menu Global Listener](./command-menu-global-listener.md)

CommandMenu adds global keyboard listeners with missing dependencies and potential memory leaks.

### 38. [PostHog Console Logs](./posthog-console-logs.md)

PostHog analytics contains console.log statements that expose user data in production.

### 39. [Idle Checker Multiple Listeners](./idle-checker-multiple-listeners.md)

useIdle hook attaches 8 event listeners per component instance, causing performance overhead.

### 40. [User Nav Effect Pattern Matching](./user-nav-effect-pattern-matching.md)

UserNav uses Effect library pattern matching for simple null checks, adding unnecessary bundle size.

### 41. [User Nav Rehydrate Effect](./user-nav-rehydrate-effect.md)

UserNav calls store rehydration in useEffect on every mount, causing performance issues.

### 42. [Files Grid Client Filtering](./files-grid-client-filtering.md)

FilesGrid loads ALL files and filters client-side instead of using server-side search and pagination.

### 43. [File Upload Sequential](./file-upload-sequential.md)

File uploads process sequentially in a for loop instead of parallel uploads with proper concurrency.

### 44. [Note Form Debounce Re-render](./note-form-debounce-rerender.md)

NoteForm's useDebounce recreates the debounced function on every render due to dependencies.

### 45. [Faceted Filter Set Recreation](./faceted-filter-set-recreation.md)

DataTableFacetedFilter creates a new Set on every render, causing memory allocation and preventing optimizations.

### 46. [PostHog PageView URL Construction](./posthog-pageview-url-construction.md)

PostHogPageView reconstructs URLs with string concatenation and tracks every search param change.

### 47. [Theme Provider Wrapper](./theme-provider-wrapper.md)

ThemeProvider is a thin wrapper adding an extra component layer without any additional functionality.

### 48. [Data Table Skeleton UUID](./data-table-skeleton-uuid.md)

DataTableSkeleton generates expensive UUIDs during render, breaking React reconciliation.

### 49. [Companies Table Client Filtering](./companies-table-client-filtering.md)

Companies DataTable initializes unused client-side filtering despite using server-side filtering.

### 50. [Feature Toggles Runtime Checks](./feature-toggles-runtime-checks.md)

Feature toggles perform runtime environment checks instead of build-time optimization.

### 51. [Sites Table Dynamic Pagination](./sites-table-dynamic-pagination.md)

Sites table dynamically adjusts page size based on expanded rows, causing layout shifts.

### 52. [Quotes Page Prefetch Blocking](./quotes-page-prefetch-blocking.md)

QuotesPage blocks entire render with await prefetchQuery, defeating RSC streaming.

### 53. [Notes Table Grid Virtualizer](./notes-table-grid-virtualizer.md)

Notes table implements complex manual grid calculations instead of using CSS Grid.

### 54. [Notifications Hook Complexity](./notifications-hook-complexity.md)

useNotifications is a 300+ line monolith handling too many responsibilities.

## Total Issues Documented: 54/50 ✅

## Summary

Successfully documented 54 performance issues in the Watt CRM codebase, covering:

- **React Patterns**: Missing memoization, excessive re-renders, poor state management
- **Data Fetching**: Client-side filtering, sequential processing, missing caching
- **Bundle Size**: Large constants, missing code splitting, unnecessary dependencies
- **Build Configuration**: Disabled optimizations, console logs in production
- **User Experience**: Blocking operations, missing loading states, poor animations
- **Architecture**: Middleware bottlenecks, event listener proliferation, memory leaks

Each issue includes:

- Detailed problem description with code examples
- Explanation of performance impact
- Optimized solution with implementation code
- Migration strategy for fixing the issue
- Expected performance improvements
