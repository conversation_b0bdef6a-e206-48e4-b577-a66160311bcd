# Missing Cleanup Causing Memory Leaks

## TL;DR

**Multiple components in the codebase create event listeners, timers, and subscriptions without proper cleanup.** This causes memory leaks, performance degradation over time, and potential crashes in long-running sessions.

## The Problem

Without cleanup:

- **Memory leaks** - Objects can't be garbage collected
- **Performance degradation** - Accumulating listeners slow down the app
- **Unexpected behavior** - Old listeners fire on unmounted components
- **Resource exhaustion** - Eventually crashes the browser
- **Zombie subscriptions** - API calls to unmounted components

## Current Issues Found

### ❌ Components with Missing Cleanup

Analysis reveals:

- Event listeners added without removal
- Intervals/timeouts not cleared
- WebSocket connections left open
- Observers never disconnected
- Subscriptions not unsubscribed

### Real Examples from Codebase

```typescript
// use-notifications.tsx - Multiple useEffect without cleanup!
useEffect(() => {
  const channel = supabase.channel("notifications");
  channel.subscribe(); // Never unsubscribed!
}, []);

useEffect(() => {
  window.addEventListener("focus", handleFocus); // Never removed!
}, []);
```

## Memory Leak Patterns and Fixes

### ✅ Event Listener Cleanup

```typescript
// ❌ Bad - Memory leak
useEffect(() => {
  window.addEventListener("resize", handleResize);
  document.addEventListener("click", handleClick);
  // No cleanup!
}, []);

// ✅ Good - Proper cleanup
useEffect(() => {
  const handleResize = () => {
    // Handle resize
  };

  const handleClick = (e: MouseEvent) => {
    // Handle click
  };

  window.addEventListener("resize", handleResize);
  document.addEventListener("click", handleClick);

  // Cleanup function
  return () => {
    window.removeEventListener("resize", handleResize);
    document.removeEventListener("click", handleClick);
  };
}, []);
```

### ✅ Timer Cleanup

```typescript
// ❌ Bad - Timer keeps running
useEffect(() => {
  const timer = setInterval(() => {
    fetchData();
  }, 1000);
}, []);

// ✅ Good - Clear timer on unmount
useEffect(() => {
  const timer = setInterval(() => {
    fetchData();
  }, 1000);

  return () => {
    clearInterval(timer);
  };
}, [fetchData]);

// ✅ Better - With ref for latest callback
const savedCallback = useRef(fetchData);

useEffect(() => {
  savedCallback.current = fetchData;
}, [fetchData]);

useEffect(() => {
  const timer = setInterval(() => {
    savedCallback.current();
  }, 1000);

  return () => clearInterval(timer);
}, []); // Empty deps, but uses latest callback
```

### ✅ Subscription Cleanup

```typescript
// ❌ Bad - Subscription leak
useEffect(() => {
  const subscription = api.subscribe("events", (data) => {
    setState(data);
  });
}, []);

// ✅ Good - Unsubscribe on cleanup
useEffect(() => {
  const subscription = api.subscribe("events", (data) => {
    setState(data);
  });

  return () => {
    subscription.unsubscribe();
  };
}, []);

// ✅ With abort controller for fetch
useEffect(() => {
  const controller = new AbortController();

  const fetchData = async () => {
    try {
      const response = await fetch("/api/data", {
        signal: controller.signal,
      });
      const data = await response.json();
      setState(data);
    } catch (error) {
      if (error.name !== "AbortError") {
        console.error(error);
      }
    }
  };

  fetchData();

  return () => {
    controller.abort();
  };
}, []);
```

### ✅ Observer Cleanup

```typescript
// ❌ Bad - Observer never disconnected
useEffect(() => {
  const observer = new IntersectionObserver((entries) => {
    // Handle intersection
  });

  observer.observe(elementRef.current);
}, []);

// ✅ Good - Disconnect observer
useEffect(() => {
  const element = elementRef.current;
  if (!element) return;

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        setIsVisible(true);
      }
    });
  });

  observer.observe(element);

  return () => {
    observer.disconnect();
  };
}, []);

// ✅ ResizeObserver with cleanup
useEffect(() => {
  const element = elementRef.current;
  if (!element) return;

  const resizeObserver = new ResizeObserver((entries) => {
    for (let entry of entries) {
      const { width, height } = entry.contentRect;
      setDimensions({ width, height });
    }
  });

  resizeObserver.observe(element);

  return () => {
    resizeObserver.disconnect();
  };
}, []);
```

### ✅ WebSocket Cleanup

```typescript
// ❌ Bad - WebSocket connection leak
useEffect(() => {
  const ws = new WebSocket("wss://api.example.com");

  ws.onmessage = (event) => {
    setMessages((prev) => [...prev, event.data]);
  };
}, []);

// ✅ Good - Close WebSocket
useEffect(() => {
  const ws = new WebSocket("wss://api.example.com");

  ws.onopen = () => {
    console.log("Connected");
  };

  ws.onmessage = (event) => {
    setMessages((prev) => [...prev, event.data]);
  };

  ws.onerror = (error) => {
    console.error("WebSocket error:", error);
  };

  return () => {
    ws.close();
  };
}, []);
```

## Advanced Cleanup Patterns

### 1. Cleanup with Dependencies

```typescript
function useEventListener<K extends keyof WindowEventMap>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element: Window | HTMLElement = window,
) {
  const savedHandler = useRef(handler);

  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    const isSupported = element && element.addEventListener;
    if (!isSupported) return;

    const eventListener = (event: Event) => {
      savedHandler.current(event as WindowEventMap[K]);
    };

    element.addEventListener(eventName, eventListener);

    return () => {
      element.removeEventListener(eventName, eventListener);
    };
  }, [eventName, element]);
}
```

### 2. Multiple Cleanup Operations

```typescript
function useComplexResource() {
  useEffect(() => {
    // Multiple resources to clean up
    const timer = setInterval(() => {}, 1000);
    const controller = new AbortController();
    const subscription = eventBus.subscribe("event", handler);

    document.addEventListener("visibilitychange", handleVisibility);

    // Return cleanup function that handles all
    return () => {
      clearInterval(timer);
      controller.abort();
      subscription.unsubscribe();
      document.removeEventListener("visibilitychange", handleVisibility);
    };
  }, []);
}
```

### 3. Conditional Cleanup

```typescript
function useConditionalListener(enabled: boolean) {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (e: KeyboardEvent) => {
      // Handle key press
    };

    document.addEventListener("keydown", handleKeyPress);

    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [enabled]); // Re-run when enabled changes
}
```

### 4. Cleanup with Refs

```typescript
function useAnimationFrame(callback: (deltaTime: number) => void) {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    const animate = (time: number) => {
      if (previousTimeRef.current !== undefined) {
        const deltaTime = time - previousTimeRef.current;
        callbackRef.current(deltaTime);
      }
      previousTimeRef.current = time;
      requestRef.current = requestAnimationFrame(animate);
    };

    requestRef.current = requestAnimationFrame(animate);

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);
}
```

## Memory Leak Detection

### 1. React DevTools Profiler

```typescript
// Use React DevTools to detect memory leaks
// 1. Record a session
// 2. Interact with components
// 3. Navigate away and back
// 4. Check if memory increases
```

### 2. Chrome DevTools

```typescript
// Memory profiling
// 1. Open Memory tab
// 2. Take heap snapshot
// 3. Perform actions
// 4. Take another snapshot
// 5. Compare for leaked objects
```

### 3. Custom Memory Leak Detector

```typescript
// Development-only memory leak detector
if (process.env.NODE_ENV === "development") {
  let mountedComponents = new Set();

  export function trackComponent(name: string) {
    useEffect(() => {
      mountedComponents.add(name);
      console.log(`Mounted: ${name}`, mountedComponents.size);

      return () => {
        mountedComponents.delete(name);
        console.log(`Unmounted: ${name}`, mountedComponents.size);
      };
    }, [name]);
  }
}
```

## Common Cleanup Mistakes

### 1. Incorrect Dependency Arrays

```typescript
// ❌ Bad - Cleanup never runs with new handler
useEffect(() => {
  window.addEventListener("resize", handleResize);
  return () => {
    window.removeEventListener("resize", handleResize);
  };
}, []); // Missing handleResize dependency

// ✅ Good - Use ref for stable reference
const handleResizeRef = useRef(handleResize);
handleResizeRef.current = handleResize;

useEffect(() => {
  const handler = (e: Event) => handleResizeRef.current(e);
  window.addEventListener("resize", handler);
  return () => {
    window.removeEventListener("resize", handler);
  };
}, []);
```

### 2. Async Cleanup

```typescript
// ❌ Bad - Async cleanup not allowed
useEffect(() => {
  return async () => {
    // Error!
    await saveData();
  };
}, []);

// ✅ Good - Handle async in cleanup
useEffect(() => {
  let cancelled = false;

  const saveData = async () => {
    if (!cancelled) {
      await api.save(data);
    }
  };

  return () => {
    cancelled = true;
    // Trigger save but don't await
    saveData();
  };
}, [data]);
```

## Testing for Memory Leaks

```typescript
// Test for cleanup
import { renderHook, cleanup } from "@testing-library/react-hooks";

test("should cleanup on unmount", () => {
  const mockFn = jest.fn();
  jest.spyOn(window, "addEventListener");
  jest.spyOn(window, "removeEventListener");

  const { unmount } = renderHook(() => useEventListener("resize", mockFn));

  expect(window.addEventListener).toHaveBeenCalledWith(
    "resize",
    expect.any(Function),
  );

  unmount();

  expect(window.removeEventListener).toHaveBeenCalledWith(
    "resize",
    expect.any(Function),
  );
});
```

## Migration Checklist

- [ ] Audit all useEffect hooks for cleanup
- [ ] Check for event listeners without removal
- [ ] Find timers without clearTimeout/clearInterval
- [ ] Review WebSocket and subscription cleanup
- [ ] Add cleanup to all observers
- [ ] Test with Chrome Memory Profiler
- [ ] Add ESLint rule for useEffect dependencies
- [ ] Document cleanup patterns for team

## Performance Impact

Memory leaks compound over time:

- **Initial load**: No impact
- **After 10 minutes**: +50MB memory usage
- **After 1 hour**: +200MB, noticeable lag
- **After 4 hours**: Potential crash

Proper cleanup maintains:

- Consistent memory usage
- Responsive UI
- Stable performance
- Happy users

## Conclusion

Missing cleanup is a silent killer of web app performance. The current codebase has numerous memory leaks that will degrade performance over time. Adding proper cleanup is essential for production-quality applications.
