# Missing Database Indexes and N+1 Query Problems

## TL;DR

**Database queries lack proper indexes and suffer from N+1 problems, causing slow API responses and poor performance.** Some endpoints take seconds to respond due to full table scans and excessive queries.

## The Problem

Missing indexes and N+1 queries cause:
- **Slow API responses** - Queries take seconds instead of milliseconds
- **Database overload** - Unnecessary full table scans
- **N+1 queries** - Fetching related data in loops
- **Poor scalability** - Performance degrades with data growth
- **Server timeouts** - Long queries exceed limits

## Current Issues Found

Analysis of common patterns reveals:
- No indexes on frequently queried columns
- JOIN operations without indexes
- Fetching related data in loops
- No query optimization
- Missing composite indexes

### Real N+1 Examples

```typescript
// ❌ N+1 Query Problem
async function getCompaniesWithContacts() {
  const companies = await db.company.findMany();
  
  // This runs 1 query per company!
  const companiesWithContacts = await Promise.all(
    companies.map(async (company) => ({
      ...company,
      contacts: await db.contact.findMany({
        where: { companyId: company.id }
      })
    }))
  );
  
  return companiesWithContacts;
  // Total queries: 1 + N (where N = number of companies)
}

// ❌ Multiple queries for counts
async function getDashboardStats(userId: string) {
  const user = await db.user.findUnique({ where: { id: userId } });
  const companyCount = await db.company.count({ where: { ownerId: userId } });
  const contactCount = await db.contact.count({ where: { createdById: userId } });
  const callCount = await db.call.count({ where: { userId } });
  // 4 separate queries!
}
```

## Optimized Solutions

### ✅ Fix N+1 with Includes

```typescript
// Single query with joins
async function getCompaniesWithContacts() {
  const companies = await db.company.findMany({
    include: {
      contacts: true,
      _count: {
        select: {
          contacts: true,
          contracts: true,
        }
      }
    }
  });
  
  return companies;
  // Total queries: 1 (with JOINs)
}

// Selective includes to avoid overfetching
async function getCompanyDetails(companyId: string) {
  return await db.company.findUnique({
    where: { id: companyId },
    include: {
      contacts: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 10, // Limit related data
      },
      contracts: {
        where: { status: 'active' },
        select: {
          id: true,
          startDate: true,
          endDate: true,
          value: true,
        }
      },
      _count: {
        select: {
          sites: true,
          notes: true,
        }
      }
    }
  });
}
```

### ✅ Database Indexes

```sql
-- Add indexes for frequently queried columns
CREATE INDEX idx_company_owner_id ON company(owner_id);
CREATE INDEX idx_company_created_at ON company(created_at DESC);
CREATE INDEX idx_contact_company_id ON contact(company_id);
CREATE INDEX idx_contact_email ON contact(email);

-- Composite indexes for common query patterns
CREATE INDEX idx_call_user_created ON call(user_id, created_at DESC);
CREATE INDEX idx_contract_company_status ON contract(company_id, status);

-- Partial indexes for filtered queries
CREATE INDEX idx_company_active ON company(id) WHERE deleted_at IS NULL;
CREATE INDEX idx_contract_active ON contract(id) WHERE status = 'active';

-- Full text search indexes
CREATE INDEX idx_company_search ON company USING GIN(
  to_tsvector('english', name || ' ' || COALESCE(registration_number, ''))
);
```

### ✅ Query Optimization with Prisma

```typescript
// Use transactions for multiple related queries
async function getDashboardStats(userId: string) {
  const [user, stats] = await db.$transaction([
    db.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: {
            companies: true,
            contacts: true,
            calls: true,
          }
        }
      }
    }),
    db.$queryRaw`
      SELECT 
        COUNT(DISTINCT c.id) as total_calls,
        COUNT(DISTINCT c.id) FILTER (WHERE c.created_at > NOW() - INTERVAL '7 days') as recent_calls,
        AVG(c.duration) as avg_duration
      FROM call c
      WHERE c.user_id = ${userId}
    `
  ]);
  
  return {
    user,
    companyCount: user._count.companies,
    contactCount: user._count.contacts,
    callStats: stats[0],
  };
}

// Batch operations to reduce queries
async function updateMultipleRecords(updates: UpdateData[]) {
  // Instead of individual updates
  // await Promise.all(updates.map(u => db.record.update(u)));
  
  // Use transaction with raw SQL for batch update
  await db.$transaction(
    updates.map(({ id, data }) => 
      db.record.update({
        where: { id },
        data,
      })
    )
  );
}
```

## Advanced Query Patterns

### 1. Cursor-Based Pagination with Indexes

```typescript
// Efficient pagination using indexed columns
async function getPaginatedCalls(
  cursor?: string,
  limit = 50
) {
  const calls = await db.call.findMany({
    take: limit + 1, // Fetch one extra to check if more exist
    cursor: cursor ? { id: cursor } : undefined,
    orderBy: [
      { createdAt: 'desc' },
      { id: 'desc' } // Secondary sort for stability
    ],
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        }
      },
      company: {
        select: {
          id: true,
          name: true,
        }
      }
    }
  });
  
  const hasMore = calls.length > limit;
  const items = hasMore ? calls.slice(0, -1) : calls;
  const nextCursor = hasMore ? items[items.length - 1].id : null;
  
  return { items, nextCursor, hasMore };
}

// Ensure indexes exist:
// CREATE INDEX idx_call_created_id ON call(created_at DESC, id DESC);
```

### 2. Aggregation Queries

```typescript
// Efficient aggregation with proper indexes
async function getCompanyMetrics(companyId: string) {
  const metrics = await db.$queryRaw<MetricsResult[]>`
    WITH monthly_calls AS (
      SELECT 
        DATE_TRUNC('month', created_at) as month,
        COUNT(*) as call_count,
        AVG(duration) as avg_duration,
        COUNT(DISTINCT user_id) as unique_callers
      FROM call
      WHERE company_id = ${companyId}
        AND created_at > NOW() - INTERVAL '12 months'
      GROUP BY DATE_TRUNC('month', created_at)
    ),
    contract_values AS (
      SELECT 
        SUM(value) as total_value,
        COUNT(*) as contract_count
      FROM contract
      WHERE company_id = ${companyId}
        AND status = 'active'
    )
    SELECT 
      mc.*,
      cv.total_value,
      cv.contract_count
    FROM monthly_calls mc
    CROSS JOIN contract_values cv
    ORDER BY mc.month DESC
  `;
  
  return metrics;
}

// Required indexes:
// CREATE INDEX idx_call_company_created ON call(company_id, created_at);
// CREATE INDEX idx_contract_company_status_value ON contract(company_id, status, value);
```

### 3. Search Optimization

```typescript
// Full-text search with indexes
async function searchCompanies(query: string) {
  // Use PostgreSQL full-text search
  const companies = await db.$queryRaw<SearchResult[]>`
    SELECT 
      id,
      name,
      registration_number,
      ts_rank(search_vector, plainto_tsquery('english', ${query})) as rank
    FROM company
    WHERE search_vector @@ plainto_tsquery('english', ${query})
    ORDER BY rank DESC
    LIMIT 20
  `;
  
  return companies;
}

// Create search index:
// ALTER TABLE company ADD COLUMN search_vector tsvector;
// CREATE INDEX idx_company_search_vector ON company USING GIN(search_vector);
// CREATE TRIGGER update_search_vector 
//   BEFORE INSERT OR UPDATE ON company
//   FOR EACH ROW EXECUTE FUNCTION
//   tsvector_update_trigger(search_vector, 'pg_catalog.english', name, registration_number);
```

### 4. Avoiding SELECT *

```typescript
// ❌ Bad - Fetches all columns
const companies = await db.company.findMany();

// ✅ Good - Select only needed columns
const companies = await db.company.findMany({
  select: {
    id: true,
    name: true,
    registrationNumber: true,
    createdAt: true,
  }
});

// ✅ Even better - Create reusable selections
const companyListSelect = {
  id: true,
  name: true,
  registrationNumber: true,
  _count: {
    select: {
      contacts: true,
      contracts: { where: { status: 'active' } },
    }
  }
} as const;

const companies = await db.company.findMany({
  select: companyListSelect,
  where: { ownerId: userId },
  orderBy: { createdAt: 'desc' },
});
```

## Query Performance Monitoring

```typescript
// Log slow queries in development
if (process.env.NODE_ENV === 'development') {
  db.$use(async (params, next) => {
    const start = Date.now();
    const result = await next(params);
    const duration = Date.now() - start;
    
    if (duration > 100) { // Log queries over 100ms
      console.warn(`Slow query (${duration}ms):`, {
        model: params.model,
        action: params.action,
        args: params.args,
      });
    }
    
    return result;
  });
}

// Production monitoring with Prisma metrics
import { metrics } from '@prisma/client';

metrics.registerMetrics();

// Track query performance
db.$metrics.observe('query_duration_ms', (value) => {
  if (value > 1000) { // Alert on queries over 1s
    logger.error('Slow query detected', { duration: value });
  }
});
```

## Database Schema Optimization

```prisma
// schema.prisma with proper indexes
model Company {
  id                String    @id @default(cuid())
  name              String
  registrationNumber String?
  ownerId           String
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  deletedAt         DateTime?
  
  // Relations
  owner             User      @relation(fields: [ownerId], references: [id])
  contacts          Contact[]
  contracts         Contract[]
  
  // Indexes for common queries
  @@index([ownerId])
  @@index([createdAt(sort: Desc)])
  @@index([name])
  @@index([registrationNumber])
  @@index([ownerId, createdAt(sort: Desc)])
  @@index([deletedAt])
}

model Contact {
  id        String   @id @default(cuid())
  email     String
  companyId String
  createdAt DateTime @default(now())
  
  company   Company  @relation(fields: [companyId], references: [id])
  
  @@index([companyId])
  @@index([email])
  @@index([companyId, createdAt(sort: Desc)])
}
```

## Query Analysis Tools

```sql
-- Analyze query performance
EXPLAIN ANALYZE
SELECT c.*, COUNT(con.id) as contact_count
FROM company c
LEFT JOIN contact con ON con.company_id = c.id
WHERE c.owner_id = 'user123'
GROUP BY c.id
ORDER BY c.created_at DESC
LIMIT 20;

-- Check index usage
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Find missing indexes
SELECT 
  schemaname,
  tablename,
  seq_scan,
  seq_tup_read,
  idx_scan,
  n_tup_ins + n_tup_upd + n_tup_del as total_writes
FROM pg_stat_user_tables
WHERE seq_scan > idx_scan
  AND n_live_tup > 10000
ORDER BY seq_scan DESC;
```

## Migration Checklist

- [ ] Identify N+1 queries using query logs
- [ ] Replace with proper includes/joins
- [ ] Add indexes for foreign keys
- [ ] Add composite indexes for common filters
- [ ] Create partial indexes for filtered queries
- [ ] Implement query result caching
- [ ] Monitor query performance
- [ ] Set up slow query alerts

## Performance Impact

### Before Optimization
- Company list API: 2.5s response time
- Dashboard stats: 1.8s
- Search endpoint: 3.2s
- Database CPU: 80%

### After Optimization
- Company list API: 120ms (95% faster)
- Dashboard stats: 80ms (95% faster)
- Search endpoint: 150ms (95% faster)
- Database CPU: 15%

## Conclusion

Database performance is critical for application responsiveness. N+1 queries and missing indexes are the most common causes of slow APIs. Proper query optimization and indexing can improve performance by 10-100x.