# Missing Loading States Causing UI Flicker and Poor UX

## TL;DR

**Most async operations in the codebase lack proper loading states, causing UI flicker, layout shifts, and confusing user experiences.** Users see empty states, then sudden content appears, or worse - errors flash before data loads.

## The Problem

Without loading states:
- **Layout shift** - Content suddenly appears, pushing other elements
- **Flicker** - Empty → Error → Data flashes on screen  
- **User confusion** - "Is it broken?" "Did my click register?"
- **Rage clicks** - Users click multiple times, causing duplicate requests
- **Poor perceived performance** - Apps feel slower without visual feedback

## Current Issues in the Codebase

### ❌ Components with Missing Loading States

Analysis reveals:
- Data tables show empty state before loading
- Forms submit without feedback
- Modals pop in/out abruptly
- Lists render empty then suddenly populate
- No skeleton screens for complex layouts

### Real Example from Codebase

```typescript
// apps/crm/src/app/account/companies/page.tsx
export default function CompaniesPage() {
  const { data: companies } = api.company.list.useQuery();
  
  // No loading state - shows nothing then suddenly appears
  return (
    <div>
      {companies?.map(company => (
        <CompanyCard key={company.id} company={company} />
      ))}
    </div>
  );
}
```

## User Experience Impact

### What Users See Now:
1. Click "Companies" → Blank screen (200ms)
2. Layout suddenly shifts when data appears
3. If error occurs, error message flashes then disappears
4. No indication if search/filter is working

### What Users Should See:
1. Click "Companies" → Immediate skeleton
2. Smooth transition from skeleton to data
3. Clear error states with retry options
4. Loading indicators for all async operations

## Implementation Patterns

### ❌ Bad Pattern (No Loading State)

```typescript
function ContactsList() {
  const { data, error } = useContacts();
  
  if (error) return <div>Error: {error.message}</div>;
  if (!data) return null; // Bad! Shows nothing
  
  return <div>{data.map(renderContact)}</div>;
}
```

### ✅ Good Pattern (With Loading States)

```typescript
function ContactsList() {
  const { data, error, isLoading } = useContacts();
  
  // Show skeleton while loading
  if (isLoading) {
    return <ContactsListSkeleton />;
  }
  
  // Show error with retry
  if (error) {
    return (
      <ErrorState 
        message={error.message}
        onRetry={() => refetch()}
      />
    );
  }
  
  // Show empty state if no data
  if (!data?.length) {
    return <EmptyState message="No contacts found" />;
  }
  
  // Show data with loading overlay for updates
  return <div>{data.map(renderContact)}</div>;
}
```

## Loading State Patterns

### 1. Skeleton Screens

```typescript
// Reusable skeleton component
export function TableSkeleton({ rows = 5, columns = 4 }) {
  return (
    <div className="space-y-2">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex gap-4">
          {Array.from({ length: columns }).map((_, j) => (
            <Skeleton key={j} className="h-8 flex-1" />
          ))}
        </div>
      ))}
    </div>
  );
}

// Usage in data table
function CallsTable() {
  const { data, isLoading } = useCalls();
  
  if (isLoading) return <TableSkeleton rows={10} columns={6} />;
  
  return <DataTable data={data} />;
}
```

### 2. Inline Loading States

```typescript
function SaveButton({ onSave }: SaveButtonProps) {
  const [isSaving, setIsSaving] = useState(false);
  
  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave();
      toast.success("Saved successfully");
    } catch (error) {
      toast.error("Failed to save");
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <Button onClick={handleSave} disabled={isSaving}>
      {isSaving ? (
        <>
          <Spinner className="mr-2 h-4 w-4" />
          Saving...
        </>
      ) : (
        'Save Changes'
      )}
    </Button>
  );
}
```

### 3. Optimistic Updates with Loading

```typescript
function TodoList() {
  const { data: todos, isLoading } = useTodos();
  const { mutate: updateTodo, isPending } = useUpdateTodo();
  
  const handleToggle = (todo: Todo) => {
    // Optimistic update
    updateTodo({
      ...todo,
      completed: !todo.completed
    }, {
      // Show loading state on specific item
      onMutate: async (newTodo) => {
        // Cancel outgoing refetches
        await queryClient.cancelQueries({ queryKey: ['todos'] });
        
        // Snapshot previous value
        const previousTodos = queryClient.getQueryData(['todos']);
        
        // Optimistically update
        queryClient.setQueryData(['todos'], old => 
          old.map(t => t.id === newTodo.id ? newTodo : t)
        );
        
        return { previousTodos };
      },
      onError: (err, newTodo, context) => {
        // Rollback on error
        queryClient.setQueryData(['todos'], context.previousTodos);
      }
    });
  };
  
  if (isLoading) return <TodoSkeleton />;
  
  return (
    <div>
      {todos.map(todo => (
        <TodoItem 
          key={todo.id}
          todo={todo}
          onToggle={handleToggle}
          isUpdating={isPending}
        />
      ))}
    </div>
  );
}
```

### 4. Progressive Loading

```typescript
function Dashboard() {
  const { data: stats, isLoading: statsLoading } = useStats();
  const { data: charts, isLoading: chartsLoading } = useCharts();
  const { data: activity, isLoading: activityLoading } = useActivity();
  
  return (
    <div className="grid gap-6">
      {/* Stats load first */}
      <section>
        {statsLoading ? (
          <StatsGridSkeleton />
        ) : (
          <StatsGrid data={stats} />
        )}
      </section>
      
      {/* Charts load independently */}
      <section>
        {chartsLoading ? (
          <ChartSkeleton />
        ) : (
          <Charts data={charts} />
        )}
      </section>
      
      {/* Activity loads last */}
      <section>
        {activityLoading ? (
          <ActivityFeedSkeleton />
        ) : (
          <ActivityFeed data={activity} />
        )}
      </section>
    </div>
  );
}
```

## Loading State Components

### Skeleton Component Library

```typescript
// Base skeleton with animation
export function Skeleton({ 
  className,
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-muted",
        className
      )}
      {...props}
    />
  );
}

// Specific skeletons
export function CardSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-1/3" />
        <Skeleton className="h-4 w-2/3" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-20 w-full" />
      </CardContent>
    </Card>
  );
}

export function FormSkeleton() {
  return (
    <div className="space-y-4">
      <div>
        <Skeleton className="h-4 w-20 mb-2" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Input */}
      </div>
      <div>
        <Skeleton className="h-4 w-24 mb-2" />
        <Skeleton className="h-10 w-full" />
      </div>
      <Skeleton className="h-10 w-32" /> {/* Button */}
    </div>
  );
}
```

## Common Loading Patterns

### 1. Search with Debounce

```typescript
function SearchableList() {
  const [search, setSearch] = useState("");
  const [debouncedSearch] = useDebounce(search, 300);
  const { data, isLoading, isFetching } = useSearch(debouncedSearch);
  
  return (
    <div>
      <div className="relative">
        <SearchInput 
          value={search}
          onChange={setSearch}
        />
        {isFetching && (
          <Spinner className="absolute right-2 top-2" />
        )}
      </div>
      
      {isLoading ? (
        <ListSkeleton />
      ) : (
        <List items={data} />
      )}
    </div>
  );
}
```

### 2. Infinite Scroll

```typescript
function InfiniteList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteQuery({
    queryKey: ['items'],
    queryFn: fetchItems,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
  });
  
  if (isLoading) return <ListSkeleton />;
  
  return (
    <div>
      {data.pages.map(page => 
        page.items.map(item => (
          <Item key={item.id} item={item} />
        ))
      )}
      
      {isFetchingNextPage ? (
        <div className="p-4 text-center">
          <Spinner />
        </div>
      ) : hasNextPage ? (
        <Button onClick={() => fetchNextPage()}>
          Load More
        </Button>
      ) : null}
    </div>
  );
}
```

## Performance Considerations

### 1. Avoid Loading State Flash

```typescript
// Show loading only after delay to avoid flash
function DelayedSpinner({ delay = 200 }) {
  const [show, setShow] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);
  
  return show ? <Spinner /> : null;
}
```

### 2. Preserve Scroll Position

```typescript
function PreserveScrollList() {
  const scrollRef = useRef<number>(0);
  const { data, isLoading, refetch } = useData();
  
  // Save scroll before refetch
  const handleRefresh = () => {
    scrollRef.current = window.scrollY;
    refetch();
  };
  
  // Restore scroll after load
  useEffect(() => {
    if (!isLoading && scrollRef.current) {
      window.scrollTo(0, scrollRef.current);
    }
  }, [isLoading]);
  
  return <List data={data} onRefresh={handleRefresh} />;
}
```

## Migration Checklist

- [ ] Audit all data fetching components
- [ ] Create reusable skeleton components
- [ ] Add loading states to all async operations
- [ ] Implement error boundaries with retry
- [ ] Add optimistic updates where applicable
- [ ] Test loading states with network throttling
- [ ] Ensure accessibility (aria-busy, announcements)
- [ ] Document loading state patterns

## Metrics to Track

- **Time to First Contentful Paint**: Should improve with skeletons
- **Layout Shift Score**: Should approach zero
- **User engagement**: Fewer rage clicks, lower bounce rate
- **Error visibility**: Users should see and understand errors

## Conclusion

Loading states are not optional - they're essential for professional applications. The current codebase's lack of loading states creates a poor user experience that feels buggy and unresponsive. Implementing proper loading patterns will dramatically improve perceived performance and user satisfaction.