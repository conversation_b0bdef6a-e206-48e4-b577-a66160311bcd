# Missing SEO and Meta Tags Optimization

## TL;DR

**The Next.js app lacks proper SEO optimization, meta tags, and structured data.** This hurts search engine rankings, social media sharing, and discoverability.

## The Problem

Missing SEO optimization causes:
- **Poor search rankings** - Google can't understand page content
- **Bad social sharing** - Ugly or missing previews on social media
- **Slow indexing** - Search engines struggle to crawl efficiently
- **Missing rich snippets** - No enhanced search results
- **Accessibility issues** - Screen readers lack context

## Current Issues Found

Analysis reveals:
- No dynamic meta tags
- Missing Open Graph tags
- No structured data (JSON-LD)
- Static titles across pages
- No sitemap or robots.txt
- Missing canonical URLs

### Real Examples

```typescript
// ❌ Current implementation - Static metadata
export default function CompanyPage() {
  return (
    <div>
      {/* No meta tags! */}
      <h1>Company Details</h1>
      {/* Content */}
    </div>
  );
}

// ❌ Layout with static title
export default function RootLayout({ children }) {
  return (
    <html>
      <head>
        <title>Watt CRM</title> {/* Same title everywhere! */}
      </head>
      <body>{children}</body>
    </html>
  );
}
```

## SEO Solutions with Next.js 13+

### ✅ Dynamic Metadata API

```typescript
// app/companies/[id]/page.tsx
import { Metadata } from 'next';

interface Props {
  params: { id: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const company = await getCompany(params.id);
  
  return {
    title: `${company.name} | Company Profile | Watt CRM`,
    description: `View details for ${company.name}. ${company.description || 'Energy industry company profile and information.'}`,
    
    openGraph: {
      title: `${company.name} - Company Profile`,
      description: company.description || 'Company information and details',
      type: 'profile',
      url: `https://crm.watt.com/companies/${company.id}`,
      images: [
        {
          url: company.logo || '/og-default.jpg',
          width: 1200,
          height: 630,
          alt: `${company.name} logo`,
        }
      ],
    },
    
    twitter: {
      card: 'summary_large_image',
      title: `${company.name} | Watt CRM`,
      description: company.description,
      images: [company.logo || '/twitter-default.jpg'],
    },
    
    alternates: {
      canonical: `https://crm.watt.com/companies/${company.id}`,
    },
    
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}
```

### ✅ Structured Data (JSON-LD)

```typescript
// components/structured-data.tsx
interface CompanyStructuredDataProps {
  company: Company;
}

export function CompanyStructuredData({ company }: CompanyStructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: company.name,
    url: `https://crm.watt.com/companies/${company.id}`,
    logo: company.logo,
    description: company.description,
    address: company.address ? {
      '@type': 'PostalAddress',
      streetAddress: company.address.street,
      addressLocality: company.address.city,
      postalCode: company.address.postcode,
      addressCountry: 'GB',
    } : undefined,
    contactPoint: company.phone ? {
      '@type': 'ContactPoint',
      telephone: company.phone,
      contactType: 'customer service',
    } : undefined,
    sameAs: [
      company.linkedin,
      company.twitter,
    ].filter(Boolean),
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

// Usage in page
export default async function CompanyPage({ params }: Props) {
  const company = await getCompany(params.id);
  
  return (
    <>
      <CompanyStructuredData company={company} />
      <CompanyDetails company={company} />
    </>
  );
}
```

### ✅ Dynamic Sitemap

```typescript
// app/sitemap.ts
import { MetadataRoute } from 'next';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://crm.watt.com';
  
  // Fetch dynamic content
  const [companies, contacts, quotes] = await Promise.all([
    getCompanies(),
    getContacts(),
    getQuotes(),
  ]);
  
  const companiesUrls = companies.map(company => ({
    url: `${baseUrl}/companies/${company.id}`,
    lastModified: company.updatedAt,
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));
  
  const contactUrls = contacts.map(contact => ({
    url: `${baseUrl}/contacts/${contact.id}`,
    lastModified: contact.updatedAt,
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }));
  
  return [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/companies`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    ...companiesUrls,
    ...contactUrls,
  ];
}

// app/robots.ts
import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/admin/', '/auth/'],
      },
    ],
    sitemap: 'https://crm.watt.com/sitemap.xml',
  };
}
```

### ✅ SEO Component for Client Components

```typescript
// For client components that can't use metadata API
'use client';

import Head from 'next/head';

interface SEOProps {
  title: string;
  description: string;
  image?: string;
  article?: boolean;
}

export function SEO({ title, description, image, article }: SEOProps) {
  const siteTitle = 'Watt CRM';
  const fullTitle = `${title} | ${siteTitle}`;
  
  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={article ? 'article' : 'website'} />
      {image && <meta property="og:image" content={image} />}
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      {image && <meta name="twitter:image" content={image} />}
    </Head>
  );
}
```

## Advanced SEO Patterns

### 1. Breadcrumb Navigation

```typescript
function BreadcrumbStructuredData({ items }: { items: BreadcrumbItem[] }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <nav aria-label="Breadcrumb">
        <ol className="flex space-x-2">
          {items.map((item, index) => (
            <li key={item.url}>
              {index > 0 && <span className="mx-2">/</span>}
              <Link href={item.url}>{item.name}</Link>
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}
```

### 2. Search Action Schema

```typescript
// Enable site search in Google
function SearchActionSchema() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    url: 'https://crm.watt.com/',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://crm.watt.com/search?q={search_term_string}',
      },
      'query-input': 'required name=search_term_string',
    },
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
```

### 3. FAQ Schema for Better Rankings

```typescript
function FAQSchema({ faqs }: { faqs: FAQ[] }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
```

### 4. Dynamic OG Image Generation

```typescript
// app/api/og/route.tsx
import { ImageResponse } from 'next/og';

export const runtime = 'edge';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  
  const title = searchParams.get('title') || 'Watt CRM';
  const description = searchParams.get('description') || '';
  
  return new ImageResponse(
    (
      <div
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#1a1a1a',
          fontSize: 32,
          fontWeight: 600,
        }}
      >
        <svg
          width="75"
          viewBox="0 0 75 65"
          fill="#FFF"
          style={{ margin: '0 75px' }}
        >
          {/* Logo SVG */}
        </svg>
        <div style={{ marginTop: 40, color: 'white' }}>{title}</div>
        {description && (
          <div style={{ marginTop: 20, fontSize: 20, color: '#888' }}>
            {description}
          </div>
        )}
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}
```

## Performance & SEO

### 1. Lazy Load Non-Critical Scripts

```typescript
// Load after page is interactive
export default function Layout({ children }) {
  return (
    <>
      {children}
      <Script
        src="https://www.googletagmanager.com/gtag/js?id=GA_ID"
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'GA_ID');
        `}
      </Script>
    </>
  );
}
```

### 2. Preconnect to External Domains

```typescript
export const metadata: Metadata = {
  other: {
    'link': [
      {
        rel: 'preconnect',
        href: 'https://fonts.googleapis.com',
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com',
        crossOrigin: 'anonymous',
      },
    ],
  },
};
```

## SEO Checklist

- [ ] Implement dynamic metadata for all pages
- [ ] Add structured data for rich snippets
- [ ] Create XML sitemap
- [ ] Configure robots.txt
- [ ] Add canonical URLs
- [ ] Implement breadcrumbs
- [ ] Generate dynamic OG images
- [ ] Add alt text to all images
- [ ] Ensure proper heading hierarchy
- [ ] Test with Google's Rich Results Test
- [ ] Monitor Core Web Vitals
- [ ] Submit sitemap to Search Console

## Common Mistakes

### 1. Duplicate Content

```typescript
// ❌ Bad - Same metadata everywhere
export const metadata = {
  title: 'Watt CRM',
  description: 'CRM for energy companies',
};

// ✅ Good - Unique per page
export async function generateMetadata({ params }) {
  // Generate unique metadata
}
```

### 2. Missing Image Dimensions

```typescript
// ❌ Bad - No dimensions
<meta property="og:image" content="/share.jpg" />

// ✅ Good - With dimensions
<meta property="og:image" content="/share.jpg" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
```

## Conclusion

SEO is not optional for modern web applications. Proper metadata, structured data, and technical SEO implementation can significantly improve search rankings and user engagement. Next.js provides excellent built-in support for SEO that should be utilized.