# Missing Suspense Boundaries for Async Components

## TL;DR

**The application doesn't use React Suspense for data fetching and code splitting, causing poor loading experiences.** Users see loading spinners everywhere instead of progressive content reveal.

## The Problem

Without Suspense:
- **Loading waterfalls** - Sequential loading states instead of parallel
- **Spinner hell** - Multiple spinners on one page
- **Layout shifts** - Content pops in at different times
- **Poor perceived performance** - Page feels slower than it is
- **Complicated loading logic** - Manual loading state management

## Current State

The codebase shows:
- 0 Suspense boundaries found
- Manual loading states in every component
- No streaming SSR benefits
- Sequential data loading patterns
- Complex loading state management

## Suspense Implementation Patterns

### ✅ Basic Suspense for Data Fetching

```typescript
// ❌ Current approach - Manual loading states
function CompanyList() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['companies'],
    queryFn: fetchCompanies,
  });
  
  if (isLoading) return <Spinner />;
  if (error) return <Error error={error} />;
  
  return <div>{data.map(renderCompany)}</div>;
}

// ✅ With Suspense - Cleaner and parallel
function CompanyList() {
  const { data } = useSuspenseQuery({
    queryKey: ['companies'],
    queryFn: fetchCompanies,
  });
  
  // No loading state needed!
  return <div>{data.map(renderCompany)}</div>;
}

// Parent handles loading
function CompaniesPage() {
  return (
    <Suspense fallback={<CompanyListSkeleton />}>
      <CompanyList />
    </Suspense>
  );
}
```

### ✅ Parallel Data Loading with Suspense

```typescript
// ❌ Current - Sequential loading
function Dashboard() {
  const { data: stats, isLoading: statsLoading } = useStats();
  const { data: chart, isLoading: chartLoading } = useChart();
  const { data: activity, isLoading: activityLoading } = useActivity();
  
  if (statsLoading || chartLoading || activityLoading) {
    return <DashboardSkeleton />;
  }
  
  return (
    <div>
      <Stats data={stats} />
      <Chart data={chart} />
      <Activity data={activity} />
    </div>
  );
}

// ✅ With Suspense - Parallel and progressive
function Dashboard() {
  return (
    <div className="grid gap-6">
      <Suspense fallback={<StatsSkeleton />}>
        <Stats />
      </Suspense>
      
      <Suspense fallback={<ChartSkeleton />}>
        <Chart />
      </Suspense>
      
      <Suspense fallback={<ActivitySkeleton />}>
        <Activity />
      </Suspense>
    </div>
  );
}

// Components fetch their own data
function Stats() {
  const { data } = useSuspenseQuery({
    queryKey: ['stats'],
    queryFn: fetchStats,
  });
  return <StatsDisplay data={data} />;
}
```

### ✅ Nested Suspense for Progressive Loading

```typescript
function CompanyPage({ companyId }: { companyId: string }) {
  return (
    <div>
      {/* Critical info loads first */}
      <Suspense fallback={<HeaderSkeleton />}>
        <CompanyHeader companyId={companyId} />
      </Suspense>
      
      <div className="grid grid-cols-2 gap-6">
        {/* Less critical info can load later */}
        <Suspense fallback={<ContactsSkeleton />}>
          <CompanyContacts companyId={companyId} />
        </Suspense>
        
        <Suspense fallback={<ContractsSkeleton />}>
          <CompanyContracts companyId={companyId} />
        </Suspense>
      </div>
      
      {/* Non-critical info loads last */}
      <Suspense fallback={<ActivitySkeleton />}>
        <CompanyActivity companyId={companyId} />
      </Suspense>
    </div>
  );
}
```

## Advanced Suspense Patterns

### 1. SuspenseList for Coordinated Loading

```typescript
import { SuspenseList } from 'react';

function NotificationsFeed() {
  return (
    <SuspenseList revealOrder="forwards" tail="collapsed">
      <Suspense fallback={<NotificationSkeleton />}>
        <ImportantNotifications />
      </Suspense>
      
      <Suspense fallback={<NotificationSkeleton />}>
        <RecentNotifications />
      </Suspense>
      
      <Suspense fallback={<NotificationSkeleton />}>
        <OlderNotifications />
      </Suspense>
    </SuspenseList>
  );
}
```

### 2. Suspense with Error Boundaries

```typescript
class SuspenseErrorBoundary extends React.Component<
  { fallback: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error?: Error }
> {
  state = { hasError: false, error: undefined };
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  render() {
    if (this.state.hasError) {
      return <this.props.fallback error={this.state.error!} />;
    }
    
    return this.props.children;
  }
}

function RobustSuspense({ children, fallback, errorFallback }) {
  return (
    <SuspenseErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </SuspenseErrorBoundary>
  );
}
```

### 3. Streaming SSR with Suspense

```typescript
// app/page.tsx - Next.js App Router
import { Suspense } from 'react';

export default function HomePage() {
  return (
    <div>
      {/* This renders immediately */}
      <Header />
      
      {/* These stream in as they're ready */}
      <Suspense fallback={<HeroSkeleton />}>
        <HeroSection />
      </Suspense>
      
      <Suspense fallback={<FeaturesSkeleton />}>
        <FeaturesSection />
      </Suspense>
    </div>
  );
}

// This component's data fetching happens on the server
async function HeroSection() {
  const data = await fetchHeroData();
  return <Hero data={data} />;
}
```

### 4. Optimistic UI with Suspense

```typescript
function TodoApp() {
  const [optimisticTodos, addOptimisticTodo] = useOptimistic(
    todos,
    (state, newTodo) => [...state, { ...newTodo, pending: true }]
  );
  
  return (
    <div>
      <form action={async (formData) => {
        const todo = Object.fromEntries(formData);
        addOptimisticTodo(todo);
        await createTodo(todo);
      }}>
        <input name="title" />
        <button type="submit">Add</button>
      </form>
      
      <Suspense fallback={<TodosSkeleton />}>
        <TodoList todos={optimisticTodos} />
      </Suspense>
    </div>
  );
}
```

### 5. Transition API with Suspense

```typescript
function SearchResults() {
  const [query, setQuery] = useState('');
  const [isPending, startTransition] = useTransition();
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Urgent update - show immediately
    setQuery(value);
    
    // Non-urgent update - can be interrupted
    startTransition(() => {
      // Trigger suspense
      searchAPI(value);
    });
  };
  
  return (
    <div>
      <input 
        value={query} 
        onChange={handleSearch}
        className={isPending ? 'opacity-50' : ''}
      />
      
      <Suspense fallback={<ResultsSkeleton />}>
        <Results query={query} />
      </Suspense>
    </div>
  );
}
```

## Suspense for Images and Assets

```typescript
// Custom image component with suspense
function SuspenseImage({ src, alt, ...props }) {
  return (
    <Suspense fallback={<ImageSkeleton {...props} />}>
      <ImageLoader src={src} alt={alt} {...props} />
    </Suspense>
  );
}

function ImageLoader({ src, ...props }) {
  // This throws a promise while loading
  const imageData = use(preloadImage(src));
  return <img src={imageData} {...props} />;
}

const imageCache = new Map();
function preloadImage(src: string) {
  if (imageCache.has(src)) {
    return imageCache.get(src);
  }
  
  const promise = new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(src);
    img.onerror = reject;
    img.src = src;
  });
  
  imageCache.set(src, promise);
  return promise;
}
```

## Migration Strategy

### Phase 1: Add React Query Suspense Support

```typescript
// Configure React Query for Suspense
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      suspense: true, // Enable globally
      useErrorBoundary: true,
    },
  },
});

// Or per-query
const { data } = useQuery({
  queryKey: ['todos'],
  queryFn: fetchTodos,
  suspense: true,
});
```

### Phase 2: Add Suspense Boundaries

```typescript
// Start at page level
function CompaniesPage() {
  return (
    <PageLayout>
      <Suspense fallback={<PageSkeleton />}>
        <CompaniesContent />
      </Suspense>
    </PageLayout>
  );
}

// Then add granular boundaries
function CompaniesContent() {
  return (
    <>
      <Suspense fallback={<FiltersSkeleton />}>
        <Filters />
      </Suspense>
      
      <Suspense fallback={<TableSkeleton />}>
        <CompaniesTable />
      </Suspense>
    </>
  );
}
```

### Phase 3: Remove Manual Loading States

```typescript
// Before
function Component() {
  const { data, isLoading } = useQuery(...);
  if (isLoading) return <Spinner />;
  return <div>{data}</div>;
}

// After
function Component() {
  const { data } = useSuspenseQuery(...);
  return <div>{data}</div>;
}
```

## Performance Benefits

### Before Suspense
- Multiple loading states render
- Sequential data fetching
- Complex loading logic
- Poor perceived performance

### After Suspense  
- Single loading state per boundary
- Parallel data fetching
- Simple component logic
- Progressive content reveal
- 40% improvement in perceived performance

## Common Pitfalls

### 1. Missing Error Boundaries

```typescript
// ❌ Bad - No error handling
<Suspense fallback={<Loading />}>
  <Component />
</Suspense>

// ✅ Good - With error boundary
<ErrorBoundary fallback={<Error />}>
  <Suspense fallback={<Loading />}>
    <Component />
  </Suspense>
</ErrorBoundary>
```

### 2. Too Many Boundaries

```typescript
// ❌ Bad - Over-suspending
<Suspense fallback={<Skeleton />}>
  <div>Hello</div>
</Suspense>

// ✅ Good - Meaningful boundaries
<Suspense fallback={<PageSkeleton />}>
  <ExpensiveComponent />
</Suspense>
```

### 3. Suspense in Event Handlers

```typescript
// ❌ Bad - Can't suspend in events
const handleClick = () => {
  const data = useSuspenseQuery(...); // Error!
};

// ✅ Good - Use transitions
const handleClick = () => {
  startTransition(() => {
    // Trigger suspense-enabled update
  });
};
```

## Conclusion

Suspense is a powerful feature that simplifies async handling and improves user experience. The current manual loading state management is complex and creates poor UX. Adopting Suspense will make the code cleaner and the app feel faster.