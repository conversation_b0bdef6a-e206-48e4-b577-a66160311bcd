# Missing Web Workers for CPU-Intensive Tasks

## TL;DR

**CPU-intensive operations run on the main thread, blocking user interactions and causing the UI to freeze.** PDF processing, data parsing, and complex calculations should use Web Workers.

## The Problem

Running heavy tasks on the main thread causes:
- **U<PERSON> freezes** - Interface becomes completely unresponsive
- **Dropped frames** - Animations stutter and jank
- **Input delay** - Clicks and keystrokes are delayed
- **Poor INP scores** - Interaction to Next Paint metrics fail
- **Browser warnings** - "Page unresponsive" dialogs

## Current Issues Found

Analysis reveals:
- PDF parsing blocks UI
- CSV/Excel processing on main thread
- Complex data transformations synchronous
- Large JSON parsing freezes page
- No worker usage found

### Real Examples

```typescript
// ❌ Current implementation - Blocks main thread
function processLargeCSV(file: File) {
  const reader = new FileReader();
  
  reader.onload = (e) => {
    const csvText = e.target.result as string;
    
    // This blocks the UI!
    const rows = csvText.split('\n');
    const parsed = rows.map(row => {
      const columns = row.split(',');
      return {
        company: columns[0],
        value: parseFloat(columns[1]),
        date: new Date(columns[2]),
        // ... complex processing
      };
    });
    
    setData(parsed);
  };
  
  reader.readAsText(file);
}

// ❌ PDF processing blocking UI
async function extractPDFText(file: File) {
  const arrayBuffer = await file.arrayBuffer();
  
  // Heavy operation on main thread!
  const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
  const textContent = [];
  
  for (let i = 1; i <= pdf.numPages; i++) {
    const page = await pdf.getPage(i);
    const content = await page.getTextContent();
    textContent.push(content.items.map(item => item.str).join(' '));
  }
  
  return textContent.join('\n');
}
```

## Web Worker Solutions

### ✅ CSV Processing Worker

```typescript
// workers/csv-processor.worker.ts
import Papa from 'papaparse';

self.addEventListener('message', async (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'PROCESS_CSV':
      try {
        // Parse CSV in worker thread
        const results = Papa.parse(data, {
          header: true,
          dynamicTyping: true,
          skipEmptyLines: true,
          transform: (value, field) => {
            // Custom transformations
            if (field === 'date') return new Date(value);
            if (field === 'amount') return parseFloat(value) || 0;
            return value;
          },
        });
        
        // Additional processing
        const processed = results.data.map(row => ({
          ...row,
          calculated: row.amount * row.quantity,
          formatted: formatCurrency(row.amount),
        }));
        
        self.postMessage({
          type: 'CSV_PROCESSED',
          data: processed,
          meta: results.meta,
        });
      } catch (error) {
        self.postMessage({
          type: 'CSV_ERROR',
          error: error.message,
        });
      }
      break;
  }
});

// Component using worker
function CSVImporter() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const workerRef = useRef<Worker>();
  
  useEffect(() => {
    workerRef.current = new Worker(
      new URL('../workers/csv-processor.worker.ts', import.meta.url),
      { type: 'module' }
    );
    
    workerRef.current.onmessage = (event) => {
      const { type, data, error } = event.data;
      
      switch (type) {
        case 'CSV_PROCESSED':
          setData(data);
          setLoading(false);
          break;
        case 'CSV_ERROR':
          console.error('CSV processing error:', error);
          setLoading(false);
          break;
        case 'PROGRESS':
          setProgress(data);
          break;
      }
    };
    
    return () => workerRef.current?.terminate();
  }, []);
  
  const handleFileSelect = async (file: File) => {
    setLoading(true);
    const text = await file.text();
    workerRef.current?.postMessage({
      type: 'PROCESS_CSV',
      data: text,
    });
  };
  
  return (
    <div>
      <FileInput 
        accept=".csv"
        onChange={handleFileSelect}
        disabled={loading}
      />
      
      {loading && (
        <div>
          <Spinner />
          <ProgressBar value={progress} />
          <p>Processing CSV in background...</p>
        </div>
      )}
      
      {data.length > 0 && (
        <DataTable data={data} />
      )}
    </div>
  );
}
```

### ✅ PDF Processing Worker

```typescript
// workers/pdf-processor.worker.ts
import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js for worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 
  'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

interface PDFProcessingResult {
  text: string;
  metadata: {
    pages: number;
    title?: string;
    author?: string;
  };
  images: string[];
}

self.addEventListener('message', async (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'PROCESS_PDF':
      try {
        const pdf = await pdfjsLib.getDocument(data).promise;
        const metadata = await pdf.getMetadata();
        
        const textContent: string[] = [];
        const images: string[] = [];
        
        // Process each page
        for (let i = 1; i <= pdf.numPages; i++) {
          // Send progress updates
          self.postMessage({
            type: 'PROGRESS',
            data: (i / pdf.numPages) * 100,
          });
          
          const page = await pdf.getPage(i);
          
          // Extract text
          const content = await page.getTextContent();
          const pageText = content.items
            .map((item: any) => item.str)
            .join(' ');
          textContent.push(pageText);
          
          // Extract images (optional)
          const ops = await page.getOperatorList();
          for (let j = 0; j < ops.fnArray.length; j++) {
            if (ops.fnArray[j] === pdfjsLib.OPS.paintImageXObject) {
              const imgIndex = ops.argsArray[j][0];
              const img = await page.getImage(imgIndex);
              // Process image...
            }
          }
        }
        
        const result: PDFProcessingResult = {
          text: textContent.join('\n'),
          metadata: {
            pages: pdf.numPages,
            title: metadata.info?.Title,
            author: metadata.info?.Author,
          },
          images,
        };
        
        self.postMessage({
          type: 'PDF_PROCESSED',
          data: result,
        });
      } catch (error) {
        self.postMessage({
          type: 'PDF_ERROR',
          error: error.message,
        });
      }
      break;
  }
});

// React hook for PDF processing
function usePDFProcessor() {
  const [result, setResult] = useState<PDFProcessingResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const workerRef = useRef<Worker>();
  
  useEffect(() => {
    workerRef.current = new Worker(
      new URL('../workers/pdf-processor.worker.ts', import.meta.url),
      { type: 'module' }
    );
    
    workerRef.current.onmessage = (event) => {
      const { type, data } = event.data;
      
      switch (type) {
        case 'PDF_PROCESSED':
          setResult(data);
          setLoading(false);
          break;
        case 'PROGRESS':
          setProgress(data);
          break;
        case 'PDF_ERROR':
          console.error('PDF processing error:', data);
          setLoading(false);
          break;
      }
    };
    
    return () => workerRef.current?.terminate();
  }, []);
  
  const processPDF = useCallback(async (file: File) => {
    setLoading(true);
    setProgress(0);
    
    const arrayBuffer = await file.arrayBuffer();
    workerRef.current?.postMessage({
      type: 'PROCESS_PDF',
      data: arrayBuffer,
    }, [arrayBuffer]); // Transfer ownership for performance
  }, []);
  
  return { processPDF, result, loading, progress };
}
```

### ✅ Data Transformation Worker Pool

```typescript
// workers/transform-pool.ts
class WorkerPool {
  private workers: Worker[] = [];
  private queue: Array<{
    data: any;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];
  private busyWorkers = new Set<Worker>();
  
  constructor(
    private workerScript: string,
    private poolSize = navigator.hardwareConcurrency || 4
  ) {
    this.initializeWorkers();
  }
  
  private initializeWorkers() {
    for (let i = 0; i < this.poolSize; i++) {
      const worker = new Worker(this.workerScript, { type: 'module' });
      
      worker.onmessage = (event) => {
        this.busyWorkers.delete(worker);
        
        const { resolve } = this.queue.shift()!;
        resolve(event.data);
        
        this.processNext();
      };
      
      worker.onerror = (error) => {
        this.busyWorkers.delete(worker);
        
        const { reject } = this.queue.shift()!;
        reject(error);
        
        this.processNext();
      };
      
      this.workers.push(worker);
    }
  }
  
  async process(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.queue.push({ data, resolve, reject });
      this.processNext();
    });
  }
  
  private processNext() {
    if (this.queue.length === 0) return;
    
    const availableWorker = this.workers.find(
      w => !this.busyWorkers.has(w)
    );
    
    if (availableWorker) {
      this.busyWorkers.add(availableWorker);
      const { data } = this.queue[0];
      availableWorker.postMessage(data);
    }
  }
  
  terminate() {
    this.workers.forEach(w => w.terminate());
    this.workers = [];
    this.queue = [];
  }
}

// Usage with React
function useWorkerPool() {
  const poolRef = useRef<WorkerPool>();
  
  useEffect(() => {
    poolRef.current = new WorkerPool(
      new URL('../workers/transform.worker.ts', import.meta.url).href
    );
    
    return () => poolRef.current?.terminate();
  }, []);
  
  const transformData = useCallback(async (items: any[]) => {
    if (!poolRef.current) return [];
    
    // Split work across workers
    const chunkSize = Math.ceil(items.length / navigator.hardwareConcurrency);
    const chunks = [];
    
    for (let i = 0; i < items.length; i += chunkSize) {
      chunks.push(items.slice(i, i + chunkSize));
    }
    
    // Process chunks in parallel
    const results = await Promise.all(
      chunks.map(chunk => poolRef.current!.process(chunk))
    );
    
    return results.flat();
  }, []);
  
  return { transformData };
}
```

## Advanced Worker Patterns

### 1. Shared Workers for Cross-Tab Communication

```typescript
// shared-worker.ts
let connections = 0;
const ports: MessagePort[] = [];
const sharedState = new Map();

self.addEventListener('connect', (event: any) => {
  const port = event.ports[0];
  ports.push(port);
  connections++;
  
  port.addEventListener('message', (e: MessageEvent) => {
    const { type, key, value } = e.data;
    
    switch (type) {
      case 'SET':
        sharedState.set(key, value);
        // Broadcast to all tabs
        ports.forEach(p => {
          p.postMessage({ type: 'UPDATE', key, value });
        });
        break;
        
      case 'GET':
        port.postMessage({ 
          type: 'VALUE', 
          key, 
          value: sharedState.get(key) 
        });
        break;
    }
  });
  
  port.start();
});

// React hook for shared worker
function useSharedState<T>(key: string, initialValue: T) {
  const [value, setValue] = useState<T>(initialValue);
  const workerRef = useRef<SharedWorker>();
  
  useEffect(() => {
    workerRef.current = new SharedWorker(
      new URL('../workers/shared-worker.ts', import.meta.url)
    );
    
    workerRef.current.port.onmessage = (event) => {
      if (event.data.type === 'UPDATE' && event.data.key === key) {
        setValue(event.data.value);
      }
    };
    
    workerRef.current.port.start();
    
    // Get initial value
    workerRef.current.port.postMessage({ type: 'GET', key });
    
    return () => workerRef.current?.port.close();
  }, [key]);
  
  const setSharedValue = useCallback((newValue: T) => {
    setValue(newValue);
    workerRef.current?.port.postMessage({ 
      type: 'SET', 
      key, 
      value: newValue 
    });
  }, [key]);
  
  return [value, setSharedValue] as const;
}
```

### 2. Offscreen Canvas Worker

```typescript
// canvas-worker.ts
let canvas: OffscreenCanvas;
let ctx: OffscreenCanvasRenderingContext2D;

self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'INIT':
      canvas = data.canvas;
      ctx = canvas.getContext('2d')!;
      break;
      
    case 'RENDER_CHART':
      renderChart(data);
      break;
  }
});

function renderChart(data: ChartData) {
  // Heavy canvas operations in worker
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // Complex chart rendering...
  data.points.forEach((point, i) => {
    const x = (i / data.points.length) * canvas.width;
    const y = canvas.height - (point.value / data.max) * canvas.height;
    
    ctx.beginPath();
    ctx.arc(x, y, 3, 0, Math.PI * 2);
    ctx.fill();
  });
  
  // Commit frame
  if ('requestAnimationFrame' in self) {
    requestAnimationFrame(() => {
      self.postMessage({ type: 'FRAME_READY' });
    });
  }
}
```

### 3. ComLink for Easier Worker Communication

```typescript
// worker with Comlink
import * as Comlink from 'comlink';

class DataProcessor {
  async processLargeDataset(data: any[]) {
    // Complex processing
    return data.map(item => ({
      ...item,
      processed: true,
      timestamp: Date.now(),
    }));
  }
  
  async generateReport(data: any[]) {
    // Generate complex report
    return {
      summary: this.calculateSummary(data),
      charts: this.generateCharts(data),
      tables: this.formatTables(data),
    };
  }
  
  private calculateSummary(data: any[]) {
    // Implementation
  }
  
  private generateCharts(data: any[]) {
    // Implementation
  }
  
  private formatTables(data: any[]) {
    // Implementation
  }
}

Comlink.expose(DataProcessor);

// Using in React
import * as Comlink from 'comlink';

function useDataProcessor() {
  const processorRef = useRef<any>();
  
  useEffect(() => {
    const worker = new Worker(
      new URL('../workers/data-processor.worker.ts', import.meta.url)
    );
    
    processorRef.current = Comlink.wrap(worker);
    
    return () => worker.terminate();
  }, []);
  
  const processData = useCallback(async (data: any[]) => {
    if (!processorRef.current) return [];
    
    // Call worker methods like regular async functions!
    const processed = await processorRef.current.processLargeDataset(data);
    return processed;
  }, []);
  
  return { processData };
}
```

## Performance Monitoring

```typescript
// Monitor worker performance
function useWorkerMetrics() {
  const metricsRef = useRef({
    tasksQueued: 0,
    tasksCompleted: 0,
    totalProcessingTime: 0,
    averageProcessingTime: 0,
  });
  
  const trackTask = useCallback((
    startTime: number,
    endTime: number
  ) => {
    const duration = endTime - startTime;
    metricsRef.current.tasksCompleted++;
    metricsRef.current.totalProcessingTime += duration;
    metricsRef.current.averageProcessingTime = 
      metricsRef.current.totalProcessingTime / 
      metricsRef.current.tasksCompleted;
    
    if (duration > 1000) {
      console.warn(`Slow worker task: ${duration}ms`);
    }
  }, []);
  
  return { metrics: metricsRef.current, trackTask };
}
```

## Best Practices

1. **Transfer ownership** when possible to avoid copying
2. **Use worker pools** for parallel processing
3. **Implement progress updates** for long tasks
4. **Handle errors gracefully** with fallbacks
5. **Terminate workers** when done to free resources

## Common Use Cases

- CSV/Excel parsing
- PDF text extraction
- Image processing
- Data encryption/decryption
- Complex calculations
- Search indexing
- Video/Audio processing
- Real-time data transformation

## Conclusion

Web Workers are essential for maintaining responsive UIs when dealing with CPU-intensive tasks. The current implementation blocks the main thread, causing poor user experience. Moving heavy operations to workers will keep the UI smooth and responsive.