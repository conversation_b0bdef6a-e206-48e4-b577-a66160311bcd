# Notification Menu Force Re-render Fix

## TL;DR

The NotificationMenu component forces a complete re-render of the Inbox component by changing its key on popover close. This is an anti-pattern that causes unnecessary component unmounting/remounting and potential memory leaks.

## The Problem

Current implementation uses a changing key to force re-render:

```tsx
const handleOpenChange = (open: boolean) => {
  if (!open) {
    setInboxKey(Date.now()); // Forces unmount/remount
  }
  setIsInboxOpen(open);
};

// Later in JSX
<Inbox key={inboxKey} ... />
```

Issues:

1. Complete component unmount/remount on every close
2. Lost component state
3. Potential memory leaks from uncleaned subscriptions
4. Performance degradation from recreating entire component tree
5. Janky user experience

## Better Solutions

### Option 1: Use Novu's refetch API (Preferred)

```tsx
import { useNovu } from "@novu/react";

export function NotificationMenu() {
  const { refetch } = useNovu();

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Refresh notifications data without unmounting
      refetch();
    }
    setIsInboxOpen(open);
  };
}
```

### Option 2: Use a refresh trigger

```tsx
export function NotificationMenu() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setRefreshTrigger(prev => prev + 1);
    }
    setIsInboxOpen(open);
  };

  // Pass refreshTrigger as a prop instead of key
  return <Inbox refreshTrigger={refreshTrigger} ... />
}
```

### Option 3: Event-based updates

```tsx
useEffect(() => {
  const handleNotificationUpdate = () => {
    // Trigger data refresh
  };

  window.addEventListener('notification-update', handleNotificationUpdate);

  return () => {
    window.removeEventListener('notification-update', handleNotificationUpdate);
  };
}, []);
```

## Performance Impact

### Before

- Full component tree recreation: ~50-100ms
- Memory spikes from unmount/remount
- Lost WebSocket connections
- Potential zombie subscriptions

### After

- Data refresh only: ~5-10ms
- Stable memory usage
- Maintained connections
- Smooth user experience

## Migration Steps

1. Check Novu's latest API for built-in refresh methods
2. Implement proper data refresh without key manipulation
3. Add cleanup for any subscriptions
4. Test notification synchronization across tabs
