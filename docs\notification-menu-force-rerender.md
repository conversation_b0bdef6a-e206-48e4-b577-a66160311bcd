# Notification Menu Force Re-render

## Issue Description

The `NotificationMenu` component uses a hacky workaround that forces the entire Inbox component to re-render by changing its key prop with `Date.now()`. This causes unnecessary re-renders and destroys the component state.

## Problem Code

In `apps/crm/src/app/account/components/notification-menu.tsx`:

```tsx
export function NotificationMenu() {
  const [isInboxOpen, setIsInboxOpen] = useState(false);
  const [inboxKey, setInboxKey] = useState(Date.now());
  
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setInboxKey(Date.now()); // Forces complete re-render
    }
    setIsInboxOpen(open);
  };

  return (
    <Inbox
      key={inboxKey} // Key change destroys and recreates component
      // ... other props
    >
```

## Why This Is a Problem

1. **Complete re-initialization**: Entire Inbox component is destroyed and recreated
2. **Lost state**: Any internal state in the Inbox is lost
3. **Network requests**: May trigger duplicate API calls
4. **Animation interruption**: Any ongoing animations are abruptly stopped
5. **Memory churn**: Creates garbage collection pressure

## Optimized Solution

Use proper state management or polling instead of force re-render:

```tsx
// Option 1: Use a refresh callback
export function NotificationMenu() {
  const [isInboxOpen, setIsInboxOpen] = useState(false);
  const inboxRef = useRef<InboxHandle>(null);
  
  const handleOpenChange = (open: boolean) => {
    if (!open && inboxRef.current) {
      inboxRef.current.refresh(); // Call refresh method instead
    }
    setIsInboxOpen(open);
  };

  return (
    <Inbox ref={inboxRef} {/* ... */}>
  );
}

// Option 2: Use polling or WebSocket updates
export function NotificationMenu() {
  const { data, refetch } = useNotifications();
  
  useEffect(() => {
    if (!isInboxOpen) return;
    
    const interval = setInterval(refetch, 30000); // Poll every 30s
    return () => clearInterval(interval);
  }, [isInboxOpen, refetch]);
}

// Option 3: Use React Query with proper invalidation
export function NotificationMenu() {
  const queryClient = useQueryClient();
  
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    }
    setIsInboxOpen(open);
  };
}
```

## Migration Strategy

1. Check if Novu provides a refresh API or method
2. Implement proper state synchronization
3. Use React Query or SWR for data fetching
4. Set up WebSocket connections for real-time updates
5. Remove the key-based re-render hack
6. Add proper error boundaries

## Performance Impact

- Prevents unnecessary component destruction
- Reduces memory allocation
- Maintains component state
- Smoother user experience
- Reduces API calls if implemented correctly