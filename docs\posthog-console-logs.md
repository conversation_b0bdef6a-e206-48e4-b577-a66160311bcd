# PostHog Console Logs in Production

## Issue Description

The PostHog analytics implementation contains console.log statements that will execute in production, exposing user IDs and properties. Additionally, the debug mode is only conditionally enabled but the console.log always runs.

## Problem Code

In `apps/crm/src/app/posthog-analytics.ts`:

```tsx
export function identifyUser(
  id: string,
  userProperties?: Record<string, unknown>
) {
  console.log("identifyUser", id, userProperties); // Always logs!
  if (posthog.__loaded) {
    posthog.register({ environment: env.NEXT_PUBLIC_ENVIRONMENT });
    posthog.identify(id, userProperties);
  }
}
```

## Why This Is a Problem

1. **Security risk**: Exposes user IDs and properties in browser console
2. **Performance impact**: Console operations are not free
3. **Privacy concerns**: User data visible to anyone with console access
4. **Production noise**: Clutters console in production
5. **Bundle size**: Debug code shipped to production

## Optimized Solution

Remove console.logs and use proper debug configuration:

```tsx
// Remove all console.logs from production
export function identifyUser(
  id: string,
  userProperties?: Record<string, unknown>
) {
  if (posthog.__loaded) {
    posthog.register({ environment: env.NEXT_PUBLIC_ENVIRONMENT });
    posthog.identify(id, userProperties);
    
    // Only log in development
    if (env.NODE_ENV === "development") {
      console.debug("PostHog: Identified user", { id });
    }
  }
}

// Or use a debug utility
const debug = env.NODE_ENV === "development" 
  ? console.debug.bind(console, "[PostHog]")
  : () => {};

export function identifyUser(
  id: string,
  userProperties?: Record<string, unknown>
) {
  debug("Identifying user", id);
  // ... rest of code
}

// Better: Use PostHog's built-in debug mode
export function initPostHog() {
  if (posthog.__loaded) return;
  
  posthog.init(env.NEXT_PUBLIC_POSTHOG_KEY, {
    name: "crm",
    api_host: "/ingest",
    debug: env.NODE_ENV === "development", // Built-in debug
    // ... other config
  });
}
```

## Migration Strategy

1. Remove all console.log statements from analytics code
2. Use environment-based conditional logging
3. Enable compiler.removeConsole in next.config.js
4. Use PostHog's built-in debug mode
5. Consider using a logging library with levels
6. Add linting rules to catch console statements

## Performance Impact

- Prevents sensitive data exposure
- Reduces console operations in production
- Smaller bundle size without debug code
- Cleaner production console output
- Better security posture