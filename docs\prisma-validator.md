# Migrating from `Prisma.validator()` to `satisfies` with Extensions

## TL;DR

**Stop using `Prisma.validator()` - use TypeScript's `satisfies` operator instead.** It provides the same type safety with zero runtime overhead and works better with Prisma Client extensions.

## The Problem with `Prisma.validator()`

When using Prisma Client extensions (like our `fullName` computed fields), `Prisma.validator()` causes several issues:

- **TypeScript compilation errors** - Types become `never` in some cases
- **Runtime overhead** - Unnecessary function calls for compile-time type checking
- **Extension incompatibility** - Doesn't work well with computed fields
- **Misleading name** - Doesn't actually perform runtime validation

## Migration Examples

### ❌ Old Way (Prisma.validator)

```typescript
// DON'T DO THIS
const noteSelect = {
  id: true,
  title: true,
  createdBy: {
    select: {
      id: true,
      fullName: true, // This breaks with extensions
    }
  }
} satisfies Prisma.NoteSelect & {
  // Ugly hack required for extensions
  createdBy: {
    select: {
      id: true;
      fullName: true;
    };
  };
};

// Or worse:
const validatedSelect = Prisma.validator<Prisma.NoteSelect>()(noteSelect);
```

### ✅ New Way (satisfies)

```typescript
// DO THIS INSTEAD
export const noteSelect = {
  id: true,
  title: true,
  description: true,
  createdAt: true,
  updatedAt: true,
  createdById: true,
  companyId: true,
  profileType: true,
  siteId: true,
  isHidden: true,
  hiddenUpdatedAt: true,
  company: {
    select: {
      name: true,
      registrationNumber: true
    }
  },
  site: {
    select: {
      siteRefId: true,
      entityAddress: {
        select: {
          displayName: true,
          postcode: true
        }
      }
    }
  },
  siteMeters: {
    select: {
      id: true,
      electricSiteMeter: {
        select: {
          mpanValue: true
        }
      },
      gasSiteMeter: {
        select: {
          mprnValue: true
        }
      }
    }
  },
  createdBy: {
    select: {
      id: true,
      // Include the fields that fullName depends on
      forename: true,
      surname: true
    }
  },
  hiddenUpdatedBy: {
    select: {
      id: true,
      forename: true,
      surname: true
    }
  }
} as const satisfies Prisma.NoteSelect;

// Extract types for reuse
export type NoteSelectType = typeof noteSelect;
export type NotePayload = Prisma.NoteGetPayload<{ select: typeof noteSelect }>;
```

## Handling Computed Fields from Extensions

Since computed fields aren't part of Prisma's generated types, you need to manually augment the types:

```typescript
// Create extended type that includes computed fields
export type NoteWithExtensions = Prisma.NoteGetPayload<{ select: typeof noteSelect }> & {
  createdBy: {
    id: string;
    forename: string;
    surname: string;
    fullName: string; // Added by extension
  } | null;
  hiddenUpdatedBy: {
    id: string;
    forename: string;
    surname: string;
    fullName: string; // Added by extension
  } | null;
};

// Usage function with proper typing
export const findNoteWithExtensions = async (
  prisma: PrismaExtendedClient,
  id: string
): Promise<NoteWithExtensions | null> => {
  return await prisma.note.findUnique({
    where: { id },
    select: noteSelect
  });
  // fullName will be available at runtime due to extensions
};
```

## Fixing TypeScript Issues with Extensions

If you encounter `never` types when using extensions, add `as const` to your boolean values:

```typescript
// If you see TypeScript errors, try this:
export const noteSelectFixed = {
  id: true as const,
  title: true as const,
  createdBy: {
    select: {
      id: true as const,
      forename: true as const,
      surname: true as const
    }
  }
} satisfies Prisma.NoteSelect;
```

## Comparison Table

| Aspect | `Prisma.validator()` | `satisfies` |
|--------|---------------------|-------------|
| **Runtime overhead** | Small function call | Zero |
| **Type safety** | ✅ | ✅ |
| **Type inference** | ✅ | ✅ Better |
| **Bundle size** | Adds code | Zero impact |
| **TypeScript version** | Any | 4.9+ (we use 5.x) |
| **Prisma extensions compatibility** | ❌ Issues | ✅ Works well |
| **Error location** | Less precise | At source |
| **Prisma recommendation** | Deprecated | ✅ Recommended |

## Best Practices

### 1. Always use `satisfies` for new code

```typescript
const userSelect = {
  id: true,
  email: true,
  name: true
} as const satisfies Prisma.UserSelect;
```

### 2. Include dependency fields for computed fields

```typescript
const profileSelect = {
  id: true,
  // Include dependencies for fullName extension
  forename: true,
  surname: true,
  // fullName will be available at runtime
} as const satisfies Prisma.ProfileSelect;
```

### 3. Create proper types for extended results

```typescript
type ProfileWithFullName = Prisma.ProfileGetPayload<{ select: typeof profileSelect }> & {
  fullName: string;
};
```

### 4. Export reusable select objects

```typescript
// Good for consistency across the codebase
export const standardUserSelect = {
  id: true,
  email: true,
  forename: true,
  surname: true
} as const satisfies Prisma.UserSelect;
```

## Migration Checklist

- [ ] Replace all `Prisma.validator<T>()()` calls with `satisfies T`
- [ ] Add `as const` to select objects
- [ ] Include dependency fields for computed fields (e.g., `forename`, `surname`)
- [ ] Remove manual type annotations that are no longer needed
- [ ] Update type definitions to account for computed fields
- [ ] Test that extensions still work correctly
- [ ] Remove unused imports of `Prisma.validator`

## Why This Matters

1. **Performance**: Zero runtime overhead vs function calls
2. **Maintainability**: Better TypeScript errors and IntelliSense
3. **Compatibility**: Works seamlessly with our Prisma extensions
4. **Future-proof**: This is Prisma's recommended approach going forward

## Questions?

If you run into issues during migration, check:

1. Are you including all dependency fields for computed fields?
2. Are you using `as const` if TypeScript shows errors?
3. Are you properly typing the extended results?

For complex cases, refer to the examples in this guide or ask the team.
