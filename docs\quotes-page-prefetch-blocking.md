# Quotes Page Prefetch Blocking

## Issue Description

The QuotesPage server component uses `await queryClient.prefetchQuery()` which blocks the entire page render until the user data is fetched. This defeats the purpose of React Server Components' streaming capabilities.

## Problem Code

In `apps/crm/src/app/account/quotes/page.tsx`:

```tsx
export default async function QuotesPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery(supabaseUserQueryOptions);

  return (
    <div className="px-4">
      <QuotesDataTable />
    </div>
  );
}
```

## Why This Is a Problem

1. **Blocks streaming**: Page can't start rendering until prefetch completes
2. **No progressive enhancement**: Users see nothing while waiting
3. **Waterfall loading**: Sequential instead of parallel data fetching
4. **Poor perceived performance**: Longer time to first paint
5. **No error boundaries**: Prefetch errors break the entire page

## Optimized Solution

Use proper RSC patterns with streaming:

```tsx
// Option 1: Don't block on prefetch
export default async function QuotesPage() {
  const queryClient = getQueryClient();
  
  // Start prefetch but don't await
  queryClient.prefetchQuery(supabaseUserQueryOptions);

  return (
    <div className="px-4">
      <QuotesDataTable />
    </div>
  );
}

// Option 2: Use Suspense for streaming
import { Suspense } from 'react';

async function QuotesWithData() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery(supabaseUserQueryOptions);
  
  return <QuotesDataTable />;
}

export default function QuotesPage() {
  return (
    <div className="px-4">
      <Suspense fallback={<QuotesDataTableSkeleton />}>
        <QuotesWithData />
      </Suspense>
    </div>
  );
}

// Option 3: Parallel data fetching
export default async function QuotesPage() {
  const queryClient = getQueryClient();
  
  // Prefetch multiple queries in parallel
  await Promise.all([
    queryClient.prefetchQuery(supabaseUserQueryOptions),
    queryClient.prefetchQuery(quotesQueryOptions),
    // other data needs
  ]);

  return <QuotesDataTable />;
}

// Option 4: Move to client-side fetching if not critical
export default function QuotesPage() {
  // Let the client handle data fetching with proper loading states
  return (
    <div className="px-4">
      <QuotesDataTable />
    </div>
  );
}
```

## Migration Strategy

1. Identify which data is truly needed for initial render
2. Use Suspense boundaries for progressive loading
3. Move non-critical prefetches to client-side
4. Implement proper error boundaries
5. Add loading skeletons for better UX
6. Measure Core Web Vitals impact

## Performance Impact

- Faster Time to First Byte (TTFB)
- Progressive page rendering
- Better perceived performance
- Parallel data fetching
- Graceful error handling