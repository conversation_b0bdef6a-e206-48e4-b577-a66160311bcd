# React 19 forwardRef Removal Guide

## TL;DR

**React 19 no longer requires `forwardRef` - refs can be passed as regular props to function components.** This guide explains how to migrate existing `forwardRef` usage to the new pattern.

## What Changed in React 19

### Key Changes

1. **`forwardRef` is deprecated** - Will be removed in future React versions
2. **`ref` is now a regular prop** - Can be accessed like any other prop in function components
3. **Simpler component code** - No more wrapper functions needed
4. **Better TypeScript support** - Cleaner type definitions

### Migration Pattern

```typescript
// ❌ Old way (React 18 and earlier)
import { forwardRef } from 'react';

const MyInput = forwardRef<HTMLInputElement, InputProps>((props, ref) => {
  return <input {...props} ref={ref} />;
});

// ✅ New way (React 19+)
interface InputProps {
  placeholder?: string;
  ref?: React.Ref<HTMLInputElement>;
}

function MyInput({ placeholder, ref }: InputProps) {
  return <input placeholder={placeholder} ref={ref} />;
}
```

## Real Examples from Our Codebase

### Example 1: Simple Input Component

**Current code using forwardRef:**

```typescript
// apps/crm/src/components/ui/input.tsx
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
```

**Migrated to React 19:**

```typescript
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  ref?: React.Ref<HTMLInputElement>;
}

function Input({ className, type, ref, ...props }: InputProps) {
  return (
    <input
      type={type}
      className={cn(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm",
        className
      )}
      ref={ref}
      {...props}
    />
  )
}
```

### Example 2: Complex Dialog Component

**Current code using forwardRef:**

```typescript
// From dialog.tsx (simplified)
const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn("fixed left-[50%] top-[50%]", className)}
      {...props}
    >
      {children}
      <DialogPrimitive.Close>
        <X className="h-4 w-4" />
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
```

**Migrated to React 19:**

```typescript
interface DialogContentProps extends React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> {
  ref?: React.Ref<React.ElementRef<typeof DialogPrimitive.Content>>;
}

function DialogContent({ className, children, ref, ...props }: DialogContentProps) {
  return (
    <DialogPortal>
      <DialogOverlay />
      <DialogPrimitive.Content
        ref={ref}
        className={cn("fixed left-[50%] top-[50%]", className)}
        {...props}
      >
        {children}
        <DialogPrimitive.Close>
          <X className="h-4 w-4" />
        </DialogPrimitive.Close>
      </DialogPrimitive.Content>
    </DialogPortal>
  )
}
```

### Example 3: Custom Hook with Ref

**Current code using forwardRef:**

```typescript
const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, ...props }, ref) => {
    const internalRef = useRef<HTMLInputElement>(null);
    const combinedRef = useCombinedRefs(ref, internalRef);

    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter' && internalRef.current) {
          onSearch?.(internalRef.current.value);
        }
      };

      internalRef.current?.addEventListener('keydown', handleKeyDown);
      return () => {
        internalRef.current?.removeEventListener('keydown', handleKeyDown);
      };
    }, [onSearch]);

    return <input ref={combinedRef} {...props} />;
  }
);
```

**Migrated to React 19:**

```typescript
interface SearchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onSearch?: (value: string) => void;
  ref?: React.Ref<HTMLInputElement>;
}

function SearchInput({ onSearch, ref, ...props }: SearchInputProps) {
  const internalRef = useRef<HTMLInputElement>(null);
  const combinedRef = useCombinedRefs(ref, internalRef);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && internalRef.current) {
        onSearch?.(internalRef.current.value);
      }
    };

    internalRef.current?.addEventListener('keydown', handleKeyDown);
    return () => {
      internalRef.current?.removeEventListener('keydown', handleKeyDown);
    };
  }, [onSearch]);

  return <input ref={combinedRef} {...props} />;
}
```

## TypeScript Best Practices

### 1. Explicit Ref Type in Props

```typescript
// ✅ Good - Explicit ref type
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  ref?: React.Ref<HTMLButtonElement>;
  variant?: 'primary' | 'secondary';
}

function Button({ ref, variant = 'primary', ...props }: ButtonProps) {
  return <button ref={ref} {...props} />;
}
```

### 2. Using ComponentProps for Simplicity

```typescript
// ✅ Good - Let React handle the types
import type { ComponentProps } from "react";

function Input(props: ComponentProps<"input">) {
  return <input {...props} />;
}

function TextArea(props: ComponentProps<"textarea">) {
  return <textarea {...props} />;
}
```

### 3. Generic Components

```typescript
// Generic component that forwards refs
interface ListItemProps<T> {
  item: T;
  renderItem: (item: T) => React.ReactNode;
  ref?: React.Ref<HTMLLIElement>;
}

function ListItem<T>({ item, renderItem, ref, ...props }: ListItemProps<T>) {
  return (
    <li ref={ref} {...props}>
      {renderItem(item)}
    </li>
  );
}
```

## Migration Checklist

1. **Find all forwardRef usage:**

   ```bash
   grep -r "forwardRef" --include="*.tsx" --include="*.ts" .
   ```

2. **For each component:**
   - [ ] Remove the `forwardRef` wrapper
   - [ ] Add `ref` to the component's props interface
   - [ ] Move the component body to a regular function
   - [ ] Update TypeScript types to include ref
   - [ ] Test that ref forwarding still works

3. **Update imports:**
   - [ ] Remove `forwardRef` from React imports
   - [ ] Update any type imports if needed

4. **Test the changes:**
   - [ ] Verify refs are properly forwarded
   - [ ] Check TypeScript has no errors
   - [ ] Ensure no runtime errors

## Common Patterns and Solutions

### Pattern 1: Component with Display Name

```typescript
// ❌ Old way
const Input = React.forwardRef<HTMLInputElement, InputProps>((props, ref) => {
  return <input ref={ref} {...props} />;
});
Input.displayName = 'Input';

// ✅ New way
function Input(props: InputProps & { ref?: React.Ref<HTMLInputElement> }) {
  return <input {...props} />;
}
Input.displayName = 'Input';
```

### Pattern 2: Ref with Imperative Handle

```typescript
// ❌ Old way
const CustomInput = React.forwardRef<CustomHandle, Props>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    clear: () => {
      if (inputRef.current) inputRef.current.value = '';
    }
  }));

  return <input ref={inputRef} {...props} />;
});

// ✅ New way
interface CustomHandle {
  focus: () => void;
  clear: () => void;
}

interface Props {
  ref?: React.Ref<CustomHandle>;
  // other props
}

function CustomInput({ ref, ...props }: Props) {
  const inputRef = useRef<HTMLInputElement>(null);

  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    clear: () => {
      if (inputRef.current) inputRef.current.value = '';
    }
  }));

  return <input ref={inputRef} {...props} />;
}
```

### Pattern 3: Polymorphic Components

```typescript
// ✅ New way with polymorphic components
type PolymorphicRef<C extends React.ElementType> = React.ComponentPropsWithRef<C>['ref'];

type PolymorphicComponentProps<C extends React.ElementType, Props = {}> = Props &
  Omit<React.ComponentPropsWithRef<C>, keyof Props | 'as'> & {
    as?: C;
  };

function Box<C extends React.ElementType = 'div'>({
  as,
  ref,
  ...props
}: PolymorphicComponentProps<C>) {
  const Component = as || 'div';
  return <Component ref={ref} {...props} />;
}
```

## Important Considerations

### 1. Props Order Matters

```typescript
// ❌ Bad - ref might be overridden
function Input({ ref, ...props }: InputProps) {
  return <input {...props} ref={ref} />;
}

// ✅ Good - ref is applied last
function Input({ ref, ...props }: InputProps) {
  return <input ref={ref} {...props} />;
}
```

### 2. Class Components Still Different

Remember that refs to class components still reference the component instance, not a DOM element:

```typescript
// Class components - ref points to component instance
class MyClassComponent extends React.Component {
  someMethod() {
    console.log('Called on instance');
  }
  render() {
    return <div>Class Component</div>;
  }
}

// Usage
const ref = useRef<MyClassComponent>(null);
<MyClassComponent ref={ref} />
// ref.current.someMethod() - works!
```

### 3. Conditional Refs

```typescript
// Handle optional refs safely
function ConditionalRefComponent({ shouldAttachRef, ref, ...props }: Props) {
  return <input ref={shouldAttachRef ? ref : undefined} {...props} />;
}
```

## Benefits of Migration

1. **Simpler Code** - No wrapper function needed
2. **Better Performance** - One less function call
3. **Easier to Understand** - Refs work like any other prop
4. **Type Safety** - More straightforward TypeScript types
5. **Future Proof** - Ready for upcoming React versions

## Tools and Resources

### ESLint Rule to Catch forwardRef

```json
{
  "rules": {
    "no-restricted-imports": [
      "error",
      {
        "paths": [{
          "name": "react",
          "importNames": ["forwardRef"],
          "message": "forwardRef is deprecated in React 19. Pass ref as a prop instead."
        }]
      }
    ]
  }
}
```

### Codemod for Automatic Migration

```bash
# Use the React 19 codemod
npx react-codemod react-19-remove-forward-ref path/to/src
```

## Current forwardRef Usage in Our Codebase

Based on analysis, we have forwardRef usage in the following areas:

### 1. Verification Components (6 files)
- `/apps/quote/components/verification/verification-value.tsx`
- `/apps/quote/components/verification/verification-trigger.tsx`
- `/apps/quote/components/verification/verification-resend.tsx`
- `/apps/quote/components/verification/verification-otp.tsx`
- `/apps/quote/components/verification/verification-change.tsx`
- `/apps/quote/components/verification/verification-cancel.tsx`

### 2. MPXN Input Components (2 files)
- `/apps/quote/components/mpxn/mprn-input.tsx`
- `/apps/quote/components/mpxn/mpan-input.tsx`

### Migration Priority
1. **High Priority**: Verification components - Core functionality for quote app
2. **Medium Priority**: MPXN inputs - Used in multiple places

## Real Migration Examples from Our Codebase

### Example 1: VerificationTrigger Component

**Before (with forwardRef):**
```typescript
// apps/quote/components/verification/verification-trigger.tsx
import { forwardRef } from "react";

export const VerificationTrigger = forwardRef<
  HTMLButtonElement,
  VerificationTriggerProps
>(
  (
    {
      children = "Verify",
      loadingText = "Sending...",
      className,
      variant = "outline",
      disabled,
      ...props
    },
    ref
  ) => {
    const { state, config, actions } = useVerification();
    // ... component logic
    return <Button ref={ref} {...props}>{children}</Button>;
  }
);

VerificationTrigger.displayName = "VerificationTrigger";
```

**After (React 19):**
```typescript
// apps/quote/components/verification/verification-trigger.tsx
export interface VerificationTriggerProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Button>, "onClick"> {
  loadingText?: string;
  ref?: React.Ref<HTMLButtonElement>;
}

export function VerificationTrigger({
  children = "Verify",
  loadingText = "Sending...",
  className,
  variant = "outline",
  disabled,
  ref,
  ...props
}: VerificationTriggerProps) {
  const { state, config, actions } = useVerification();
  // ... component logic
  return <Button ref={ref} {...props}>{children}</Button>;
}
```

### Example 2: MPANInput Component

**Before (with forwardRef):**
```typescript
// apps/quote/components/mpxn/mpan-input.tsx
import { forwardRef } from "react";

export const MPANInput = forwardRef<HTMLInputElement, MPANInputProps>(
  function MPANInput(
    {
      value,
      isSelected = false,
      isEditable = false,
      onChange,
      onKeyDown,
      onPaste,
      className,
      ...props
    },
    ref
  ) {
    return (
      <input
        {...props}
        ref={ref}
        readOnly={!isEditable}
        disabled={!isEditable}
        value={value}
        onChange={e => onChange?.(e.target.value)}
        onKeyDown={onKeyDown}
        onPaste={onPaste}
        className={cn(
          "w-full rounded-md border-2 bg-primary/5 px-0.5 py-1 text-center",
          className
        )}
      />
    );
  }
);
```

**After (React 19):**
```typescript
// apps/quote/components/mpxn/mpan-input.tsx
type MPANInputProps = Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  "onChange" | "value" | "onKeyDown" | "onPaste"
> & {
  value: string;
  isSelected?: boolean;
  isEditable?: boolean;
  onChange?: (value: string) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onPaste?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
  ref?: React.Ref<HTMLInputElement>;
};

export function MPANInput({
  value,
  isSelected = false,
  isEditable = false,
  onChange,
  onKeyDown,
  onPaste,
  className,
  ref,
  ...props
}: MPANInputProps) {
  return (
    <input
      {...props}
      ref={ref}
      readOnly={!isEditable}
      disabled={!isEditable}
      value={value}
      onChange={e => onChange?.(e.target.value)}
      onKeyDown={onKeyDown}
      onPaste={onPaste}
      className={cn(
        "w-full rounded-md border-2 bg-primary/5 px-0.5 py-1 text-center",
        className
      )}
    />
  );
}
```

## Step-by-Step Migration Process

### Step 1: Remove forwardRef import
```diff
- import { forwardRef } from "react";
```

### Step 2: Convert component definition
```diff
- export const Component = forwardRef<HTMLElement, Props>((props, ref) => {
+ export function Component({ ref, ...props }: Props & { ref?: React.Ref<HTMLElement> }) {
```

### Step 3: Update TypeScript interface
```diff
export interface ComponentProps {
  // existing props
+ ref?: React.Ref<HTMLElement>;
}
```

### Step 4: Remove displayName assignment (if using function declaration)
```diff
- Component.displayName = "Component";
+ // displayName is automatically inferred from function name
```

### Step 5: Test the component
- Verify ref forwarding works
- Check TypeScript has no errors
- Ensure component behaves the same

## Conclusion

Migrating from `forwardRef` to ref-as-prop is a straightforward process that results in cleaner, more maintainable code. While `forwardRef` still works in React 19, migrating now prepares your codebase for future React versions and takes advantage of the simplified API.
