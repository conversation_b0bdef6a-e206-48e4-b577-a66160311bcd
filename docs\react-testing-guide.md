# React Component Testing Guide with React Testing Library

This guide provides comprehensive instructions for writing high-quality unit tests for React components using React Testing Library, Jest, and TypeScript. We'll use the Verification component system as practical examples throughout this guide.

## Table of Contents

1. [Core Testing Principles](#core-testing-principles)
2. [Test Environment Setup](#test-environment-setup)
3. [Test Utilities and Helpers](#test-utilities-and-helpers)
4. [Writing Meaningful Tests](#writing-meaningful-tests)
5. [Testing Patterns and Best Practices](#testing-patterns-and-best-practices)
6. [Common Pitfalls and Solutions](#common-pitfalls-and-solutions)
7. [Real-World Examples](#real-world-examples)

## Core Testing Principles

### 1. Test User Behavior, Not Implementation

```typescript
// ❌ Bad: Testing implementation details
expect(component.state.isVerified).toBe(true);

// ✅ Good: Testing what the user sees
expect(screen.getByText('Email verified')).toBeInTheDocument();
```

### 2. Use Strongly Typed Test Data

Always use the `createGenericBuilder<T>()` pattern for creating test data:

```typescript
const testDataBuilder = createGenericBuilder(TestDataSchema);
const testData = testDataBuilder
  .set('email', '<EMAIL>')
  .set('isVerified', false)
  .build();
```

### 3. Write Tests First (TDD)

1. Write a failing test
2. Write minimal code to make it pass
3. Refactor and enhance
4. Repeat

## Test Environment Setup

### Directory Structure

```
components/verification/
├── __tests__/
│   ├── verification.spec.tsx
│   ├── verification-context.spec.tsx
│   ├── verification-input.spec.tsx
│   ├── verification-otp.spec.tsx
│   └── verification-integration.spec.tsx
├── test-utils/
│   ├── builders.ts
│   └── render-utils.tsx
```

### Jest Configuration

For Next.js apps, create a `jest.config.ts` file:

```typescript
import type { Config } from "jest";
import nextJest from "next/jest";
import { pathsToModuleNameMapper } from "ts-jest";
import { compilerOptions } from "./tsconfig.json";

const createJestConfig = nextJest({
  dir: "./"
});

const config: Config = {
  preset: "ts-jest",
  testEnvironment: "jsdom",
  // ... rest of config
};

export default createJestConfig(config);
```

### Essential Imports

```typescript
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
```

### Important Note on React Imports

If your components use JSX without the new JSX transform, ensure all components include:

```typescript
import React from 'react';
```

This prevents "React is not defined" errors in tests.

## Test Utilities and Helpers

### Custom Render Function

When testing components that require context providers, create a custom render function:

```typescript
// test-utils/render-utils.tsx
import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { Verification, VerificationConfig } from '../index';

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  verificationConfig?: Partial<VerificationConfig>;
}

export function renderWithVerification(
  ui: ReactElement,
  options?: CustomRenderOptions
) {
  const defaultConfig: VerificationConfig = {
    value: '',
    onChange: jest.fn(),
    onSend: jest.fn().mockResolvedValue(undefined),
    onVerify: jest.fn().mockResolvedValue(true),
    ...options?.verificationConfig
  };

  return render(
    <Verification {...defaultConfig}>{ui}</Verification>,
    options
  );
}
```

### Test Data Builders

```typescript
// test-utils/builders.ts
import { z } from 'zod';
import { createGenericBuilder } from '@watt/common/src/utils/generic-fluent-builder';

const VerificationTestDataSchema = z.object({
  email: z.string().email(),
  phoneNumber: z.string(),
  otpCode: z.string(),
  isVerified: z.boolean(),
  errorMessage: z.string().optional()
});

export const createVerificationTestData = () =>
  createGenericBuilder(VerificationTestDataSchema);
```

### Promise and Timer Helpers

```typescript
// Helper to flush promises when using fake timers
export const flushPromises = () => 
  new Promise(resolve => process.nextTick(resolve));

// Helper to advance timers and flush promises together
export async function advanceTimersAndFlush(ms: number) {
  await act(async () => {
    jest.advanceTimersByTime(ms);
    await flushPromises();
  });
}
```

## Writing Meaningful Tests

### Step 1: Start with a Failing Test

```typescript
describe('VerificationInput', () => {
  it('should update value when user types', async () => {
    const onChange = jest.fn();
    
    renderWithVerification(
      <VerificationField>
        <VerificationInput placeholder="Enter email" />
      </VerificationField>,
      { verificationConfig: { onChange } }
    );

    const input = screen.getByPlaceholderText('Enter email');
    await userEvent.type(input, '<EMAIL>');

    // This will fail initially because the component doesn't exist yet
    expect(onChange).toHaveBeenLastCalledWith('<EMAIL>');
  });
});
```

### Step 2: Make It Pass

Implement the minimal code needed to make the test pass.

### Step 3: Enhance with Edge Cases

```typescript
describe('VerificationInput', () => {
  // Parameterized tests using test.each
  const inputTestCases = [
    { input: '<EMAIL>', expected: '<EMAIL>', description: 'valid email' },
    { input: '', expected: '', description: 'empty string' },
    { input: '  <EMAIL>  ', expected: '  <EMAIL>  ', description: 'with spaces' }
  ];

  test.each(inputTestCases)(
    'should handle $description correctly',
    async ({ input, expected }) => {
      const onChange = jest.fn();
      
      renderWithVerification(
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
        </VerificationField>,
        { verificationConfig: { onChange } }
      );

      const inputElement = screen.getByPlaceholderText('Enter email');
      await userEvent.type(inputElement, input);

      expect(onChange).toHaveBeenLastCalledWith(expected);
    }
  );
});
```

## Testing Patterns and Best Practices

### 1. Testing Component State Transitions

```typescript
describe('Verification State Transitions', () => {
  it('should transition from input to OTP state when verify is clicked', async () => {
    const onSend = jest.fn().mockResolvedValue(undefined);
    
    const { rerender } = renderWithVerification(
      <>
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
          <VerificationTrigger>Verify</VerificationTrigger>
        </VerificationField>
        <VerificationOTPField>
          <VerificationOTP />
        </VerificationOTPField>
      </>,
      { 
        verificationConfig: { 
          value: '<EMAIL>',
          onSend 
        } 
      }
    );

    // Initial state: input should be visible
    expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument();
    expect(screen.queryByText('Enter verification code')).not.toBeInTheDocument();

    // Click verify button
    const verifyButton = screen.getByText('Verify');
    await userEvent.click(verifyButton);

    // Wait for state transition
    await waitFor(() => {
      expect(screen.queryByPlaceholderText('Enter email')).not.toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument(); // OTP input
    });

    expect(onSend).toHaveBeenCalledWith('<EMAIL>');
  });
});
```

### 2. Testing Async Operations

```typescript
describe('Async Operations', () => {
  it('should handle send OTP success', async () => {
    const onSend = jest.fn().mockResolvedValue(undefined);
    const onError = jest.fn();

    renderWithVerification(
      <VerificationField>
        <VerificationTrigger>Send Code</VerificationTrigger>
      </VerificationField>,
      { 
        verificationConfig: { 
          value: '<EMAIL>',
          onSend,
          onError 
        } 
      }
    );

    const sendButton = screen.getByText('Send Code');
    
    // Button should be enabled initially
    expect(sendButton).not.toBeDisabled();

    // Click and wait for async operation
    await act(async () => {
      await userEvent.click(sendButton);
    });

    // Verify success behavior
    expect(onSend).toHaveBeenCalledTimes(1);
    expect(onError).not.toHaveBeenCalled();
  });

  it('should handle send OTP failure', async () => {
    const error = new Error('Network error');
    const onSend = jest.fn().mockRejectedValue(error);
    const onError = jest.fn();

    renderWithVerification(
      <>
        <VerificationField>
          <VerificationTrigger>Send Code</VerificationTrigger>
        </VerificationField>
        <VerificationError />
      </>,
      { 
        verificationConfig: { 
          value: '<EMAIL>',
          onSend,
          onError 
        } 
      }
    );

    await act(async () => {
      await userEvent.click(screen.getByText('Send Code'));
    });

    // Verify error handling
    await waitFor(() => {
      expect(onError).toHaveBeenCalledWith(error);
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });
  });
});
```

### 3. Testing Timer-Based Functionality

```typescript
describe('Timer Functionality', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should handle resend timer countdown', async () => {
    const onSend = jest.fn().mockResolvedValue(undefined);

    renderWithVerification(
      <VerificationOTPField>
        <VerificationResend />
      </VerificationOTPField>,
      { 
        verificationConfig: { 
          value: '<EMAIL>',
          onSend,
          resendDelay: 10000 // 10 seconds
        } 
      }
    );

    // Trigger initial send to start timer
    // (Assuming the OTP field is shown after initial send)

    // Initially should show countdown
    expect(screen.getByText('Resend in 10s')).toBeInTheDocument();

    // Advance timer by 5 seconds
    await advanceTimersAndFlush(5000);
    expect(screen.getByText('Resend in 5s')).toBeInTheDocument();

    // Advance to completion
    await advanceTimersAndFlush(5000);
    expect(screen.getByText('Resend OTP')).toBeInTheDocument();
    
    // Button should now be clickable
    const resendButton = screen.getByText('Resend OTP');
    expect(resendButton).not.toBeDisabled();
  });
});
```

### 4. Testing User Interactions

```typescript
describe('User Interactions', () => {
  it('should handle complete verification flow', async () => {
    const user = userEvent.setup();
    const onChange = jest.fn();
    const onVerifiedChange = jest.fn();
    const onSend = jest.fn().mockResolvedValue(undefined);
    const onVerify = jest.fn().mockResolvedValue(true);

    renderWithVerification(
      <>
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
          <VerificationTrigger>Verify</VerificationTrigger>
        </VerificationField>
        <VerificationOTPField>
          <VerificationOTP />
        </VerificationOTPField>
        <VerificationStatus>
          <VerificationValue />
          <VerificationChange>Change Email</VerificationChange>
        </VerificationStatus>
      </>,
      {
        verificationConfig: {
          value: '',
          onChange,
          onSend,
          onVerify,
          onVerifiedChange
        }
      }
    );

    // Step 1: Enter email
    const emailInput = screen.getByPlaceholderText('Enter email');
    await user.type(emailInput, '<EMAIL>');
    expect(onChange).toHaveBeenLastCalledWith('<EMAIL>');

    // Step 2: Click verify
    await user.click(screen.getByText('Verify'));
    await waitFor(() => {
      expect(onSend).toHaveBeenCalledWith('<EMAIL>');
    });

    // Step 3: Enter OTP (assuming 6 digit OTP)
    const otpInput = screen.getByRole('textbox');
    await user.type(otpInput, '123456');

    // Step 4: Verify OTP was processed
    await waitFor(() => {
      expect(onVerify).toHaveBeenCalledWith('<EMAIL>', '123456');
      expect(onVerifiedChange).toHaveBeenCalledWith(true);
    });

    // Step 5: Check verified state
    expect(screen.getByDisplayValue('<EMAIL>')).toBeDisabled();
    expect(screen.getByText('Change Email')).toBeInTheDocument();
  });
});
```

## Common Pitfalls and Solutions

### 1. Forgetting to Wrap Async Operations in act()

```typescript
// ❌ Bad: May cause warnings
await userEvent.click(button);

// ✅ Good: Properly wrapped
await act(async () => {
  await userEvent.click(button);
});
```

### 2. Not Cleaning Up After Tests

```typescript
describe('Component', () => {
  let mockFn: jest.Mock;

  beforeEach(() => {
    mockFn = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
    // Clean up any timers, subscriptions, etc.
  });
});
```

### 3. Testing Implementation Instead of Behavior

```typescript
// ❌ Bad: Testing React hooks directly
expect(useState).toHaveBeenCalledWith(false);

// ✅ Good: Testing the result of state changes
expect(screen.getByText('Verified')).toBeInTheDocument();
```

### 4. Not Waiting for Async Updates

```typescript
// ❌ Bad: Not waiting for updates
userEvent.click(button);
expect(screen.getByText('Success')).toBeInTheDocument();

// ✅ Good: Using waitFor
await userEvent.click(button);
await waitFor(() => {
  expect(screen.getByText('Success')).toBeInTheDocument();
});
```

## Real-World Examples

### Complete Test Suite for VerificationInput (TDD Approach)

Here's how we developed the VerificationInput tests using TDD:

**Step 1: Write Failing Test**
```typescript
// Start with the most basic test
it('should render with placeholder', () => {
  renderWithVerification(
    <VerificationField>
      <VerificationInput placeholder="Enter email" />
    </VerificationField>
  );
  
  // This will fail initially because component doesn't exist
  expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument();
});
```

**Step 2: Make It Pass**
Implement minimal code to make the test pass.

**Step 3: Add More Tests**
```typescript
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { 
  VerificationInput,
  VerificationField,
  VerificationTrigger
} from '../index';
import { renderWithVerification } from '../test-utils/render-utils';

describe('VerificationInput', () => {
  describe('Basic Functionality', () => {
    it('should render with placeholder', () => {
      renderWithVerification(
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
        </VerificationField>
      );

      expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument();
    });

    it('should call onChange when user types', async () => {
      const onChange = jest.fn();
      const user = userEvent.setup();

      renderWithVerification(
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
        </VerificationField>,
        { verificationConfig: { onChange } }
      );

      const input = screen.getByPlaceholderText('Enter email');
      await user.type(input, '<EMAIL>');

      expect(onChange).toHaveBeenLastCalledWith('<EMAIL>');
    });

    it('should show current value', () => {
      renderWithVerification(
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
        </VerificationField>,
        { verificationConfig: { value: '<EMAIL>' } }
      );

      const input = screen.getByPlaceholderText('Enter email');
      expect(input).toHaveValue('<EMAIL>');
    });
  });

  describe('Disabled States', () => {
    it('should be disabled when config.disabled is true', () => {
      renderWithVerification(
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
        </VerificationField>,
        { verificationConfig: { disabled: true } }
      );

      expect(screen.getByPlaceholderText('Enter email')).toBeDisabled();
    });

    it('should be disabled while sending OTP', async () => {
      const onSend = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));

      renderWithVerification(
        <VerificationField>
          <VerificationInput placeholder="Enter email" />
          <VerificationTrigger>Send</VerificationTrigger>
        </VerificationField>,
        { 
          verificationConfig: { 
            value: '<EMAIL>',
            onSend 
          } 
        }
      );

      const input = screen.getByPlaceholderText('Enter email');
      expect(input).not.toBeDisabled();

      // Click send and check disabled state
      await act(async () => {
        await userEvent.click(screen.getByText('Send'));
      });

      expect(input).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('should clear error when user types', async () => {
      const user = userEvent.setup();
      const onChange = jest.fn();

      // Render with an error state
      renderWithVerification(
        <>
          <VerificationField>
            <VerificationInput placeholder="Enter email" />
          </VerificationField>
          <VerificationError />
        </>,
        { 
          verificationConfig: { 
            onChange,
            onSend: jest.fn().mockRejectedValue(new Error('Test error'))
          } 
        }
      );

      // Trigger error by attempting to send
      // ... error triggering logic ...

      // Type to clear error
      const input = screen.getByPlaceholderText('Enter email');
      await user.type(input, 'a');

      await waitFor(() => {
        expect(screen.queryByText('Test error')).not.toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    const edgeCaseData = [
      { value: '', description: 'empty string' },
      { value: ' ', description: 'single space' },
      { value: 'a'.repeat(255), description: 'very long string' },
      { value: '测试@example.com', description: 'unicode characters' },
      { value: '<script>alert("xss")</script>', description: 'XSS attempt' }
    ];

    test.each(edgeCaseData)(
      'should handle $description correctly',
      async ({ value }) => {
        const onChange = jest.fn();
        const user = userEvent.setup();

        renderWithVerification(
          <VerificationField>
            <VerificationInput placeholder="Enter value" />
          </VerificationField>,
          { verificationConfig: { onChange } }
        );

        const input = screen.getByPlaceholderText('Enter value');
        await user.clear(input);
        await user.type(input, value);

        expect(onChange).toHaveBeenLastCalledWith(value);
        expect(input).toHaveValue(value);
      }
    );
  });
});
```

## Key Learnings from TDD Implementation

### 1. Jest Configuration Challenges

When setting up tests for a Next.js app, you may encounter:

```typescript
// Error: React is not defined
// Solution: Ensure all components import React
import React from 'react';
```

### 2. Testing Empty Strings with userEvent

```typescript
// ❌ Bad: userEvent.type doesn't handle empty strings
await user.type(input, ''); // This will throw an error

// ✅ Good: Use clear() for empty strings
await user.clear(input);
expect(onChange).toHaveBeenCalledWith('');
```

### 3. Parameterized Tests with test.each

```typescript
const testCases = [
  { value: ' ', description: 'single space' },
  { value: 'a'.repeat(255), description: 'very long string' },
  { value: '测试@example.com', description: 'unicode characters' }
];

test.each(testCases)(
  'should handle $description correctly',
  async ({ value }) => {
    // Test implementation
  }
);
```

### 4. Coverage Insights

Our TDD approach achieved:
- **57.49%** statement coverage
- **22.9%** branch coverage
- **23.07%** function coverage
- **57.67%** line coverage

This demonstrates that even with focused unit tests, we're testing a significant portion of the component ecosystem.

## Summary

This guide demonstrates how to write comprehensive, meaningful tests for React components using React Testing Library. Key takeaways:

1. **Always test from the user's perspective** - Focus on what users see and do, not implementation details
2. **Use strongly typed test data** - Leverage TypeScript and builders for type-safe test data
3. **Handle async operations properly** - Use act(), waitFor(), and proper timer mocking
4. **Write tests first** - Follow TDD principles for better test coverage and design
5. **Test edge cases and error states** - Ensure your components handle all scenarios gracefully
6. **Configure Jest properly** - Ensure proper TypeScript and JSX support
7. **Use parameterized tests** - test.each() for efficient testing of multiple scenarios

Remember: The more your tests resemble the way your software is used, the more confidence they can give you.