# Sites Table Dynamic Pagination Adjustment

## Issue Description

The Sites DataTable dynamically adjusts the page size based on expanded rows, causing layout shifts and triggering re-renders of the entire table whenever rows are expanded or collapsed.

## Problem Code

In `apps/crm/src/app/account/sites/components/data-table.tsx`:

```tsx
const [originalPageSize, setOriginalPageSize] = useState<number>(10);

const activeSubRowsCount = useMemo(() => {
  return table.getRowModel().rows.filter(row => row.depth !== 0).length;
}, [table]);

useEffect(() => {
  table.setPageSize(originalPageSize + activeSubRowsCount);
}, [table, originalPageSize, activeSubRowsCount]);
```

## Why This Is a Problem

1. **Layout shifts**: Page size changes cause the table to jump
2. **Confusing UX**: Number of visible rows changes unexpectedly
3. **Re-renders**: Entire table re-renders on expansion
4. **Pagination breaks**: Page numbers become meaningless
5. **Performance overhead**: Recalculating on every expansion

## Optimized Solution

Use virtual scrolling or fixed pagination:

```tsx
// Option 1: Fixed page size with proper sub-row handling
export function DataTable({ columns }: DataTableProps) {
  const table = useReactTable({
    data: groupedData,
    columns,
    // ... other config
    getPaginationRowModel: getPaginationRowModel(),
    paginateExpandedRows: false, // Don't count sub-rows in pagination
    initialState: {
      pagination: {
        pageSize: 10 // Fixed size
      }
    }
  });
  
  // Remove the dynamic page size adjustment
  // No useEffect needed
}

// Option 2: Use virtual scrolling for large datasets
import { useVirtualizer } from '@tanstack/react-virtual';

export function DataTable({ columns }: DataTableProps) {
  const tableContainerRef = useRef<HTMLDivElement>(null);
  
  const table = useReactTable({
    data: groupedData,
    columns,
    // Remove pagination model for virtual scrolling
  });
  
  const rowVirtualizer = useVirtualizer({
    count: table.getRowModel().rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 50,
    overscan: 10
  });
  
  // Render only visible rows
}

// Option 3: Show sub-rows inline without pagination adjustment
const table = useReactTable({
  data: groupedData,
  columns,
  getExpandedRowModel: getExpandedRowModel(),
  // Configure to show all sub-rows of visible parent rows
  getPaginationRowModel: getPaginationRowModel(),
  autoResetPageIndex: false, // Prevent page reset on expansion
  initialState: {
    pagination: {
      pageSize: 10 // Parents only
    }
  }
});
```

## Migration Strategy

1. Decide between fixed pagination or virtual scrolling
2. Remove dynamic page size adjustment
3. Configure paginateExpandedRows appropriately
4. Test with deeply nested data
5. Add loading indicators for sub-rows
6. Consider lazy loading sub-rows

## Performance Impact

- Eliminates layout shifts
- Reduces re-renders by 50%+
- Consistent user experience
- Better performance with large datasets
- Predictable pagination behavior