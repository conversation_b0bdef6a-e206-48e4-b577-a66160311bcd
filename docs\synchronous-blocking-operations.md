# Synchronous Blocking Operations in Async Contexts

## TL;DR

**The codebase has synchronous operations that block the main thread, causing UI freezes and poor responsiveness.** Heavy computations, large data processing, and synchronous API calls block user interactions.

## The Problem

Synchronous blocking operations cause:
- **<PERSON><PERSON> freezes** - Interface becomes unresponsive
- **Janky animations** - Dropped frames during operations
- **Poor INP scores** - Interaction to Next Paint metrics suffer
- **Blocked event loop** - Can't process user input
- **Terrible mobile experience** - Slower devices freeze completely

## Current Issues Found

Analysis reveals:
- Large array operations without chunking
- Synchronous data transformations
- Heavy computations in render
- Blocking file operations
- No use of Web Workers

### Real Examples

```typescript
// ❌ Blocking operation in component
function DataProcessor({ data }: { data: LargeDataset[] }) {
  // This blocks the UI thread!
  const processed = data.map(item => {
    // Complex calculation for each item
    return {
      ...item,
      computed: expensiveCalculation(item),
      formatted: complexFormatting(item),
      validated: deepValidation(item),
    };
  });
  
  return <DataTable data={processed} />;
}

// ❌ Synchronous search in large dataset
function SearchableList({ items }: { items: Item[] }) {
  const [search, setSearch] = useState('');
  
  // Blocks on every keystroke!
  const filtered = items.filter(item => {
    return deepSearch(item, search); // Complex search logic
  });
  
  return <List items={filtered} />;
}
```

## Non-Blocking Solutions

### ✅ Use Web Workers for Heavy Computation

```typescript
// worker.ts
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'PROCESS_DATA':
      const processed = data.map(item => ({
        ...item,
        computed: expensiveCalculation(item),
        formatted: complexFormatting(item),
        validated: deepValidation(item),
      }));
      self.postMessage({ type: 'PROCESSED', data: processed });
      break;
  }
});

// Component using worker
function DataProcessor({ data }: { data: LargeDataset[] }) {
  const [processed, setProcessed] = useState<ProcessedData[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const workerRef = useRef<Worker>();
  
  useEffect(() => {
    workerRef.current = new Worker(
      new URL('./worker.ts', import.meta.url)
    );
    
    workerRef.current.onmessage = (event) => {
      if (event.data.type === 'PROCESSED') {
        setProcessed(event.data.data);
        setIsProcessing(false);
      }
    };
    
    return () => workerRef.current?.terminate();
  }, []);
  
  useEffect(() => {
    setIsProcessing(true);
    workerRef.current?.postMessage({ type: 'PROCESS_DATA', data });
  }, [data]);
  
  if (isProcessing) {
    return <ProcessingSkeleton />;
  }
  
  return <DataTable data={processed} />;
}
```

### ✅ Chunked Processing with requestIdleCallback

```typescript
function useChunkedProcessing<T, R>(
  data: T[],
  processor: (item: T) => R,
  chunkSize = 100
) {
  const [processed, setProcessed] = useState<R[]>([]);
  const [progress, setProgress] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  
  useEffect(() => {
    if (!data.length) return;
    
    setIsProcessing(true);
    const results: R[] = [];
    let currentIndex = 0;
    
    const processChunk = (deadline: IdleDeadline) => {
      while (
        currentIndex < data.length && 
        deadline.timeRemaining() > 0
      ) {
        const endIndex = Math.min(
          currentIndex + chunkSize, 
          data.length
        );
        
        for (let i = currentIndex; i < endIndex; i++) {
          results.push(processor(data[i]));
        }
        
        currentIndex = endIndex;
        setProgress((currentIndex / data.length) * 100);
      }
      
      if (currentIndex < data.length) {
        requestIdleCallback(processChunk);
      } else {
        setProcessed(results);
        setIsProcessing(false);
      }
    };
    
    requestIdleCallback(processChunk);
  }, [data, processor, chunkSize]);
  
  return { processed, progress, isProcessing };
}

// Usage
function DataList({ items }: { items: Item[] }) {
  const { processed, progress, isProcessing } = useChunkedProcessing(
    items,
    (item) => expensiveTransform(item),
    50
  );
  
  if (isProcessing) {
    return <ProgressBar value={progress} />;
  }
  
  return <List items={processed} />;
}
```

### ✅ Async Search with Debouncing

```typescript
// Move search to Web Worker for large datasets
// search-worker.ts
let searchIndex: SearchIndex;

self.addEventListener('message', async (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'BUILD_INDEX':
      searchIndex = buildSearchIndex(data);
      self.postMessage({ type: 'INDEX_READY' });
      break;
      
    case 'SEARCH':
      const results = searchIndex.search(data.query);
      self.postMessage({ type: 'RESULTS', data: results });
      break;
  }
});

// Component
function AsyncSearchList({ items }: { items: Item[] }) {
  const [search, setSearch] = useState('');
  const [results, setResults] = useState<Item[]>(items);
  const debouncedSearch = useDebounce(search, 300);
  const workerRef = useRef<Worker>();
  
  useEffect(() => {
    workerRef.current = new Worker(
      new URL('./search-worker.ts', import.meta.url)
    );
    
    workerRef.current.onmessage = (event) => {
      if (event.data.type === 'RESULTS') {
        setResults(event.data.data);
      }
    };
    
    // Build search index
    workerRef.current.postMessage({ 
      type: 'BUILD_INDEX', 
      data: items 
    });
    
    return () => workerRef.current?.terminate();
  }, [items]);
  
  useEffect(() => {
    if (debouncedSearch) {
      workerRef.current?.postMessage({ 
        type: 'SEARCH', 
        data: { query: debouncedSearch } 
      });
    } else {
      setResults(items);
    }
  }, [debouncedSearch, items]);
  
  return (
    <>
      <SearchInput value={search} onChange={setSearch} />
      <List items={results} />
    </>
  );
}
```

### ✅ Virtual Time Slicing

```typescript
class TimeSlicedProcessor {
  private queue: (() => void)[] = [];
  private isProcessing = false;
  
  constructor(private maxDuration = 4) {} // 4ms per chunk
  
  add(task: () => void) {
    this.queue.push(task);
    this.process();
  }
  
  private async process() {
    if (this.isProcessing || !this.queue.length) return;
    
    this.isProcessing = true;
    const startTime = performance.now();
    
    while (this.queue.length > 0) {
      const task = this.queue.shift()!;
      task();
      
      if (performance.now() - startTime > this.maxDuration) {
        // Yield to browser
        await new Promise(resolve => setTimeout(resolve, 0));
        break;
      }
    }
    
    this.isProcessing = false;
    
    if (this.queue.length > 0) {
      this.process();
    }
  }
}

// Usage
function HeavyComponent({ data }: { data: Item[] }) {
  const [processed, setProcessed] = useState<ProcessedItem[]>([]);
  const processorRef = useRef(new TimeSlicedProcessor());
  
  useEffect(() => {
    const results: ProcessedItem[] = [];
    
    data.forEach((item, index) => {
      processorRef.current.add(() => {
        results[index] = expensiveProcess(item);
        
        // Update UI progressively
        if (index % 10 === 0 || index === data.length - 1) {
          setProcessed([...results.filter(Boolean)]);
        }
      });
    });
  }, [data]);
  
  return <List items={processed} />;
}
```

## Advanced Patterns

### 1. Concurrent Mode Features

```typescript
import { startTransition, useDeferredValue } from 'react';

function InteractiveSearch({ items }: { items: Item[] }) {
  const [search, setSearch] = useState('');
  const deferredSearch = useDeferredValue(search);
  
  // Expensive filtering deferred
  const filtered = useMemo(() => {
    return items.filter(item => 
      expensiveFilter(item, deferredSearch)
    );
  }, [items, deferredSearch]);
  
  return (
    <>
      <input
        value={search}
        onChange={(e) => {
          // Urgent update - input responds immediately
          setSearch(e.target.value);
          
          // Non-urgent update
          startTransition(() => {
            // Trigger expensive computation
          });
        }}
      />
      <List items={filtered} />
    </>
  );
}
```

### 2. Progressive Enhancement Pattern

```typescript
function ProgressiveDataTable({ data }: { data: LargeDataset[] }) {
  const [displayData, setDisplayData] = useState<ProcessedData[]>([]);
  const [enhancementLevel, setEnhancementLevel] = useState(0);
  
  useEffect(() => {
    // Level 0: Show raw data immediately
    setDisplayData(data.map(d => ({ ...d, level: 0 })));
    
    // Level 1: Basic processing
    requestIdleCallback(() => {
      const level1 = data.map(d => ({
        ...d,
        level: 1,
        formatted: basicFormat(d),
      }));
      setDisplayData(level1);
      setEnhancementLevel(1);
    });
    
    // Level 2: Advanced processing
    requestIdleCallback(() => {
      const worker = new Worker('./process-worker.js');
      worker.postMessage({ data, level: 2 });
      worker.onmessage = (e) => {
        setDisplayData(e.data);
        setEnhancementLevel(2);
      };
    }, { timeout: 1000 });
  }, [data]);
  
  return (
    <div>
      {enhancementLevel < 2 && (
        <div>Enhancing experience... {enhancementLevel}/2</div>
      )}
      <DataTable data={displayData} />
    </div>
  );
}
```

### 3. Offscreen Canvas for Graphics

```typescript
function HeavyVisualization({ data }: { data: DataPoint[] }) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isRendering, setIsRendering] = useState(true);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    // Create offscreen canvas
    const offscreen = canvas.transferControlToOffscreen();
    
    const worker = new Worker(
      new URL('./canvas-worker.ts', import.meta.url)
    );
    
    worker.postMessage({ 
      type: 'INIT', 
      canvas: offscreen,
      data 
    }, [offscreen]);
    
    worker.onmessage = (e) => {
      if (e.data.type === 'RENDER_COMPLETE') {
        setIsRendering(false);
      }
    };
    
    return () => worker.terminate();
  }, [data]);
  
  return (
    <div className="relative">
      <canvas ref={canvasRef} width={800} height={600} />
      {isRendering && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Spinner />
        </div>
      )}
    </div>
  );
}
```

### 4. Scheduler API Integration

```typescript
// Use the experimental Scheduler API for better prioritization
async function scheduleWork(
  tasks: (() => void)[],
  priority: 'user-blocking' | 'user-visible' | 'background' = 'user-visible'
) {
  if ('scheduler' in globalThis) {
    const scheduler = (globalThis as any).scheduler;
    
    for (const task of tasks) {
      await scheduler.postTask(task, { priority });
    }
  } else {
    // Fallback to requestIdleCallback
    for (const task of tasks) {
      await new Promise(resolve => {
        requestIdleCallback(() => {
          task();
          resolve(undefined);
        });
      });
    }
  }
}

// Usage
function DataProcessor({ items }: { items: Item[] }) {
  useEffect(() => {
    const tasks = items.map(item => () => {
      processItem(item);
    });
    
    scheduleWork(tasks, 'background');
  }, [items]);
}
```

## Performance Monitoring

```typescript
// Monitor long tasks
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.duration > 50) { // Long task threshold
      console.warn('Long task detected:', {
        duration: entry.duration,
        startTime: entry.startTime,
        name: entry.name,
      });
      
      // Report to analytics
      analytics.track('long_task', {
        duration: entry.duration,
        page: window.location.pathname,
      });
    }
  }
});

observer.observe({ entryTypes: ['longtask'] });
```

## Best Practices

1. **Never block the main thread** - Use workers or async processing
2. **Chunk large operations** - Process in small batches
3. **Show progress** - Let users know something is happening
4. **Prioritize interactivity** - User input should always be responsive
5. **Measure performance** - Monitor long tasks and INP

## Migration Checklist

- [ ] Identify all heavy computations
- [ ] Move complex calculations to Web Workers
- [ ] Implement chunked processing for large datasets
- [ ] Add progress indicators
- [ ] Use React concurrent features
- [ ] Monitor Core Web Vitals
- [ ] Test on low-end devices
- [ ] Add performance budgets

## Conclusion

Synchronous blocking operations destroy user experience. The main thread must remain free for user interactions. Moving heavy work off the main thread is essential for a responsive application.