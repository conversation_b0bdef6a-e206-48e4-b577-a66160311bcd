# Theme Provider Unnecessary Wrapper

## Issue Description

The `ThemeProvider` component is a thin wrapper around `NextThemesProvider` that adds no additional functionality, creating an unnecessary component layer and increasing the component tree depth.

## Problem Code

In `apps/crm/src/components/theme-provider.tsx`:

```tsx
"use client";

import { ThemeProvider as NextThemesProvider } from "next-themes";
import type { ThemeProviderProps } from "next-themes/dist/types";
import React from "react";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}
```

## Why This Is a Problem

1. **Extra component layer**: Adds depth to React component tree
2. **No added value**: Simply passes through all props
3. **Bundle size**: Includes unnecessary wrapper code
4. **Debugging complexity**: Extra layer in React DevTools
5. **Performance overhead**: Additional function calls and renders

## Optimized Solution

Use the NextThemesProvider directly:

```tsx
// Remove the wrapper component entirely

// In app/layout.tsx or providers.tsx
import { ThemeProvider } from "next-themes";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  );
}

// Or if you need custom configuration, use a config object
const themeConfig = {
  attribute: "class",
  defaultTheme: "system",
  enableSystem: true,
  disableTransitionOnChange: true,
  themes: ["light", "dark", "system"]
} as const;

// Then use it directly
<ThemeProvider {...themeConfig}>
  {children}
</ThemeProvider>

// If you need type safety, export the type instead
export type { ThemeProviderProps } from "next-themes/dist/types";
```

## Migration Strategy

1. Find all imports of the custom ThemeProvider
2. Replace with direct import from "next-themes"
3. Update any custom props if needed
4. Remove the wrapper file
5. Test theme switching functionality
6. Update any documentation

## Performance Impact

- Reduces component tree depth
- Eliminates unnecessary wrapper renders
- Smaller bundle size
- Cleaner React DevTools tree
- Faster component initialization