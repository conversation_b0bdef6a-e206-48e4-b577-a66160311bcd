# TRPC Client Recreation on Every Render

## TL;DR

**The TRPC client is recreated on every render due to improper useState initialization, causing potential connection churn and performance issues.** The headers processing logic also runs on every render.

## The Problem

Client recreation causes:

- **Connection churn** - New HTTP connections on re-renders
- **Memory leaks** - Old clients not properly cleaned up
- **Lost cache** - Client-side cache reset
- **Performance overhead** - Initialization cost repeated
- **Unstable references** - Breaks React optimizations

## Current Issue Found

### Real Example from Codebase

```typescript
// apps/crm/src/trpc/trpc-react-provider.tsx - lines 34-67
export function TRPCReactProvider({
  children,
  readonlyHeaders,
  showDevTools
}: TRPCReactProviderProps) {
  const queryClient = getQueryClient();

  const [client] = useState(() =>
    trpcClient.createClient({
      links: [
        loggerLink<AppRouter>({
          enabled: opts =>
            (env.NODE_ENV === "development" ||
              (opts.direction === "down" && opts.result instanceof Error)) &&
            TRPC_LOGS
        }),
        httpBatchLink({
          transformer,
          url: `${tRPCBaseUrl()}/api/trpc`,
          headers() {
            // This function runs on EVERY request!
            if (process.env.NODE_ENV === "development") {
              const headers = new Headers(readonlyHeaders);
              headers.set("x-trpc-source", "nextjs-react");
              return headers;
            }

            const headers = new Headers();
            if (readonlyHeaders) {
              for (const [key, value] of readonlyHeaders.entries()) {
                headers.append(key, value);
              }
            }
            headers.set("x-trpc-source", "nextjs-react");
            return headers;
          }
        })
      ]
    })
  );
```

The issue: `readonlyHeaders` is used inside the useState initializer, but it's a prop that could change, causing potential stale closures.

## Optimized Solutions

### ✅ Stable Client Creation with Memoization

```typescript
export function TRPCReactProvider({
  children,
  readonlyHeaders,
  showDevTools
}: TRPCReactProviderProps) {
  const queryClient = getQueryClient();

  // Memoize client creation
  const client = useMemo(() => {
    return trpcClient.createClient({
      links: [
        loggerLink<AppRouter>({
          enabled: opts =>
            (env.NODE_ENV === "development" ||
              (opts.direction === "down" && opts.result instanceof Error)) &&
            TRPC_LOGS
        }),
        httpBatchLink({
          transformer,
          url: `${tRPCBaseUrl()}/api/trpc`,
          headers() {
            // Still runs per request, but client is stable
            return createHeaders(readonlyHeaders);
          }
        })
      ]
    });
  }, []); // Empty deps - client created once

  // Handle header updates separately if needed
  useEffect(() => {
    // Update client headers if they change
    if (client && readonlyHeaders) {
      // Update logic here
    }
  }, [readonlyHeaders, client]);

  return (
    <trpcClient.Provider client={client} queryClient={queryClient}>
      {/* ... */}
    </trpcClient.Provider>
  );
}
```

### ✅ Optimize Headers Function

```typescript
// Memoize header creation
const createHeaders = useMemo(() => {
  return (baseHeaders?: Headers) => {
    const headers = new Headers();

    // Copy headers more efficiently
    if (baseHeaders) {
      // Use spread instead of loop
      baseHeaders.forEach((value, key) => {
        headers.set(key, value);
      });
    }

    headers.set("x-trpc-source", "nextjs-react");

    // Cache common headers
    if (process.env.NODE_ENV === "development") {
      headers.set("x-dev-mode", "true");
    }

    return headers;
  };
}, []);

// Use in client
httpBatchLink({
  transformer,
  url: `${tRPCBaseUrl()}/api/trpc`,
  headers: createHeaders
})
```

### ✅ Static Client Pattern

```typescript
// Create client outside component for truly static instance
const createTRPCClient = () => {
  return trpcClient.createClient({
    links: [
      loggerLink<AppRouter>({
        enabled: opts =>
          (env.NODE_ENV === "development" ||
            (opts.direction === "down" && opts.result instanceof Error)) &&
          TRPC_LOGS
      }),
      httpBatchLink({
        transformer,
        url: `${tRPCBaseUrl()}/api/trpc`,
        headers: {
          "x-trpc-source": "nextjs-react"
        }
      })
    ]
  });
};

// Singleton instance
let clientInstance: ReturnType<typeof createTRPCClient> | null = null;

export function TRPCReactProvider({ children }: TRPCReactProviderProps) {
  const queryClient = getQueryClient();

  // Create once
  if (!clientInstance) {
    clientInstance = createTRPCClient();
  }

  return (
    <trpcClient.Provider client={clientInstance} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </trpcClient.Provider>
  );
}
```

### ✅ Headers Context Pattern

```typescript
// Separate headers management from client creation
const HeadersContext = createContext<Headers | null>(null);

export function TRPCReactProvider({
  children,
  readonlyHeaders
}: TRPCReactProviderProps) {
  const queryClient = getQueryClient();

  // Client created once
  const client = useMemo(() => {
    return trpcClient.createClient({
      links: [
        loggerLink<AppRouter>({ /* ... */ }),
        httpBatchLink({
          transformer,
          url: `${tRPCBaseUrl()}/api/trpc`,
          headers(opts) {
            // Access headers from context
            const ctx = opts.ctx as { headers?: Headers };
            return ctx.headers || new Headers();
          }
        })
      ]
    });
  }, []);

  // Headers can change without recreating client
  const headersValue = useMemo(() => {
    const headers = new Headers(readonlyHeaders);
    headers.set("x-trpc-source", "nextjs-react");
    return headers;
  }, [readonlyHeaders]);

  return (
    <HeadersContext.Provider value={headersValue}>
      <trpcClient.Provider client={client} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      </trpcClient.Provider>
    </HeadersContext.Provider>
  );
}
```

## Performance Best Practices

### 1. Avoid Functions in Config

```typescript
// ❌ Bad - Function recreated
httpBatchLink({
  headers() {
    return new Headers();
  }
})

// ✅ Good - Static or memoized
const headers = new Headers();
httpBatchLink({
  headers
})
```

### 2. Cache Expensive Operations

```typescript
// Cache URL computation
const trpcUrl = useMemo(() => `${tRPCBaseUrl()}/api/trpc`, []);

// Cache link creation
const links = useMemo(() => [
  loggerLink({ /* ... */ }),
  httpBatchLink({ /* ... */ })
], []);
```

### 3. Use Stable References

```typescript
// ❌ Bad - New object every time
const client = createClient({
  links: [/* ... */]
});

// ✅ Good - Stable reference
const [client] = useState(() => createClient({
  links: [/* ... */]
}));
```

## Performance Impact

### Before (Potential Recreation)

- Client initialization: Multiple times
- Memory usage: Growing with recreations
- HTTP connections: Potentially churning
- Cache efficiency: Poor

### After (Stable Client)

- Client initialization: Once
- Memory usage: Stable
- HTTP connections: Reused
- Cache efficiency: Optimal

## Common Pitfalls

### 1. Dependencies in useState

```typescript
// ❌ Bad - Closure over props
const [client] = useState(() =>
  createClient({ url: props.url }) // Stale prop!
);

// ✅ Good - No dependencies
const [client] = useState(() =>
  createClient({ url: DEFAULT_URL })
);
```

### 2. Inline Object Creation

```typescript
// ❌ Bad - New object every render
<trpcClient.Provider
  client={client}
  queryClient={getQueryClient()} // New instance!
>

// ✅ Good - Stable references
const queryClient = getQueryClient();
<trpcClient.Provider client={client} queryClient={queryClient}>
```

## Migration Checklist

- [ ] Audit TRPC client creation
- [ ] Check for prop dependencies in useState
- [ ] Memoize expensive operations
- [ ] Use stable references
- [ ] Test client stability
- [ ] Monitor connection reuse
- [ ] Profile memory usage

## Conclusion

While the current implementation uses useState correctly to create the client once, the dependency on `readonlyHeaders` inside the initializer could cause issues. Additionally, the headers function runs on every request creating new objects. Optimizing these patterns will improve performance and stability.
