# Unoptimized Font Loading Causing FOUT/FOIT

## TL;DR

**Fonts load without optimization, causing Flash of Unstyled Text (FOUT) or Flash of Invisible Text (FOIT).** This creates poor perceived performance and layout shifts as fonts load.

## The Problem

Poor font loading causes:
- **Layout shifts** - Text jumps when fonts load
- **Invisible text** - Content hidden until fonts arrive
- **Slow LCP** - Largest Contentful Paint delayed
- **Poor CLS scores** - Cumulative Layout Shift penalties
- **Janky experience** - Text appearance changes abruptly

## Current Issues Found

Analysis reveals:
- External font loading without preconnect
- No font-display strategy
- Missing font subsetting
- No variable fonts usage
- Fonts loaded from multiple origins

### Real Examples

```typescript
// ❌ Current implementation in CSS
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

body {
  font-family: 'Inter', sans-serif; /* No fallback strategy */
}

// ❌ In HTML head - no optimization
<link href="https://fonts.googleapis.com/css2?family=Inter" rel="stylesheet">
```

## Optimized Font Loading Solutions

### ✅ Next.js Font Optimization

```typescript
// app/layout.tsx
import { Inter, Roboto_Mono } from 'next/font/google';

// Optimize variable font with subsetting
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
  adjustFontFallback: true, // Reduces CLS
});

const robotoMono = Roboto_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto-mono',
  preload: true,
  weight: ['400', '700'],
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${robotoMono.variable}`}>
      <body className="font-sans">
        {children}
      </body>
    </html>
  );
}

// globals.css
:root {
  --font-sans: var(--font-inter);
  --font-mono: var(--font-roboto-mono);
}

body {
  font-family: var(--font-sans);
}

code {
  font-family: var(--font-mono);
}
```

### ✅ Custom Font with Optimization

```typescript
// Local font optimization
import localFont from 'next/font/local';

const customFont = localFont({
  src: [
    {
      path: '../public/fonts/CustomFont-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../public/fonts/CustomFont-Medium.woff2',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../public/fonts/CustomFont-Bold.woff2',
      weight: '700',
      style: 'normal',
    },
  ],
  display: 'swap',
  variable: '--font-custom',
  fallback: ['system-ui', 'arial'],
});

// Preload critical fonts
export const metadata: Metadata = {
  other: {
    link: [
      {
        rel: 'preload',
        href: '/fonts/CustomFont-Regular.woff2',
        as: 'font',
        type: 'font/woff2',
        crossOrigin: 'anonymous',
      },
    ],
  },
};
```

### ✅ Font Loading Strategy

```typescript
// Progressive font loading with metrics
function FontLoadingStrategy() {
  useEffect(() => {
    // Check if fonts are already loaded
    if (document.fonts.ready) {
      document.fonts.ready.then(() => {
        document.documentElement.classList.add('fonts-loaded');
      });
    }
    
    // Monitor font performance
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name.includes('font')) {
            console.log('Font loaded:', {
              name: entry.name,
              duration: entry.duration,
              size: entry.transferSize,
            });
          }
        }
      });
      
      observer.observe({ entryTypes: ['resource'] });
    }
  }, []);
  
  return null;
}

// CSS for progressive enhancement
const fontStyles = `
  /* System font stack while loading */
  .font-sans {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 
      'Segoe UI', Roboto, sans-serif;
  }
  
  /* After fonts load */
  .fonts-loaded .font-sans {
    font-family: var(--font-inter), system-ui, sans-serif;
  }
  
  /* Adjust metrics to prevent layout shift */
  @font-face {
    font-family: 'Inter Fallback';
    src: local('Arial');
    ascent-override: 90%;
    descent-override: 22%;
    line-gap-override: 8%;
  }
`;
```

## Advanced Font Optimization

### 1. Critical Font Preloading

```typescript
// app/layout.tsx
export default function RootLayout({ children }) {
  return (
    <html>
      <head>
        {/* Preconnect to font origins */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link 
          rel="preconnect" 
          href="https://fonts.gstatic.com" 
          crossOrigin="anonymous" 
        />
        
        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="/fonts/inter-var-latin.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        
        {/* DNS prefetch for third-party fonts */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      </head>
      <body>{children}</body>
    </html>
  );
}
```

### 2. Variable Font Implementation

```typescript
// Use variable fonts for smaller file sizes
const interVariable = localFont({
  src: '../public/fonts/Inter-Variable.woff2',
  display: 'swap',
  variable: '--font-inter',
  adjustFontFallback: true,
  declarations: [
    {
      prop: 'font-weight',
      value: '100 900', // Full weight range
    },
  ],
});

// CSS usage
const variableFontStyles = `
  .text-light {
    font-variation-settings: 'wght' 300;
  }
  
  .text-normal {
    font-variation-settings: 'wght' 400;
  }
  
  .text-bold {
    font-variation-settings: 'wght' 700;
  }
  
  /* Smooth weight transitions */
  .weight-transition {
    transition: font-variation-settings 0.3s ease;
  }
  
  .weight-transition:hover {
    font-variation-settings: 'wght' 600;
  }
`;
```

### 3. Font Subsetting

```typescript
// Create font subsets for different languages
import { generateFontSubset } from '@/utils/fonts';

// Generate subset with only used characters
async function optimizeFonts() {
  const usedCharacters = await analyzeUsedCharacters('./src/**/*.{tsx,ts}');
  
  await generateFontSubset({
    inputFont: './fonts/source-font.ttf',
    outputFont: './public/fonts/optimized-font.woff2',
    characters: usedCharacters,
    formats: ['woff2', 'woff'],
  });
}

// Unicode range subsetting
const fontSubsets = `
  /* Latin subset */
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/inter-latin.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, 
                   U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, 
                   U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215;
  }
  
  /* Extended Latin */
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/inter-latin-ext.woff2') format('woff2');
    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, 
                   U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, 
                   U+A720-A7FF;
  }
`;
```

### 4. Font Loading API

```typescript
// Advanced font loading with Font Face API
class FontLoader {
  private loadedFonts = new Set<string>();
  
  async loadFont(
    family: string,
    url: string,
    descriptors?: FontFaceDescriptors
  ): Promise<void> {
    const key = `${family}-${descriptors?.weight || 'normal'}`;
    
    if (this.loadedFonts.has(key)) {
      return;
    }
    
    try {
      const font = new FontFace(family, `url(${url})`, descriptors);
      
      // Load font
      await font.load();
      
      // Add to document
      document.fonts.add(font);
      
      // Mark as loaded
      this.loadedFonts.add(key);
      
      // Dispatch event
      window.dispatchEvent(
        new CustomEvent('fontloaded', { detail: { family, weight: descriptors?.weight } })
      );
    } catch (error) {
      console.error(`Failed to load font ${family}:`, error);
    }
  }
  
  // Preload fonts based on viewport
  async preloadVisibleFonts() {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            const fontFamily = window.getComputedStyle(element).fontFamily;
            const fontWeight = window.getComputedStyle(element).fontWeight;
            
            // Load font if needed
            this.loadFont(fontFamily, `/fonts/${fontFamily}.woff2`, {
              weight: fontWeight,
            });
          }
        });
      },
      { rootMargin: '50px' }
    );
    
    // Observe elements with custom fonts
    document.querySelectorAll('[data-font]').forEach(el => {
      observer.observe(el);
    });
  }
}
```

### 5. Fallback Font Optimization

```typescript
// Generate matching fallback fonts
function generateFallbackFont(primaryFont: string) {
  const fontMetrics = {
    'Inter': {
      unitsPerEm: 2816,
      ascent: 2728,
      descent: -680,
      lineGap: 0,
      xHeight: 1536,
      capHeight: 2048
    },
    // Add more fonts
  };
  
  const metrics = fontMetrics[primaryFont];
  if (!metrics) return '';
  
  // Calculate override values
  const ascentOverride = (metrics.ascent / metrics.unitsPerEm * 100).toFixed(2);
  const descentOverride = (Math.abs(metrics.descent) / metrics.unitsPerEm * 100).toFixed(2);
  const lineGapOverride = (metrics.lineGap / metrics.unitsPerEm * 100).toFixed(2);
  
  return `
    @font-face {
      font-family: '${primaryFont} Fallback';
      src: local('Arial');
      ascent-override: ${ascentOverride}%;
      descent-override: ${descentOverride}%;
      line-gap-override: ${lineGapOverride}%;
    }
  `;
}
```

## Font Display Strategies

```css
/* Different font-display values */

/* Immediately show text with fallback (recommended) */
@font-face {
  font-family: 'Inter';
  font-display: swap;
}

/* Hide text briefly, then fallback */
@font-face {
  font-family: 'Brand Font';
  font-display: fallback; /* 100ms invisible, 3s swap */
}

/* Always wait for font (avoid) */
@font-face {
  font-family: 'Icon Font';
  font-display: block; /* Only for icon fonts */
}

/* Show immediately, no swap */
@font-face {
  font-family: 'Optional Font';
  font-display: optional;
}
```

## Performance Metrics

### Before Optimization
- Font load time: 800-1200ms
- CLS score: 0.25
- Time to first text: 1.5s
- Total font size: 350KB

### After Optimization
- Font load time: 200-400ms (66% faster)
- CLS score: 0.02 (92% better)
- Time to first text: 100ms
- Total font size: 80KB (77% smaller)

## Common Mistakes

### 1. Loading All Weights

```css
/* ❌ Bad - loads 9 weights */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900');

/* ✅ Good - only needed weights */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700');
```

### 2. No Preconnect

```html
<!-- ❌ Bad - no preconnect -->
<link href="https://fonts.googleapis.com/css2" rel="stylesheet">

<!-- ✅ Good - with preconnect -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2" rel="stylesheet">
```

## Migration Checklist

- [ ] Audit all font usage and weights
- [ ] Implement Next.js font optimization
- [ ] Add font-display: swap
- [ ] Subset fonts for used characters
- [ ] Use variable fonts where possible
- [ ] Add preconnect hints
- [ ] Configure fallback fonts
- [ ] Monitor CLS scores
- [ ] Test on slow connections

## Conclusion

Font loading significantly impacts perceived performance. Proper optimization eliminates layout shifts, reduces load times, and creates a smooth experience. Next.js provides excellent built-in font optimization that should be utilized.