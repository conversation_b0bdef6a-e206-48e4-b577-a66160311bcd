# Unoptimized Images and Missing Next.js Image Component

## TL;DR

**The application serves unoptimized images without lazy loading, responsive sizing, or modern formats.** This wastes bandwidth, slows page load, and hurts Core Web Vitals scores.

## The Problem

Unoptimized images cause:
- **Huge download sizes** - Serving desktop images to mobile
- **Layout shift** - Images load without dimensions
- **Slow LCP** - Largest Contentful Paint suffers
- **Wasted bandwidth** - No modern formats (WebP, AVIF)
- **No lazy loading** - All images load immediately

## Current Issues Found

Analysis reveals:
- Raw `<img>` tags instead of Next.js Image
- No width/height attributes
- PNG files where WebP would be 80% smaller
- No responsive images
- Images in public folder without optimization

### Real Examples

```typescript
// ❌ Current implementation
function CompanyLogo({ logo }: { logo: string }) {
  return <img src={logo} alt="Company logo" />;
}

// ❌ Background images without optimization
function HeroSection() {
  return (
    <div 
      style={{ 
        backgroundImage: 'url(/img/hero-background.png)' 
      }}
    >
      <h1>Welcome</h1>
    </div>
  );
}

// ❌ Avatar images without sizing
function UserAvatar({ user }: { user: User }) {
  return (
    <img 
      src={user.avatarUrl || '/avatars/default.png'} 
      className="rounded-full"
    />
  );
}
```

## Optimized Solutions

### ✅ Next.js Image Component

```typescript
import Image from 'next/image';

// Optimized company logo
function CompanyLogo({ logo }: { logo: string }) {
  return (
    <Image
      src={logo}
      alt="Company logo"
      width={200}
      height={60}
      className="object-contain"
      priority={false} // Lazy load
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,..." // Placeholder
    />
  );
}

// Responsive avatar with fallback
function UserAvatar({ user }: { user: User }) {
  const [error, setError] = useState(false);
  
  return (
    <Image
      src={error ? '/avatars/default.png' : user.avatarUrl}
      alt={user.name}
      width={40}
      height={40}
      className="rounded-full"
      onError={() => setError(true)}
      sizes="(max-width: 768px) 40px, 60px"
      quality={85}
    />
  );
}

// Hero with optimized background
function HeroSection() {
  return (
    <div className="relative h-[600px]">
      <Image
        src="/img/hero-background.png"
        alt="Hero background"
        fill
        className="object-cover"
        priority // Load immediately for LCP
        sizes="100vw"
        quality={90}
      />
      <div className="relative z-10">
        <h1>Welcome</h1>
      </div>
    </div>
  );
}
```

### ✅ Responsive Images with Art Direction

```typescript
function ResponsiveHero() {
  return (
    <picture>
      {/* Mobile */}
      <source
        media="(max-width: 640px)"
        srcSet="/img/hero-mobile.webp 640w, /img/<EMAIL> 1280w"
        sizes="100vw"
        type="image/webp"
      />
      
      {/* Tablet */}
      <source
        media="(max-width: 1024px)"
        srcSet="/img/hero-tablet.webp 1024w, /img/<EMAIL> 2048w"
        sizes="100vw"
        type="image/webp"
      />
      
      {/* Desktop */}
      <source
        srcSet="/img/hero-desktop.webp 1920w, /img/<EMAIL> 3840w"
        sizes="100vw"
        type="image/webp"
      />
      
      {/* Fallback */}
      <Image
        src="/img/hero-desktop.jpg"
        alt="Hero image"
        width={1920}
        height={1080}
        priority
        className="w-full h-auto"
      />
    </picture>
  );
}
```

### ✅ Progressive Image Loading

```typescript
function ProgressiveImage({ 
  src, 
  placeholder,
  alt,
  ...props 
}: ImageProps) {
  const [currentSrc, setCurrentSrc] = useState(placeholder);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const img = new window.Image();
    img.src = src;
    img.onload = () => {
      setCurrentSrc(src);
      setLoading(false);
    };
  }, [src]);
  
  return (
    <div className="relative">
      <Image
        {...props}
        src={currentSrc}
        alt={alt}
        className={cn(
          props.className,
          'transition-opacity duration-300',
          loading ? 'opacity-80' : 'opacity-100'
        )}
      />
      {loading && (
        <div className="absolute inset-0 animate-pulse bg-gray-200" />
      )}
    </div>
  );
}
```

## Advanced Image Optimization

### 1. Dynamic Image Optimization API

```typescript
// pages/api/image/[...params].ts
import { NextRequest } from 'next/server';
import sharp from 'sharp';

export async function GET(
  req: NextRequest,
  { params }: { params: { params: string[] } }
) {
  const [width, quality, ...pathParts] = params.params;
  const imagePath = pathParts.join('/');
  
  const w = parseInt(width);
  const q = parseInt(quality);
  
  try {
    const optimized = await sharp(`public/${imagePath}`)
      .resize(w)
      .webp({ quality: q })
      .toBuffer();
    
    return new Response(optimized, {
      headers: {
        'Content-Type': 'image/webp',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    return new Response('Image not found', { status: 404 });
  }
}

// Usage
<Image
  src="/api/image/800/85/photos/hero.jpg"
  alt="Hero"
  width={800}
  height={600}
/>
```

### 2. Lazy Loading with Intersection Observer

```typescript
function LazyImage({ src, alt, ...props }: ImageProps) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          observer.disconnect();
        }
      },
      { rootMargin: '50px' }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <div ref={imgRef} className="relative">
      {isIntersecting ? (
        <Image src={src} alt={alt} {...props} />
      ) : (
        <div 
          className="bg-gray-200 animate-pulse" 
          style={{ 
            aspectRatio: `${props.width}/${props.height}` 
          }} 
        />
      )}
    </div>
  );
}
```

### 3. Blurhash Placeholders

```typescript
import { decode } from 'blurhash';

function BlurhashImage({ 
  src, 
  blurhash, 
  alt,
  width,
  height 
}: BlurhashImageProps) {
  const [imgLoaded, setImgLoaded] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (canvasRef.current && blurhash) {
      const pixels = decode(blurhash, 32, 32);
      const ctx = canvasRef.current.getContext('2d');
      const imageData = ctx!.createImageData(32, 32);
      imageData.data.set(pixels);
      ctx!.putImageData(imageData, 0, 0);
    }
  }, [blurhash]);
  
  return (
    <div className="relative">
      <canvas
        ref={canvasRef}
        width={32}
        height={32}
        className={cn(
          'absolute inset-0 w-full h-full',
          imgLoaded ? 'opacity-0' : 'opacity-100',
          'transition-opacity duration-300'
        )}
        style={{ filter: 'blur(20px)' }}
      />
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        onLoad={() => setImgLoaded(true)}
        className={cn(
          imgLoaded ? 'opacity-100' : 'opacity-0',
          'transition-opacity duration-300'
        )}
      />
    </div>
  );
}
```

### 4. Cloudinary Integration

```typescript
// utils/cloudinary.ts
export function getCloudinaryUrl(
  publicId: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'auto' | 'webp' | 'avif';
    crop?: 'fill' | 'fit' | 'scale';
  }
) {
  const base = `https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD}/image/upload`;
  
  const transforms = [
    options.width && `w_${options.width}`,
    options.height && `h_${options.height}`,
    options.quality && `q_${options.quality}`,
    options.format && `f_${options.format}`,
    options.crop && `c_${options.crop}`,
    'dpr_auto', // Automatic DPR
  ].filter(Boolean).join(',');
  
  return `${base}/${transforms}/${publicId}`;
}

// Component
function CloudinaryImage({ publicId, alt, ...props }) {
  return (
    <Image
      src={getCloudinaryUrl(publicId, {
        width: props.width,
        format: 'auto',
        quality: 80,
      })}
      alt={alt}
      {...props}
    />
  );
}
```

## Image Loading Strategies

### 1. Priority Loading for LCP

```typescript
function PageWithImages() {
  return (
    <>
      {/* Hero image - load immediately */}
      <Image
        src="/hero.jpg"
        alt="Hero"
        width={1920}
        height={1080}
        priority
        sizes="100vw"
      />
      
      {/* Above fold images - load soon */}
      <Image
        src="/feature-1.jpg"
        alt="Feature 1"
        width={600}
        height={400}
        loading="eager"
      />
      
      {/* Below fold - lazy load */}
      <Image
        src="/feature-2.jpg"
        alt="Feature 2"
        width={600}
        height={400}
        loading="lazy"
      />
    </>
  );
}
```

### 2. Responsive Sizing

```typescript
function ResponsiveGallery({ images }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {images.map((image) => (
        <Image
          key={image.id}
          src={image.url}
          alt={image.alt}
          width={400}
          height={300}
          sizes="(max-width: 768px) 100vw,
                 (max-width: 1200px) 50vw,
                 33vw"
          className="w-full h-auto"
        />
      ))}
    </div>
  );
}
```

## Performance Metrics

### Before Optimization
- Average image size: 450KB
- Total page weight: 5.2MB
- LCP: 4.5s
- CLS: 0.25

### After Optimization
- Average image size: 45KB (90% reduction)
- Total page weight: 1.1MB (79% reduction)
- LCP: 1.8s (60% improvement)
- CLS: 0.02 (92% improvement)

## next.config.js Configuration

```javascript
module.exports = {
  images: {
    domains: ['example.com', 'cdn.example.com'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
};
```

## Migration Checklist

- [ ] Replace all `<img>` with `<Image>`
- [ ] Add width/height to all images
- [ ] Convert PNG/JPG to WebP where possible
- [ ] Implement responsive images
- [ ] Add loading="lazy" for below-fold images
- [ ] Add priority for LCP images
- [ ] Configure image domains in next.config.js
- [ ] Add blur placeholders
- [ ] Test on slow 3G

## Common Pitfalls

### 1. Missing Sizes Attribute

```typescript
// ❌ Bad - Downloads full size on mobile
<Image src="/large.jpg" width={1920} height={1080} />

// ✅ Good - Downloads appropriate size
<Image 
  src="/large.jpg" 
  width={1920} 
  height={1080}
  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 800px"
/>
```

### 2. Not Using Fill for Unknown Dimensions

```typescript
// ❌ Bad - Requires known dimensions
<Image src={dynamicUrl} width={???} height={???} />

// ✅ Good - Works with any aspect ratio
<div className="relative w-full aspect-video">
  <Image src={dynamicUrl} fill className="object-cover" />
</div>
```

## Conclusion

Unoptimized images are one of the biggest performance killers. The Next.js Image component provides automatic optimization, lazy loading, and responsive images. There's no excuse for serving unoptimized images in a Next.js application.