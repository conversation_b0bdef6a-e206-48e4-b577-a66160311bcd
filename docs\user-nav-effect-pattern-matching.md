# User Nav Effect Pattern Matching

## Issue Description

The `UserNav` component uses complex Effect pattern matching for simple data extraction, adding unnecessary computational overhead and bundle size. The pattern matching library is overkill for basic null checking and default values.

## Problem Code

In `apps/crm/src/app/account/components/user-nav.tsx`:

```tsx
import { Match, pipe } from "effect";

function extractProfileData(
  profileData: UserWithProfileMetaData["profileData"]
) {
  return pipe(
    Match.value(profileData),
    Match.when(
      (pd): pd is NonNullable<typeof pd> => pd != null,
      pd => ({
        id: pd.id,
        email: pd.email,
        forename: pd.forename,
        // ... many more fields
      })
    ),
    Match.orElse(() => ({
      id: "",
      email: "",
      forename: "",
      // ... many default values
    }))
  );
}
```

## Why This Is a Problem

1. **Bundle size**: Effect library adds significant weight
2. **Runtime overhead**: Pattern matching is slower than simple checks
3. **Complexity**: Over-engineered for basic null checking
4. **Learning curve**: Harder for team members to understand
5. **Performance**: Function composition adds unnecessary overhead

## Optimized Solution

Use simple JavaScript patterns:

```tsx
// Simple null check with defaults
function extractProfileData(
  profileData: UserWithProfileMetaData["profileData"]
) {
  if (!profileData) {
    return {
      id: "",
      email: "",
      forename: "",
      surname: "",
      directDial: "",
      directDialE164: "",
      role: "SALES_AGENT" as UserRole,
      huntGroups: [],
      disabled: false,
      userId: "",
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  return {
    id: profileData.id,
    email: profileData.email,
    forename: profileData.forename,
    surname: profileData.surname,
    directDial: profileData.directDial,
    directDialE164: profileData.directDialE164,
    role: profileData.role,
    huntGroups: profileData.huntGroups ?? [],
    disabled: profileData.disabled,
    userId: profileData.userId,
    createdAt: profileData.createdAt,
    updatedAt: profileData.updatedAt
  };
}

// Or even simpler with nullish coalescing
function extractProfileData(
  profileData: UserWithProfileMetaData["profileData"]
) {
  const defaults = {
    id: "",
    email: "",
    forename: "",
    surname: "",
    directDial: "",
    directDialE164: "",
    role: "SALES_AGENT" as UserRole,
    huntGroups: [],
    disabled: false,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  };

  return profileData ? { ...defaults, ...profileData } : defaults;
}

// Or use optional chaining
const forename = profileData?.forename ?? "";
const email = profileData?.email ?? "";
```

## Migration Strategy

1. Replace Effect pattern matching with native JavaScript
2. Use nullish coalescing and optional chaining
3. Remove Effect import if not used elsewhere
4. Consider using Zod for schema validation if needed
5. Use TypeScript's built-in type guards
6. Measure bundle size reduction

## Performance Impact

- Reduces bundle size by removing Effect dependency
- Faster execution with native operations
- Better tree-shaking opportunities
- Simpler code is easier to optimize
- Lower cognitive overhead for developers