# User Nav Rehydrate Side Effect

## Issue Description

The `UserNav` component calls `useAppStore.persist.rehydrate()` in a useEffect on every render, which can cause performance issues and unexpected state updates. This pattern is problematic in a component that renders frequently.

## Problem Code

In `apps/crm/src/app/account/components/user-nav.tsx`:

```tsx
export function UserNav({ isCollapsed }: UserNavProps) {
  const { theme, setTheme } = useTheme();
  const { profileData } = useAppStore(state => state.userData);
  const { forename, surname } = extractProfileData(profileData);

  useEffect(() => {
    // Rehydrate the store on the client side
    useAppStore.persist.rehydrate();
  }, []); // Called on every mount
```

## Why This Is a Problem

1. **Multiple rehydrations**: UserNav may mount multiple times
2. **Race conditions**: Concurrent rehydrations can conflict
3. **Performance overhead**: Rehydration involves localStorage reads
4. **Unnecessary work**: Store should rehydrate once globally
5. **Component responsibility**: UserNav shouldn't manage global state

## Optimized Solution

Move rehydration to app initialization:

```tsx
// In app/layout.tsx or providers.tsx
function AppProviders({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Rehydrate once at app level
    useAppStore.persist.rehydrate();
  }, []);

  return <>{children}</>;
}

// Or in store initialization
const useAppStore = create(
  persist(
    (set) => ({
      // ... store definition
    }),
    {
      name: 'app-store',
      onRehydrateStorage: () => (state) => {
        console.log('Store rehydrated:', state);
      },
      // Auto-rehydrate on creation
      skipHydration: false
    }
  )
);

// In UserNav, just use the store
export function UserNav({ isCollapsed }: UserNavProps) {
  const { profileData } = useAppStore(state => state.userData);
  // Remove the useEffect entirely
  
  // ... rest of component
}

// Or if you need client-only behavior
export function UserNav({ isCollapsed }: UserNavProps) {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);

  const { profileData } = useAppStore(state => 
    isClient ? state.userData : null
  );
}
```

## Migration Strategy

1. Identify all components calling rehydrate
2. Move rehydration to a single app-level location
3. Ensure store is properly typed for SSR
4. Handle hydration mismatches properly
5. Use suspense boundaries if needed
6. Test that state persists correctly

## Performance Impact

- Eliminates redundant localStorage reads
- Prevents race conditions
- Reduces component mount overhead
- Cleaner component lifecycle
- Better SSR/hydration behavior