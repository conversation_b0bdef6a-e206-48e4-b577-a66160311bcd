# User Navigation Rehydration Optimization

## TL;DR

The UserNav component calls `useAppStore.persist.rehydrate()` in a useEffect with empty dependencies on every mount, causing unnecessary rehydration and potential race conditions. Using a module-level rehydration or singleton pattern fixes this.

## The Problem

```tsx
useEffect(() => {
  // Rehydrate the store on the client side
  useAppStore.persist.rehydrate();
}, []);
```

Issues:

1. Rehydrates on every component mount (page navigation, modals, etc.)
2. Multiple UserNav instances cause multiple rehydrations
3. No check if already rehydrated
4. Potential race conditions with concurrent rehydrations

## Solutions

### Solution 1: Module-Level Rehydration (Recommended)

```tsx
// At the top of the file, outside component
if (typeof window !== 'undefined') {
  // Only rehydrate once when module loads
  useAppStore.persist.rehydrate();
}

// Remove the useEffect entirely from component
```

### Solution 2: Singleton Pattern with Ref

```tsx
// Outside component
let hasRehydrated = false;

function UserNav() {
  useEffect(() => {
    if (!hasRehydrated) {
      useAppStore.persist.rehydrate();
      hasRehydrated = true;
    }
  }, []);
}
```

### Solution 3: Store-Level Rehydration Flag

```tsx
// In the store
interface AppStore {
  hasRehydrated: boolean;
  // ... other state
}

// In component
useEffect(() => {
  const { hasRehydrated } = useAppStore.getState();
  if (!hasRehydrated) {
    useAppStore.persist.rehydrate();
    useAppStore.setState({ hasRehydrated: true });
  }
}, []);
```

## Performance Impact

### Before

- Rehydration on every mount: ~50-100ms
- Multiple concurrent rehydrations
- Potential state conflicts
- Unnecessary storage reads

### After

- Single rehydration: one-time cost
- No redundant operations
- Predictable state initialization
- Better performance on navigation

## Best Practices

1. **Rehydrate once** at application startup
2. **Use module-level** for client-only operations
3. **Avoid component-level** rehydration
4. **Consider SSR** implications with typeof window check
