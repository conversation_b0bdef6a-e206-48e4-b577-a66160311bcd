import { UserRole } from "@watt/db/src/enums";
import { z } from "zod";

import { getCallsList } from "../service/calls";
import {
  createTRPCRouter,
  protectedAdminProcedure,
  protectedProcedure
} from "../trpc";
import { InfiniteQueryInput } from "../types/common";

export const callsRouter = createTRPCRouter({
  myCallsList: protectedProcedure
    .input(InfiniteQueryInput)
    .query(async ({ ctx, input }) => {
      try {
        const { searchFilters, cursor } = input;
        const email = ctx.user?.email;
        const userProfile = await ctx.prisma.profile.findUnique({
          where: {
            email
          }
        });

        if (!userProfile?.directDial) {
          throw new Error(`No direct dial number provided for user ${email}`);
        }

        const result = await getCallsList(userProfile, input, ctx.prisma);
        return result;
      } catch (error) {
        ctx.logger.error("router/call.myCallsList: ", {
          error,
          data: {
            user: ctx.user
          }
        });
        return {
          items: [],
          meta: {
            totalRowCount: 0
          },
          nextCursor: undefined
        };
      }
    }),
  allCallsList: protectedAdminProcedure
    .input(InfiniteQueryInput)
    .query(async ({ ctx, input }) => {
      try {
        const email = ctx.user?.email;
        const userProfile = await ctx.prisma.profile.findUnique({
          where: {
            email
          }
        });

        const userRole = userProfile?.role;

        if (
          !userRole ||
          (userRole &&
            !(
              [
                UserRole.ADMIN,
                UserRole.COMPLIANCE,
                UserRole.MANAGER,
                UserRole.FINANCE,
                UserRole.DIRECTOR
              ] as UserRole[]
            ).includes(userRole))
        ) {
          throw new Error(`User ${email} is not allowed to make this call`);
        }

        const isAdminCall = true;
        const result = await getCallsList(
          userProfile,
          input,
          ctx.prisma,
          isAdminCall
        );
        return result;
      } catch (error) {
        ctx.logger.error("router/call.allCallsList: ", {
          error,
          data: {
            user: ctx.user
          }
        });
        return {
          items: [],
          meta: {
            totalRowCount: 0
          },
          nextCursor: undefined
        };
      }
    }),
  usersCallsList: protectedAdminProcedure
    .input(InfiniteQueryInput.extend({ email: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const { searchFilters, cursor } = input;
        const email = input.email;
        const userProfile = await ctx.prisma.profile.findUnique({
          where: {
            email
          }
        });

        if (!userProfile?.directDial) {
          throw new Error(`No direct dial number provided for user ${email}`);
        }

        const result = await getCallsList(userProfile, input, ctx.prisma);
        return result;
      } catch (error) {
        ctx.logger.error("router/call.myCallsList: ", {
          error,
          data: {
            user: ctx.user
          }
        });
        return {
          items: [],
          meta: {
            totalRowCount: 0
          },
          nextCursor: undefined
        };
      }
    })
});
