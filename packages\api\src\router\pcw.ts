import { UtilityType } from "@prisma/client";
import type { inferProcedureOutput } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { log } from "@watt/common/src/utils/axiom-logger";
import { getBusinessTarget } from "@watt/external-apis/src/libs/experian/business-targeter";
import { z } from "zod";
import { ClientFacingError } from "../lib/exceptions";

import {
  downloadSignedContract,
  generatePrefilledContractAndUpsertContract,
  getConfirmationDetails,
  signContract
} from "../service/contract";
import { createCompanyAndContact } from "../service/pcw/company";
import {
  getAllPCWQuotesFromQuoteList,
  getPCWQuoteById,
  signUpQuote,
  updateQuotes
} from "../service/pcw/quote";
import { submitSiteInformation } from "../service/pcw/site";
import {
  getBandedUsageForMeter,
  submitUsageInformation
} from "../service/pcw/usage";
import {
  AddressLookupError,
  AddressesInputSchema,
  searchAddressesWhere
} from "../service/search-addresses";
import { getSiteMetersByCompanyAndAddressId } from "../service/site-meter";
import {
  createTRPCRouter,
  pcwProtectedProcedure,
  publicProcedure
} from "../trpc";
import {
  DownloadSignedContractSchema,
  GeneratePrefilledContractSchema,
  SignContractSchema
} from "../types/contract";
import { CreateCompanyAndContactInputSchema } from "../types/pcw/company";
import {
  PcwSignUpQuoteInputSchema,
  PcwUpdateQuoteInputSchema
} from "../types/pcw/quote";
import { SiteInputSchema } from "../types/pcw/site";
import { UsageInputSchema } from "../types/pcw/usage";
import { UtilityTypeMeterNumbersInputSchema } from "../types/site-meter";
import { BusinessTargetInputSchema } from "./business-target";

export const pcwRouter = createTRPCRouter({
  businessTargetGet: publicProcedure
    .input(BusinessTargetInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const businessTarget = await getBusinessTarget(input);

        if (businessTarget.error) {
          log.error(
            "businessTargetRouter.get-businessTarget: ",
            businessTarget.error
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message:
              "Failed to retrieve business information. Please try again later.",
            cause: businessTarget.error
          });
        }

        if (!businessTarget || !businessTarget.data) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message:
              "No company found matching your search criteria. Please check the business name or number and try again."
          });
        }

        return businessTarget.data;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        ctx.logger.error("businessTargetRouter.get-catch: ", { error });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message:
            "An unexpected error occurred while searching for the business. Please try again.",
          cause: error
        });
      }
    }),
  siteMeterFindCompanySiteMeters: publicProcedure
    .input(UtilityTypeMeterNumbersInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await getSiteMetersByCompanyAndAddressId(input);
      } catch (error) {
        ctx.logger.error("siteMeterRouter.findCompanySiteMeters: ", {
          error
        });
        return [];
      }
    }),
  quoteGetQuotesByQuoteListId: pcwProtectedProcedure
    .input(z.object({ quoteListId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        return await getAllPCWQuotesFromQuoteList(input.quoteListId);
      } catch (error) {
        ctx.logger.error("quoteRouter.getQuotesByQuoteListId: ", {
          error,
          data: {
            user: ctx.user
          }
        });
        return {
          quoteList: null,
          maxDecimalPlaces: 0
        };
      }
    }),
  quoteGetQuoteById: pcwProtectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        return await getPCWQuoteById(input.id);
      } catch (error) {
        ctx.logger.error("quoteRouter.quoteGetQuoteById: ", {
          error,
          data: {
            user: ctx.user
          }
        });
        throw error;
      }
    }),
  addressFindMany: publicProcedure
    .input(AddressesInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const addresses = await searchAddressesWhere({
          input
        });
        return addresses;
      } catch (error) {
        if (error === AddressLookupError.BAD_QUERY) {
          ctx.logger.warn("addressRouter.findMany: Bad query input", { input });
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "Invalid search parameters. Please provide a valid postcode or address."
          });
        }
        if (error === AddressLookupError.BAD_POSTCODE) {
          ctx.logger.warn("addressRouter.findMany: Bad postcode input", {
            input
          });
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "Invalid postcode format. Please enter a valid UK postcode."
          });
        }
        if (error === AddressLookupError.NONE_FOUND) {
          return [];
        }
        if (error === AddressLookupError.DATA_SOURCE_FAILED) {
          ctx.logger.error(
            "addressRouter.findMany: Data source failed during search",
            { input }
          );
          throw new TRPCError({
            code: "SERVICE_UNAVAILABLE",
            message:
              "Address lookup service is temporarily unavailable. Please try again later."
          });
        }

        ctx.logger.error("addressRouter.findMany: ", { error, input });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve addresses. Please try again.",
          cause: error
        });
      }
    }),
  usageGetUsageData: pcwProtectedProcedure
    .input(
      z.object({
        meterIdentifier: z.string(),
        utilityType: z.nativeEnum(UtilityType)
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        return await getBandedUsageForMeter(
          input.meterIdentifier,
          input.utilityType
        );
      } catch (error) {
        ctx.logger.error("usageRouter.getAnnualUsage: ", {
          error,
          meterIdentifier: input.meterIdentifier,
          utilityType: input.utilityType
        });

        // Handle ClientFacingError
        if (error instanceof ClientFacingError) {
          throw new TRPCError({
            code: "UNPROCESSABLE_CONTENT",
            message: error.message
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve usage data. Please try again.",
          cause: error
        });
      }
    }),
  companyCreateCompanyAndContact: pcwProtectedProcedure
    .input(CreateCompanyAndContactInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await createCompanyAndContact(input, {
          createdById: ctx.user.id,
          userRole: ctx.profile.role
        });
      } catch (error) {
        ctx.logger.error("companyRouter.createCompanyAndContact: ", { error });

        // Check for specific error types
        if (error instanceof Error) {
          if (error.message.includes("Address with GUID")) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message:
                "Selected address could not be found. Please select a different address."
            });
          }
          if (error.message.includes("Failed to create contact")) {
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to create contact information. Please try again."
            });
          }
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create company and contact. Please try again.",
          cause: error
        });
      }
    }),
  generatePrefilledContract: pcwProtectedProcedure
    .input(GeneratePrefilledContractSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await generatePrefilledContractAndUpsertContract(
          input,
          ctx.user.id,
          ctx.profile.role
        );
      } catch (error) {
        ctx.logger.error("companyRouter.generatePrefilledContract: ", {
          error
        });
        // TODO this Error is vague and not helpful.
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate contract. Please try again.",
          cause: error
        });
      }
    }),
  getConfirmationDetails: pcwProtectedProcedure
    .input(z.object({ quoteId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        return await getConfirmationDetails(input.quoteId, ctx.user.id);
      } catch (error) {
        ctx.logger.error("quoteRouter.getConfirmationDetails: ", {
          error,
          data: {
            user: ctx.user
          }
        });
        throw error;
      }
    }),
  signContract: pcwProtectedProcedure
    .input(SignContractSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        if (!ctx.ipAddress) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            // TODO: We don't want to tell the user we're trying to verify their location.
            // We should be more vague here and say there as an issue when attempting to sign your contract.
            message:
              "Unable to verify your location. Please check your connection and try again."
          });
        }

        return await signContract(
          input,
          ctx.user.id,
          ctx.ipAddress,
          ctx.profile.role
        );
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        ctx.logger.error("companyRouter.signContract: ", {
          error
        });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to sign contract. Please try again.",
          cause: error
        });
      }
    }),
  downloadSignedContract: pcwProtectedProcedure
    .input(DownloadSignedContractSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await downloadSignedContract(input, ctx.user.id);
      } catch (error) {
        ctx.logger.error("companyRouter.downloadSignedContract: ", {
          error
        });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to download contract. Please try again.",
          cause: error
        });
      }
    }),
  siteSubmitSiteInformation: pcwProtectedProcedure
    .input(SiteInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await submitSiteInformation(input, {
          createdById: ctx.user.id,
          userRole: ctx.profile.role
        });
      } catch (error) {
        ctx.logger.error("siteRouter.submitSiteInformation: ", { error });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to submit site information. Please try again.",
          cause: error
        });
      }
    }),
  usageSubmitUsageInformation: pcwProtectedProcedure
    .input(UsageInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await submitUsageInformation(input, {
          createdById: ctx.user.id,
          userRole: ctx.profile.role
        });
      } catch (error) {
        ctx.logger.error("usageRouter.submitUsageInformation: ", { error });

        if (error instanceof ClientFacingError) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: error.message
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to submit usage information. Please try again.",
          cause: error
        });
      }
    }),
  quoteUpdateQuotes: pcwProtectedProcedure
    .input(PcwUpdateQuoteInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await updateQuotes(input, {
          createdById: ctx.user.id,
          userRole: ctx.profile.role
        });
      } catch (error) {
        ctx.logger.error("quoteRouter.updateQuotes: ", { error });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update quotes. Please try again.",
          cause: error
        });
      }
    }),
  quoteSignUpQuote: pcwProtectedProcedure
    .input(PcwSignUpQuoteInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        return await signUpQuote(input, ctx.user.id);
      } catch (error) {
        ctx.logger.error("quoteRouter.signUpQuote: ", { error });
        throw new Error("Internal server error");
      }
    })
});

type PcwRouter = typeof pcwRouter;
export type BusinessTargetGet = inferProcedureOutput<
  PcwRouter["_def"]["procedures"]["businessTargetGet"]
>;

export type QuoteGetQuotesByQuoteListId = inferProcedureOutput<
  PcwRouter["_def"]["procedures"]["quoteGetQuotesByQuoteListId"]
>;
