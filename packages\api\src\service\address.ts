import { prisma } from "@watt/db/src/client";
import { UtilityType } from "@watt/db/src/enums";
import type { GetMeterNumbersByAddressAndUtility } from "../types/address";
import { getAddressById } from "./get-address";

export async function getMeterNumbersForAddressAndUtility(
  input: GetMeterNumbersByAddressAndUtility
) {
  const { addressId, utilityType } = input;
  // Check if the address already exists in entity address table
  const internalDbAddress = await prisma.entityAddress.findUnique({
    where: {
      id: addressId
    },
    select: {
      mpans: {
        select: {
          value: true
        }
      },
      mprns: {
        select: {
          value: true
        }
      }
    }
  });

  // TODO: If address is found but there are no meters in the entity address then maybe we should fetch from Aperture
  if (internalDbAddress) {
    if (utilityType === UtilityType.ELECTRICITY) {
      return internalDbAddress.mpans.map(mpan => mpan.value);
    }

    return internalDbAddress.mprns.map(mprn => mprn.value);
  }

  // Fetch address and meter data from Aperture, store it in the entity address table and return the meter numbers
  const address = await getAddressById({ id: addressId });

  if (!address) {
    return [];
  }

  if (utilityType === UtilityType.ELECTRICITY) {
    return address.mpans.map(mpan => mpan.value);
  }

  return address.mprns.map(mprn => mprn.value);
}
