import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>risma, Profile } from "@prisma/client";
import { getDurationFilterArray } from "@watt/common/src/utils/duration-filter-array";
import { getEndofDayDate } from "@watt/common/src/utils/end-of-day-date";
import { phoneNumberToE164 } from "@watt/common/src/utils/phone-number-to-e164";
import type { PrismaExtendedClient } from "@watt/db/src/client";

import type {
  DateRangeArrayFilterType,
  InfiniteQueryData,
  StringArrayFilterType
} from "../types/common";
import { getE164Array } from "../utils/phone-number-to-e164";

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
function gatherRecordings(initiatorCallSid: string, currentCall: any) {
  if (!currentCall) {
    return [];
  }

  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  const parentRecordings: any =
    currentCall.parentCall && initiatorCallSid !== currentCall.parentCall.sid
      ? gatherRecordings(initiatorCallSid, currentCall.parentCall)
      : [];

  const childRecordings = currentCall.childCalls
    ? currentCall.childCalls
        // biome-ignore lint/suspicious/noExplicitAny: <fix later>
        .filter((childCall: any) => initiatorCallSid !== childCall.sid)
        // biome-ignore lint/suspicious/noExplicitAny: <fix later>
        .flatMap((childCall: any) =>
          gatherRecordings(initiatorCallSid, childCall)
        )
    : [];

  return [...currentCall.recordings, ...parentRecordings, ...childRecordings];
}

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
function addAssociatedCallRecordings(call: any) {
  const recordings = gatherRecordings(call.sid, call);

  return {
    ...call,
    recordings: recordings
      .sort((a, b) => {
        return (
          new Date(a.dateCreated).getTime() - new Date(b.dateCreated).getTime()
        );
      })
      // biome-ignore lint/suspicious/noExplicitAny: <fix later>
      .filter((recording: any, index: number, self: any[]) => {
        // biome-ignore lint/suspicious/noExplicitAny: <fix later>
        return index === self.findIndex((t: any) => t.sid === recording.sid);
      })
  };
}

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
function isDurationInFilter(call: any, durationFilterArray: any[] | undefined) {
  if (!durationFilterArray || durationFilterArray.length === 0) {
    return true;
  }

  const callDuration = Number.parseInt(call.duration);
  return (
    !Number.isNaN(callDuration) &&
    durationFilterArray.some(
      range => callDuration >= range.min && callDuration <= range.max
    )
  );
}

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
function adjustCallStatus(call: any, parentCall: any) {
  return call.status === "COMPLETED" && parentCall.status === "NO_ANSWER"
    ? parentCall.status
    : call.status;
}

// biome-ignore lint/suspicious/noExplicitAny: <fix later>
function adjustCallDirection(call: any, userProfile: Profile) {
  const toMe = call.toFormatted === userProfile?.directDialE164;

  if (toMe && call.direction === "outbound-api") {
    return "inbound-api";
  }

  return call.direction;
}

function filterCalls(
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  calls: any[],
  userProfile: Profile,
  // biome-ignore lint/suspicious/noExplicitAny: <fix later>
  durationFilterArray: any[] | undefined,
  isAdminCall: boolean
) {
  return calls.filter(call => {
    if (!isDurationInFilter(call, durationFilterArray)) {
      return false;
    }

    const fromMe = call.fromFormatted === userProfile?.directDialE164;
    const toMe = call.toFormatted === userProfile?.directDialE164;

    if (call.childCalls.length > 0 && !fromMe && !toMe) {
      // biome-ignore lint/suspicious/noExplicitAny: <fix later>
      return call.childCalls.some((childCall: any) => {
        const isClientCall =
          childCall.to?.includes("client:") ||
          childCall.from?.includes("client:");
        const fromMe = childCall.fromFormatted === userProfile?.directDialE164;
        const inboundCall = childCall.direction === "inbound";

        return isClientCall && (fromMe || isAdminCall) && inboundCall;
      });
    }

    if (call.parentCall) {
      const isNotClientCall =
        !call.to?.includes("client:") && !call.from?.includes("client:");

      if (isNotClientCall) {
        call.status = adjustCallStatus(call, call.parentCall);
      }
    }

    call.direction = adjustCallDirection(call, userProfile);

    if (isAdminCall) {
      return true;
    }

    return fromMe || toMe;
  });
}

export async function getCallsList(
  userProfile: Profile,
  queryData: InfiniteQueryData,
  prisma: PrismaExtendedClient,
  isAdminCall = false
) {
  const { calls, totalCount, nextCursor, durationFilterArray } =
    await fetchCallsData(
      queryData,
      userProfile.directDialE164 || "",
      prisma,
      isAdminCall
    );

  const filteredCalls = filterCalls(
    calls,
    userProfile,
    durationFilterArray,
    isAdminCall
  );
  const itemsWithAllRecordings = filteredCalls.flatMap(call =>
    addAssociatedCallRecordings(call)
  );

  const itemsWithoutClientCalls = itemsWithAllRecordings.filter(
    call => !call.to?.includes("client:") && !call.from?.includes("client:")
  );

  return {
    items: itemsWithoutClientCalls,
    meta: { totalRowCount: totalCount },
    nextCursor
  };
}

const fetchCallsData = async (
  queryData: InfiniteQueryData,
  userPhoneNumberE164: string,
  prisma: PrismaExtendedClient,
  isAdminCall = false
) => {
  const { searchFilters, cursor } = queryData;

  // TODO (Bidur): Create a function to extract filters from the columnFilters object with correct types
  const textFilter = searchFilters?.globalFilter;
  const statusFilter = searchFilters?.columnFilters?.find(
    ({ id }) => id === "status"
  ) as StringArrayFilterType;
  const durationFilter = searchFilters?.columnFilters?.find(
    ({ id }) => id === "duration"
  ) as StringArrayFilterType;
  const directionFilter = searchFilters?.columnFilters?.find(
    ({ id }) => id === "direction"
  ) as StringArrayFilterType;
  const fromFilter = searchFilters?.columnFilters?.find(
    ({ id }) => id === "from"
  ) as StringArrayFilterType;
  const toFilter = searchFilters?.columnFilters?.find(
    ({ id }) => id === "to"
  ) as StringArrayFilterType;
  const dateCreatedFilter = searchFilters?.columnFilters?.find(
    ({ id }) => id === "dateCreated"
  ) as DateRangeArrayFilterType;

  // Get min and max duration from the duration filter which is an array of strings split by '-'
  const durationFilterArray =
    durationFilter?.value && getDurationFilterArray(durationFilter.value);

  const limit = 500;

  const dateCreatedFilterFrom = dateCreatedFilter?.value?.from;
  const dateCreatedFilterTo = getEndofDayDate(
    dateCreatedFilter?.value?.to ?? dateCreatedFilterFrom
  );

  const { phoneNumber: textFilterE164 } = phoneNumberToE164(textFilter || "");
  const toFilterE164Array = getE164Array(toFilter?.value);
  const fromFilterE164Array = getE164Array(fromFilter?.value);

  const whereClause: Prisma.CallInstanceWhereInput = {
    AND: [
      // Conditionally add the filter criteria based on whether the filters are defined
      // At current global filter takes precedence when searching for a phone number
      ...(textFilter
        ? [
            {
              OR: [
                { to: { contains: textFilterE164 } },
                { from: { contains: textFilterE164 } },
                { parentCall: { to: { contains: textFilterE164 } } },
                { parentCall: { from: { contains: textFilterE164 } } }
              ]
            }
          ]
        : [
            ...(toFilter?.value
              ? [
                  {
                    OR: [
                      { to: { in: toFilterE164Array } },
                      { parentCall: { to: { in: toFilterE164Array } } }
                    ]
                  }
                ]
              : []),
            ...(fromFilter?.value
              ? [
                  {
                    OR: [
                      { from: { in: fromFilterE164Array } },
                      { parentCall: { from: { in: fromFilterE164Array } } }
                    ]
                  }
                ]
              : [])
          ]),
      ...(directionFilter?.value
        ? [{ direction: { in: directionFilter.value } }]
        : []),
      ...(statusFilter?.value
        ? [{ status: { in: statusFilter.value as CallStatus[] } }]
        : []),
      ...(dateCreatedFilterFrom
        ? [
            {
              dateCreated: {
                gte: dateCreatedFilterFrom
              }
            }
          ]
        : []),
      ...(dateCreatedFilterTo
        ? [
            {
              dateCreated: {
                lte: dateCreatedFilterTo
              }
            }
          ]
        : []),
      ...(!isAdminCall
        ? [
            {
              OR: [
                { to: { contains: userPhoneNumberE164 } },
                { from: { contains: userPhoneNumberE164 } },
                {
                  childCalls: {
                    some: {
                      OR: [
                        { to: { contains: userPhoneNumberE164 } },
                        { from: { contains: userPhoneNumberE164 } }
                      ]
                    }
                  }
                },
                {
                  parentCall: {
                    OR: [
                      { to: { contains: userPhoneNumberE164 } },
                      { from: { contains: userPhoneNumberE164 } }
                    ]
                  }
                }
              ]
            }
          ]
        : [])
    ]
  };

  // TODO totalCount will be higher than the actual number of calls returned
  // So we need to modify it with the other function
  const totalCount = await prisma.callInstance.count({
    where: whereClause
  });

  const calls = await prisma.callInstance.findMany({
    // cacheStrategy: { ttl: 60, swr: 60 },
    take: limit + 1, // get an extra item at the end which we'll use as next pageIndex
    cursor: cursor ? { sid: cursor } : undefined,
    orderBy: {
      dateCreated: "desc"
    },
    select: {
      sid: true,
      parentCallSid: true,
      dateCreated: true,
      from: true,
      to: true,
      toFormatted: true,
      fromFormatted: true,
      toIdentity: true,
      fromIdentity: true,
      direction: true,
      status: true,
      duration: true,
      recordings: {
        select: {
          sid: true,
          callSid: true,
          duration: true,
          status: true,
          source: true,
          mediaUrl: true,
          dateCreated: true
        }
      },
      childCalls: {
        select: {
          sid: true,
          parentCallSid: true,
          dateCreated: true,
          from: true,
          to: true,
          toFormatted: true,
          fromFormatted: true,
          toIdentity: true,
          fromIdentity: true,
          direction: true,
          status: true,
          duration: true,
          recordings: {
            select: {
              sid: true,
              callSid: true,
              duration: true,
              status: true,
              source: true,
              mediaUrl: true,
              dateCreated: true
            }
          }
        }
      },
      parentCall: {
        select: {
          sid: true,
          parentCallSid: true,
          dateCreated: true,
          from: true,
          to: true,
          toFormatted: true,
          fromFormatted: true,
          toIdentity: true,
          fromIdentity: true,
          direction: true,
          status: true,
          duration: true,
          recordings: {
            select: {
              sid: true,
              callSid: true,
              duration: true,
              status: true,
              source: true,
              mediaUrl: true,
              dateCreated: true
            }
          },
          childCalls: {
            select: {
              sid: true,
              parentCallSid: true,
              dateCreated: true,
              from: true,
              to: true,
              toFormatted: true,
              fromFormatted: true,
              toIdentity: true,
              fromIdentity: true,
              direction: true,
              status: true,
              duration: true,
              recordings: {
                select: {
                  sid: true,
                  callSid: true,
                  duration: true,
                  status: true,
                  source: true,
                  mediaUrl: true,
                  dateCreated: true
                }
              }
            }
          }
        }
      }
    },
    where: whereClause
  });

  let nextCursor: typeof cursor | undefined = undefined;

  if (calls.length > limit) {
    const nextItem = calls.pop();
    nextCursor = nextItem?.sid;
  }

  return {
    calls,
    totalCount,
    nextCursor,
    durationFilterArray
  };
};
