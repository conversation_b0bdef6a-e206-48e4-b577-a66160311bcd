import type { Prisma } from "@prisma/client";
import { prisma } from "@watt/db/src/client";
import type {
  DeleteFileInputType,
  EditFileInputType,
  GetCompanyFilesInputType,
  RegisterFileInputType
} from "../types/company-files";

/**
 * Registers a new company file in the database
 * @param input - File registration parameters
 * @param userId - ID of the user creating the file
 * @returns Newly created company file
 */
export async function registerFile(
  input: RegisterFileInputType,
  userId: string
) {
  const { sites, siteMeters, ...fileData } = input;

  return await prisma.companyFile.create({
    data: {
      ...fileData,
      ...(sites && {
        sites: {
          connect: sites.map(id => ({ id }))
        }
      }),
      ...(siteMeters && {
        siteMeters: {
          connect: siteMeters.map(id => ({ id }))
        }
      }),
      createdById: userId
    }
  });
}

const getCompanyFilesQueryOptions = {
  include: {
    sites: {
      select: {
        id: true,
        siteRefId: true
      }
    },
    createdBy: {
      select: {
        forename: true,
        surname: true
      }
    },
    siteMeters: {
      select: {
        id: true,
        utilityType: true,
        companySite: {
          select: {
            id: true,
            siteRefId: true
          }
        },
        electricSiteMeter: {
          select: {
            mpan: {
              select: {
                value: true
              }
            }
          }
        },
        gasSiteMeter: {
          select: {
            mprn: {
              select: {
                value: true
              }
            }
          }
        }
      }
    }
  }
} satisfies Prisma.CompanyFileDefaultArgs;

/**
 * Retrieves company files based on provided filters
 * @param input - File retrieval parameters
 * @returns Array of company files with associated data
 */
export async function getCompanyFiles(input: GetCompanyFilesInputType) {
  const { companyId, fileTypes, filterByFilename, fileIds } = input;

  const where = {
    deletedAt: null,
    companyId,
    ...(fileIds?.length && {
      id: {
        in: fileIds
      }
    }),
    ...(fileTypes?.length && {
      type: {
        in: fileTypes
      }
    }),
    ...(filterByFilename && {
      filename: {
        contains: filterByFilename,
        mode: "insensitive" as const
      }
    })
  } satisfies Prisma.CompanyFileWhereInput;

  return await prisma.companyFile.findMany({
    ...getCompanyFilesQueryOptions,
    where,
    orderBy: {
      updatedAt: "desc"
    },
    take: 250 // TODO: Keep until better solution (pagination / infinite scroll)
  });
}

/**
 * Soft deletes a company file
 * @param input - File deletion parameters
 * @param userId - ID of the user performing the deletion
 * @returns Deleted company file
 */
export async function deleteFile(input: DeleteFileInputType, userId: string) {
  const { fileId } = input;

  return await prisma.companyFile.update({
    where: { id: fileId },
    data: {
      deletedAt: new Date(),
      deletedById: userId
    }
  });
}

/**
 * Updates a company file's properties
 * @param input - File update parameters
 * @returns Updated company file
 */
export async function editFile(input: EditFileInputType) {
  const { fileId, filename, sites, siteMeters } = input;

  return await prisma.companyFile.update({
    where: { id: fileId },
    data: {
      ...(filename && { filename }),
      ...(sites && {
        sites: {
          set: [],
          connect: sites.map(id => ({ id }))
        }
      }),
      ...(siteMeters && {
        siteMeters: {
          set: [],
          connect: siteMeters.map(id => ({ id }))
        }
      })
    }
  });
}
