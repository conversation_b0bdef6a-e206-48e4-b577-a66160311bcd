import { createGenericBuilder } from "@watt/common/src/utils/generic-fluent-builder";
import * as z from "zod";
import { compareAddresses } from "./entityAddress";

const RelatedEntityAddressModel = z.object({
  id: z.string(),
  postcode: z.string(),
  county: z.string().nullable(),
  postalTown: z.string().nullable(),
  country: z.string().nullable(),
  displayName: z.string().nullable(),
  addressLine1: z.string().nullable(),
  addressLine2: z.string().nullable(),
  houseName: z.string().nullable(),
  houseNumber: z.string().nullable(),
  flatNumber: z.string().nullable(),
  apertureOrigin: z.boolean().nullable(),
  mpans: z.array(
    z.object({
      value: z.string()
    })
  ),
  mprns: z.array(
    z.object({
      value: z.string()
    })
  )
});

function createAddressBuilder() {
  return createGenericBuilder(RelatedEntityAddressModel)
    .set("id", "test-id")
    .set("county", "Test County")
    .set("postalTown", "Test Town")
    .set("country", "Test Country")
    .set("displayName", "Test Address")
    .set("addressLine1", "Test Address Line 1")
    .set("addressLine2", "Test Address Line 2")
    .set("houseNumber", "10")
    .set("flatNumber", "1")
    .set("houseName", "Test House")
    .set("postcode", "TEST1 2AB")
    .set("apertureOrigin", false)
    .set("mpans", [])
    .set("mprns", []);
}

describe("compareAddresses", () => {
  it("sorts by houseNumber when different", () => {
    const addressA = createAddressBuilder().set("houseNumber", "10").build();
    const addressB = createAddressBuilder().set("houseNumber", "20").build();

    const result = compareAddresses(addressA, addressB);
    expect(result).toBeLessThan(0); // 10 < 20
  });

  it("sorts by flatNumber when houseNumber is equal", () => {
    const addressA = createAddressBuilder()
      .set("houseNumber", "10")
      .set("flatNumber", "1")
      .build();
    const addressB = createAddressBuilder()
      .set("houseNumber", "10")
      .set("flatNumber", "2")
      .build();

    const result = compareAddresses(addressA, addressB);
    expect(result).toBeLessThan(0); // 1 < 2
  });

  it("falls back to houseName when numbers are equal", () => {
    const addressA = createAddressBuilder()
      .set("houseNumber", "10")
      .set("houseName", "Aardvark House")
      .build();
    const addressB = createAddressBuilder()
      .set("houseNumber", "10")
      .set("houseName", "Zebra Lodge")
      .build();

    const result = compareAddresses(addressA, addressB);
    expect(result).toBeLessThan(0); // "Aardvark" < "Zebra"
  });

  it("returns 0 when all fields are equal", () => {
    const addressA = createAddressBuilder()
      .set("houseNumber", "10")
      .set("flatNumber", "1")
      .set("houseName", "Test House")
      .set("addressLine1", "Main St")
      .set("addressLine2", "Apt")
      .build();
    const addressB = createAddressBuilder()
      .set("houseNumber", "10")
      .set("flatNumber", "1")
      .set("houseName", "Test House")
      .set("addressLine1", "Main St")
      .set("addressLine2", "Apt")
      .build();

    const result = compareAddresses(addressA, addressB);
    expect(result).toBe(0);
  });
});
