import type { Prisma } from "@prisma/client";
import { ONE_DAY_SEC } from "@watt/common/src/constants/time-durations";
import { prisma } from "@watt/db/src/client";
import { UtilityType } from "@watt/db/src/enums";
import type { ApertureAddressFormatted } from "@watt/external-apis/src/common/aperture";
import { getFormattedApertureAddressByGlobalAddressKey } from "@watt/external-apis/src/libs/aperture/meter-by-global-address-key";
import type { ApertureCombinedAddressSearchSuggestion } from "@watt/external-apis/src/libs/aperture/search-meter-addresses";
import { cacheWrap } from "@watt/redis/src/cache";
import { redis } from "@watt/redis/src/client";

export const AddressFormattedError = {
  INVALID_GLOBAL_ADDRESS_KEY: "INVALID_GLOBAL_ADDRESS_KEY",
  DATA_SOURCE_FAILED: "DATA_SOURCE_FAILED",
  NONE_FOUND: "NONE_FOUND"
} as const;

const getEntityAddressQueryOptions = {
  select: {
    id: true,
    uprn: true,
    postcode: true,
    county: true,
    pesRegion: true,
    postalTown: true,
    country: true,
    displayName: true,
    addressLine1: true,
    addressLine2: true,
    houseName: true,
    houseNumber: true,
    flatNumber: true,
    mpans: {
      select: {
        value: true,
        uprn: true,
        meterType: true,
        profileClass: true,
        mtc: true,
        lineLossFactor: true,
        standardSettlementConfigurationId: true,
        measurementClass: true
      }
    },
    mprns: {
      select: { value: true, uprn: true, udprn: true }
    }
  }
} satisfies Prisma.EntityAddressDefaultArgs;

export type EntityAddressGetPayload = Prisma.EntityAddressGetPayload<
  typeof getEntityAddressQueryOptions
>;

export type FormattedAddress = {
  id: string;
  address: string | null;
  mpans: string[];
  mprns: string[];
};

export const formatAddress = (
  entityAddress: EntityAddressGetPayload
): FormattedAddress => {
  const { displayName, ...rest } = entityAddress;
  return {
    ...rest,
    // Rename displayName to address - legacy field to support quote app address lookup API
    address: displayName,
    mpans: entityAddress.mpans.map(mpan => mpan.value),
    mprns: entityAddress.mprns.map(mprn => mprn.value)
  };
};

/**
 * Retrieves the Global Address Keys (GAKs) for a given id from Redis.
 * @param id - The id of the address to get.
 * @returns The global address keys.
 */
async function getGlobalAddressKeysById(id: string) {
  const payload = await redis.get<ApertureCombinedAddressSearchSuggestion>(
    `gak_by_id:${id}`
  );
  if (!payload) {
    return {
      electricGlobalAddressKey: undefined,
      gasGlobalAddressKey: undefined
    };
  }
  return {
    electricGlobalAddressKey: payload.electricGlobalAddressKey,
    gasGlobalAddressKey: payload.gasGlobalAddressKey
  };
}

/**
 * Construct the displayName for the Aperture address.
 * @param address - The Aperture address to construct the displayName for.
 * @returns The displayName for the address.
 */
function constructDisplayName(address: ApertureAddressFormatted["address"]) {
  const addressElements = {
    addressLine1: address.addressLine1,
    addressLine2: address.addressLine2,
    addressLine3: address.addressLine3,
    locality: address.locality.toLocaleUpperCase(),
    region: address.region,
    postalCode: address.postalCode
  };
  return Object.values(addressElements).filter(Boolean).join(", ");
}

/**
 * Get the PES region by the mpan.
 * @param mpan - The mpan to get the PES region for.
 * @returns The PES region.
 */
function pesRegionFromMpan(mpan: string): string | null {
  const distributorId = mpan.slice(0, 2);
  const map: Record<string, string> = {
    "10": "EA",
    "11": "EM",
    "12": "LO",
    "13": "ME",
    "14": "MI",
    "15": "NE",
    "16": "NW",
    "17": "SE",
    "18": "SO",
    "19": "SW",
    "20": "SA",
    "21": "YK",
    "22": "SP",
    "23": "SH"
  };
  return map[distributorId] ?? null;
}

/**
 * Maps the Aperture addresses to an entity address.
 * @param id - The id of the address to maintain the same reference.
 * @param electricAddress - The Aperture address to map to an entity address.
 * @param gasAddress - The Aperture address to map to an entity address.
 * @returns The entity address.
 */
function mapApertureAddressesToEntityAddress(
  id: string,
  electricAddress: ApertureAddressFormatted | null,
  gasAddress: ApertureAddressFormatted | null
): EntityAddressGetPayload {
  const primary = electricAddress?.address ?? gasAddress?.address;

  if (!primary) {
    throw AddressFormattedError.NONE_FOUND;
  }

  const electricityMeters =
    electricAddress?.layoutName === "ElectricityUtility"
      ? electricAddress.address.electricityMeters
      : [];

  const gasMeters =
    gasAddress?.layoutName === "GasUtility" ? gasAddress.address.gasMeters : [];

  const uprn = electricityMeters[0]?.uprn ?? gasMeters[0]?.uprn ?? null;
  const mpan = electricityMeters[0]?.mpan ?? null;

  return {
    id,
    postcode: primary.postalCode,
    county: primary.region,
    uprn,
    pesRegion: mpan ? pesRegionFromMpan(mpan) : null,
    postalTown: primary.locality.toLocaleUpperCase(),
    country: primary.country,
    displayName: constructDisplayName(primary),
    addressLine1: primary.addressLine1,
    addressLine2: primary.addressLine2,
    houseName: "",
    houseNumber: "",
    flatNumber: "",
    mpans: electricityMeters.map(m => ({
      value: m.mpan,
      uprn: m.uprn,
      udprn: null,
      meterType: m.meterType,
      profileClass: m.profileClass,
      mtc: m.meterTimeswitchClass,
      lineLossFactor: m.lineLossFactor,
      standardSettlementConfigurationId: m.standardSettlementConfiguration,
      measurementClass: m.measurementClass
    })),
    mprns: gasMeters.map(g => ({
      value: g.mprn,
      uprn: g.uprn,
      udprn: null
    }))
  } satisfies EntityAddressGetPayload;
}

/**
 * Get an entity address by its id from Aperture.
 * @param id - The id of the address to get.
 * @returns The address.
 */
async function getApertureEntityAddressById(
  id: string
): Promise<EntityAddressGetPayload | null> {
  const globalAddressKeys = await getGlobalAddressKeysById(id);

  if (
    !globalAddressKeys.electricGlobalAddressKey &&
    !globalAddressKeys.gasGlobalAddressKey
  ) {
    return null;
  }

  const [electricAddressResult, gasAddressResult] = await Promise.allSettled([
    getFormattedApertureAddressByGlobalAddressKey(
      UtilityType.ELECTRICITY,
      globalAddressKeys.electricGlobalAddressKey
    ),
    getFormattedApertureAddressByGlobalAddressKey(
      UtilityType.GAS,
      globalAddressKeys.gasGlobalAddressKey
    )
  ]);

  const electricAddress =
    electricAddressResult.status === "fulfilled"
      ? electricAddressResult.value
      : null;
  const gasAddress =
    gasAddressResult.status === "fulfilled" ? gasAddressResult.value : null;

  if (!electricAddress && !gasAddress) {
    return null;
  }

  return mapApertureAddressesToEntityAddress(id, electricAddress, gasAddress);
}

/**
 * Map a manual address to an entity address.
 * @param address - The manual address to map.
 * @returns The entity address.
 */
function mapManualAddressToEntityAddress(
  address: ApertureCombinedAddressSearchSuggestion
): EntityAddressGetPayload {
  const { id, text: displayName, postcode, additionalAttributes } = address;

  const { addressLine1, addressLine2, postalTown, county } =
    additionalAttributes.reduce(
      (acc, attr) => {
        acc[attr.name] = attr.value;
        return acc;
      },
      {} as Record<string, string>
    );

  return {
    id,
    uprn: null,
    postcode,
    county: county ?? null,
    pesRegion: null,
    postalTown: postalTown ?? null,
    country: null,
    displayName,
    addressLine1: addressLine1 ?? null,
    addressLine2: addressLine2 ?? null,
    houseName: "",
    houseNumber: "",
    flatNumber: "",
    mpans: [],
    mprns: []
  };
}

/**
 *
 * @param id
 * @returns
 */
async function getCachedManualEntityAddressById(
  id: string
): Promise<EntityAddressGetPayload | null> {
  const address = await redis.get<ApertureCombinedAddressSearchSuggestion>(
    `gak_by_id:${id}`
  );

  if (!address) {
    return null;
  }

  return mapManualAddressToEntityAddress(address);
}

/**
 * Get a formatted address by its public guid.
 * @param id - The id of the address to get.
 * @param hideAddressWithCreatedById - Whether to hide the address created by user.
 * @param options - Optional configuration for address retrieval and creation.
 * @returns The address.
 */
export async function getAddressById({
  id,
  hideAddressWithCreatedById
}: {
  id: string;
  hideAddressWithCreatedById?: boolean;
}): Promise<EntityAddressGetPayload | null> {
  const address = await cacheWrap(
    `get-addresses-by-id:${id}`,
    async () => {
      const [dbRes, apiRes, manualRes] = await Promise.allSettled([
        await prisma.entityAddress.findFirst({
          ...getEntityAddressQueryOptions,
          where: {
            id,
            // TODO: Still will need to be changed to ignore test users rather than ignoring all
            ...(hideAddressWithCreatedById && { createdById: null })
          }
        }),
        await getApertureEntityAddressById(id),
        await getCachedManualEntityAddressById(id)
      ]);

      if (
        dbRes.status === "rejected" &&
        apiRes.status === "rejected" &&
        manualRes.status === "rejected"
      ) {
        throw AddressFormattedError.DATA_SOURCE_FAILED;
      }

      const dbData = dbRes.status === "fulfilled" ? dbRes.value : null;

      // If the address is found in the database, return it.
      if (dbData) {
        return dbData;
      }

      const manualData =
        manualRes.status === "fulfilled" ? manualRes.value : null;

      const apiData = apiRes.status === "fulfilled" ? apiRes.value : null;

      const uprn = apiData?.uprn ?? null;

      if (uprn) {
        const existingAddress = await prisma.entityAddress.findFirst({
          ...getEntityAddressQueryOptions,
          where: { uprn }
        });

        if (existingAddress) {
          return existingAddress;
        }
      }

      // If the address is found in Aperture, return it.
      if (apiData) {
        const createPayload = {
          id,
          uprn: apiData.uprn,
          postcode: apiData.postcode,
          county: apiData.county,
          pesRegion: apiData.pesRegion,
          postalTown: apiData.postalTown,
          country: apiData.country,
          displayName: apiData.displayName,
          addressLine1: apiData.addressLine1,
          addressLine2: apiData.addressLine2,
          houseName: apiData.houseName,
          houseNumber: apiData.houseNumber,
          flatNumber: apiData.flatNumber,
          apertureOrigin: true,
          mpans: {
            connectOrCreate: apiData.mpans.map(m => ({
              where: { value: m.value },
              create: {
                value: m.value,
                uprn: m.uprn,
                udprn: apiData.uprn,
                meterType: m.meterType,
                profileClass: m.profileClass,
                mtc: m.mtc,
                lineLossFactor: m.lineLossFactor,
                standardSettlementConfigurationId:
                  m.standardSettlementConfigurationId,
                measurementClass: m.measurementClass
              }
            }))
          },
          mprns: {
            connectOrCreate: apiData.mprns.map(m => ({
              where: { value: m.value },
              create: {
                value: m.value,
                uprn: m.uprn,
                udprn: m.udprn
              }
            }))
          }
        } satisfies Prisma.EntityAddressCreateInput;

        const createdAddress = await prisma.entityAddress.create({
          data: createPayload,
          ...getEntityAddressQueryOptions
        });

        // Delete the search-addresses-by-postcode cache key to ensure fresh results on subsequent searches
        const postcodeForCache = apiData.postcode.replaceAll(" ", "_");
        await redis.del(`search-addresses-by-postcode:${postcodeForCache}`);

        return createdAddress;
      }

      // If the address was not found via Aperture, return the manual address.
      if (manualData) {
        // const createPayload = {
        //     id: manualData.id,
        //     postcode: manualData.postcode,
        //     county: manualData.county,
        //     postalTown: manualData.postalTown,
        //     country: manualData.country,
        //     displayName: manualData.displayName,
        //     addressLine1: manualData.addressLine1,
        //     addressLine2: manualData.addressLine2,
        //     houseName: manualData.houseName,
        //     houseNumber: manualData.houseNumber,
        //     flatNumber: manualData.flatNumber
        //   } satisfies Prisma.CustomerAddressSubmissionCreateInput;

        // after(async () => {
        //   await prisma.customerAddressSubmission.create({
        //     data: createPayload
        //   });
        // });

        // By not returning the manual address, the legacy QuoteApp will show an error page to the user
        // As we do not want to allow manual addresses to be used in the QuoteApp.
        return manualData;
      }

      // If the address is not found in the database or Aperture, return null.
      return null;
    },
    ONE_DAY_SEC
  );

  return address ? address : null;
}
