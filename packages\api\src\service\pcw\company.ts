import type { Profile } from "@prisma/client";
import { log } from "@watt/common/src/utils/axiom-logger";
import { phoneNumberToE164 } from "@watt/common/src/utils/phone-number-to-e164";
import { prisma } from "@watt/db/src/client";
import { UserRole } from "@watt/db/src/enums";
import { supabaseAdmin } from "@watt/db/src/supabase/supabase";
import type { AdditionalInputData } from "../../types/general";
import type { CreateCompanyAndContactInput } from "../../types/pcw/company";
import { getAddressById } from "../get-address";
import { createContact } from "./contact";

async function ensureUserProfile(
  userId: string,
  contactData: CreateCompanyAndContactInput["contact"]
): Promise<{ profile: Profile; created: boolean }> {
  log.info("[ensureUserProfile] Starting profile check/creation", {
    userId,
    email: contactData.email
  });

  // Check if profile already exists
  const existingProfile = await prisma.profile.findUnique({
    where: { userId }
  });

  if (existingProfile) {
    log.info("[ensureUserProfile] Profile already exists", {
      userId,
      profileId: existingProfile.id
    });
    return { profile: existingProfile, created: false };
  }

  log.info("[ensureUserProfile] No existing profile found, creating new one", {
    userId
  });

  // Create profile with QUOTE_APP role
  const directDialE164 = contactData.phoneNumber
    ? phoneNumberToE164(contactData.phoneNumber).phoneNumber
    : null;

  try {
    const profile = await prisma.profile.create({
      data: {
        userId,
        email: contactData.email,
        forename: contactData.forename,
        surname: contactData.surname,
        directDial: contactData.phoneNumber,
        directDialE164,
        role: UserRole.QUOTE_APP,
        disabled: false,
        huntGroups: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    log.info("[ensureUserProfile] Profile created successfully", {
      userId,
      profileId: profile.id
    });

    // Update Supabase user metadata to match
    const { error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      user_metadata: {
        forename: contactData.forename,
        surname: contactData.surname,
        directDial: contactData.phoneNumber,
        role: UserRole.QUOTE_APP,
        huntGroups: [],
        disabled: false
      }
    });

    if (error) {
      // Log error but don't fail - profile is created
      log.error("[ensureUserProfile] Failed to update Supabase user metadata", {
        userId,
        error
      });
    }

    return { profile, created: true };
  } catch (err) {
    const error = err as Error;
    log.error("[ensureUserProfile] Failed to create profile", {
      userId,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

export async function createCompanyAndContact(
  input: CreateCompanyAndContactInput,
  additionalData: AdditionalInputData
) {
  const { company, contact } = input;
  const addressResult = await getAddressById({
    id: company.businessAddressId
  });

  if (!addressResult || !addressResult.id) {
    throw new Error(
      `Address with GUID ${company.businessAddressId} not found.`
    );
  }

  const companyData = await prisma.company.upsert({
    where: {
      registrationNumber: company.businessNumber
    },
    update: {
      // Customers can change the business address as it does not impact the site address / meters
      // TODO: consider what impact this has on the LOA
      entityAddressId: addressResult.id
      // TODO We're not sure if we should allow a customer to be able to change the business reg / type / name. Requires more thought.
      // businessType: company.businessType,
      // name: company.businessName,
      // registrationNumber: company.businessNumber,
    },
    create: {
      businessType: company.businessType,
      name: company.businessName,
      registrationNumber: company.businessNumber,
      entityAddressId: addressResult.id
    },
    select: {
      id: true,
      registrationNumber: true,
      entityAddressId: true,
      contacts: {
        where: {
          forename: contact.forename,
          surname: contact.surname,
          emails: {
            some: {
              email: contact.email
            }
          },
          phoneNumbers: {
            some: {
              phoneNumber: contact.phoneNumber
            }
          }
        },
        select: {
          id: true
        }
      }
    }
  });

  if (companyData.contacts.length > 0) {
    const firstContact = companyData.contacts[0];
    if (!firstContact) {
      throw new Error(
        "Unexpected: contacts array has length > 0 but no first element"
      );
    }
    return {
      companyId: companyData.id,
      companyAddressId: companyData.entityAddressId,
      contactId: firstContact.id,
      profileCreated: false
    };
  }

  // Ensure user profile exists before creating contact
  const { created: profileCreated } = await ensureUserProfile(
    additionalData.createdById,
    input.contact
  );

  const contactData = await createContact(input, companyData, additionalData);

  if (!contactData) {
    throw new Error("Failed to create contact");
  }

  return {
    companyId: companyData.id,
    companyAddressId: companyData.entityAddressId,
    contactId: contactData.id,
    profileCreated
  };
}
