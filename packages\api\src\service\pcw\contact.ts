import { type Company, PhoneNumberType } from "@prisma/client";
import { prisma } from "@watt/db/src/client";
import type { AdditionalInputData } from "../../types/general";
import type { CreateCompanyAndContactInput } from "../../types/pcw/company";
import type { UpsertContactFormData } from "../../types/people";
import { updateCompanyContact } from "../people";

export async function createContact(
  input: CreateCompanyAndContactInput,
  company: Pick<Company, "id" | "registrationNumber" | "entityAddressId">,
  additionalData: AdditionalInputData
) {
  const existingContact = await prisma.companyContact.findFirst({
    where: {
      company: {
        registrationNumber: company.registrationNumber
      },
      site: {
        entityAddressId: company.entityAddressId
      },
      forename: input.contact.forename,
      surname: input.contact.surname,
      emails: {
        some: {
          email: input.contact.email
        }
      },
      phoneNumbers: {
        some: {
          phoneNumber: input.contact.phoneNumber
        }
      }
    },
    select: {
      id: true
    }
  });

  if (existingContact) {
    return existingContact;
  }

  const contactData = {
    forename: input.contact.forename,
    surname: input.contact.surname,
    position: input.contact.position,
    dateOfBirth: input.contact.dateOfBirth,
    contactEmailsData: [
      {
        email: input.contact.email,
        isPrimary: true
      }
    ],
    phoneNumbers: [
      {
        phoneNumber: input.contact.phoneNumber,
        isPrimary: true,
        type: PhoneNumberType.MOBILE
      }
    ],
    addresses: input.contact.addresses,
    companyId: company.id
  } satisfies UpsertContactFormData;

  return await updateCompanyContact({
    input: contactData,
    userId: additionalData.createdById
  });
}

export async function linkContactToSite(
  contactId: string,
  siteId: string,
  additionalData: AdditionalInputData
) {
  await prisma.companyContact.update({
    where: {
      id: contactId
    },
    data: {
      siteId,
      updatedById: additionalData.createdById
    }
  });
}
