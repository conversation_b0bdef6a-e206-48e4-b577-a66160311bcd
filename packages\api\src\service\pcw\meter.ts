import { prisma } from "@watt/db/src/client";
import { UtilityType } from "@watt/db/src/enums";
import type { AdditionalInputData } from "../../types/general";
import { getTariffRatesByMpanCore } from "../meter";
import { createSiteMeterForUtilityType } from "../site-meter";

/**
 * This function ensures the meter exists in the database and is linked to the site meter
 * If the meter does not exist, it will be added to the same entity address as the company site and then linked to the site meter
 * This pcw step is different to the crm because in crm the agent adds the missing meter to an address before quoting
 */
export async function checkAndAddQuotedMeterToSite({
  companyId,
  companySiteId,
  siteRefId,
  companySiteEntityAddressId,
  meterIdentifier,
  utilityType,
  createdById,
  userRole
}: {
  companyId: string;
  companySiteId: string;
  siteRefId: number;
  companySiteEntityAddressId: string;
  meterIdentifier: string;
  utilityType: UtilityType;
} & AdditionalInputData) {
  const siteMeter = await prisma.siteMeter.findFirst({
    where: {
      companySiteId,
      OR: [
        {
          electricSiteMeter: {
            mpanValue: meterIdentifier
          }
        },
        {
          gasSiteMeter: {
            mprnValue: meterIdentifier
          }
        }
      ]
    },
    select: {
      id: true
    }
  });

  // Site meter already exists, so we don't need to add it
  if (siteMeter) {
    return;
  }

  // Link the meter to the site meter
  if (utilityType === UtilityType.ELECTRICITY) {
    const mpan = await prisma.mpan.findFirst({
      where: {
        value: meterIdentifier
      },
      select: {
        entityAddressId: true
      }
    });

    // If the meter does not exist in the mpan table, create a new entry
    if (!mpan) {
      await prisma.mpan.create({
        data: {
          value: meterIdentifier,
          entityAddressId: companySiteEntityAddressId
        }
      });
    }

    const tarrifRates = await getTariffRatesByMpanCore(meterIdentifier);

    // Then link the meter to the site meter
    await createSiteMeterForUtilityType({
      companyId,
      companySiteId,
      siteRefId,
      utilityType,
      meterIdentifier,
      tarrifRates,
      createdById,
      userRole,
      isLinkedToExistingMeter: mpan
        ? mpan.entityAddressId !== companySiteEntityAddressId
        : false // If the meter exists in a another address, we need to link it to the site meter
    });
  }

  if (utilityType === UtilityType.GAS) {
    const mprn = await prisma.mprn.findFirst({
      where: {
        value: meterIdentifier
      },
      select: {
        entityAddressId: true
      }
    });

    if (!mprn) {
      await prisma.mprn.create({
        data: {
          value: meterIdentifier,
          entityAddressId: companySiteEntityAddressId
        }
      });
    }

    await createSiteMeterForUtilityType({
      companyId,
      companySiteId,
      siteRefId,
      utilityType,
      meterIdentifier,
      tarrifRates: null,
      createdById,
      userRole,
      isLinkedToExistingMeter: mprn
        ? mprn.entityAddressId !== companySiteEntityAddressId
        : false // If the meter exists in a another address, we need to link it to the site meter
    });
  }
}
