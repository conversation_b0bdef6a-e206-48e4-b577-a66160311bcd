import {
  ContractStatus,
  ContractType,
  QuoteStatus,
  UtilityType
} from "@prisma/client";
import { log } from "@watt/common/src/utils/axiom-logger";
import { calculateCommission } from "@watt/common/src/utils/calculate-commission";
import { hasQuoteExpired } from "@watt/common/src/utils/quote-expiration";
import { splitUsageByRate } from "@watt/common/src/utils/split-usage-by-rate";
import { prisma } from "@watt/db/src/client";
import { getQuotesMinimumDecimalPlaces } from "@watt/db/src/utils/get-quotes-minimum-decimal-places";
import { after } from "next/server";
import { ClientFacingError } from "../../lib/exceptions";
import type { AdditionalInputData } from "../../types/general";
import type {
  PcwGetQuoteInput,
  PcwSignUpQuoteInput,
  PcwUpdateQuoteInput
} from "../../types/pcw/quote";
import {
  type FindFirstQuoteListGetPayload,
  findFirstQuoteListQuery,
  findUniqueQuoteListSelectQuotes,
  findUniqueQuoteSelectQuote
} from "../../types/quote/quote-queries";
import type { ConsumptionData } from "../../types/quote/quote-types";
import { getEacBandStr } from "../../utils/banded-annual-usage";
import { getUsageDataFromManualInputs } from "../../utils/quote";
import { getTariffSplitRatio } from "../meter";
import {
  buildQuoteSignUpEmailPayload,
  buildSignContractEmailPayload,
  sendReminderEmailNotification
} from "../notification";
import { getElectricQuotes, getGasQuotes } from "../quote/quote-generation";

export async function getQuote(
  input: Omit<PcwGetQuoteInput, "contact">,
  additionalData: AdditionalInputData
) {
  log.info("[getQuote] Starting quote generation", {
    utilityType: input.utilityType,
    createdById: additionalData.createdById,
    userRole: additionalData.userRole
  });

  const DEFAULT_QUOTE_VALUES = {
    creditScore: 100,
    upliftRate: 1,
    isSmartMeter: true,
    isCustomQuotesOnly: false
  };

  let consumptionData = {
    dayUsage: input.totalUsage,
    nightUsage: 0,
    weekendUsage: 0,
    manualConsumptionEntry: !!input.manualConsumptionEntry,
    totalUsage: input.totalUsage
  } satisfies ConsumptionData;

  if (input.utilityType === UtilityType.ELECTRICITY) {
    const splitRatio = await getTariffSplitRatio(input.meterIdentifier);

    if (!splitRatio) {
      throw new Error(
        `Unable to determine tariff split ratio for meter: ${input.meterIdentifier}`
      );
    }

    const splitUsage = splitUsageByRate({
      totalAnnualUsage: input.totalUsage,
      splitRatio
    });

    consumptionData = {
      ...consumptionData,
      dayUsage: splitUsage.day,
      nightUsage: splitUsage.night,
      weekendUsage: splitUsage.weekend
    };
  }

  const {
    utilityType,
    sitePostcode,
    manualConsumptionEntry,
    totalUsage,
    ...rest
  } = input;

  if (!sitePostcode) {
    throw new ClientFacingError("Invalid sitePostcode");
  }

  if (
    utilityType !== UtilityType.ELECTRICITY &&
    utilityType !== UtilityType.GAS
  ) {
    throw new ClientFacingError("Invalid utilityType");
  }

  let quoteListId: string;

  if (utilityType === UtilityType.ELECTRICITY) {
    log.info("[getQuote] Calling getElectricQuotes", {
      createdById: additionalData.createdById,
      userRole: additionalData.userRole,
      consumptionData
    });

    quoteListId = await getElectricQuotes({
      ...rest,
      ...DEFAULT_QUOTE_VALUES,
      ...additionalData,
      sitePostcode,
      consumptionData,
      isQuoteApp: true
    });
  } else {
    log.info("[getQuote] Calling getGasQuotes", {
      createdById: additionalData.createdById,
      userRole: additionalData.userRole,
      consumptionData
    });

    quoteListId = await getGasQuotes({
      ...rest,
      ...DEFAULT_QUOTE_VALUES,
      ...additionalData,
      sitePostcode,
      consumptionData,
      isQuoteApp: true
    });
  }

  return quoteListId;
}

export async function updateQuotes(
  input: PcwUpdateQuoteInput,
  additionalData: AdditionalInputData
) {
  // Check first if the quote user is trying to update already exists and is not expired
  const existingQuoteList = await prisma.quoteList.findFirst({
    where: {
      contractStartDate: new Date(input.contractStartDate),
      siteMeterId: input.siteMeterId,
      OR: [
        {
          electricityUsage: {
            totalUsage: input.totalUsage,
            mpanValue: input.meterIdentifier
          }
        },
        {
          gasUsage: {
            totalUsage: input.totalUsage,
            mprnValue: input.meterIdentifier
          }
        }
      ],
      status: {
        not: QuoteStatus.EXPIRED
      }
    },
    select: {
      id: true,
      createdAt: true,
      status: true,
      quotes: {
        select: {
          electricQuote: {
            select: {
              isBespokeUplift: true
            }
          },
          gasQuote: {
            select: {
              isBespokeUplift: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: "desc"
    }
  });

  // Check to see if the existing quoteList has expired by createdAt date
  // If custom uplifts are provided, we don't check for existing quotes
  const existingQuoteHasExpiredByDate =
    existingQuoteList && hasQuoteExpired(existingQuoteList.createdAt);
  const quoteAlreadyAccepted =
    existingQuoteList?.status === QuoteStatus.ACCEPTED;

  if (
    existingQuoteList &&
    !existingQuoteHasExpiredByDate &&
    !quoteAlreadyAccepted
  ) {
    return existingQuoteList.id;
  }

  const quoteListToUseData = await prisma.quoteList.findFirst({
    where: {
      id: input.quoteListId
    },
    include: findFirstQuoteListQuery
  });

  if (!quoteListToUseData) {
    throw new ClientFacingError(
      "Quotes not found. If this issue persists, please try generating a new quote."
    );
  }

  const newQuoteListId = await pcwComposeInputPayloadAndGetQuotes(
    quoteListToUseData,
    input,
    additionalData
  );

  after(async () => {
    // Get contact data to send email notification
    // TODO(Bidur): use user session to get the contact information
    const contactData = await prisma.companyContact.findUnique({
      where: {
        id: input.contactId
      },
      select: {
        id: true,
        emails: {
          where: {
            isPrimary: true
          },
          select: {
            email: true
          }
        }
      }
    });

    const contactEmail = contactData?.emails[0]?.email;

    if (!contactData || !contactEmail) {
      return;
    }

    try {
      const emailPayload = await buildQuoteSignUpEmailPayload(
        contactData.id,
        contactEmail,
        quoteListToUseData.siteMeter.companySite.companyId,
        quoteListToUseData.siteMeter.companySite.entityAddress.id,
        quoteListToUseData.utilityType,
        getEacBandStr(input.totalUsage),
        quoteListToUseData.currentProvider.displayName
      );

      await sendReminderEmailNotification(
        emailPayload,
        additionalData.createdById
      );
    } catch (error) {
      log.error(`Failed to send quote sign up email to ${contactEmail}`, {
        error
      });
    }
  });

  return newQuoteListId;
}

async function pcwComposeInputPayloadAndGetQuotes(
  existingQuoteList: FindFirstQuoteListGetPayload,
  updateInput: PcwUpdateQuoteInput,
  additionalData: AdditionalInputData
) {
  const { manualConsumptionEntry } = getUsageDataFromManualInputs(
    existingQuoteList,
    updateInput.totalUsage
  );

  // TODO (Stephen): Poorly typed, we don't use psql discriminators yet this will always be defined
  const meterIdentifier =
    existingQuoteList.utilityType === UtilityType.ELECTRICITY
      ? existingQuoteList.siteMeter.electricSiteMeter?.mpanValue
      : existingQuoteList.siteMeter.gasSiteMeter?.mprnValue;

  if (!meterIdentifier) {
    throw new Error("Invalid meterIdentifier");
  }

  const fullInput = {
    utilityType: existingQuoteList.utilityType,
    businessNumber:
      existingQuoteList.siteMeter.companySite.company.registrationNumber,
    businessName: existingQuoteList.siteMeter.companySite.company.name,
    businessType: existingQuoteList.siteMeter.companySite.company.businessType,
    meterIdentifier,
    businessAddressId:
      existingQuoteList.siteMeter.companySite.company.entityAddress.id,
    siteAddressId: existingQuoteList.siteMeter.companySite.entityAddress.id,
    sitePostcode:
      existingQuoteList.siteMeter.companySite.entityAddress.postcode,
    currentSupplier: existingQuoteList.currentProvider.udcoreId || "",
    contractStartDate: updateInput.contractStartDate,
    manualConsumptionEntry,
    totalUsage: updateInput.totalUsage
  } satisfies Omit<PcwGetQuoteInput, "contact">;

  return await getQuote(fullInput, additionalData);
}

export async function getAllPCWQuotesFromQuoteList(quoteListId: string) {
  const quoteList = await prisma.quoteList.findUnique({
    where: {
      id: quoteListId
    },
    select: {
      id: true,
      utilityType: true,
      contractStartDate: true,
      upliftRate: true,
      status: true,
      createdAt: true,
      siteMeter: {
        select: {
          id: true,
          electricSiteMeter: {
            select: {
              mpanValue: true
            }
          },
          gasSiteMeter: {
            select: {
              mprnValue: true
            }
          }
        }
      },
      electricityUsage: {
        select: {
          totalUsage: true
        }
      },
      gasUsage: {
        select: {
          totalUsage: true
        }
      },
      quotes: {
        select: findUniqueQuoteListSelectQuotes,
        orderBy: [
          {
            status: "desc"
          },
          {
            electricQuote: {
              annualPrice: "asc"
            }
          },
          {
            gasQuote: {
              annualPrice: "asc"
            }
          }
        ]
      }
    }
  });

  if (!quoteList) {
    return {
      quoteList: null,
      maxDecimalPlaces: 0
    };
  }

  const maxDecimalPlaces = getQuotesMinimumDecimalPlaces(quoteList.quotes);

  return {
    quoteList,
    maxDecimalPlaces
  };
}

export async function getPCWQuoteById(quoteId: string) {
  const quote = await prisma.quote.findUnique({
    where: { id: quoteId },
    select: findUniqueQuoteSelectQuote
  });

  if (!quote) {
    throw new ClientFacingError("Quote not found");
  }

  const deal = await prisma.deal.findFirst({
    where: {
      contract: {
        quoteId: quoteId
      }
    },
    select: {
      signedAt: true
    }
  });

  const maxDecimalPlaces = getQuotesMinimumDecimalPlaces([quote]);

  return {
    quote,
    deal,
    maxDecimalPlaces
  };
}

export async function signUpQuote(
  input: PcwSignUpQuoteInput,
  createdById: string
) {
  const quoteData = await prisma.quote.findUnique({
    where: { id: input.quoteId },
    select: {
      endDate: true,
      duration: true,
      quoteList: {
        select: {
          utilityType: true,
          contractStartDate: true,
          currentProvider: {
            select: {
              displayName: true
            }
          },
          siteMeter: {
            select: {
              id: true,
              companySite: {
                select: {
                  id: true,
                  company: {
                    select: {
                      id: true
                    }
                  },
                  entityAddress: {
                    select: {
                      id: true
                    }
                  }
                }
              }
            }
          },
          electricityUsage: {
            select: {
              totalUsage: true
            }
          },
          gasUsage: {
            select: {
              totalUsage: true
            }
          }
        }
      },
      // TODO(Bidur): Consider performance of conditionally selecting electricQuote or gasQuote
      electricQuote: {
        select: {
          unitRateUplift: true,
          standingChargeUplift: true
        }
      },
      gasQuote: {
        select: {
          unitRateUplift: true,
          standingChargeUplift: true
        }
      }
    }
  });

  if (!quoteData) {
    throw new ClientFacingError("Quote not found");
  }

  const existingContract = await prisma.contract.findFirst({
    where: {
      quoteId: input.quoteId,
      contactId: input.contactId
    },
    select: {
      id: true,
      status: true
    }
  });

  if (existingContract?.status === ContractStatus.SIGNED) {
    throw new ClientFacingError("User has already signed the contract");
  }

  const totalEAC =
    quoteData.quoteList.electricityUsage?.totalUsage ??
    quoteData.quoteList.gasUsage?.totalUsage;

  if (!totalEAC) {
    throw new ClientFacingError("Total annual usage cannot be found");
  }

  const uplift =
    quoteData.electricQuote?.unitRateUplift ??
    quoteData.gasQuote?.unitRateUplift;
  const standingChargeUplift =
    quoteData.electricQuote?.standingChargeUplift ??
    quoteData.gasQuote?.standingChargeUplift;
  const termInYears = quoteData.duration / 12;

  const commission = calculateCommission(
    totalEAC,
    uplift,
    standingChargeUplift,
    termInYears
  );
  const contractId = await (async () => {
    if (existingContract) {
      return existingContract.id;
    }

    const newContract = await prisma.contract.create({
      data: {
        utilityType: quoteData.quoteList.utilityType,
        type: ContractType.WRITTEN,
        status: ContractStatus.GENERATED,
        startDate: quoteData.quoteList.contractStartDate,
        endDate: quoteData.endDate,
        term: quoteData.duration,
        commission,
        companyId: quoteData.quoteList.siteMeter.companySite.company.id,
        contactId: input.contactId,
        siteId: quoteData.quoteList.siteMeter.companySite.id,
        siteMeterId: quoteData.quoteList.siteMeter.id,
        quoteId: input.quoteId,
        createdById
      }
    });

    return newContract.id;
  })();

  after(async () => {
    // Get contact data to send email notification
    // TODO(Bidur): use user session to get the contact information
    const contactData = await prisma.companyContact.findUnique({
      where: {
        id: input.contactId
      },
      select: {
        id: true,
        emails: {
          where: {
            isPrimary: true
          },
          select: {
            email: true
          }
        }
      }
    });

    const contactEmail = contactData?.emails[0]?.email;

    if (!contactData || !contactEmail) {
      return;
    }

    try {
      const emailPayload = await buildSignContractEmailPayload(
        contactData.id,
        contactEmail,
        quoteData.quoteList.siteMeter.companySite.company.id,
        quoteData.quoteList.siteMeter.companySite.entityAddress.id,
        quoteData.quoteList.utilityType,
        getEacBandStr(totalEAC),
        quoteData.quoteList.currentProvider.displayName,
        contractId
      );

      await sendReminderEmailNotification(emailPayload, createdById);
    } catch (error) {
      log.error(`Failed to send sign contract email to ${contactEmail}`, {
        error
      });
    }
  });

  return contractId;
}
