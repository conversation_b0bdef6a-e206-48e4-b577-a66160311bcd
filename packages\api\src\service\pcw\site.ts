import { log } from "@watt/common/src/utils/axiom-logger";
import { prisma } from "@watt/db/src/client";
import { after } from "next/server";
import type { AdditionalInputData } from "../../types/general";
import type { SiteInput } from "../../types/pcw/site";
import { getAddressById } from "../get-address";
import {
  buildCurrentSupplierInformationEmailPayload,
  sendReminderEmailNotification
} from "../notification";
import { createAssociatedSiteMetersForNewSite } from "../site-meter";
import { linkContactToSite } from "./contact";

export async function submitSiteInformation(
  input: SiteInput,
  additionalData: AdditionalInputData
) {
  const companyData = await prisma.company.findUnique({
    where: {
      id: input.companyId
    },
    select: {
      registrationNumber: true
    }
  });

  if (!companyData) {
    throw new Error("Company not found");
  }

  const siteData = await createOrConnectCompanySite(input, additionalData);

  after(async () => {
    await linkContactToSite(input.contactId, siteData.siteId, additionalData);

    // Get contact data to send email notification
    // TODO(Bidur): use user session to get the contact information
    const contactData = await prisma.companyContact.findUnique({
      where: {
        id: input.contactId
      },
      select: {
        id: true,
        emails: {
          where: {
            isPrimary: true
          },
          select: {
            email: true
          }
        }
      }
    });

    const contactEmail = contactData?.emails[0]?.email;

    if (!contactData || !contactEmail) {
      return;
    }

    try {
      const emailPayload = await buildCurrentSupplierInformationEmailPayload(
        contactData.id,
        contactEmail,
        input.companyId,
        siteData.siteAddressId,
        input.utilityType
      );

      await sendReminderEmailNotification(
        emailPayload,
        additionalData.createdById
      );
    } catch (error) {
      log.error(
        `Failed to send current supplier information email to ${contactEmail}`,
        {
          error
        }
      );
    }
  });

  return {
    companyReg: companyData.registrationNumber,
    siteAddressId: siteData.siteAddressId,
    utilityType: input.utilityType
  };
}

async function createOrConnectCompanySite(
  input: SiteInput,
  additionalData: AdditionalInputData
) {
  const addressResult = await getAddressById({
    id: input.siteAddressId
  });

  if (!addressResult || !addressResult.id) {
    throw new Error("Address not found");
  }

  const siteData = await prisma.companySite.findFirst({
    where: {
      companyId: input.companyId,
      entityAddressId: addressResult.id
    },
    select: {
      id: true,
      entityAddressId: true
    }
  });

  if (siteData) {
    return {
      siteId: siteData.id,
      siteAddressId: siteData.entityAddressId
    };
  }

  const newSiteData = await prisma.companySite.create({
    data: {
      companyId: input.companyId,
      entityAddressId: addressResult.id
    },
    select: {
      id: true,
      siteRefId: true,
      entityAddressId: true
    }
  });

  await createAssociatedSiteMetersForNewSite(
    input.companyId,
    newSiteData.id,
    newSiteData.siteRefId,
    newSiteData.entityAddressId,
    "",
    additionalData.createdById,
    additionalData.userRole
  );

  return {
    siteId: newSiteData.id,
    siteAddressId: newSiteData.entityAddressId
  };
}
