import { ContractStatus, UtilityType } from "@prisma/client";
import { log } from "@watt/common/src/utils/axiom-logger";
import { prisma } from "@watt/db/src/client";
import { getGasMeterData } from "@watt/external-apis/src/libs/aperture/lookup-gas-meter";
import { getEACAndFullTPRByMpanBottomLine } from "@watt/external-apis/src/libs/electralink/eac-full-tpr";
import { after } from "next/server";
import { ClientFacingError } from "../../lib/exceptions";
import type { AdditionalInputData } from "../../types/general";
import type { PcwGetQuoteInput } from "../../types/pcw/quote";
import type { UsageInput } from "../../types/pcw/usage";
import { getEacBandStr } from "../../utils/banded-annual-usage";
import {
  buildQuoteSignUpEmailPayload,
  sendReminderEmailNotification
} from "../notification";
import { checkAndAddQuotedMeterToSite } from "./meter";
import { getQuote } from "./quote";

/**
 * Get the banded usage for a meter
 * @param meterIdentifier - The meter identifier
 * @param utilityType - The utility type
 * @returns The banded usage for the meter
 */
export async function getBandedUsageForMeter(
  meterIdentifier: string,
  utilityType: UtilityType
) {
  if (utilityType === UtilityType.ELECTRICITY) {
    const eacFullTprData = await getEACAndFullTPRByMpanBottomLine({
      mpan: meterIdentifier
    });

    if (eacFullTprData.error) {
      log.error(
        `Error fetching EAC and TPR data for mpanBottomLine: ${meterIdentifier}`,
        {
          error: eacFullTprData.error.message,
          statusCode: eacFullTprData.error.statusCode,
          meterIdentifier,
          utilityType
        }
      );

      // Treat all errors (including 403) as "no data found"
      throw new ClientFacingError(
        "No usage or supplier data found. Please provide the data manually.",
        422
      );
    }

    const totalUsage = eacFullTprData.data.tprData
      ? eacFullTprData.data.tprData.reduce((acc, curr) => acc + curr.tprEac, 0)
      : eacFullTprData.data.totalEac;

    return {
      totalUsage: totalUsage ? getEacBandStr(totalUsage) : "0",
      supplier: eacFullTprData.data.supplierId
    };
  }

  if (utilityType === UtilityType.GAS) {
    const gasMeterData = await getGasMeterData({ mprn: meterIdentifier });

    if (gasMeterData.error) {
      throw new ClientFacingError(
        "No usage or supplier data found. Please provide the data manually.",
        422
      );
    }

    const totalUsage = Number.parseInt(gasMeterData.data.offtakeQuantityAnnual);

    return {
      totalUsage: getEacBandStr(totalUsage),
      supplier: gasMeterData.data.supplierName
    };
  }

  throw new ClientFacingError("Invalid utility type");
}

/**
 * Get the usage for a meter - not banded
 * @param meterIdentifier - The meter identifier
 * @param utilityType - The utility type
 * @returns The usage for the meter
 */
export async function getUsageForMeter(
  meterIdentifier: string,
  utilityType: UtilityType
) {
  if (utilityType === UtilityType.ELECTRICITY) {
    const eacFullTprData = await getEACAndFullTPRByMpanBottomLine({
      mpan: meterIdentifier
    });

    if (eacFullTprData.error) {
      log.error(
        `Error fetching EAC and TPR data for mpanBottomLine: ${meterIdentifier}`,
        {
          error: eacFullTprData.error.message,
          statusCode: eacFullTprData.error.statusCode,
          meterIdentifier,
          utilityType
        }
      );

      // Treat all errors (including 403) as "no data found"
      throw new ClientFacingError(
        "No usage or supplier data found. Please provide the data manually.",
        422
      );
    }

    const totalUsage = eacFullTprData.data.tprData
      ? eacFullTprData.data.tprData.reduce((acc, curr) => acc + curr.tprEac, 0)
      : eacFullTprData.data.totalEac;

    return {
      totalUsage: totalUsage,
      supplier: eacFullTprData.data.supplierId
    };
  }

  if (utilityType === UtilityType.GAS) {
    const gasMeterData = await getGasMeterData({ mprn: meterIdentifier });

    if (gasMeterData.error) {
      throw new ClientFacingError(
        "No usage or supplier data found. Please provide the data manually.",
        422
      );
    }

    const totalUsage = Number.parseInt(gasMeterData.data.offtakeQuantityAnnual);

    return {
      totalUsage: totalUsage,
      supplier: gasMeterData.data.supplierName
    };
  }

  throw new ClientFacingError("Invalid utility type");
}

export async function submitUsageInformation(
  input: UsageInput,
  additionalData: AdditionalInputData
) {
  log.info("[submitUsageInformation] Starting submission", {
    input,
    createdById: additionalData.createdById,
    userRole: additionalData.userRole
  });

  const companySite = await prisma.companySite.findFirst({
    where: {
      entityAddressId: input.siteAddressId,
      company: {
        registrationNumber: input.companyReg
      }
    },
    select: {
      id: true,
      siteRefId: true,
      company: {
        select: {
          id: true,
          businessType: true,
          name: true,
          registrationNumber: true,
          entityAddressId: true
        }
      },
      entityAddress: {
        select: {
          id: true,
          postcode: true
        }
      }
    }
  });

  if (!companySite) {
    log.error("[submitUsageInformation] Company site not found", {
      siteAddressId: input.siteAddressId,
      companyReg: input.companyReg
    });
    throw new Error("Company site not found");
  }

  log.info("[submitUsageInformation] Found company site", {
    companySiteId: companySite.id,
    companyId: companySite.company.id
  });

  const meterHasSignedContract =
    (await prisma.contract.count({
      where: {
        companyId: companySite.company.id,
        status: ContractStatus.SIGNED,
        siteMeter: {
          companySiteId: companySite.id,
          utilityType: input.utilityType,
          ...(input.utilityType === UtilityType.ELECTRICITY
            ? {
                electricSiteMeter: {
                  mpanValue: input.meterIdentifier
                }
              }
            : input.utilityType === UtilityType.GAS
              ? {
                  gasSiteMeter: {
                    mprnValue: input.meterIdentifier
                  }
                }
              : {})
        }
      }
    })) > 0;

  if (meterHasSignedContract) {
    throw new ClientFacingError("Existing signed contract found for meter");
  }

  log.info("[submitUsageInformation] Checking and adding meter to site", {
    meterIdentifier: input.meterIdentifier,
    utilityType: input.utilityType
  });

  await checkAndAddQuotedMeterToSite({
    companyId: companySite.company.id,
    companySiteId: companySite.id,
    siteRefId: companySite.siteRefId,
    companySiteEntityAddressId: companySite.entityAddress.id,
    meterIdentifier: input.meterIdentifier,
    utilityType: input.utilityType,
    ...additionalData
  });

  log.info("[submitUsageInformation] Successfully checked/added meter to site");

  const totalUsage =
    typeof input.totalUsage === "string"
      ? ((await getUsageForMeter(input.meterIdentifier, input.utilityType))
          .totalUsage ?? 0)
      : input.totalUsage;

  const payload = {
    ...input,
    totalUsage,
    manualConsumptionEntry: typeof input.totalUsage !== "string", // If totalUsage is a number, it's a manual consumption entry
    businessType: companySite.company.businessType,
    businessName: companySite.company.name,
    businessNumber: companySite.company.registrationNumber,
    businessAddressId: companySite.company.entityAddressId,
    siteAddressId: companySite.entityAddress.id,
    sitePostcode: companySite.entityAddress.postcode
  } satisfies Omit<PcwGetQuoteInput, "contact">;

  log.info("[submitUsageInformation] Calling getQuote with payload", {
    payload,
    createdById: additionalData.createdById,
    userRole: additionalData.userRole
  });

  const quoteListId = await getQuote(payload, additionalData);

  log.info("[submitUsageInformation] Successfully created quote", {
    quoteListId
  });

  after(async () => {
    // Get contact data to send email notification
    // TODO(Bidur): use user session to get the contact information
    const contactData = await prisma.companyContact.findUnique({
      where: {
        id: input.contactId
      },
      select: {
        id: true,
        emails: {
          where: {
            isPrimary: true
          },
          select: {
            email: true
          }
        }
      }
    });

    const contactEmail = contactData?.emails[0]?.email;

    if (!contactData || !contactEmail) {
      return;
    }

    const currentSupplier = await prisma.provider.findFirst({
      where: {
        udcoreId: input.currentSupplier
      },
      select: {
        displayName: true
      }
    });

    if (!currentSupplier) {
      return;
    }

    try {
      const emailPayload = await buildQuoteSignUpEmailPayload(
        contactData.id,
        contactEmail,
        companySite.company.id,
        companySite.entityAddress.id,
        input.utilityType,
        getEacBandStr(payload.totalUsage),
        currentSupplier.displayName
      );

      await sendReminderEmailNotification(
        emailPayload,
        additionalData.createdById
      );
    } catch (error) {
      log.error(`Failed to send quote sign up email to ${contactEmail}`, {
        error
      });
    }
  });

  return { quoteListId };
}
