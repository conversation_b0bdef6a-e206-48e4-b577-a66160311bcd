import type { QuoteDefinitions } from "../../types";

// Create a simple test for the filtering functionality
describe("Quote App Provider Filtering", () => {
  // Sample data for tests
  const mockQuoteDefinitions: QuoteDefinitions = {
    electric: [
      {
        Plans: [
          {
            Id: 1,
            Duration: 12,
            PlanType: null,
            StandingChargeType: null,
            IsRenewable: null,
            IncludeMOPCharges: null,
            HalfHourlyNumberOfRates: null,
            Pricebook: null
          }
        ],
        Supplier: "Provider1"
      },
      {
        Plans: [
          {
            Id: 2,
            Duration: 24,
            PlanType: null,
            StandingChargeType: null,
            IsRenewable: null,
            IncludeMOPCharges: null,
            HalfHourlyNumberOfRates: null,
            Pricebook: null
          }
        ],
        Supplier: "Provider2"
      },
      {
        Plans: [
          {
            Id: 3,
            Duration: 36,
            PlanType: null,
            StandingChargeType: null,
            IsRenewable: null,
            IncludeMOPCharges: null,
            HalfHourlyNumberOfRates: null,
            Pricebook: null
          }
        ],
        Supplier: "Provider3"
      }
    ],
    gas: [
      {
        Plans: [
          {
            Id: 4,
            Duration: 12,
            PlanType: null,
            StandingChargeType: null,
            IsRenewable: null,
            IncludeMOPCharges: null,
            HalfHourlyNumberOfRates: null,
            Pricebook: null
          }
        ],
        Supplier: "Provider1"
      },
      {
        Plans: [
          {
            Id: 5,
            Duration: 24,
            PlanType: null,
            StandingChargeType: null,
            IsRenewable: null,
            IncludeMOPCharges: null,
            HalfHourlyNumberOfRates: null,
            Pricebook: null
          }
        ],
        Supplier: "Provider2"
      }
    ]
  };

  // Mock the filterQuoteDefinitionsForQuoteApp function
  const filterQuoteDefinitionsForQuoteApp = (
    definitions: QuoteDefinitions,
    hiddenProviderIds: Set<string>
  ): QuoteDefinitions => {
    return {
      electric: definitions.electric.filter(
        def => !hiddenProviderIds.has(def.Supplier)
      ),
      gas: definitions.gas.filter(def => !hiddenProviderIds.has(def.Supplier))
    };
  };

  test("should filter out providers that are not visible in quote app", () => {
    // Providers not visible in quote app
    const hiddenProviderIds = new Set(["Provider2"]);

    const filteredDefinitions = filterQuoteDefinitionsForQuoteApp(
      mockQuoteDefinitions,
      hiddenProviderIds
    );

    // Expected result after filtering
    const expectedFilteredDefinitions = {
      electric: [
        mockQuoteDefinitions.electric[0],
        mockQuoteDefinitions.electric[2]
      ],
      gas: [mockQuoteDefinitions.gas[0]]
    };

    expect(filteredDefinitions).toEqual(expectedFilteredDefinitions);
  });

  test("should return all providers when none are hidden", () => {
    // No hidden providers
    const hiddenProviderIds = new Set([]);

    const filteredDefinitions = filterQuoteDefinitionsForQuoteApp(
      mockQuoteDefinitions,
      hiddenProviderIds
    );

    // Should return the original definitions unchanged
    expect(filteredDefinitions).toEqual(mockQuoteDefinitions);
  });

  test("should handle multiple hidden providers", () => {
    // Multiple providers not visible in quote app
    const hiddenProviderIds = new Set(["Provider1", "Provider3"]);

    const filteredDefinitions = filterQuoteDefinitionsForQuoteApp(
      mockQuoteDefinitions,
      hiddenProviderIds
    );

    // Expected result after filtering
    const expectedFilteredDefinitions = {
      electric: [mockQuoteDefinitions.electric[1]],
      gas: [mockQuoteDefinitions.gas[1]]
    };

    expect(filteredDefinitions).toEqual(expectedFilteredDefinitions);
  });

  test("should handle all providers hidden", () => {
    // All providers hidden
    const hiddenProviderIds = new Set(["Provider1", "Provider2", "Provider3"]);

    const filteredDefinitions = filterQuoteDefinitionsForQuoteApp(
      mockQuoteDefinitions,
      hiddenProviderIds
    );

    // Expected result after filtering - empty arrays
    const expectedFilteredDefinitions = {
      electric: [],
      gas: []
    };

    expect(filteredDefinitions).toEqual(expectedFilteredDefinitions);
  });
});
