import { UtilityType } from "@prisma/client";

interface MockQuoteProps {
  sitePostcode: string;
  upliftRate: number;
  currentSupplier: string;
  creditScore: number;
  utilityType: UtilityType;
}

interface MockResult {
  functionCalled: "getQuoteDefinitionsForQuoteApp" | "getQuoteDefinitions";
  isQuoteApp: boolean;
}

describe("Quote Generation with isQuoteApp Flag", () => {
  // Mock the composePayloadAndGetQuotesFromUdcore function
  const composePayloadAndGetQuotesFromUdcore = (
    _quotesProps: MockQuoteProps,
    options?: { isQuoteApp?: boolean }
  ): MockResult => {
    const isQuoteApp = options?.isQuoteApp ?? false;

    // Return which function would be called based on the isQuoteApp flag
    return {
      functionCalled: isQuoteApp
        ? "getQuoteDefinitionsForQuoteApp"
        : "getQuoteDefinitions",
      isQuoteApp
    };
  };

  test("should use getQuoteDefinitionsForQuoteApp when isQuoteApp is true", () => {
    const mockElectricQuoteProps = {
      sitePostcode: "AB12 3CD",
      upliftRate: 1.0,
      currentSupplier: "Provider1",
      creditScore: 100,
      utilityType: UtilityType.ELECTRICITY
    };

    const result = composePayloadAndGetQuotesFromUdcore(
      mockElectricQuoteProps,
      { isQuoteApp: true }
    );

    expect(result.functionCalled).toBe("getQuoteDefinitionsForQuoteApp");
    expect(result.isQuoteApp).toBe(true);
  });

  test("should use getQuoteDefinitions when isQuoteApp is false", () => {
    const mockElectricQuoteProps = {
      sitePostcode: "AB12 3CD",
      upliftRate: 1.0,
      currentSupplier: "Provider1",
      creditScore: 100,
      utilityType: UtilityType.ELECTRICITY
    };

    const result = composePayloadAndGetQuotesFromUdcore(
      mockElectricQuoteProps,
      { isQuoteApp: false }
    );

    expect(result.functionCalled).toBe("getQuoteDefinitions");
    expect(result.isQuoteApp).toBe(false);
  });

  test("should default to getQuoteDefinitions when options are not provided", () => {
    const mockElectricQuoteProps = {
      sitePostcode: "AB12 3CD",
      upliftRate: 1.0,
      currentSupplier: "Provider1",
      creditScore: 100,
      utilityType: UtilityType.ELECTRICITY
    };

    const result = composePayloadAndGetQuotesFromUdcore(mockElectricQuoteProps);

    expect(result.functionCalled).toBe("getQuoteDefinitions");
    expect(result.isQuoteApp).toBe(false);
  });
});
