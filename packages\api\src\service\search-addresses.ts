import { randomUUID } from "node:crypto";
import type { Prisma } from "@prisma/client";
import { ONE_DAY_SEC } from "@watt/common/src/constants/time-durations";
import { MPANSchema } from "@watt/common/src/mpan/mpan";
import { parseShortMpan } from "@watt/common/src/mpan/mpan";
import { MPRNSchema } from "@watt/common/src/mprn/mprn";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  formatPostcode,
  postcodeSchema
} from "@watt/common/src/utils/format-postcode";
import { prisma } from "@watt/db/src/client";
import {
  type ApertureCombinedAddressSearchSuggestion,
  getApertureAddresses
} from "@watt/external-apis/src/libs/aperture/search-meter-addresses";
import { cacheWrap } from "@watt/redis/src/cache";
import { redis } from "@watt/redis/src/client";
import { z } from "zod";
import {
  type EntityAddressWithMeterData,
  compareEntityAddressesWithMeterData,
  entityAddressQueryOptions,
  formatEntityAddresses,
  internalExtension
} from "./entityAddress";

export const AddressLookupError = {
  BAD_QUERY: "BAD_QUERY",
  BAD_POSTCODE: "BAD_POSTCODE",
  BAD_MPAN: "BAD_MPAN",
  BAD_MPRN: "BAD_MPRN",
  DATA_SOURCE_FAILED: "DATA_SOURCE_FAILED",
  NONE_FOUND: "NONE_FOUND",
  TOO_MANY_MATCHES: "TOO_MANY_MATCHES"
} as const;

/**
 * Convert a combined Aperture suggestion into the public DTO.
 * @param addressSuggestion - The combined Aperture suggestion.
 * @returns The public DTO.
 */
export function toPublicAddressDto(
  addressSuggestion: Readonly<ApertureCombinedAddressSearchSuggestion>
): EntityAddressWithMeterData {
  const id = randomUUID();

  // Store the Global Address Keys by id for 24 hours
  void redis.set(`gak_by_id:${id}`, JSON.stringify(addressSuggestion), {
    ex: ONE_DAY_SEC
  });

  return {
    id,
    displayName: addressSuggestion.text,
    postcode: addressSuggestion.postcode,
    siteMeters: { 1: 0, 2: 0 },
    meterPoints: { 1: 0, 2: 0 },
    address: addressSuggestion.text,
    meterNumbers: undefined
  };
}

const PostcodeInputSchema = z.object({
  postcode: postcodeSchema
});

const MpanInputSchema = z.object({
  mpan: MPANSchema
});

const MprnInputSchema = z.object({
  mprn: MPRNSchema
});

const IdInputSchema = z.object({
  id: z.string().uuid()
});

export const AddressesInputSchema = z.union([
  PostcodeInputSchema,
  MprnInputSchema,
  MpanInputSchema,
  IdInputSchema
]);

export type AddressesInput = z.infer<typeof AddressesInputSchema>;

function getQueryOptions(input: AddressesInput, take?: number) {
  if ("postcode" in input) {
    const postcode = formatPostcode(input.postcode);
    if (!postcode) {
      throw new Error(AddressLookupError.BAD_POSTCODE);
    }
    return {
      query: postcode,
      key: "postcode"
    };
  }

  if ("mprn" in input) {
    const parseResult = MPRNSchema.safeParse(input.mprn);
    if (!parseResult.success) {
      throw new Error(AddressLookupError.BAD_MPRN);
    }
    return {
      query: input.mprn,
      key: "mprn"
    };
  }

  if ("mpan" in input) {
    const parseResult = parseShortMpan(input.mpan);
    if (parseResult.error) {
      log.warn("getQueryOptions: Invalid short MPAN provided", {
        mpan: input.mpan,
        error: parseResult.error
      });
      throw new Error(AddressLookupError.BAD_MPAN);
    }
    return {
      query: input.mpan,
      key: "mpan"
    };
  }

  return {
    query: input.id,
    key: "id"
  };
}

async function findManyAddressesWhere({
  input,
  take,
  hideAddressWithCreatedById
}: {
  input: AddressesInput;
  take?: number;
  hideAddressWithCreatedById?: boolean;
}): Promise<{
  addresses: EntityAddressWithMeterData[];
  countItemsNotFromAperture: number;
}> {
  console.log("[findManyAddressesWhere] input", input);
  try {
    function buildAddressWhereClause(
      input: AddressesInput
    ): Prisma.EntityAddressWhereInput {
      const { query, key } = getQueryOptions(input);
      if (!query) {
        throw new Error(AddressLookupError.BAD_QUERY);
      }

      if (key === "postcode") {
        if (!query) {
          throw new Error(AddressLookupError.BAD_POSTCODE);
        }

        const postcodeWithoutSpaces = query.replace(/\s+/g, "");
        return {
          OR: [{ postcode: query }, { postcode: postcodeWithoutSpaces }]
        };
      }

      if (key === "mprn") {
        return {
          mprns: {
            some: { value: query }
          }
        };
      }

      if (key === "mpan") {
        return {
          mpans: {
            some: { value: query }
          }
        };
      }

      return {
        id: query
      };
    }

    const addresses = await prisma.entityAddress.findMany({
      select: entityAddressQueryOptions,
      take,
      where: {
        ...buildAddressWhereClause(input),
        ...(hideAddressWithCreatedById && { createdById: null })
      }
    });

    if (!addresses || addresses.length === 0) {
      return {
        addresses: [],
        countItemsNotFromAperture: 0
      };
    }

    const addressesWithUtilities = formatEntityAddresses(addresses, {
      transformationStrategy: internalExtension
    });

    return {
      addresses: addressesWithUtilities,
      countItemsNotFromAperture: addresses.filter(
        address => address.apertureOrigin === false
      ).length
    };
  } catch (error) {
    log.error("service.search-addresses.findManyAddressesWhere: ", { error });
    throw new Error("Internal server error");
  }
}

/**
 * Federation entry-point to search addresses by postcode.
 * @param input - The postcode to search for.
 * @param take - The number of addresses to return.
 * @param hideAddressWithCreatedById - Whether to hide the address created by user.
 * @returns The addresses found in the DB and Aperture or throws typed errors for HTTP mapping.
 */
export async function searchAddressesWhere({
  input,
  take,
  hideAddressWithCreatedById
}: {
  input: AddressesInput;
  take?: number;
  hideAddressWithCreatedById?: boolean;
}): Promise<readonly EntityAddressWithMeterData[]> {
  const { query, key } = getQueryOptions(input);

  //
  if (!query) {
    throw AddressLookupError.BAD_QUERY;
  }

  console.log("[searchAddressesWhere] input", input);

  const addressesByPostcode = await cacheWrap(
    `search-addresses-by-${key}:${query.replaceAll(" ", "_")}`,
    async () => {
      const [dbRes, apiRes] = await Promise.allSettled([
        findManyAddressesWhere({ input, take, hideAddressWithCreatedById }),
        getApertureAddresses(query, key)
      ]);

      if (dbRes.status === "rejected" && apiRes.status === "rejected") {
        throw AddressLookupError.DATA_SOURCE_FAILED;
      }

      const dbData =
        dbRes.status === "fulfilled"
          ? dbRes.value
          : { addresses: [], countItemsNotFromAperture: 0 };

      console.log(
        "[searchAddressesWhere] dbData",
        JSON.stringify(dbData, null, 2)
      );
      console.log(
        "[searchAddressesWhere] apiRes",
        JSON.stringify(apiRes, null, 2)
      );

      if (key === "id") {
        // If we're finding an address by it's 'id' then it can only be found in the db.
        return dbData.addresses.sort(compareEntityAddressesWithMeterData);
      }

      // If the list of addresses from the db by postcode contains addresses that are not from Aperture,
      // then we must show the databases version of the address list
      // as we do not have a way to merge the two lists accurately.
      //
      // Conversely if all db addresses by postcode are from Aperture,
      // then we can show the Aperture addresses as the database results
      // are likely only a subset of the Aperture results.

      // List of addresses from the db contains addresses that are NOT from Aperture
      if (dbData.addresses.length > 0 && dbData.countItemsNotFromAperture > 0) {
        return dbData.addresses.sort(compareEntityAddressesWithMeterData);
      }

      const apiData =
        apiRes.status === "fulfilled" ? apiRes.value : ([] as const);

      const apertureAddressesPublicDto = apiData.map(toPublicAddressDto);

      // List of addresses from the db has ALL the addresses from Aperture
      if (
        dbData.addresses.length > 0 &&
        dbData.countItemsNotFromAperture === 0
      ) {
        const aperturePublicDtoWithoutDbAddresses =
          apertureAddressesPublicDto.filter(
            apertureAddress =>
              !dbData.addresses.some(
                dbAddress =>
                  dbAddress.displayName === apertureAddress.displayName
              )
          );

        const mergedAddresses = [
          ...aperturePublicDtoWithoutDbAddresses,
          ...dbData.addresses
        ];

        return mergedAddresses.sort(compareEntityAddressesWithMeterData);
      }

      if (apiData.length > 0) {
        return apiData.map(toPublicAddressDto);
      }

      throw AddressLookupError.NONE_FOUND;
    },
    ONE_DAY_SEC
  );

  return take ? addressesByPostcode.slice(0, take) : addressesByPostcode;
}
