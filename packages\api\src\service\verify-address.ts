import { randomUUID } from "node:crypto";
import { ONE_DAY_SEC } from "@watt/common/src/constants/time-durations";
import { log } from "@watt/common/src/utils/axiom-logger";
import { formatPostcode } from "@watt/common/src/utils/format-postcode";
import { generateConsistentHash } from "@watt/common/src/utils/generate-consistent-hash";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { searchApertureAddresses } from "@watt/external-apis/src/libs/aperture/search-meter-addresses";
import { cacheWrap } from "@watt/redis/src/cache";
import { redis } from "@watt/redis/src/client";
import { z } from "zod";
import type { EntityAddressWithMeterData } from "./entityAddress";
import { AddressLookupError, toPublicAddressDto } from "./search-addresses";

export const VerifyAddressPayloadSchema = z.object({
  addressLine1: z.string().min(1, "Address Line 1 is required"),
  addressLine2: z.string().optional(),
  postalTown: z.string().optional(), // Not sure if this is missing sometimes.
  county: z.string().optional(),
  postcode: z
    .string()
    .min(5, "Postcode is required and must be at least 5 characters")
});

export type VerifyAddressPayload = z.infer<typeof VerifyAddressPayloadSchema>;

/**
 * Verifies a full address string against the Aperture API using caching.
 * Assumes underlying searchApertureAddresses function can handle a full address string.
 * Maps results using toPublicAddressDto for consistency and GAK storage.
 * @param {string} address - The full address string to verify (e.g., "10 Downing Street, Westminster, London, SW1A 2AA").
 * @returns {Promise<readonly EntityAddressWithMeterData[]>} A promise resolving to an array of matching addresses formatted
 * as EntityAddressWithMeterData. Returns an empty array if the input address is invalid or if no matches are found via Aperture.
 * @throws {AddressLookupError.DATA_SOURCE_FAILED} If the call to the underlying data source (Aperture) fails.
 */
export async function verifyAddress(
  addressData: VerifyAddressPayload
): Promise<readonly EntityAddressWithMeterData[]> {
  const formattedPostcode = formatPostcode(addressData.postcode);

  if (!formattedPostcode) {
    log.warn("verifyAddress: Received invalid postcode.", {
      postcode: addressData.postcode
    });
    return [];
  }

  const addressParts = [
    addressData.addressLine1,
    addressData.addressLine2,
    addressData.postalTown,
    addressData.county,
    formattedPostcode
  ];

  const validAddressParts = addressParts.filter(
    part => typeof part === "string" && part.trim() !== ""
  );
  const concatenatedAddress = validAddressParts.join(", ");

  if (
    !concatenatedAddress ||
    typeof concatenatedAddress !== "string" ||
    concatenatedAddress.trim().length === 0
  ) {
    log.warn("verifyAddress: Received empty or invalid address string.", {
      address: concatenatedAddress
    });
    return [];
  }

  const formattedAddressDisplayName = getAddressDisplayName({
    displayName: concatenatedAddress,
    postcode: formattedPostcode
  });

  const cacheKey = generateConsistentHash(formattedAddressDisplayName);

  const verifiedAddresses = await cacheWrap(
    `verify-address:${cacheKey}`,
    async () => {
      try {
        const apiResult = await searchApertureAddresses(
          formattedAddressDisplayName
        );

        const additionalAttributes = Object.entries(addressData)
          .filter(([key]) => key !== "postcode")
          .map(([name, value]) => ({ name, value: String(value) }));

        const score = apiResult[0]?.additionalAttributes.find(
          attr => attr.name === "score"
        )?.value;

        if (
          apiResult.length === 0 ||
          !(apiResult.length === 1 && score && score === "100")
        ) {
          const manual = toPublicAddressDto({
            id: randomUUID(),
            text: formattedAddressDisplayName,
            postcode: formattedPostcode,
            electricGlobalAddressKey: undefined,
            gasGlobalAddressKey: undefined,
            additionalAttributes
          });

          const cachableApertureStyleAddress = {
            id: manual.id,
            text: manual.displayName,
            postcode: manual.postcode,
            electricGlobalAddressKey: null,
            gasGlobalAddressKey: null,
            additionalAttributes
          };

          await redis.set(
            `gak_by_guid:${manual.id}`,
            cachableApertureStyleAddress
          );

          return [manual];
        }

        const formattedResults = apiResult.map(toPublicAddressDto);
        return formattedResults;
      } catch (error: unknown) {
        const isTooManyMatchesCase =
          error === AddressLookupError.TOO_MANY_MATCHES ||
          (error instanceof Error && error.message === "TOO_MANY_MATCHES");

        if (isTooManyMatchesCase) {
          throw AddressLookupError.TOO_MANY_MATCHES;
        }

        log.error(
          `verifyAddress: Unexpected error processing for cache key ${cacheKey}`,
          { address: formattedAddressDisplayName, error, cacheKey }
        );
        throw AddressLookupError.DATA_SOURCE_FAILED;
      }
    },
    ONE_DAY_SEC
  );

  return verifiedAddresses;
}
