import { TRPCError } from "@trpc/server";
import { fetchRequestHandler } from "@trpc/server/adapters/fetch";
import { log } from "@watt/common/src/utils/axiom-logger";
import { withAxiom } from "next-axiom";
import { appRouter } from "./routes";
import { createTRPCContext } from "./trpc";

/**
 * Configure basic CORS headers
 * You should extend this to match your needs
 */
function setCorsHeaders(res: Response) {
  res.headers.set("Access-Control-Allow-Origin", "*");
  res.headers.set("Access-Control-Request-Method", "*");
  res.headers.set("Access-Control-Allow-Methods", "OPTIONS, GET, POST");
  res.headers.set("Access-Control-Allow-Headers", "*");
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export function OPTIONS() {
  const response = new Response(null, {
    status: 204
  });
  setCorsHeaders(response);
  return response;
}

/**
 * Main TRPC handler for GET and POST requests
 */
const handler = withAxiom(async (req: Request) => {
  try {
    const response = await fetchRequestHandler({
      endpoint: "/api/trpc",
      router: appRouter,
      req,
      createContext: () => createTRPCContext({ headers: req.headers }),
      onError({ error, type, path, input }) {
        const isUserError =
          error instanceof TRPCError &&
          ["BAD_REQUEST", "UNAUTHORIZED", "FORBIDDEN", "NOT_FOUND"].includes(
            error.code
          );

        const logData = {
          message: error.message,
          code:
            error instanceof TRPCError ? error.code : "INTERNAL_SERVER_ERROR",
          type,
          path,
          ...(isUserError && { input }), // Only include input for user errors
          ...(!isUserError && { stack: error.stack }) // Only include stack for server errors
        };

        if (isUserError) {
          log.info("TRPC User Error", logData);
        } else {
          log.error("TRPC Server Error", logData);
        }
      }
    });
    setCorsHeaders(response);
    return response;
  } catch (e) {
    // Check if it's a SuperJSON serialization error
    if (e instanceof Error && e.message.includes("constructor")) {
      log.warn("SuperJSON serialization warning", {
        message: "Object contains 'constructor' property",
        errorMessage: e.message
      });
      // Return a clean error response
      return new Response(
        JSON.stringify({
          error: {
            message: "Response serialization error",
            code: "INTERNAL_SERVER_ERROR"
          }
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    log.error("Unhandled error in fetchRequestHandler", {
      message: e instanceof Error ? e.message : "Unknown error",
      stack: e instanceof Error ? e.stack : undefined
    });
    return new Response("Internal Server Error", { status: 500 });
  }
});

export { handler as GET, handler as POST };
