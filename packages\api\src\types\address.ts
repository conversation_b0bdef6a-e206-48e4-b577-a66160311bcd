import { MPANRegexShortSchema } from "@watt/common/src/mpan/mpan";
import { MPRNSchema } from "@watt/common/src/mprn/mprn";
import { UtilityType } from "@watt/db/src/enums";
import { z } from "zod";

export const CreateOrLinkMetersFormSchema = z.object({
  addressId: z.string().min(1, "Address id is required"),
  companyReg: z.string().optional(),
  mpanNumbers: z.array(MPANRegexShortSchema).optional(),
  mprnNumbers: z.array(MPRNSchema).optional()
});

export const GetMeterNumbersByAddressAndUtilityFormSchema = z.object({
  addressId: z.string(),
  utilityType: z.nativeEnum(UtilityType)
});

export type CreateOrLinkMeters = z.infer<typeof CreateOrLinkMetersFormSchema>;
export type GetMeterNumbersByAddressAndUtility = z.infer<
  typeof GetMeterNumbersByAddressAndUtilityFormSchema
>;
