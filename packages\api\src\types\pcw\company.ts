import { BusinessType } from "@prisma/client";
import { isValidBusinessReference } from "@watt/common/src/regex/company-number";
import { dateFormats } from "@watt/common/src/utils/format-date";
import { isAddressLessThanThreeYears } from "@watt/common/src/utils/is-address-less-than-three-years";
import { differenceInYears, parse } from "date-fns";
import { z } from "zod";
import { PcwQuoteContactSchema } from "./quote";

export const CompanySchema = z.object({
  businessNumber: z.string().refine(isValidBusinessReference, {
    message: "Please enter a valid business reference."
  }),
  businessName: z.string().min(1, "Please enter a valid business name"),
  businessType: z.nativeEnum(BusinessType),
  businessAddressId: z.string().min(1, "Please enter a valid business address")
});

export const ContactSchema = z.object({
  forename: z.string().min(1, "Please enter a valid forename"),
  surname: z.string().min(1, "Please enter a valid surname"),
  email: z.string().email(),
  phoneNumber: z.string().min(1, "Please enter a valid phone number"),
  position: z.string().min(1, "Please enter a valid position")
});

export const AgreementSchema = z.object({
  isAuthorized: z.boolean().refine(val => val === true, {
    message: "Required"
  }),
  letterOfAuthority: z.boolean().refine(val => val === true, {
    message: "Required"
  }),
  termsAndConditions: z.boolean().refine(val => val === true, {
    message: "Required"
  })
});

export const CreateCompanyAndContactInputSchema = z
  .object({
    company: CompanySchema,
    contact: PcwQuoteContactSchema,
    agreements: AgreementSchema
  })
  .superRefine((data, ctx) => {
    const { company, contact } = data;
    const parsedContact = PcwQuoteContactSchema.safeParse(contact);

    if (parsedContact.error) {
      return;
    }

    const { dateOfBirth, addresses } = parsedContact.data;
    const isSoleTrader = company.businessType === BusinessType.SOLE_TRADER;

    if (isSoleTrader) {
      if (!dateOfBirth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Required for sole trader",
          path: ["contact", "dateOfBirth"]
        });
      } else {
        const dob = parse(dateOfBirth, dateFormats.DD_MM_YYYY, new Date());
        const age = differenceInYears(new Date(), dob);

        if (age < 16) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message:
              "Must be at least 16 years old to register as a sole trader",
            path: ["contact", "dateOfBirth"]
          });
        }
      }

      if (!addresses.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Provide 3 years' history or 2+ addresses",
          path: ["contact", "addresses"]
        });
      } else {
        const hasCurrentAddress = addresses.some(address => address.isCurrent);

        if (!hasCurrentAddress) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Provide a current address",
            path: ["contact", "addresses"]
          });
        }

        const firstAddress = addresses[0];
        if (!firstAddress) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Address array is empty",
            path: ["contact", "addresses"]
          });
          return;
        }

        const isGivenAddressLessThanThreeYears = isAddressLessThanThreeYears(
          firstAddress.movedInDate,
          firstAddress.movedOutDate
        );

        const hasMultipleAddresses = addresses.length > 1;

        // If only one address is provided, it must be 3+ years old otherwise we need 2+ addresses
        if (!hasMultipleAddresses && isGivenAddressLessThanThreeYears) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Provide 3 years' history or 2+ addresses",
            path: ["contact", "addresses"]
          });
        }
      }
    }
  });

export type CreateCompanyAndContactInput = z.infer<
  typeof CreateCompanyAndContactInputSchema
>;
