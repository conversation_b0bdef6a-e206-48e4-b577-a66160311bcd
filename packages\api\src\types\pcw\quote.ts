import { BusinessType, UtilityType } from "@prisma/client";
import { MPANSchema } from "@watt/common/src/mpan/mpan";
import { MPRNSchema } from "@watt/common/src/mprn/mprn";
import { isValidBusinessReference } from "@watt/common/src/regex/company-number";
import { formatPostcode } from "@watt/common/src/utils/format-postcode";
import { z } from "zod";
import {
  ContactAddressSchema,
  createUkPhoneNumberSchema,
  dobDateSchema
} from "../people";

export const PcwQuoteContactSchema = z.object({
  forename: z.string().min(1, "Required"),
  surname: z.string().min(1, "Required"),
  email: z.string().trim().min(1, "Required").email("Invalid email format"),
  phoneNumber: createUkPhoneNumberSchema(
    "Invalid phone number. Allowed formats: ************ or 020 7946 0958"
  ).pipe(z.string().min(1, "Required")),
  dateOfBirth: dobDateSchema(),
  position: z.string().min(1, "Required"),
  addresses: z.array(ContactAddressSchema)
});

export const PcwGetQuoteInputSchema = z.object({
  utilityType: z.nativeEnum(UtilityType),
  businessNumber: z.string().refine(isValidBusinessReference, {
    message: "Please enter a valid business reference."
  }),
  businessName: z.string().min(1, "Please enter a valid business name"),
  businessType: z.nativeEnum(BusinessType),
  meterIdentifier: z.string().min(1, "Please enter a valid meter identifier"),
  businessAddressId: z.string().min(1, "Please enter a valid business address"),
  siteAddressId: z.string().min(1, "Please enter a valid site address"),
  sitePostcode: z
    .string()
    .trim()
    .transform(pc => (pc ? (formatPostcode(pc) ?? undefined) : pc)),
  currentSupplier: z.string().min(1, "Please select a current supplier"),
  contractStartDate: z
    .string()
    .min(1, "Please enter a valid contract start date"),
  manualConsumptionEntry: z.boolean().optional(),
  totalUsage: z.coerce
    .number()
    .min(0, "Please enter a valid usage")
    .default(-1),
  contact: PcwQuoteContactSchema
});

export const PcwUpdateQuoteInputSchema = z.object({
  quoteListId: z.string(),
  siteMeterId: z.string(),
  contactId: z.string(),
  meterIdentifier: z.union([MPANSchema, MPRNSchema]),
  contractStartDate: z.string().refine(value => {
    const startDate = new Date(value);
    const todayAtMidnight = new Date(new Date().toDateString());
    return startDate >= todayAtMidnight;
  }, "Start date must be today or a future date"),
  totalUsage: z.coerce.number().min(1, "Please enter a valid usage")
});

export const PcwSignUpQuoteInputSchema = z.object({
  quoteId: z.string(),
  contactId: z.string()
});

export type PcwGetQuoteInput = z.infer<typeof PcwGetQuoteInputSchema>;
export type PcwQuoteContact = z.infer<typeof PcwQuoteContactSchema>;
export type PcwUpdateQuoteInput = z.infer<typeof PcwUpdateQuoteInputSchema>;
export type PcwSignUpQuoteInput = z.infer<typeof PcwSignUpQuoteInputSchema>;
