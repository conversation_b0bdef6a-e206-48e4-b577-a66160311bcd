import { UtilityType } from "@prisma/client";
import { formatPostcode } from "@watt/common/src/utils/format-postcode";
import { z } from "zod";

export const SiteInputSchema = z.object({
  companyId: z.string().min(1, "Please enter a valid company id"),
  contactId: z.string().min(1, "Please enter a valid contact id"),
  siteAddressId: z.string().min(1, "Please enter a valid site address"),
  sitePostcode: z
    .string()
    .trim()
    .transform(pc => (pc ? (formatPostcode(pc) ?? undefined) : pc)),
  utilityType: z.nativeEnum(UtilityType)
});

export type SiteInput = z.infer<typeof SiteInputSchema>;
