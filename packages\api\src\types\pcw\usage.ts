import { UtilityType } from "@prisma/client";
import { isShortMpan } from "@watt/common/src/mpan/mpan";
import { isValidMPRN } from "@watt/common/src/mprn/mprn";
import { z } from "zod";

const totalUsageSchema = z.union([
  z.coerce.number().min(0, "Please enter a valid usage").default(-1),
  z
    .string()
    .regex(/^\d+\+$/, "Please enter a valid usage")
    .or(z.string().regex(/^\d+-\d+$/, "Please enter a valid usage"))
]);

export const UsageInputSchema = z
  .object({
    companyReg: z
      .string()
      .min(1, "Please enter a valid company registration number"),
    siteAddressId: z.string().min(1, "Please enter a valid site address id"),
    contactId: z.string().min(1, "Please enter a valid contact id"),
    utilityType: z.nativeEnum(UtilityType),
    useCustomMeter: z.boolean(),
    customMeterIdentifier: z.string().trim().nullable(),
    meterIdentifier: z
      .string()
      .trim()
      .min(1, "Please enter a valid meter identifier"),
    contractStartDate: z
      .string()
      .min(1, "Please enter a valid contract start date"),
    currentSupplier: z.string().min(1, "Please select a current supplier"),
    totalUsage: totalUsageSchema
  })
  .superRefine((data, ctx) => {
    const { utilityType, meterIdentifier } = data;

    if (utilityType === UtilityType.ELECTRICITY) {
      if (!isShortMpan(meterIdentifier)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid MPAN format",
          path: ["meterIdentifier"]
        });
      }
    }

    if (utilityType === UtilityType.GAS) {
      if (!isValidMPRN(meterIdentifier)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid MPRN format",
          path: ["meterIdentifier"]
        });
      }
    }
  });

export type UsageInput = z.infer<typeof UsageInputSchema>;
