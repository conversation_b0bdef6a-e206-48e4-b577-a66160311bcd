import { getEacBand, getEacBandStr } from "./banded-annual-usage";

type BandTestCase = {
  description: string;
  input: number;
  expected: { lowerBound: number; upperBound: number | null };
};

type BandStrTestCase = {
  description: string;
  input: number;
  expected: string;
};

describe("getEacBand", () => {
  const testCases: BandTestCase[] = [
    {
      description: "Returns first band for value 4000",
      input: 4000,
      expected: { lowerBound: 0, upperBound: 5000 }
    },
    {
      description: "Returns second band for value 5000",
      input: 5000,
      expected: { lowerBound: 0, upperBound: 5000 }
    },
    {
      description: "Returns next band for value 7500",
      input: 7500,
      expected: { lowerBound: 5000, upperBound: 10000 }
    },
    {
      description: "Returns final band for value 20000",
      input: 20000,
      expected: { lowerBound: 15000, upperBound: 20000 }
    },
    {
      description: "Returns overflow band for value 25000",
      input: 25000,
      expected: { lowerBound: 20000, upperBound: null }
    }
  ];

  test.each(testCases)("$description", ({ input, expected }) => {
    expect(getEacBand(input)).toEqual(expected);
  });
});

describe("getEacBandStr", () => {
  const testCases: BandStrTestCase[] = [
    {
      description: "Formats valid range correctly",
      input: 10000,
      expected: "5000-10000"
    },
    {
      description: "Formats overflow correctly",
      input: 999999,
      expected: "20000+"
    }
  ];

  test.each(testCases)("$description", ({ input, expected }) => {
    expect(getEacBandStr(input)).toBe(expected);
  });
});
