const EAC_BAND_UPPER_BOUNDS = [5000, 10000, 15000, 20000];

type EacBand = {
  lowerBound: number;
  upperBound: number | null;
};

export function getEacBand(eac: number): EacBand {
  let lowerBound = 0;

  for (const upperBound of EAC_BAND_UPPER_BOUNDS) {
    if (eac <= upperBound) {
      return { lowerBound, upperBound };
    }
    lowerBound = upperBound;
  }

  const lastBound = EAC_BAND_UPPER_BOUNDS[EAC_BAND_UPPER_BOUNDS.length - 1];
  if (lastBound === undefined) {
    throw new Error("EAC_BAND_UPPER_BOUNDS is empty");
  }

  return {
    lowerBound: lastBound,
    upperBound: null
  };
}

export function getEacBandStr(eac: number): string {
  const { lowerBound, upperBound } = getEacBand(eac);

  return upperBound === null ? `${lowerBound}+` : `${lowerBound}-${upperBound}`;
}
