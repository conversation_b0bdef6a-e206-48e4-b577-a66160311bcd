import { getClientIpAddress } from "./get-client-ip-address";

type IpTestCase = {
  description: string;
  headers: Headers;
  remoteAddr?: string;
  expected: string | null;
};

describe("getClientIpAddress", () => {
  const createHeaders = (entries: Record<string, string>): Headers => {
    return new Headers(entries);
  };

  const testCases: IpTestCase[] = [
    // Header priority tests
    {
      description: "Returns first IP from x-forwarded-for",
      headers: createHeaders({
        "x-forwarded-for": "***********, ********",
        "true-client-ip": "********",
        "cf-connecting-ip": "********",
        "x-real-ip": "********"
      }),
      expected: "***********"
    },
    {
      description: "Returns true-client-ip when x-forwarded-for is invalid",
      headers: createHeaders({
        "x-forwarded-for": "invalid-ip",
        "true-client-ip": "***********"
      }),
      expected: "***********"
    },
    {
      description:
        "Returns cf-connecting-ip when higher priority headers are missing",
      headers: createHeaders({
        "cf-connecting-ip": "***********"
      }),
      expected: "***********"
    },
    {
      description: "Returns x-real-ip when higher priority headers are missing",
      headers: createHeaders({
        "x-real-ip": "***********"
      }),
      expected: "***********"
    },

    // IPv6 handling
    {
      description: "Converts IPv6 localhost to IPv4 localhost in headers",
      headers: createHeaders({
        "x-forwarded-for": "::1"
      }),
      expected: "127.0.0.1"
    },
    {
      description: "Converts IPv6 localhost to IPv4 localhost in remoteAddr",
      headers: createHeaders({}),
      remoteAddr: "::1",
      expected: "127.0.0.1"
    },
    {
      description: "Handles valid IPv6 address",
      headers: createHeaders({
        "x-forwarded-for": "2001:db8:85a3:8d3:1319:8a2e:370:7348"
      }),
      expected: "2001:db8:85a3:8d3:1319:8a2e:370:7348"
    },

    // RemoteAddr fallback
    {
      description: "Uses remoteAddr when headers have no valid IP",
      headers: createHeaders({}),
      remoteAddr: "***********",
      expected: "***********"
    },
    {
      description: "Prefers valid header IP over remoteAddr",
      headers: createHeaders({
        "x-forwarded-for": "***********"
      }),
      remoteAddr: "***********",
      expected: "***********"
    },

    // Edge cases
    {
      description: "Returns null when no valid IPs are found",
      headers: createHeaders({}),
      expected: null
    },
    {
      description: "Returns null for invalid remoteAddr",
      headers: createHeaders({}),
      remoteAddr: "invalid-ip",
      expected: null
    },
    {
      description: "Handles whitespace in IPs",
      headers: createHeaders({
        "x-forwarded-for": "  ***********  , ********"
      }),
      expected: "***********"
    }
  ];

  test.each(testCases)("$description", ({ headers, remoteAddr, expected }) => {
    expect(getClientIpAddress(headers, remoteAddr)).toBe(expected);
  });

  // Error handling tests
  describe("error handling", () => {
    test("handles null headers gracefully", () => {
      // @ts-expect-error Testing null headers
      expect(getClientIpAddress(null)).toBe(null);
    });

    test("handles undefined headers gracefully", () => {
      // @ts-expect-error Testing undefined headers
      expect(getClientIpAddress(undefined)).toBe(null);
    });

    test("handles invalid header type gracefully", () => {
      // @ts-expect-error Testing invalid header type
      expect(getClientIpAddress("not headers")).toBe(null);
    });

    test("handles invalid remoteAddr type gracefully", () => {
      // @ts-expect-error Testing invalid remoteAddr type
      expect(getClientIpAddress(new Headers(), {})).toBe(null);
    });
  });
});
