import { isIP } from "node:net";
import { log } from "@watt/common/src/utils/axiom-logger";

// Main function to get client IP, converting IPv6 localhost to IPv4 localhost
export function getClientIpAddress(
  headers: Headers,
  remoteAddr?: string
): string | null {
  const ip = getRawClientIp(headers, remoteAddr);

  if (ip === "::1") {
    return "127.0.0.1";
  }

  return ip;
}

// Validates if a string is a valid IPv4 or IPv6 address
function isValidIp(ip: string): boolean {
  return isIP(ip) !== 0; // Returns 4 for IPv4, 6 for IPv6, 0 for invalid
}

// Extracts IP from headers, checking common proxy headers in order
function getClientIpAddressImpl(headers: Headers): string | null {
  try {
    // 1. Check x-forwarded-for (proxy chains)
    const forwardedFor = headers.get("x-forwarded-for");
    if (forwardedFor) {
      const ips = forwardedFor.split(",").map(ip => ip.trim());
      if (ips[0] && isValidIp(ips[0])) {
        return ips[0]; // Return the leftmost (client) IP
      }
    }

    // 2. Check true-client-ip (e.g., Akamai)
    const trueClientIp = headers.get("true-client-ip")?.trim();
    if (trueClientIp && isValidIp(trueClientIp)) {
      return trueClientIp;
    }

    // 3. Check cf-connecting-ip (Cloudflare)
    const cfIp = headers.get("cf-connecting-ip")?.trim();
    if (cfIp && isValidIp(cfIp)) {
      return cfIp;
    }

    // 4. Check x-real-ip (e.g., Nginx)
    const realIp = headers.get("x-real-ip")?.trim();
    if (realIp && isValidIp(realIp)) {
      return realIp;
    }

    return null; // No valid IP found in headers
  } catch (error) {
    log.error("Error in IP address extraction:", { error });
    return null;
  }
}

// Gets raw client IP from headers or remote address
function getRawClientIp(headers: Headers, remoteAddr?: string): string | null {
  try {
    // First, try to get IP from headers
    const ipAddress = getClientIpAddressImpl(headers);
    if (ipAddress) {
      return ipAddress;
    }

    // Fallback to remoteAddr if provided and valid
    if (remoteAddr && isValidIp(remoteAddr)) {
      return remoteAddr;
    }

    return null; // No valid IP found
  } catch (error) {
    log.error("Error getting client IP address:", { error });
    return null;
  }
}
