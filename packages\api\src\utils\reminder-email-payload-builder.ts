import { NotificationType } from "@prisma/client";
import type { UtilityType } from "@watt/db/src/enums";
import { NOTIFICATION_TAGS } from "@watt/notifications/src/config";
import type {
  CurrentSupplierInformationEmailPayload,
  QuoteSignUpEmailPayload,
  SignContractEmailPayload
} from "@watt/notifications/src/schemas/email";

type ReminderEmailType =
  | typeof NotificationType.CURRENT_SUPPLIER_INFORMATION_EMAIL
  | typeof NotificationType.QUOTE_SIGNUP_EMAIL
  | typeof NotificationType.SIGN_CONTRACT_EMAIL;

export class ReminderEmailPayloadBuilder {
  // TODO(Bidur): remove the key any and possible construct a discriminated union type
  private payload: {
    createdAt: string;
    tag: (typeof NOTIFICATION_TAGS)[keyof typeof NOTIFICATION_TAGS];
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    [key: string]: any;
  };

  constructor() {
    this.payload = {
      createdAt: new Date().toISOString(),
      tag: NOTIFICATION_TAGS.REMINDER_EMAILS
    };
  }

  addType(type: ReminderEmailType): ReminderEmailPayloadBuilder {
    this.payload.type = type;
    return this;
  }

  addRecipient(
    contactId: string,
    contactEmail: string
  ): ReminderEmailPayloadBuilder {
    this.payload.recipientId = contactId;
    this.payload.recipientEmail = contactEmail;
    return this;
  }

  addSubject(subject: string): ReminderEmailPayloadBuilder {
    this.payload.subject = subject;
    return this;
  }

  addCompanyInformation(companyName: string): ReminderEmailPayloadBuilder {
    this.payload.companyName = companyName;
    return this;
  }

  addSiteAddress(
    displayName: string,
    postcode: string
  ): ReminderEmailPayloadBuilder {
    this.payload.siteAddress = {
      displayName,
      postcode
    };
    return this;
  }

  addMeterInformation(
    meterNumber: string,
    utilityType: UtilityType
  ): ReminderEmailPayloadBuilder {
    if (
      this.payload.type === NotificationType.QUOTE_SIGNUP_EMAIL ||
      this.payload.type === NotificationType.SIGN_CONTRACT_EMAIL
    ) {
      this.payload.meterNumber = meterNumber;
    }
    this.payload.utilityType = utilityType;
    return this;
  }

  addUsageInformation(totalAnnualUsage: string): ReminderEmailPayloadBuilder {
    if (
      this.payload.type === NotificationType.QUOTE_SIGNUP_EMAIL ||
      this.payload.type === NotificationType.SIGN_CONTRACT_EMAIL
    ) {
      this.payload.totalAnnualUsage = totalAnnualUsage;
    }
    return this;
  }

  addRejoinLink(rejoinLink: string): ReminderEmailPayloadBuilder {
    this.payload.rejoinLink = rejoinLink;
    return this;
  }

  addCurrentSupplier(currentSupplier: string): ReminderEmailPayloadBuilder {
    if (
      this.payload.type !== NotificationType.CURRENT_SUPPLIER_INFORMATION_EMAIL
    ) {
      this.payload.currentSupplier = currentSupplier;
    }
    return this;
  }

  addSelectedSupplier(selectedSupplier: string): ReminderEmailPayloadBuilder {
    if (this.payload.type === NotificationType.SIGN_CONTRACT_EMAIL) {
      this.payload.selectedSupplier = selectedSupplier;
    }
    return this;
  }

  buildCurrentSupplierInformationEmail() {
    return {
      type: NotificationType.CURRENT_SUPPLIER_INFORMATION_EMAIL,
      recipientId: this.payload.recipientId,
      recipientEmail: this.payload.recipientEmail,
      createdAt: this.payload.createdAt,
      tag: this.payload.tag,
      subject: this.payload.subject,
      companyName: this.payload.companyName,
      siteAddress: this.payload.siteAddress,
      utilityType: this.payload.utilityType,
      rejoinLink: this.payload.rejoinLink
    } satisfies CurrentSupplierInformationEmailPayload;
  }

  buildQuoteSignUpEmail() {
    if (this.payload.type !== NotificationType.QUOTE_SIGNUP_EMAIL) {
      throw new Error("Invalid payload type");
    }

    return {
      type: NotificationType.QUOTE_SIGNUP_EMAIL,
      recipientId: this.payload.recipientId,
      recipientEmail: this.payload.recipientEmail,
      createdAt: this.payload.createdAt,
      tag: this.payload.tag,
      subject: this.payload.subject,
      companyName: this.payload.companyName,
      siteAddress: this.payload.siteAddress,
      utilityType: this.payload.utilityType,
      meterNumber: this.payload.meterNumber,
      currentSupplier: this.payload.currentSupplier,
      totalAnnualUsage: this.payload.totalAnnualUsage,
      rejoinLink: this.payload.rejoinLink!
    } satisfies QuoteSignUpEmailPayload;
  }

  buildSignContractEmail() {
    if (this.payload.type !== NotificationType.SIGN_CONTRACT_EMAIL) {
      throw new Error("Invalid payload type");
    }

    return {
      type: NotificationType.SIGN_CONTRACT_EMAIL,
      recipientId: this.payload.recipientId,
      recipientEmail: this.payload.recipientEmail,
      createdAt: this.payload.createdAt,
      tag: this.payload.tag,
      subject: this.payload.subject,
      companyName: this.payload.companyName,
      siteAddress: this.payload.siteAddress,
      utilityType: this.payload.utilityType,
      meterNumber: this.payload.meterNumber,
      currentSupplier: this.payload.currentSupplier,
      selectedSupplier: this.payload.selectedSupplier,
      totalAnnualUsage: this.payload.totalAnnualUsage,
      rejoinLink: this.payload.rejoinLink!
    } satisfies SignContractEmailPayload;
  }
}
