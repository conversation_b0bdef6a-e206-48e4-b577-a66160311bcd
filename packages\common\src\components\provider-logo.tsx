import Image from "next/image";
import { cn } from "../utils/classname-tailwind-merge";

interface ProviderLogoProps {
  logoFileName?: string | null;
  displayName: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackClassName?: string;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  responsive?: boolean;
}

export function ProviderLogo({
  logoFileName,
  displayName,
  width = 60,
  height = 20,
  className,
  fallbackClassName,
  onError,
  responsive
}: ProviderLogoProps) {
  if (!logoFileName) {
    return (
      <span className={cn("font-medium text-sm", fallbackClassName)}>
        {displayName}
      </span>
    );
  }

  return (
    <>
      <Image
        src={`/static/providers/${logoFileName}.png`}
        alt={displayName}
        width={width}
        height={height}
        className={className}
        style={responsive ? { width: "auto", height: "auto" } : undefined}
        onError={e => {
          if (onError) {
            onError(e);
          } else {
            // Default error handling: hide image and show text fallback
            const img = e.currentTarget;
            img.style.display = "none";
            const textElement = img.nextElementSibling;
            if (textElement?.classList.contains("hidden")) {
              textElement.classList.remove("hidden");
            }
          }
        }}
      />
      <span className={cn("hidden font-medium text-sm", fallbackClassName)}>
        {displayName}
      </span>
    </>
  );
}
