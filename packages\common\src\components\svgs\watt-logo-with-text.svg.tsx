export function WattLogoWithText(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      role="img"
      xmlns="http://www.w3.org/2000/svg"
      width={213.534}
      height={51.537}
      viewBox="0 0 213.534 51.537"
      xmlSpace="preserve"
      aria-labelledby="titleId descId"
      {...props}
    >
      <title id="titleId">Logo of Watt.co.uk with text</title>
      <desc id="descId">
        A graphic representation of the Watt.co.uk logo, consisting of a
        stylized W in green and the text "Watt.co.uk" in white.
      </desc>
      <path
        fill="#93BD22"
        d="M20.95 40.02c-.02 0-.038 0-.057-.003-.603-.02-1.143-.378-1.397-.924l-9.25-19.796c-.377-.802-.03-1.758.772-2.132.803-.375 1.757-.028 2.132.774l7.92 16.946 2.82-5.092-1.972-3.566c-.722-1.29-3.078-5.683-3.078-7.94 0-3.728 3.032-6.76 6.76-6.76 3.727 0 6.76 3.033 6.76 6.76 0 2.13-2.135 6.566-2.79 7.873l-.03.062-1.983 3.576 2.786 5.03 7.92-16.942c.373-.807 1.327-1.15 2.13-.776.804.375 1.15 1.33.775 2.132l-9.25 19.797c-.257.547-.8.902-1.4.924-.604.02-1.168-.3-1.46-.827l-3.336-6.03-3.374 6.087c-.28.508-.82.825-1.4.825zm4.65-25.286c-1.958 0-3.55 1.594-3.55 3.553 0 1 1.358 4.03 2.672 6.383l1.004 1.815.99-1.79c1.202-2.404 2.437-5.45 2.437-6.41 0-1.956-1.593-3.55-3.553-3.55z"
      />
      <path
        fill="#FFF"
        d="M25.706 51.412C11.53 51.412 0 39.882 0 25.706 0 11.532 11.53 0 25.706 0s25.707 11.532 25.707 25.706c0 14.176-11.532 25.706-25.707 25.706zm0-48.14C13.334 3.27 3.27 13.334 3.27 25.706c0 12.372 10.063 22.437 22.436 22.437S48.143 38.08 48.143 25.707c0-12.372-10.065-22.436-22.437-22.436z"
      />
      <g fill="#FFF">
        <path d="M70.556 33.85c-.238 1.207-.537 1.992-.897 2.36-.362.364-.99.547-1.878.547-.8 0-1.363-.135-1.683-.408-.322-.272-.68-1.095-1.078-2.47l-4.55-15.797c-.11-.416-.184-.725-.217-.927-.035-.204-.058-.38-.067-.527 0-.683.206-1.234.617-1.654.41-.42.936-.63 1.58-.63.62 0 1.104.188 1.445.563.34.375.63 1.057.868 2.045l3.19 13.097 2.7-13.067c.207-1.008.52-1.7.94-2.076.42-.376 1.065-.564 1.937-.564.86 0 1.492.177 1.898.534.403.356.726 1.108.962 2.254l2.67 12.92 3.042-12.608c.306-1.256.636-2.086.987-2.492.35-.406.843-.607 1.477-.607.672 0 1.18.184 1.52.557.342.37.512.927.512 1.667 0 .15-.02.343-.06.58-.038.238-.1.51-.178.817l-4.582 15.87c-.04.12-.086.273-.135.46-.464 1.643-1.313 2.463-2.55 2.463-.882 0-1.493-.168-1.84-.505-.346-.336-.643-1.128-.89-2.373l-2.863-14.12-2.878 14.09zM102.017 24.34v9.168c0 .326.024.702.074 1.128.05.425.074.687.074.786 0 .514-.16.917-.488 1.21-.324.29-.78.436-1.364.436-.8 0-1.393-.207-1.774-.623-.38-.414-.575-1.068-.584-1.957-.543.84-1.256 1.48-2.138 1.92-.88.44-1.902.66-3.068.66-1.544 0-2.74-.44-3.6-1.32-.854-.88-1.28-2.11-1.28-3.69 0-1.485.36-2.62 1.083-3.407.72-.785 1.946-1.36 3.68-1.727.66-.14 1.504-.27 2.527-.393 1.024-.124 1.688-.234 1.996-.334.228-.08.395-.223.504-.43.11-.208.163-.47.163-.787 0-.653-.202-1.12-.607-1.402-.406-.28-1.082-.422-2.032-.422-.494 0-.946.07-1.356.207-.41.14-.773.34-1.09.61-.11.097-.273.266-.49.503-.683.742-1.32 1.113-1.914 1.113-.495 0-.907-.172-1.237-.513-.33-.34-.497-.76-.497-1.254 0-1.078.62-2.02 1.86-2.826 1.24-.806 2.768-1.208 4.576-1.208 2.255 0 3.983.39 5.185 1.17 1.2.78 1.8 1.91 1.8 3.38zm-4.137 4.214c-.71.337-1.524.6-2.44.793-.914.193-1.387.296-1.416.305-.752.218-1.28.49-1.58.815-.302.326-.452.775-.452 1.35 0 .613.198 1.09.594 1.432.395.34.948.51 1.66.51 1.1 0 1.98-.333 2.64-1 .663-.667.995-1.56.995-2.677v-1.53zM107.31 23.392h-.696c-.572 0-1.005-.125-1.296-.377-.292-.253-.438-.63-.438-1.135 0-.504.146-.88.438-1.134.29-.252.724-.377 1.296-.377h.697v-2.197c0-.8.177-1.4.527-1.802.352-.4.882-.6 1.594-.6.725 0 1.265.2 1.625.6.362.4.552 1.007.57 1.82v2.18h1.1c.584 0 1.018.123 1.297.37.282.247.423.628.423 1.142 0 .505-.143.883-.43 1.135s-.717.378-1.29.378h-1.1v8.928c0 .396.067.664.202.802.134.138.378.208.732.208.06 0 .168-.005.326-.016.158-.01.297-.016.416-.016.475 0 .845.133 1.112.395.267.262.4.626.4 1.09 0 .692-.24 1.193-.712 1.506-.476.313-1.232.468-2.27.468-1.712 0-2.892-.317-3.545-.957-.653-.637-.98-1.864-.98-3.685v-8.723h.004zM117.97 23.392h-.696c-.576 0-1.01-.125-1.3-.377-.292-.253-.438-.63-.438-1.135 0-.504.146-.88.438-1.134.29-.253.724-.377 1.3-.377h.695v-2.197c0-.8.175-1.4.525-1.802.352-.4.882-.6 1.595-.6.723 0 1.263.2 1.625.6.36.4.55 1.007.57 1.82v2.18h1.1c.582 0 1.014.123 1.297.37.28.247.423.628.423 1.142 0 .505-.144.883-.43 1.135-.287.252-.717.378-1.29.378h-1.1v8.928c0 .396.067.664.2.802.134.138.378.208.734.208.058 0 .168-.005.326-.016.158-.01.295-.016.414-.016.475 0 .845.133 1.112.395.267.262.4.626.4 1.09 0 .692-.237 1.193-.712 1.506-.476.313-1.23.468-2.27.468-1.71 0-2.892-.317-3.545-.957-.653-.637-.98-1.864-.98-3.685v-8.723zM127.78 34.235c0-.684.247-1.273.74-1.772.497-.5 1.09-.75 1.78-.75.712 0 1.316.245 1.81.734.495.49.74 1.085.74 1.786 0 .702-.25 1.303-.754 1.802-.506.5-1.105.75-1.795.75-.683 0-1.272-.252-1.77-.757-.502-.504-.75-1.1-.75-1.793zM150.26 24.34c0 .485-.186.902-.557 1.248-.37.346-.823.518-1.357.518-.365 0-.702-.09-1.008-.274-.308-.183-.657-.517-1.054-1-.455-.554-.873-.927-1.254-1.122-.382-.19-.833-.288-1.358-.288-1.057 0-1.85.42-2.372 1.26-.525.84-.787 2.102-.787 3.784 0 1.552.275 2.746.823 3.582.548.835 1.337 1.253 2.366 1.253.465 0 .867-.076 1.21-.236.34-.158.664-.414.972-.77.108-.12.256-.312.444-.578.653-.91 1.315-1.366 1.987-1.366.543 0 1.003.186 1.38.557.375.37.563.823.563 1.357 0 .494-.132.983-.394 1.468s-.655.96-1.18 1.424c-.73.644-1.52 1.118-2.357 1.424-.842.307-1.766.46-2.773.46-2.364 0-4.212-.765-5.54-2.292-1.33-1.528-1.996-3.64-1.996-6.34 0-2.69.68-4.8 2.04-6.328s3.23-2.29 5.613-2.29c1.82 0 3.373.462 4.66 1.385 1.285.925 1.93 1.98 1.93 3.166zM161.226 19.788c2.532 0 4.527.767 5.985 2.3 1.46 1.533 2.19 3.64 2.19 6.318 0 2.69-.73 4.8-2.19 6.334-1.457 1.533-3.452 2.3-5.984 2.3-2.54 0-4.538-.767-5.992-2.3-1.453-1.533-2.18-3.644-2.18-6.334 0-2.68.727-4.785 2.18-6.318 1.456-1.534 3.452-2.3 5.992-2.3zm0 3.634c-1.216 0-2.153.432-2.81 1.298-.657.864-.987 2.094-.987 3.685 0 1.604.327 2.84.985 3.71.66.87 1.595 1.304 2.81 1.304 1.2 0 2.124-.436 2.785-1.306.654-.87.984-2.105.984-3.71 0-1.59-.328-2.82-.984-3.684-.66-.866-1.585-1.298-2.784-1.298zM172.523 34.235c0-.684.245-1.273.74-1.772.494-.5 1.088-.75 1.78-.75.71 0 1.314.245 1.81.734.494.49.742 1.085.742 1.786 0 .702-.253 1.303-.757 1.802-.506.5-1.104.75-1.796.75-.68 0-1.272-.252-1.77-.757-.502-.504-.75-1.1-.75-1.793zM191.635 22.487c0-.81.18-1.42.54-1.824.36-.406.897-.608 1.61-.608.71 0 1.25.203 1.616.608.366.405.55 1.013.55 1.824v11.88c0 .812-.178 1.412-.534 1.803-.357.39-.9.585-1.632.585-.622 0-1.096-.158-1.417-.474-.32-.317-.506-.816-.556-1.497l-.057-.55c-.525.88-1.205 1.548-2.04 2.003-.837.455-1.798.683-2.885.683-1.772 0-3.1-.466-3.985-1.396-.883-.93-1.326-2.318-1.326-4.168v-8.87c0-.81.18-1.42.54-1.824.363-.406.898-.608 1.61-.608.713 0 1.252.203 1.617.608.365.405.548 1.013.548 1.824v7.625c0 1.148.193 1.967.58 2.456.386.49 1.023.734 1.914.734 1.02 0 1.823-.303 2.417-.905.594-.604.89-1.424.89-2.462v-7.446h.002zM204.308 34.368c0 .78-.18 1.375-.54 1.78-.36.404-.895.607-1.596.607-.69 0-1.208-.197-1.55-.594-.342-.393-.512-.992-.512-1.794v-17.62c0-.81.17-1.414.51-1.81.344-.395.86-.594 1.552-.594.7 0 1.235.203 1.595.608.36.406.54 1.004.54 1.795v9.36l4.288-4.836c.425-.494.77-.816 1.038-.965.268-.148.567-.222.904-.222.523 0 .985.18 1.38.542.396.36.593.778.593 1.253s-.404 1.153-1.216 2.032l-.045.045-2.24 2.39 3.797 6.526c.297.484.492.853.587 1.105.093.253.14.492.14.72 0 .603-.192 1.1-.58 1.482-.386.386-.88.578-1.48.578-.526 0-.978-.113-1.358-.34-.38-.228-.71-.59-.987-1.084l-3.19-5.71-1.63 1.766v2.982h-.002z" />
      </g>
    </svg>
  );
}
