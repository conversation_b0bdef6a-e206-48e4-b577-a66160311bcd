import type { UtilityType } from "@prisma/client";
import {
  ActivitySquare,
  BatteryCharging,
  Coins,
  Droplet,
  FileLock2,
  Flame,
  Lightbulb,
  type LucideIcon,
  Phone,
  Sun,
  Wifi
} from "lucide-react";

// Color definitions for each utility type
export const UTILITY_COLORS: Record<UtilityType, string> = {
  ELECTRICITY: "#fec800",
  GAS: "#D13125",
  WATER: "#0586CC",
  TELECOM: "#FD6E01",
  INTERNET: "#13BFB2",
  SOLAR: "#ff914d",
  BATTERY_STORAGE: "hsl(76, 70%, 44%)",
  ENERGY_AUDIT: "#7336ee",
  FINANCE: "#ff66c4",
  BUSINESS_INSURANCE: "#093c5b"
};

// Icon component mapping for each utility type
export const UTILITY_ICONS: Record<UtilityType, LucideIcon> = {
  ELECTRICITY: Lightbulb,
  GAS: Flame,
  WATER: Droplet,
  TELECOM: Phone,
  INTERNET: Wifi,
  SOLAR: Sun,
  BATTERY_STORAGE: BatteryCharging,
  ENERGY_AUDIT: ActivitySquare,
  FINANCE: Coins,
  BUSINESS_INSURANCE: FileLock2
};

// Utility metadata including name and availability
export type UtilityMetadata = {
  id: UtilityType;
  name: string;
  icon: LucideIcon;
  color: string;
  disabled?: boolean;
  suppliers?: number;
};

export const UTILITY_METADATA: UtilityMetadata[] = [
  {
    id: "ELECTRICITY" as UtilityType,
    name: "Electricity",
    icon: Lightbulb,
    color: UTILITY_COLORS.ELECTRICITY,
    suppliers: 54,
    disabled: false
  },
  {
    id: "GAS" as UtilityType,
    name: "Gas",
    icon: Flame,
    color: UTILITY_COLORS.GAS,
    suppliers: 11,
    disabled: false
  },
  {
    id: "WATER" as UtilityType,
    name: "Water",
    icon: Droplet,
    color: UTILITY_COLORS.WATER,
    disabled: true
  },
  {
    id: "TELECOM" as UtilityType,
    name: "Telecom",
    icon: Phone,
    color: UTILITY_COLORS.TELECOM,
    disabled: true
  },
  {
    id: "INTERNET" as UtilityType,
    name: "Internet",
    icon: Wifi,
    color: UTILITY_COLORS.INTERNET,
    disabled: true
  }
];

// Helper function to get utility icon component with color
export function getUtilityIconComponent(
  utilityType: UtilityType
): LucideIcon | null {
  return UTILITY_ICONS[utilityType] || null;
}

// Helper function to get utility color
export function getUtilityColor(utilityType: UtilityType): string | null {
  return UTILITY_COLORS[utilityType] || null;
}
