import type { <PERSON>ieMethodsServer } from "@supabase/ssr";
import type { cookies } from "next/headers";
import { type NextRequest, NextResponse } from "next/server";

/**
 * Cookie adapter type using Supabase's server cookie methods
 */
export type CookieAdapter = CookieMethodsServer;

/**
 * Server component adapter factory
 * @param cookieStore
 * @returns
 */
export const createServerComponentAdapter = (
  cookieStore: Awaited<ReturnType<typeof cookies>>
): CookieAdapter => ({
  getAll: () => cookieStore.getAll(),
  setAll: cookiesToSet => {
    try {
      for (const { name, value } of cookiesToSet) {
        cookieStore.set(name, value);
      }
    } catch {
      // Server Components are read-only
      // The `setAll` method was called from a Server Component.
      // This can be ignored if you have middleware refreshing
      // user sessions.
    }
  }
});

/**
 * Middleware adapter factory with response mutation
 * @param request - The request object
 * @param onResponseUpdate - A callback function to update the response
 * @returns A cookie adapter object
 */
export const createMiddlewareAdapter = (
  request: NextRequest,
  onResponseUpdate: (response: NextResponse) => void
): CookieAdapter => ({
  getAll: () => request.cookies.getAll(),
  setAll: cookiesToSet => {
    // Update request cookies
    for (const { name, value } of cookiesToSet) {
      request.cookies.set(name, value);
    }

    // Create new response
    const newResponse = NextResponse.next({ request });

    // Update response cookies
    for (const { name, value, options } of cookiesToSet) {
      newResponse.cookies.set(name, value, options);
    }

    // Notify about response update
    onResponseUpdate(newResponse);
  }
});
