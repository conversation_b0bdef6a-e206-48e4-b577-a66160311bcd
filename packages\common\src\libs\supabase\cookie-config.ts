import { env } from "../../config/env";

/**
 * Type for application names used in cookie configuration
 */
export type AppName = "crm" | "quote";

/**
 * Generates a custom cookie name for Supabase authentication to prevent
 * cookie conflicts between CRM and Quote applications.
 *
 * @param appName - The application name ('crm' or 'quote')
 * @returns Cookie name in format: sb-{app}-{identifier}-auth-token
 *
 * Examples:
 * - Local: sb-crm-localhost-auth-token, sb-quote-localhost-auth-token
 * - Dev: sb-crm-vomrghiulbmrfvmhlflk-auth-token, sb-quote-vomrghiulbmrfvmhlflk-auth-token
 */
export function getSupabaseCookieName(appName: AppName): string {
  const url = env.NEXT_PUBLIC_SUPABASE_URL;

  // Extract identifier from URL
  const identifier = getIdentifierFromUrl(url);

  // Return format: sb-{app}-{identifier}-auth-token
  // Note: Supabase uses the name as-is, it doesn't append -auth-token
  return `sb-${appName}-${identifier}-auth-token`;
}

/**
 * Extracts the identifier from the Supabase URL
 * @param url - The Supabase URL
 * @returns The identifier from the URL
 */
function getIdentifierFromUrl(url: string): string {
  // Local: http://localhost:54321 → localhost
  if (url.includes("localhost")) {
    return "localhost";
  }

  // Supabase: https://vomrghiulbmrfvmhlflk.supabase.co → vomrghiulbmrfvmhlflk
  const match = url.match(/https:\/\/([^.]+)\.supabase\.co/);
  return match?.[1] ?? "default";
}
