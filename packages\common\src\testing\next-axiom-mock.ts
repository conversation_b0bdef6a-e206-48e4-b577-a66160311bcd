/**
 * Shared mock implementation for next-axiom to be used in Jest tests
 * This implementation ensures that all async operations are properly handled
 * to prevent "Cannot log after tests are done" errors
 */

// Create a mock logger function that returns immediately and does nothing
const createNoopLogger = () => {
  // Create a base logger with all methods as no-op functions
  const logger = {
    debug: jest.fn(),
    info: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    flush: jest.fn().mockResolvedValue(undefined),
    sendLogs: jest.fn().mockResolvedValue(undefined),
    withContext: jest.fn(),
    with: jest.fn()
  };

  // Make withContext and with return the logger itself to allow chaining
  logger.withContext.mockImplementation(() => logger);
  logger.with.mockImplementation(() => logger);

  return logger;
};

// Create a single shared logger instance to be used by all mocks
const sharedLogger = createNoopLogger();

// Mock implementation for next-axiom
const nextAxiomMock = {
  // Logger constructor mock
  Logger: jest.fn().mockImplementation(() => sharedLogger),

  // createLogger function mock
  createLogger: jest.fn().mockImplementation(() => sharedLogger),

  // withAxiom HOC mock - just returns the component
  withAxiom: jest.fn().mockImplementation(component => component),

  // AxiomClient mock
  AxiomClient: jest.fn().mockImplementation(() => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  })),

  // Global log object mock
  log: sharedLogger
};

// Ensure the mock is properly initialized before tests run
if (typeof beforeAll === "function") {
  beforeAll(() => {
    // Clear any pending timers or async operations
    if (typeof jest !== "undefined") {
      jest.useFakeTimers();
      jest.runAllTimers();
    }
  });
}

export default nextAxiomMock;
