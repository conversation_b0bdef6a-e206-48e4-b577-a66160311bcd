import type { Salutation } from "@prisma/client";
import { composeCompanyContact } from "./company-contact";

describe("composeCompanyContact", () => {
  test.each([
    // Basic cases
    ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Mr. <PERSON>"],
    ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mrs. <PERSON>"],
    ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>. <PERSON>"],
    // Without salutation
    ["<PERSON>", "<PERSON>", undefined, "<PERSON>"],
    ["<PERSON>", "<PERSON>", null, "<PERSON>"],
    // Whitespace handling
    ["  <PERSON>  ", "  <PERSON><PERSON>  ", "<PERSON>", "<PERSON>. <PERSON>"],
    ["Multiple    Spaces", "Between", "MR", "Mr. Multiple Spaces Between"],
    // Empty cases
    ["", "", "MR", "N/A"],
    ["", "", undefined, "N/A"],
    ["", "", null, "N/A"]
  ])("formats '%s %s' correctly", (forename, surname, salutation, expected) => {
    expect(
      composeCompanyContact(forename, surname, salutation as Salutation)
    ).toBe(expected);
  });
});
