import type { Salutation } from "@prisma/client";
import { humanize } from "./humanize-string";

const CONSECUTIVE_WHITESPACE_PATTERN = /\s+/g;

/**
 * Composes a formatted name string with an optional salutation
 * @param forename - The contact's first name
 * @param surname - The contact's last name
 * @param salutation - Optional Prisma Salutation enum value
 * @param fallback - Fallback value to return if no name is provided
 * @returns A trimmed string combining salutation (if provided) with the full name
 * @example
 * composeCompanyContact("<PERSON>", "<PERSON><PERSON>", "<PERSON>") // returns "Mr. <PERSON>"
 * composeCompanyContact("<PERSON>", "<PERSON><PERSON>") // returns "<PERSON>"
 */
export function composeCompanyContact(
  forename: string,
  surname: string,
  salutation?: Salutation | null | undefined,
  fallback?: unknown
) {
  if (!forename && !surname) {
    return fallback ?? "N/A";
  }

  const normalizedForename = forename.trim();
  const normalizedSurname = surname.trim();
  const salutationPart = salutation ? `${humanize(salutation)}. ` : "";

  const formattedName = `${salutationPart}${normalizedForename} ${normalizedSurname}`;

  return formattedName.replace(CONSECUTIVE_WHITESPACE_PATTERN, " ").trim();
}
