import {
  type AddressSubUnitMatch,
  extractAddressSubUnits
} from "./extract-address-sub-units";

describe("extractAddressSubUnits", () => {
  type TestCase = {
    input: string;
    expected: AddressSubUnitMatch[];
    description: string;
  };

  const testCases: TestCase[] = [
    {
      input: "Flat 123A",
      expected: [
        {
          type: "flat",
          number: 123,
          suffix: "a",
          priority: 1
        }
      ],
      description: "Single priority-1 match with suffix"
    },
    {
      input: "Suite 10, 123 High Street",
      // Priority-1 match for "Suite 10", priority-2 for "123"
      expected: [
        {
          type: "suite",
          number: 10,
          suffix: "",
          priority: 1
        },
        {
          type: "",
          number: 123,
          suffix: "",
          priority: 2
        }
      ],
      description: "Multiple matches with priority-1 and priority-2"
    },
    {
      input: "42 Main Road",
      expected: [
        {
          type: "",
          number: 42,
          suffix: "",
          priority: 2
        }
      ],
      description: "Priority-2 only match"
    },
    {
      input: "",
      expected: [],
      description: "Empty string"
    },
    {
      input: "No digits or subunits",
      expected: [],
      description: "No matches"
    },
    {
      input: "FLAT1A",
      expected: [
        {
          type: "flat",
          number: 1,
          suffix: "a",
          priority: 1
        }
      ],
      description: "Flat typed in uppercase and no space"
    },
    {
      input: "Flat 2B, Office 3C, 33D",
      // Priority-1: 'Flat 2B' => type=flat, number=2, suffix='b'
      // Priority-1: 'Office 3C' => type=office, number=3, suffix='c'
      // Priority-2: '33D' => type='', number=33, suffix='d'
      expected: [
        {
          type: "flat",
          number: 2,
          suffix: "b",
          priority: 1
        },
        {
          type: "office",
          number: 3,
          suffix: "c",
          priority: 1
        },
        {
          type: "",
          number: 33,
          suffix: "d",
          priority: 2
        }
      ],
      description: "Multiple priority-1 matches plus a priority-2"
    },
    {
      input: "  Apartment 5A   ",
      expected: [
        {
          type: "apartment",
          number: 5,
          suffix: "a",
          priority: 1
        }
      ],
      description: "Handles leading/trailing spaces"
    }
  ];

  test.each(testCases)("$description", ({ input, expected }) => {
    const result = extractAddressSubUnits(input);
    expect(result).toHaveLength(expected.length);
    result.forEach((item, index) => {
      const expectedItem = expected[index];
      if (!expectedItem) {
        throw new Error(`No expected item at index ${index}`);
      }
      expect(item).toMatchObject(expectedItem);
    });
  });
});
