import { extractSuffix } from "./extract-number";

import { extractLeadingNumber } from "./extract-number";

export type AddressSubUnitMatch = {
  type: string;
  number: number;
  suffix: string;
  priority: number;
};

/**
 * Extracts all address subunit identifiers from a string.
 * Searches for flat numbers, unit numbers, office numbers, and other subunit identifiers
 * in the provided address string.
 *
 * @param {string} str - The address string to analyze
 * @returns {AddressSubUnitMatch[]} An array of identified subunit matches with their type,
 * number, suffix, and priority for sorting
 */
export function extractAddressSubUnits(str: string): AddressSubUnitMatch[] {
  if (!str) {
    return [];
  }

  const lower = str.toLowerCase();
  const matches: AddressSubUnitMatch[] = [];

  // Single pass for priority 1 matches
  const priority1Matches = lower.matchAll(
    /(flat|unit|office|apt|apartment|suite|ste|room)\s*(\d+[a-zA-Z-]*)/g
  );
  for (const match of priority1Matches) {
    const type = match[1];
    const fullNumber = match[2];

    if (!type || !fullNumber) {
      continue; // Skip invalid matches
    }

    matches.push({
      type,
      number: extractLeadingNumber(fullNumber),
      suffix: extractSuffix(fullNumber),
      priority: 1
    });
  }

  // Single pass for priority 2 matches, excluding already matched numbers
  const matchedPositions = new Set(
    matches.map(m => lower.indexOf(m.number.toString()))
  );
  const priority2Matches = lower.matchAll(/\b(\d+[a-zA-Z-]*)\b/g);
  for (const match of priority2Matches) {
    const position = match.index!;
    if (!matchedPositions.has(position)) {
      const fullNumber = match[1];

      if (!fullNumber) {
        continue; // Skip invalid matches
      }

      matches.push({
        type: "",
        number: extractLeadingNumber(fullNumber),
        suffix: extractSuffix(fullNumber),
        priority: 2
      });
    }
  }

  return matches;
}
