import { extractLeadingNumber } from "./extract-number";

describe("extractLeadingNumber", () => {
  type TestCase = {
    input: string;
    expected: number;
    description: string;
  };

  const testCases: TestCase[] = [
    {
      input: "123",
      expected: 123,
      description: "Simple integer"
    },
    {
      input: "123a",
      expected: 123,
      description: "Integer followed by letter"
    },
    {
      input: "a123",
      expected: 123,
      description: "No leading digits (digits come after letters)"
    },
    {
      input: "abc",
      expected: Number.NaN,
      description: "No digits at all"
    },
    {
      input: "",
      expected: Number.NaN,
      description: "Empty string"
    },
    {
      input: "0",
      expected: 0,
      description: "Leading zero integer"
    },
    {
      input: "0abc",
      expected: 0,
      description: "Leading zero integer with trailing text"
    },
    {
      input: "  45abc",
      expected: 45,
      description:
        "Leading whitespace before digits - note: code doesn't trim by default"
    },
    {
      input: "45 67",
      expected: 45,
      description:
        "If multiple numbers appear, first block is 45 but we only parse the leading portion"
    }
  ];

  test.each(testCases)("$description", ({ input, expected }) => {
    const result = extractLeadingNumber(input);
    if (Number.isNaN(expected)) {
      expect(result).toBeNaN();
    } else {
      expect(result).toBe(expected);
    }
  });
});
