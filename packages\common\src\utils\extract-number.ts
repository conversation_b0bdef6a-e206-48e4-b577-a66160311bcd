/**
 * Extracts any leading integer from a string.
 * For "148b" returns 148, for "142" returns 142, for "abc" returns NaN.
 * @param {string} str - The input string.
 * @returns {number} The extracted leading number or NaN if none found.
 */
export function extractLeadingNumber(text: string): number {
  const match = text.match(/\d+/);
  return match ? Number.parseInt(match[0], 10) : Number.NaN;
}

/**
 * Extracts an alphanumeric suffix that follows an initial integer sequence.
 * For "flat 148b" returns "148b"
 * @param {string} str - The input string.
 * @returns {string} The extracted suffix.
 */
export function extractSuffix(str: string): string {
  const match = str.match(/^([0-9]+)(.*)/);
  return match?.[2]?.trim() ?? "";
}
