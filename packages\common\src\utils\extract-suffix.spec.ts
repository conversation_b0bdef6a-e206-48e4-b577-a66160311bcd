import { extractSuffix } from "./extract-number";

describe("extractSuffix", () => {
  type TestCase = {
    input: string;
    expected: string;
    description: string;
  };

  const testCases: TestCase[] = [
    {
      input: "123",
      expected: "",
      description: "Number without suffix"
    },
    {
      input: "123a",
      expected: "a",
      description: "Number with single letter suffix"
    },
    {
      input: "123-B",
      expected: "-B",
      description: "Number with hyphenated letter suffix"
    },
    {
      input: "42ABC",
      expected: "ABC",
      description: "Number with multi-letter suffix"
    },
    {
      input: "flat 123",
      expected: "",
      description: "String has digits but not at start"
    },
    {
      input: "",
      expected: "",
      description: "Empty string"
    },
    {
      input: "abc",
      expected: "",
      description: "String without leading digits"
    },
    {
      input: "0xyz",
      expected: "xyz",
      description: "Zero as leading digit then suffix"
    }
  ];

  test.each(testCases)(
    "$description - input: '$input'",
    ({ input, expected }) => {
      const result = extractSuffix(input);
      expect(result).toBe(expected);
    }
  );
});
