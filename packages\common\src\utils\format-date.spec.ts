import * as dateFnsTz from "date-fns-tz";
import {
  convertLocalDateToUTCString,
  convertUTCStringToLocalDate,
  dateFormats
} from "./format-date";

// Mock the date-fns-tz functions
jest.mock("date-fns-tz", () => ({
  utcToZonedTime: jest.fn(),
  zonedTimeToUtc: jest.fn(),
  format: jest.fn()
}));

describe("Date conversion functions", () => {
  // Original implementation to restore after tests
  let originalDateTimeFormatFn: typeof Intl.DateTimeFormat;

  beforeEach(() => {
    // Store original implementation
    originalDateTimeFormatFn = Intl.DateTimeFormat;

    // Clear mocks
    jest.clearAllMocks();

    // Default mock for Intl.DateTimeFormat
    mockTimezone("UTC");
  });

  afterEach(() => {
    // Restore original implementation
    global.Intl.DateTimeFormat = originalDateTimeFormatFn;
  });

  describe("convertUTCStringToLocalDate", () => {
    const testCases = [
      {
        description: "converts UTC string to New York timezone",
        timezone: "America/New_York",
        utcInput: "2023-06-15",
        expected: new Date("2023-06-15T00:00:00Z")
      },
      {
        description: "converts UTC string to London timezone",
        timezone: "Europe/London",
        utcInput: "2023-06-15",
        expected: new Date("2023-06-15T00:00:00Z")
      },
      {
        description: "converts UTC string to Tokyo timezone",
        timezone: "Asia/Tokyo",
        utcInput: "2023-06-15",
        expected: new Date("2023-06-15T00:00:00Z")
      },
      {
        description: "handles UTC string with time component",
        timezone: "UTC",
        utcInput: "2023-06-15T12:30:45Z",
        expected: new Date("2023-06-15T12:30:45Z")
      }
    ];

    test.each(testCases)("$description", ({ timezone, utcInput, expected }) => {
      // Mock the timezone
      mockTimezone(timezone);

      // Mock the utcToZonedTime to return the expected date
      const mockedUtcToZonedTime = dateFnsTz.utcToZonedTime as jest.Mock;
      mockedUtcToZonedTime.mockReturnValue(expected);

      // Run the conversion
      const result = convertUTCStringToLocalDate(utcInput);

      // Check that utcToZonedTime was called with correct parameters
      expect(mockedUtcToZonedTime).toHaveBeenCalledWith(utcInput, timezone);

      // Verify the result matches what we mocked
      expect(result).toBe(expected);
    });
  });

  describe("convertLocalDateToUTCString", () => {
    const testCases = [
      {
        description: "converts from New York timezone to UTC string",
        timezone: "America/New_York",
        localDate: new Date("2023-06-15T12:00:00"),
        expected: "2023-06-15"
      },
      {
        description: "converts from London timezone to UTC string",
        timezone: "Europe/London",
        localDate: new Date("2023-06-15T12:00:00"),
        expected: "2023-06-15"
      },
      {
        description: "converts to custom format",
        timezone: "UTC",
        localDate: new Date("2023-06-15T12:00:00Z"),
        expected: "15/06/2023",
        format: dateFormats.DD_MM_YYYY
      }
    ];

    test.each(testCases)(
      "$description",
      ({ timezone, localDate, expected, format }) => {
        // Mock the timezone
        mockTimezone(timezone);

        // Mock the zonedTimeToUtc function
        const utcDate = new Date("2023-06-15T00:00:00Z");
        const mockedZonedTimeToUtc = dateFnsTz.zonedTimeToUtc as jest.Mock;
        mockedZonedTimeToUtc.mockReturnValue(utcDate);

        // Mock the format function
        const mockedFormat = dateFnsTz.format as jest.Mock;
        mockedFormat.mockReturnValue(expected);

        // Run the conversion
        const result = convertLocalDateToUTCString(localDate, format);

        // Verify zonedTimeToUtc was called correctly
        expect(mockedZonedTimeToUtc).toHaveBeenCalledWith(localDate, timezone);

        // Verify format was called correctly
        expect(mockedFormat).toHaveBeenCalledWith(
          utcDate,
          format || dateFormats.YYYY_MM_DD_HYPHEN,
          { timeZone: timezone }
        );

        // Verify the result
        expect(result).toBe(expected);
      }
    );

    test("uses default format when not specified", () => {
      // Mock timezone
      const timezone = "UTC";
      mockTimezone(timezone);

      // Mock the required functions
      const utcDate = new Date("2023-06-15T00:00:00Z");
      const localDate = new Date("2023-06-15T12:00:00Z");
      const expected = "2023-06-15";

      const mockedZonedTimeToUtc = dateFnsTz.zonedTimeToUtc as jest.Mock;
      mockedZonedTimeToUtc.mockReturnValue(utcDate);

      const mockedFormat = dateFnsTz.format as jest.Mock;
      mockedFormat.mockReturnValue(expected);

      // Run the conversion
      const result = convertLocalDateToUTCString(localDate);

      // Verify the correct default format was used
      expect(mockedFormat).toHaveBeenCalledWith(
        utcDate,
        dateFormats.YYYY_MM_DD_HYPHEN,
        { timeZone: timezone }
      );

      // Verify result
      expect(result).toBe(expected);
    });
  });

  describe("roundtrip conversion", () => {
    test("preserves date when converting UTC to local and back", () => {
      // Mock timezone
      const timezone = "America/New_York";
      mockTimezone(timezone);

      // Input and expected output
      const utcInput = "2023-06-15";
      const localDate = new Date("2023-06-15T00:00:00-04:00");
      const expectedOutput = "2023-06-15";

      // Mock utcToZonedTime for the first conversion
      const mockedUtcToZonedTime = dateFnsTz.utcToZonedTime as jest.Mock;
      mockedUtcToZonedTime.mockReturnValue(localDate);

      // Mock zonedTimeToUtc for the second conversion
      const utcDate = new Date("2023-06-15T04:00:00Z");
      const mockedZonedTimeToUtc = dateFnsTz.zonedTimeToUtc as jest.Mock;
      mockedZonedTimeToUtc.mockReturnValue(utcDate);

      // Mock format for string output
      const mockedFormat = dateFnsTz.format as jest.Mock;
      mockedFormat.mockReturnValue(expectedOutput);

      // Step 1: Convert UTC string to local date
      const resultLocalDate = convertUTCStringToLocalDate(utcInput);
      expect(resultLocalDate).toBe(localDate);
      expect(mockedUtcToZonedTime).toHaveBeenCalledWith(utcInput, timezone);

      // Step 2: Convert local date back to UTC string
      const resultUtcString = convertLocalDateToUTCString(resultLocalDate);
      expect(resultUtcString).toBe(expectedOutput);
      expect(mockedZonedTimeToUtc).toHaveBeenCalledWith(localDate, timezone);
      expect(mockedFormat).toHaveBeenCalledWith(
        utcDate,
        dateFormats.YYYY_MM_DD_HYPHEN,
        { timeZone: timezone }
      );
    });
  });
});

/**
 * Mocks the timezone for testing by replacing the Intl.DateTimeFormat
 * implementation with a mock that returns the specified timezone
 */
function mockTimezone(timezone: string): void {
  // Create a mock implementation of Intl.DateTimeFormat
  const mockImplementation = (
    _locales?: string | string[],
    _options?: Intl.DateTimeFormatOptions
  ) => ({
    resolvedOptions: () => ({
      timeZone: timezone
    }),
    format: () => "",
    formatToParts: () => [],
    formatRange: () => "",
    formatRangeToParts: () => []
  });

  // Add the required static methods
  mockImplementation.supportedLocalesOf =
    Intl.DateTimeFormat.supportedLocalesOf;

  // Apply the mock
  global.Intl.DateTimeFormat =
    mockImplementation as unknown as typeof Intl.DateTimeFormat;
}
