import crypto from "node:crypto";
import "server-only";

/**
 * Generates a consistent SHA-256 hash for any string by normalizing it first.
 * Normalization includes lowercase conversion, trimming, and standardizing whitespace.
 * @param text - The string to normalize and hash.
 * @returns A SHA-256 hex digest of the normalized string.
 */
export function generateConsistentHash(text: string): string {
  const normalized = text.toLowerCase().trim().replace(/\s+/g, " ");
  const hash = crypto.createHash("sha256").update(normalized).digest("hex");
  return hash;
}
