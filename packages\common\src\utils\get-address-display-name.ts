import { capitalize } from "./capitalise";
import { formatPostcode } from "./format-postcode";

type AddressDisplayNameAndPostcode = {
  displayName: string | null;
  postcode: string;
};

// TODO: Fix this file is named incorrectly
export function getAddressDisplayName(
  address: AddressDisplayNameAndPostcode | undefined,
  options: { multiLine?: boolean } = {}
): string {
  if (!address) {
    return "";
  }

  const { displayName, postcode } = address;
  const formattedPostcode = formatPostcode(postcode);
  const { multiLine = false } = options;

  if (!displayName) {
    return formattedPostcode as string;
  }

  // Use regex to remove postcode with any preceding comma or space
  const cleanedDisplayName = capitalize(displayName)
    .replace(new RegExp(`(,\\s*|\\s+)${postcode}\\b`), "") // Remove postcode with any preceding comma or space
    .trim();

  if (multiLine) {
    // Split the address by commas and join with new lines
    return `${cleanedDisplayName.split(", ").join(",\n")},\n${formattedPostcode}`;
  }

  return `${cleanedDisplayName}, ${formattedPostcode}`;
}
