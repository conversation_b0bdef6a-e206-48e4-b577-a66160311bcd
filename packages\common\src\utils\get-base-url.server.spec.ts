describe("getBaseUrl", () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset the module registry so that modules re-import fresh copies.
    jest.resetModules();
    // Set the initial test environment.
    process.env = {
      ...originalEnv,
      VERCEL_URL: undefined,
      PORT: undefined
    };
  });

  afterAll(() => {
    // Restore the original environment.
    process.env = originalEnv;
  });

  // Case 1: VERCEL_URL starts with https
  test("VERCEL_URL is defined and starts with https", async () => {
    process.env.VERCEL_URL = "https://example.com";
    const { getBaseUrl } = await import("./get-base-url");
    expect(getBaseUrl()).toBe("https://example.com");
    expect(getBaseUrl()).toBe("https://example.com");
  });

  // Case 2: VERCEL_URL doesn't start with https
  test("VERCEL_URL is defined but doesn't start with https", async () => {
    process.env.VERCEL_URL = "example.com";
    const { getBaseUrl } = await import("./get-base-url");
    expect(getBaseUrl()).toBe("https://example.com");
    expect(getBaseUrl()).toBe("https://example.com");
  });

  // Case 2: Custom local port
  test("Custom local port", async () => {
    const { getBaseUrl } = await import("./get-base-url");
    expect(getBaseUrl({ localPort: "8000" })).toBe("http://localhost:8000");
  });
});
