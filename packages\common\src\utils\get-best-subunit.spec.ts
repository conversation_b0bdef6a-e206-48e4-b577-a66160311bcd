import type { AddressSubUnitMatch } from "./extract-address-sub-units";
import { getBestSubunit } from "./get-best-subunit";

describe("getBestSubunit", () => {
  type TestCase = {
    input: string;
    expected: AddressSubUnitMatch;
    description: string;
  };

  const testCases: TestCase[] = [
    {
      input: "Flat 1A, Office 2B, 33C",
      expected: {
        type: "flat",
        number: 1,
        suffix: "a",
        priority: 1
      },
      description:
        "Chooses the subunit with smallest priority and smallest number"
    },
    {
      input: "Unit 10A, Flat 10B",
      expected: {
        type: "unit",
        number: 10,
        suffix: "a",
        priority: 1
      },
      description: "Ties on priority and number => suffix decides"
    },
    {
      input: "42 High Street",
      expected: {
        type: "",
        number: 42,
        suffix: "",
        priority: 2
      },
      description: "Single priority-2 match"
    },
    {
      input: "No matches here",
      expected: {
        type: "",
        number: Number.POSITIVE_INFINITY,
        suffix: "",
        priority: Number.POSITIVE_INFINITY
      },
      description: "No subunits => returns default with Infinity"
    },
    {
      input: "Unit 10B, Suite 10B",
      expected: {
        type: "unit",
        number: 10,
        suffix: "b",
        priority: 1
      },
      description:
        "Exact tie in priority, number, suffix (result depends on match order)."
    }
  ];

  test.each(testCases)("$description", ({ input, expected }) => {
    const result = getBestSubunit(input);
    expect(result).toEqual(expected);
  });
});
