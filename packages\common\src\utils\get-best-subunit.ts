import {
  type AddressSubUnitMatch,
  extractAddressSubUnits
} from "./extract-address-sub-units";

/**
 * Finds the best (most relevant) subunit identifier from an address string.
 * Prioritizes explicit unit identifiers (flat, unit, etc.) over general numbers,
 * and sorts by numeric value and suffix when multiple matches have the same priority.
 *
 * @param {string} str - The address string to analyze
 * @returns {AddressSubunitMatch} The most relevant subunit match, or a default match with
 * infinity values if no subunit identifiers are found
 */
export function getBestSubunit(str: string): AddressSubUnitMatch {
  if (!str) {
    return {
      type: "",
      number: Number.POSITIVE_INFINITY,
      suffix: "",
      priority: Number.POSITIVE_INFINITY
    };
  }

  const matches = extractAddressSubUnits(str);
  if (matches.length === 0) {
    return {
      type: "",
      number: Number.POSITIVE_INFINITY,
      suffix: "",
      priority: Number.POSITIVE_INFINITY
    };
  }

  // Sort by priority first, then by number
  const sorted = matches.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    if (a.number !== b.number) {
      return a.number - b.number;
    }
    return a.suffix.localeCompare(b.suffix);
  });

  const bestMatch = sorted[0];
  if (!bestMatch) {
    // This should never happen since we check for empty array above
    throw new Error("Unexpected empty array after sorting");
  }

  return bestMatch;
}
