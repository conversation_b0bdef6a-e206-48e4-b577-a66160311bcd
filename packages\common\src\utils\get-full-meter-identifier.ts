import type { Mpan } from "@prisma/client";
import { constructMpan } from "./mpan";

type FullMpan = Pick<Mpan, "value" | "profileClass" | "mtc" | "lineLossFactor">;

export function getFullMeterIdentifier<
  TMeterIdentifer extends FullMpan | string
>(meterIdentifier: TMeterIdentifer | undefined, bottomLine?: string) {
  if (!meterIdentifier) {
    return bottomLine ?? "";
  }

  if (typeof meterIdentifier !== "string") {
    if (
      !meterIdentifier ||
      !meterIdentifier.profileClass ||
      !meterIdentifier.mtc ||
      !meterIdentifier.lineLossFactor
    ) {
      return bottomLine ?? "";
    }
    const meterConstruct = constructMpan({
      mpanBottom: meterIdentifier.value,
      profileClass: meterIdentifier.profileClass,
      mtc: meterIdentifier.mtc,
      llf: meterIdentifier.lineLossFactor
    });

    return meterConstruct.mpan;
  }

  return meterIdentifier;
}
