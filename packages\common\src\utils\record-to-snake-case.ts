/**
 * Convert a record to snake_case.
 * @param obj - The object to convert.
 * @returns The converted object.
 */
export function recordToSnakeCase(obj: unknown): unknown {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => recordToSnakeCase(item));
  }

  return Object.entries(obj).reduce<Record<string, unknown>>(
    (acc, [key, value]) => {
      const snakeKey = convertToSnake(key);
      acc[snakeKey] = recordToSnakeCase(value);
      return acc;
    },
    {}
  );
}

/**
 * Convert a string to snake_case.
 * @param str - The string to convert.
 * @returns The converted string.
 */
function convertToSnake(str: string): string {
  return (
    str
      // Insert underscore between lower-to-upper transitions: fooBar → foo_Bar
      .replace(/([a-z0-9])([A-Z])/g, "$1_$2")
      // Insert underscore between two uppers when second is followed by lower: XMLParser → XML_Parser
      .replace(/([A-Z])([A-Z][a-z])/g, "$1_$2")
      // Replace hyphens with underscores
      .replace(/-/g, "_")
      .toLowerCase()
  );
}
