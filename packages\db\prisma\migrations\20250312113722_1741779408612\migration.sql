-- AlterTable
ALTER TABLE "public"."company_files" ADD COLUMN     "deletedAt" TIMESTAMP(3),
ADD COLUMN     "deletedById" UUID;

-- CreateIndex
CREATE INDEX "company_file_deleted_by_id_idx" ON "public"."company_files"("deletedById");

-- AddForeignKey
ALTER TABLE "public"."company_files" ADD CONSTRAINT "company_files_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "public"."profile"("userId") ON DELETE SET NULL ON UPDATE CASCADE;
