-- AlterTable
ALTER TABLE "public"."entity_address" ADD COLUMN     "addressLine3" TEXT;

-- CreateTable
CREATE TABLE "public"."customer_address_submissions" (
    "id" TEXT NOT NULL,
    "guid" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "postcode" TEXT NOT NULL,
    "county" TEXT,
    "postalTown" TEXT,
    "country" TEXT,
    "displayName" TEXT,
    "addressLine1" TEXT,
    "addressLine2" TEXT,
    "addressLine3" TEXT,
    "houseName" TEXT,
    "houseNumber" TEXT,
    "flatNumber" TEXT,

    CONSTRAINT "customer_address_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "customer_address_submissions_guid_key" ON "public"."customer_address_submissions"("guid");
