-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_direction_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_parent_call_sid_date_created_direction_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_parent_call_sid_date_created_duration_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_parent_call_sid_date_created_status_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_parent_sid_to_from_date_created_direction_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_parent_sid_to_from_date_created_duration_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_parent_sid_to_from_date_created_status_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."call_instance_status_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."idx_commercial_delphi_score_company_number";

-- DropIndex
DROP INDEX IF EXISTS "public"."electric_site_meter_mpan_value_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."address_address_id_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."address_postcode_id_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."entity_address_display_name_id_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."entity_address_postcode_id_idx";

-- DropIndex
DROP INDEX IF EXISTS "public"."gas_site_meter_mprn_value_idx";

-- CreateIndex
CREATE INDEX "activity_company_date_idx" ON "public"."activity_log"("companyId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "call_instance_status_dateCreated_idx" ON "public"."call_instance"("status", "dateCreated");

-- CreateIndex
CREATE INDEX "callback_user_status_time_idx" ON "public"."callbacks"("createdById", "status", "callbackTime");

-- CreateIndex
CREATE INDEX "callback_site_status_time_idx" ON "public"."callbacks"("companySiteId", "status", "callbackTime");

-- CreateIndex
CREATE INDEX "delphi_company_date_idx" ON "public"."commercial_delphi_score"("companyNumber", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "company_contact_addresses_contactId_idx" ON "public"."company_contact_addresses"("contactId");

-- CreateIndex
CREATE INDEX "company_contact_emails_contactId_idx" ON "public"."company_contact_emails"("contactId");

-- CreateIndex
CREATE INDEX "company_contact_phone_numbers_contactId_idx" ON "public"."company_contact_phone_numbers"("contactId");

-- CreateIndex
CREATE INDEX "electric_site_meter_mpan_value_site_meter_id_idx" ON "public"."electric_site_meter"("mpanValue", "siteMeterId");

-- CreateIndex
CREATE INDEX "gas_site_meter_mprn_value_site_meter_id_idx" ON "public"."gas_site_meter"("mprnValue", "siteMeterId");

-- CreateIndex
CREATE INDEX "quote_list_dedupe_idx" ON "public"."quote_list"("siteMeterId", "contractStartDate", "upliftRate", "isCustomQuotesOnly");

-- RenameIndex
ALTER INDEX "public"."call_instance_date_created_idx" RENAME TO "call_instance_dateCreated_idx";

-- RenameIndex
ALTER INDEX "public"."call_instance_parent_call_sid_date_created_idx" RENAME TO "call_instance_parentCallSid_dateCreated_idx";

-- RenameIndex
ALTER INDEX "public"."call_instance_parent_call_sid_to_from_date_created_idx" RENAME TO "call_instance_parentCallSid_to_from_dateCreated_idx";

-- -- CreateIndex
CREATE INDEX "entity_address_uprn_idx" ON "public"."entity_address"("uprn");
