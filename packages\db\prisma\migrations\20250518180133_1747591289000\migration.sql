-- 1. Rename columns that genuinely changed
ALTER TABLE "public"."provider" RENAME COLUMN "name" TO "displayName";
ALTER TABLE "public"."provider" RENAME COLUMN "smartMeterRequired" TO "isSmartMeterRequired";

-- 2. Preserve existing mpid data, then swap to mpids[]
ALTER TABLE "public"."provider"
  ADD COLUMN "mpids" TEXT[] NOT NULL DEFAULT ARRAY[]::TEXT[];

UPDATE "public"."provider"
  SET "mpids" = ARRAY["mpid"]           -- copy old scalar value
WHERE "mpid" IS NOT NULL;

ALTER TABLE "public"."provider" DROP COLUMN "mpid";

-- 3. Add new columns required by the latest model
ALTER TABLE "public"."provider"
  ADD COLUMN "isUDQuotable"                     BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN "requiresPortalForWrittenContract" BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN "isVisibleInQuoteApp"                BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN "electricWrittenContractTemplateKey" TEXT,
  ADD COLUMN "gasWrittenContractTemplateKey"      TEXT;

-- 4. Ensure utilitiesProvided cannot be NULL (only if it was nullable before)
ALTER TABLE "public"."provider"
  ALTER COLUMN "utilitiesProvided" SET NOT NULL;
