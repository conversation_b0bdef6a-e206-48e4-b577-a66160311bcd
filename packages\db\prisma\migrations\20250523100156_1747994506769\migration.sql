/*
  Warnings:

  - You are about to drop the column `isSigned` on the `deals` table. All the data in the column will be lost.
  - Added the required column `createdById` to the `company_banking_details` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."company_banking_details" ADD COLUMN     "createdById" UUID NOT NULL;

-- AlterTable
ALTER TABLE "public"."deals" DROP COLUMN "isSigned",
ADD COLUMN     "comments" TEXT,
ADD COLUMN     "ipAddress" TEXT,
ADD COLUMN     "signature" TEXT,
ADD COLUMN     "signedAt" TIMESTAMP(3);

-- AddForeignKey
ALTER TABLE "public"."company_banking_details" ADD CONSTRAINT "company_banking_details_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "public"."profile"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;
