-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "public"."NotificationType" ADD VALUE 'CURRENT_SUPPLIER_INFORMATION_EMAIL';
ALTER TYPE "public"."NotificationType" ADD VALUE 'QUOTE_SIGNUP_EMAIL';
ALTER TYPE "public"."NotificationType" ADD VALUE 'SIGN_CONTRACT_EMAIL';

-- DropIndex
DROP INDEX "public"."contract_contract_id_idx";

-- AlterTable
ALTER TABLE "public"."contract" ADD COLUMN     "contactId" TEXT;

-- AlterTable
ALTER TABLE "public"."notifications" ADD COLUMN     "contactId" TEXT;

-- CreateIndex
CREATE INDEX "contract_contact_id_quote_id_idx" ON "public"."contract"("contactId", "quoteId");

-- CreateIndex
CREATE INDEX "notification_callback_id_idx" ON "public"."notifications"("callbackId");

-- CreateIndex
CREATE INDEX "notification_contact_id_type_idx" ON "public"."notifications"("contactId", "type");

-- AddForeignKey
ALTER TABLE "public"."contract" ADD CONSTRAINT "contract_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "public"."company_contact"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."notifications" ADD CONSTRAINT "notifications_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "public"."company_contact"("id") ON DELETE SET NULL ON UPDATE CASCADE;
