import { prisma } from "../../src/client";
import { supabaseAdmin } from "../../src/supabase/supabase";

export async function seedMissingUsersMetadata() {
  const {
    data: { users }
  } = await supabaseAdmin.auth.admin.listUsers({
    perPage: 1000
  });

  const userIds = users
    .filter(u => JSON.stringify(u.user_metadata) === "{}")
    .map(u => u.id);

  const usersInDb = await prisma.profile.findMany({
    where: {
      userId: {
        in: userIds
      }
    }
  });

  const usersMetadata = usersInDb.map(u => {
    return {
      id: u.userId,
      metadata: {
        email: u.email,
        forename: u.forename,
        surname: u.surname,
        directDial: u.directDial,
        role: u.role,
        huntGroups: u.huntGroups,
        disabled: u.disabled
      }
    };
  });

  for (const user of usersMetadata) {
    await supabaseAdmin.auth.admin.updateUserById(user.id, {
      user_metadata: user.metadata
    });
  }
}
