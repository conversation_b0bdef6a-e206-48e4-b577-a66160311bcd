import fs from "node:fs";
import path from "node:path";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { ONE_HOUR_SEC } from "@watt/common/src/constants/time-durations";
import { isProductionEnvironment } from "@watt/common/src/utils/is-production-environment";
import { writtenContractTemplates } from "@watt/db/constants/providers/providers-sold-by-us";
import { supabaseAdmin } from "../../src/supabase/supabase";

async function uploadWrittenContractPDF(templateName: string) {
  const filePath = path.resolve(
    process.cwd(),
    "../../data/pdfs/written-contracts",
    `${templateName}.pdf`
  );

  try {
    const fileData = fs.readFileSync(filePath);

    const { error } = await supabaseAdmin.storage
      .from(STORAGE_BUCKETS.WRITTEN_CONTRACT_TEMPLATES)
      .upload(`${templateName}.pdf`, fileData, {
        contentType: "application/pdf",
        cacheControl: String(ONE_HOUR_SEC),
        upsert: true
      });

    if (error) {
      console.error(`Error uploading ${templateName}:`, error);
    }
  } catch (error) {
    console.error(`Error reading or uploading ${templateName}:`, error);
  }
}

export async function uploadWrittenContractPDFs() {
  for (const { templateName } of writtenContractTemplates) {
    await uploadWrittenContractPDF(templateName);
  }

  await uploadWrittenContractPDF("audit-page");
}

export async function clearAllWrittenContractFiles() {
  if (isProductionEnvironment()) {
    throw new Error(
      "This function should not be run in production. It will attempt to delete all written contract files."
    );
  }

  const { data: files, error: listError } = await supabaseAdmin.storage
    .from(STORAGE_BUCKETS.WRITTEN_CONTRACT_TEMPLATES)
    .list();

  if (listError) {
    console.error("Error listing files:", listError);
    return;
  }

  for (const file of files) {
    const { error: deleteError } = await supabaseAdmin.storage
      .from(STORAGE_BUCKETS.WRITTEN_CONTRACT_TEMPLATES)
      .remove([file.name]);

    if (deleteError) {
      console.error(`Error deleting file ${file.name}:`, deleteError);
    }
  }
}
