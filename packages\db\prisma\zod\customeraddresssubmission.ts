import * as z from "zod"
import * as imports from "../zod-schemas"

export const CustomerAddressSubmissionModel = z.object({
  /**
   * The unique identifier for this CustomerAddressSubmission resource.
   */
  id: z.string(),
  /**
   * External GUID for the address.
   */
  guid: z.string(),
  /**
   * The postal code of the address.
   */
  postcode: z.string(),
  /**
   * The county where the address is located.
   */
  county: z.string().nullish(),
  /**
   * The postal town of the address.
   */
  postalTown: z.string().nullish(),
  /**
   * Country of the address.
   */
  country: z.string().nullish(),
  /**
   * Detailed Address string.
   */
  displayName: z.string().nullish(),
  /**
   * EntityAddress line 1 detail.
   */
  addressLine1: z.string().nullish(),
  /**
   * Additional address line 2 detail.
   */
  addressLine2: z.string().nullish(),
  /**
   * Additional address line 3 detail.
   */
  addressLine3: z.string().nullish(),
  /**
   * Name of the house.
   */
  houseName: z.string().nullish(),
  /**
   * House Number associated with the address.
   */
  houseNumber: z.string().nullish(),
  /**
   * Flat number or identifier in case of a multi-dwelling unit.
   */
  flatNumber: z.string().nullish(),
})
