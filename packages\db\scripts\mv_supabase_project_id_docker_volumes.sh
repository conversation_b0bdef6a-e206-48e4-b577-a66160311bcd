#!/bin/bash
# Run this script from the packages/db/scripts directory

# Function to extract project_id from a TOML file or piped input
extract_project_id() {
  if [ -p /dev/stdin ]; then
    grep '^project_id' | sed 's/project_id = "\(.*\)"/\1/'
  else
    grep '^project_id' "$1" | sed 's/project_id = "\(.*\)"/\1/'
  fi
}

# Configuration
SCRIPT_DIR=$(dirname "$0")  # Gets the directory of the script (packages/db/scripts)
CONFIG_FILE="$SCRIPT_DIR/../supabase/config.toml"  # Path to config.toml (packages/db/supabase/config.toml)
VOLUME_PREFIXES=(
  "supabase_studio"
  "supabase_pg_meta"
  "supabase_edge_runtime"
  "storage_imgproxy"
  "supabase_storage"
  "supabase_rest"
  "realtime-dev.supabase_realtime"
  "supabase_inbucket"
  "supabase_auth"
  "supabase_kong"
  "supabase_db"
  "supabase_config"
)

# Debugging: Print the script directory and config file path
echo "Script directory: $SCRIPT_DIR"
echo "Looking for config.toml at: $CONFIG_FILE"

# Check if config.toml exists
if [ ! -f "$CONFIG_FILE" ]; then
  echo "Error: $CONFIG_FILE not found!"
  exit 1
else
  echo "Config file found successfully."
fi

# Get current project_id
current_project_id=$(extract_project_id "$CONFIG_FILE")
if [ -z "$current_project_id" ]; then
  echo "Error: project_id not found in $CONFIG_FILE"
  exit 1
fi

# Get active/previous project_id by inspecting Docker containers
# Use label filter for the known old project ID 'db' for reliability
echo "Attempting to find an existing container from project 'db' to determine the active project ID..."
found_container=$(docker ps -a --filter "label=com.docker.compose.project=db" --format '{{.Names}}' | head -n 1)

if [ -n "$found_container" ]; then
  # If found, inspect it to get the project label
  echo "Found container: $found_container. Inspecting for project label..."
  # Execute inspect command, handle potential errors or missing labels
  # Store the result in previous_project_id to match the rest of the script
  previous_project_id=$(docker inspect --format '{{ index .Config.Labels "com.docker.compose.project" }}' "$found_container" 2>/dev/null)

  # Check if the inspect command succeeded and found a non-empty label
  if [ $? -eq 0 ] && [ -n "$previous_project_id" ]; then
    echo "Found active project ID from Docker label: $previous_project_id"
  else
    # Handle cases where inspect fails or label is empty/missing
    echo "Warning: Found container $found_container, but failed to get a valid project label. Assuming no active project." >&2
    previous_project_id=""
  fi
else
  # If no container was found matching the filter
  echo "No container found starting with 'supabase_db'. Assuming no active project."
  previous_project_id=""
fi

# Determine if volume handling is needed
if [ "$current_project_id" == "$previous_project_id" ] || [ -z "$previous_project_id" ]; then
  echo "No change in project_id or no previous project_id found. Skipping volume handling."
  need_to_handle_volumes=false
else
  need_to_handle_volumes=true
fi

# Handle volumes if necessary
if [ "$need_to_handle_volumes" = true ]; then
  # Stop containers using the previous project_id to free volumes
  if [ ! -z "$previous_project_id" ]; then
    # Stop Supabase with the previous project_id
    echo "Stopping Supabase with project_id '$previous_project_id'..."
    pnpm -C "$SCRIPT_DIR/.." run supabase:local stop
    docker stop $(docker ps -q --filter "name=supabase.*_${previous_project_id}") 2>/dev/null || true
  fi

  for prefix in "${VOLUME_PREFIXES[@]}"; do
    new_volume="${prefix}_${current_project_id}"
    old_volume="${prefix}_${previous_project_id}"

    # Check if the new volume already exists (idempotency)
    if docker volume inspect "$new_volume" > /dev/null 2>&1; then
      echo "Volume $new_volume already exists. Skipping creation and copying."
    else
      echo "Creating volume $new_volume"
      docker volume create "$new_volume"

      # Copy data from old volume if it exists
      if docker volume inspect "$old_volume" > /dev/null 2>&1; then
        echo "Copying data from $old_volume to $new_volume"
        docker run --rm -v "$old_volume:/from" -v "$new_volume:/to" alpine ash -c "cp -a /from/. /to/"
      else
        echo "Old volume $old_volume does not exist. Creating new volume without copying."
      fi
    fi
  done
fi

# Start Supabase with the current project_id
echo "Starting Supabase with project_id '$current_project_id'..."
pnpm -C "$SCRIPT_DIR/.." run supabase:local start
start_status=$?

# Handle --destroy option
if [ "$1" == "--destroy" ]; then
  # Safety check: Only proceed if Supabase started successfully AND the *active* project ID matches the desired one
  echo "Checking current state before attempting destroy..."

  # Re-check the active project ID after the start attempt
  # Filter for the *new* expected database container name
  active_container_after_start=$(docker ps -a --filter "name=^supabase_db_${current_project_id}" --format '{{.Names}}' | head -n 1)
  currently_active_id_after_start=""
  if [ -n "$active_container_after_start" ]; then
    currently_active_id_after_start=$(docker inspect --format '{{ index .Config.Labels "com.docker.compose.project" }}' "$active_container_after_start" 2>/dev/null)
  fi

  # Condition 1: Supabase start command must have succeeded (exit code 0)
  # Condition 2: The currently active project ID must match the ID from config.toml
  # Condition 3: There must have been a *different* previous project ID to clean up
  if [ $start_status -eq 0 ] && [ "$currently_active_id_after_start" == "$current_project_id" ] && [ -n "$previous_project_id" ] && [ "$current_project_id" != "$previous_project_id" ]; then
    echo "Confirmation: Supabase is running with the correct project ID ('$current_project_id'). Proceeding to check for old resources ('$previous_project_id')."

    # Find old volumes and containers associated with the previous_project_id
    echo "Searching for resources associated with old project ID: $previous_project_id"
    # Construct the regex pattern carefully for volumes
    prefixes_pattern=$(IFS='|'; echo "${VOLUME_PREFIXES[*]}")
    # Ensure grep matches the full volume name ending correctly
    old_volumes=$(docker volume ls -q --filter "name=_${previous_project_id}$" | grep -E "^(${prefixes_pattern})_${previous_project_id}$")
    # Find containers associated with the old project ID
    old_containers=$(docker ps -a -q --filter "label=com.docker.compose.project=${previous_project_id}")

    if [ -z "$old_volumes" ] && [ -z "$old_containers" ]; then
      echo "No old volumes or containers found for project_id '$previous_project_id'. Nothing to destroy."
    else
      echo "The following old resources associated with '$previous_project_id' will be removed:"
      if [ -n "$old_volumes" ]; then echo "**Volumes:**"; echo "$old_volumes"; fi
      if [ -n "$old_containers" ]; then echo "**Containers:**"; echo "$old_containers"; fi

      read -p "Are you sure you want to remove them? Type 'Y' to proceed: " confirm

      if [ "$confirm" == "Y" ]; then
        # Remove containers first
        if [ ! -z "$old_containers" ]; then
          echo "Removing old containers..."
          docker rm -f $old_containers
        fi
        # Remove volumes
        if [ ! -z "$old_volumes" ]; then
          echo "Removing old volumes..."
          docker volume rm $old_volumes
        fi
        echo "Old resources for '$previous_project_id' removed successfully."
      else
        echo "Aborted. No changes made."
      fi
    fi
  else
    # Explain why cleanup is skipped
    echo "Skipping cleanup for --destroy flag."
    if [ $start_status -ne 0 ]; then
        echo "Reason: Supabase start command failed."
    elif [ "$currently_active_id_after_start" != "$current_project_id" ]; then
        echo "Reason: Currently active project ID ('$currently_active_id_after_start') does not match the target ID ('$current_project_id')."
    elif [ -z "$previous_project_id" ] || [ "$current_project_id" == "$previous_project_id" ]; then
        echo "Reason: No different previous project ID ('$previous_project_id') was detected to clean up."
    fi
    echo "Please ensure Supabase is running correctly with project ID '$current_project_id' before attempting cleanup manually if needed."
  fi
fi

exit 0
