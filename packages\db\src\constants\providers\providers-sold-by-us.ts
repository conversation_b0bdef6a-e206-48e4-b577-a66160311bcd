// TODO (<PERSON>): need to refactor the clients use of this data to go via a tRPC route / prisma query.
// Do not use the data or the utility functions in this file in client code. It will expose all of the provider data to the client.
// Instead use prisma and get the data from the database and expose it via tRPC routes.
//import "server-only";

import { type Prisma, UtilityType } from "@prisma/client";

/**
 * TODO this should not be used on the client. The client should be forced to retrieve the data it needs for a specific supplier or list of them
 * via tRPC and Prisma. If the client uses this object directly then it will be bundled in the client and fully exposed.
 */
export const providersSoldByUs = [
  {
    id: "de483124-d958-4302-ac76-2c68924bb057",
    displayName: "Valda Energy",
    udcoreId: "Valda Energy",
    mpids: ["SCAF"],
    reccoId: "Valda Energy Limited",
    logoFileName: "valda",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "Valda_Electric",
    gasWrittenContractTemplateKey: "Valda_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: false
  },
  {
    id: "d993aa82-4b22-41e1-8f63-30ad676526b2",
    displayName: "British Gas",
    udcoreId: "British Gas",
    mpids: [
      "BGAS",
      "BIZZ",
      "ENRD",
      "OGAS",
      "TILL",
      "ARIZ",
      "CMAS",
      "ECOA",
      "SANT",
      "IRID"
    ],
    reccoId: "British Gas Trading Limited", // Stephen: Not 100% sure this is correct
    logoFileName: "british-gas",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "BG_Core_Electric",
    gasWrittenContractTemplateKey: "BG_Core_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: false
  },
  {
    id: "0af8db2f-c1f5-4d06-acb8-222d1df238cd",
    displayName: "British Gas Lite",
    udcoreId: "British Gas Lite",
    mpids: [],
    reccoId: "British Gas Trading Limited2", // Stephen: Not 100% sure this is correct
    logoFileName: "british-gas-lite",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "BG_Lite_Electric",
    gasWrittenContractTemplateKey: "BG_Lite_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: false
  },
  {
    id: "f03ff477-0397-4fcd-8080-11988c757dd8",
    displayName: "British Gas Corp",
    udcoreId: "British Gas Corp",
    mpids: [],
    reccoId: "British Gas Business",
    logoFileName: "british-gas",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: false,
    isEnabled: false,
    electricWrittenContractTemplateKey: "BG_Core_Electric",
    gasWrittenContractTemplateKey: "BG_Core_Gas",
    isVisibleInQuoteApp: false,
    requiresPortalForWrittenContract: true
  },
  {
    id: "6cc06037-ef59-4546-a340-facb62420cd5",
    displayName: "Scottish Power",
    udcoreId: "Scottish Power",
    mpids: ["MANW", "SPOW"],
    reccoId: "Scottish Power Energy Retail Limited",
    logoFileName: "scottish-power",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    isDualFuel: true,
    electricWrittenContractTemplateKey: "Scottish_Power_Electric",
    gasWrittenContractTemplateKey: "Scottish_Power_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "5e0e08c8-1889-435e-aafa-88ef629b53cb",
    displayName: "Corona Energy",
    udcoreId: "Corona Energy",
    mpids: ["THRE", "CORE"],
    reccoId: "Corona Energy Retail 4 Limited",
    logoFileName: "corona",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "Corona_Electric",
    gasWrittenContractTemplateKey: "Corona_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "36f103f0-5673-4ece-9f27-05bdafecf99f",
    displayName: "EDF",
    udcoreId: "EDF",
    mpids: ["EDFE", "LOND", "SEEB", "SWEB", "ZPYR"],
    reccoId: "EDF Energy Customers Limited",
    logoFileName: "edf",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "EDF_Electric",
    gasWrittenContractTemplateKey: "EDF_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "c9ce0f46-87ac-4c7d-8d4d-1d5fa148eafd",
    displayName: "Yorkshire Gas And Power",
    udcoreId: "Yorkshire Gas And Power",
    mpids: ["EGML"],
    reccoId: "EE Solutions Limited",
    logoFileName: "yorkshire-gas-and-power",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "YGP_Electric",
    gasWrittenContractTemplateKey: "YGP_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "3c50f4ec-70c3-4c61-87f2-f149f0d12607",
    displayName: "Smartest Energy",
    udcoreId: "Smartest Energy",
    mpids: ["SMAR", "SMAE"],
    reccoId: "Smartestenergy Business Ltd",
    logoFileName: "smartest",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "Smartest_Electric",
    gasWrittenContractTemplateKey: "Smartest_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: false
  },
  {
    id: "23ce701f-84d1-4327-a54c-b3ce2a0abb1f",
    displayName: "E.On Next",
    udcoreId: "E.On Next",
    mpids: [
      "EOND",
      "EMEB",
      "ECON",
      "EELC",
      "EESL",
      "EPHH",
      "INDE",
      "MIDE",
      "NATP",
      "NEEB",
      "NORW",
      "PGEN",
      "PSUK",
      "ROSE",
      "SYMB",
      "YELG"
    ],
    reccoId: "E.ON Next Energy Limited",
    logoFileName: "e-on",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "EON_Electric",
    gasWrittenContractTemplateKey: "EON_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "064d4f0f-14b9-4f5f-bcd0-84272de976d7",
    displayName: "Yu Energy",
    udcoreId: "Yu Energy",
    mpids: ["KENS"],
    reccoId: "YU ENERGY RETAIL LIMITED",
    logoFileName: "yu",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "Yu_Electric",
    gasWrittenContractTemplateKey: "Yu_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "859a3c9e-1a92-41ab-a5fa-ca52eb9292dd",
    displayName: "Dyce Energy",
    udcoreId: "Dyce Energy",
    mpids: ["PROZ"],
    reccoId: null,
    logoFileName: "dyce",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "Dyce_Electric",
    gasWrittenContractTemplateKey: "Dyce_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "a2e2b617-3a0d-4bde-b82e-f4ce9c80a352",
    displayName: "Total Energy",
    udcoreId: "TotalEnergies",
    mpids: ["RUTH", "TGPL"],
    reccoId: "TOTALENERGIES GAS & POWER LIMITED",
    logoFileName: "total",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "Total_Electric",
    gasWrittenContractTemplateKey: "Total_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: false
  },
  {
    id: "75e60664-79e0-4366-8e17-eee65ec7018b",
    displayName: "SSE",
    udcoreId: "SSE",
    mpids: [],
    reccoId: null,
    logoFileName: "scottish-and-southern",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: "SSE_Electric",
    gasWrittenContractTemplateKey: "SSE_Gas",
    isVisibleInQuoteApp: true,
    requiresPortalForWrittenContract: true
  },
  {
    id: "74b66fce-8db5-4509-b047-2381716ff8d6",
    displayName: "Utilita",
    udcoreId: "Utilita",
    mpids: ["UMOL", "GETW", "UEDS", "EVER"],
    reccoId: "Utilita Energy Limited",
    logoFileName: "utilita",
    utilitiesProvided: [UtilityType.ELECTRICITY, UtilityType.GAS],
    isUDQuotable: true,
    isEnabled: true,
    electricWrittenContractTemplateKey: null,
    gasWrittenContractTemplateKey: null,
    isVisibleInQuoteApp: false,
    requiresPortalForWrittenContract: true
  },
  {
    id: "923de41b-139b-443b-a606-ace3049fb731",
    displayName: "Other",
    udcoreId: "Other",
    mpids: [],
    reccoId: null,
    logoFileName: null,
    utilitiesProvided: [],
    isEnabled: true,
    electricWrittenContractTemplateKey: null,
    gasWrittenContractTemplateKey: null,
    isUDQuotable: false,
    isVisibleInQuoteApp: false,
    requiresPortalForWrittenContract: false
  }
] satisfies Prisma.ProviderCreateManyInput[];

export type SeededProvider = (typeof providersSoldByUs)[number];

export function getProviderByMpid(mpid: string): SeededProvider | undefined {
  return providersSoldByUs.find(provider =>
    provider.mpids.some(m => m === mpid)
  );
}

export function getProviderByUdcoreId(
  udcoreId: string
): SeededProvider | undefined {
  return providersSoldByUs.find(provider => provider.udcoreId === udcoreId);
}

export function getProviderByReccoId(
  reccoId: string
): SeededProvider | undefined {
  return providersSoldByUs.find(provider => provider.reccoId === reccoId);
}

export function getSoldByUsProviders(): Pick<
  SeededProvider,
  "id" | "displayName"
>[] {
  return providersSoldByUs
    .filter(provider => provider.isEnabled === true)
    .map(({ id, displayName }) => ({ id, displayName }));
}

export const providersByUdcoreId = Object.fromEntries(
  providersSoldByUs.map(p => [p.udcoreId, p] as const)
) satisfies Record<string, (typeof providersSoldByUs)[number]>;

export function getWrittenContractTemplateKeyByUdCoreId(
  udcoreId: string,
  utility: UtilityType
): string | null {
  const provider = providersByUdcoreId[udcoreId];
  if (!provider) {
    return null;
  }

  return utility === UtilityType.ELECTRICITY
    ? (provider.electricWrittenContractTemplateKey ?? null)
    : (provider.gasWrittenContractTemplateKey ?? null);
}

export const writtenContractTemplates = providersSoldByUs.flatMap(p => {
  const list: { templateName: string; utility: UtilityType }[] = [];

  if (p.electricWrittenContractTemplateKey) {
    list.push({
      templateName: p.electricWrittenContractTemplateKey,
      utility: UtilityType.ELECTRICITY
    });
  }
  if (p.gasWrittenContractTemplateKey) {
    list.push({
      templateName: p.gasWrittenContractTemplateKey,
      utility: UtilityType.GAS
    });
  }
  return list;
});
