import { providersNotSoldByUs } from "@watt/db/constants/providers/providers-not-sold-by-us";
import { providersSoldByUs } from "./providers-sold-by-us";

type AllSeededProvider = (
  | typeof providersSoldByUs
  | typeof providersNotSoldByUs
)[number];

export const providers: AllSeededProvider[] = [
  ...providersSoldByUs,
  // We need to seed the other providers we validate the users current provider against the db.
  // the QuoteList has `currentProvider` and so this is a required relationship.
  ...providersNotSoldByUs
];
