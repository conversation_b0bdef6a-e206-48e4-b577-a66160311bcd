import type { Profile, UserRole } from "@prisma/client";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { SupabaseSafeUser } from "@watt/common/src/libs/supabase/role-helpers";
import { phoneNumberToE164 } from "@watt/common/src/utils/phone-number-to-e164";
import { getUserPermissions } from "./role-permissions";
import { supabaseAdmin } from "./supabase";
import { SupabaseSafeSession } from "./supabase-safe-session";
import type {
  SupabaseProfileData,
  SupabaseSafeUserWithProfile
} from "./user-type";

export const placeholderUserData: SupabaseSafeUser = {
  id: "",
  session_id: "",
  app_metadata: {},
  user_metadata: {},
  email: "",
  phone: "",
  role: ""
};

export const defaultUserData = {
  user: null,
  profileData: null,
  permissions: {
    isAuthorised: false,
    isAdmin: false,
    isSalesAgent: false,
    isManager: false,
    isAllowedToEditPriceList: false,
    isSystemUser: false
  }
} satisfies SupabaseSafeUserWithProfile;

/**
 * Fetch function: obtains the current Supabase user.
 * @returns SupabaseSafeUser | null
 */
async function getUser(
  supabase?: SupabaseClient
): Promise<SupabaseSafeUser | null> {
  const client = supabase ?? supabaseAdmin;

  const safeSession = new SupabaseSafeSession(client);
  const { data: user, error: safeSessionError } = await safeSession.getUser();

  return user; // SupabaseSafeUser | null
}

/**
 * Use on the server to get the supabase user object with the metadata properly typed
 * and the permissions set. This is safe and does not require trusting request cookies.
 * @returns {UserWithProfileMetaData}
 */
export async function getSupabaseUser(
  client?: SupabaseClient
): Promise<SupabaseSafeUserWithProfile> {
  // TODO (Stephen): Lacks try catch and error handling

  const user = await getUser(client);

  if (!user?.email) {
    return defaultUserData;
  }

  const userMetaDataWithProfileData = {
    userId: user.id,
    email: user.email,
    forename: user.user_metadata.forename,
    surname: user.user_metadata.surname,
    directDial: user.user_metadata.directDial,
    directDialE164: user.user_metadata.directDial
      ? phoneNumberToE164(user.user_metadata.directDial).phoneNumber
      : null,
    role: user.user_metadata.role as UserRole,
    huntGroups: user.user_metadata.huntGroups,
    disabled: user.user_metadata.disabled
  } satisfies SupabaseProfileData;

  const {
    isAllowedToEditPriceList,
    isAuthorised,
    isAdmin,
    isSalesAgent,
    isManager,
    isSystemUser
  } = getUserPermissions(userMetaDataWithProfileData?.role, user.email);

  const userWithProfileData: SupabaseSafeUserWithProfile = {
    user,
    profileData: userMetaDataWithProfileData as Profile,
    permissions: {
      isAuthorised,
      isAdmin,
      isSalesAgent,
      isManager,
      isAllowedToEditPriceList,
      isSystemUser
    }
  };

  return userWithProfileData;
}
