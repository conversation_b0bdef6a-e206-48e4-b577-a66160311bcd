import type { StorageBucket } from "@watt/common/src/constants/storage-buckets";
import { supabaseAdmin } from "./supabase";

/**
 * @deprecated Should no longer be needed in environments
 * where the new seeding script has been run.
 * @todo Remove this altogether upon confirmation that the
 * new seeding script has been run in all environments.
 */
export async function ensureBucketExists(
  bucketName: StorageBucket
): Promise<void> {
  const { error: bucketExistsError } =
    await supabaseAdmin.storage.getBucket(bucketName);

  if (bucketExistsError && bucketExistsError.message === "Bucket not found") {
    const { error: createBucketError } =
      await supabaseAdmin.storage.createBucket(bucketName, {
        public: false
      });

    if (createBucketError) {
      throw new Error(`Error creating bucket: ${createBucketError.message}`);
    }
  } else if (bucketExistsError) {
    throw new Error(
      `Error checking bucket existence: ${bucketExistsError.message}`
    );
  }
}

/**
 * Allows us to get a proxy url for the Supabase storage service.
 * Changes urls of the form;
 * http://localhost:54321/storage/v1/object/sign/email-attachments/hDO_Qs-9ToGssXNXND2sMw/quote.pdf?token={token}
 * to
 * http://localhost:3000/api/storage/v1/object/sign/email-attachments/hDO_Qs-9ToGssXNXND2sMw/quote.pdf?token={token}
 * @param bucketName The name of the bucket
 * @param path The path to the object
 * @param expiresIn The time in seconds until the signed URL expires
 * @returns proxied signed storage url
 */
export async function getStorageProxyUrl(
  bucketName: StorageBucket,
  path: string,
  expiresIn: number
): Promise<string | null> {
  const { data, error } = await supabaseAdmin.storage
    .from(bucketName)
    .createSignedUrl(path, expiresIn);

  if (error) {
    throw new Error(`Error generating signed URL: ${error.message}`);
  }

  if (data?.signedUrl) {
    const signedUrl = new URL(data.signedUrl);

    const token = signedUrl.searchParams.get("token");

    const proxyUrl = `/api/storage/v1/object/sign/${bucketName}/${path}?token=${token}`;

    return proxyUrl;
  }

  return null;
}
