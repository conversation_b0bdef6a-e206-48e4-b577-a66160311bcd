import { createClient } from "@supabase/supabase-js";
import { env } from "@watt/common/src/config/env";

/**
 * Supabase admin client for interacting with the Supabase API.
 * Uses the PRIVATE service role key to authenticate.
 * This should only be used for administrative tasks, background jobs,
 * and Inngest functions that don't have access to user sessions.
 */
export const supabaseAdmin = createClient(
  env.NEXT_PUBLIC_SUPABASE_URL,
  env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: true
    }
  }
);
