import type { Prisma } from "@prisma/client";
import type { User } from "@supabase/supabase-js";
import type { SupabaseSafeUser } from "@watt/common/src/libs/supabase/role-helpers";

const supabaseMetaDataProfile = {
  userId: true,
  email: true,
  role: true,
  forename: true,
  surname: true,
  directDial: true,
  directDialE164: true,
  huntGroups: true,
  disabled: true
} satisfies Prisma.ProfileSelect;

export type SupabaseProfileData = Prisma.ProfileGetPayload<{
  select: typeof supabaseMetaDataProfile;
}>;

export type UserWithProfileMetaData = {
  user: Omit<User, "user_metadata"> | null;
  profileData: SupabaseProfileData | null;
  permissions: {
    isAuthorised: boolean;
    isAdmin: boolean;
    isSalesAgent: boolean;
    isManager: boolean;
    isAllowedToEditPriceList: boolean;
    isSystemUser: boolean;
  };
};

export type SupabaseSafeUserWithProfile = {
  user: SupabaseSafeUser | null;
  profileData: SupabaseProfileData | null;
  permissions: {
    isAuthorised: boolean;
    isAdmin: boolean;
    isSalesAgent: boolean;
    isManager: boolean;
    isAllowedToEditPriceList: boolean;
    isSystemUser: boolean;
  };
};
