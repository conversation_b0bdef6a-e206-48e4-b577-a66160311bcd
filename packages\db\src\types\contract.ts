import type { Prisma } from "@prisma/client";

export const findFirstQuoteSelectQuoteListPrefilledContract = {
  utilityType: true,
  duration: true,
  endDate: true,
  provider: {
    select: {
      udcoreId: true
    }
  },
  electricQuote: {
    select: {
      unitRateUplift: true,
      contractType: true,
      unitRate: true,
      nightUnitRate: true,
      weekendUnitRate: true,
      standingCharge: true,
      standingChargeUplift: true,
      annualPrice: true,
      capacityChargeKva: true
    }
  },
  gasQuote: {
    select: {
      unitRateUplift: true,
      contractType: true,
      unitRate: true,
      standingCharge: true,
      standingChargeUplift: true,
      annualPrice: true
    }
  },
  quoteList: {
    select: {
      currentProvider: {
        select: {
          udcoreId: true
        }
      },
      contractStartDate: true,
      siteMeter: {
        select: {
          id: true,
          electricSiteMeter: {
            select: {
              mpan: {
                select: {
                  value: true,
                  profileClass: true,
                  mtc: true,
                  lineLossFactor: true
                }
              }
            }
          },
          gasSiteMeter: {
            select: {
              mprn: {
                select: {
                  value: true
                }
              }
            }
          },
          companySite: {
            select: {
              id: true,
              entityAddress: {
                select: {
                  displayName: true,
                  postalTown: true,
                  county: true,
                  postcode: true,
                  addressLine1: true
                }
              },
              company: {
                select: {
                  id: true,
                  name: true,
                  businessType: true,
                  registrationNumber: true,
                  entityAddress: {
                    select: {
                      displayName: true,
                      postalTown: true,
                      county: true,
                      postcode: true,
                      addressLine1: true
                    }
                  }
                }
              }
            }
          }
        }
      },
      electricityUsage: {
        select: {
          totalUsage: true,
          capacityFigureKva: true,
          dayUsage: true,
          nightUsage: true,
          weekendUsage: true
        }
      },
      gasUsage: {
        select: {
          totalUsage: true
        }
      }
    }
  }
} satisfies Prisma.QuoteSelect;

export type FindFirstQuoteSelectQuoteListPrefilledContractGetPayload =
  Prisma.QuoteGetPayload<{
    select: typeof findFirstQuoteSelectQuoteListPrefilledContract;
  }>;

export const findFirstCompanyContactSelectCompanyContactPrefilledContract = {
  id: true,
  salutation: true,
  forename: true,
  surname: true,
  position: true,
  emails: {
    select: {
      email: true,
      isPrimary: true
    }
  },
  phoneNumbers: {
    select: {
      phoneNumber: true,
      isPrimary: true
    }
  },
  dateOfBirth: true
} satisfies Prisma.CompanyContactSelect;

export type FindFirstCompanyContactSelectCompanyContactPrefilledContractGetPayload =
  Prisma.CompanyContactGetPayload<{
    select: typeof findFirstCompanyContactSelectCompanyContactPrefilledContract;
  }>;

export const findManyCompanyContactAddressSelectCompanyContactAddressPrefilledContract =
  {
    movedInDate: true,
    movedOutDate: true,
    isCurrent: true,
    address: {
      select: {
        displayName: true,
        postalTown: true,
        county: true,
        postcode: true,
        addressLine1: true
      }
    }
  } satisfies Prisma.CompanyContactAddressSelect;

export type FindManyCompanyContactAddressSelectCompanyContactAddressPrefilledContractGetPayload =
  Prisma.CompanyContactAddressGetPayload<{
    select: typeof findManyCompanyContactAddressSelectCompanyContactAddressPrefilledContract;
  }>;

export const findFirstCompanyBankingDetailSelectCompanyBankingDetailPrefilledContract =
  {
    id: true,
    bankName: true,
    accountHolderName: true,
    accountNumber: true,
    sortCode: true
  } satisfies Prisma.CompanyBankingDetailSelect;

export type FindFirstCompanyBankingDetailSelectCompanyBankingDetailPrefilledContractGetPayload =
  Prisma.CompanyBankingDetailGetPayload<{
    select: typeof findFirstCompanyBankingDetailSelectCompanyBankingDetailPrefilledContract;
  }>;

export const findFirstQuoteSelectQuoteConfirmationDetails = {
  utilityType: true,
  endDate: true,
  electricQuote: {
    select: {
      contractType: true
    }
  },
  gasQuote: {
    select: {
      contractType: true
    }
  },
  quoteList: {
    select: {
      contractStartDate: true,
      siteMeter: {
        select: {
          companySite: {
            select: {
              id: true,
              company: {
                select: {
                  registrationNumber: true
                }
              },
              entityAddress: {
                select: {
                  id: true,
                  postcode: true,
                  displayName: true
                }
              }
            }
          },
          electricSiteMeter: {
            select: {
              mpan: {
                select: {
                  value: true
                }
              }
            }
          },
          gasSiteMeter: {
            select: {
              mprn: {
                select: {
                  value: true
                }
              }
            }
          }
        }
      }
    }
  }
} satisfies Prisma.QuoteSelect;

export const findFirstDealSelectDealConfirmationDetails = {
  signedAt: true
} satisfies Prisma.DealSelect;
