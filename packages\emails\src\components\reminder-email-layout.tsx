import { Body, Html, Img, Preview, Tailwind } from "@react-email/components";
import type { PropsWithChildren, ReactNode } from "react";
import config from "../../tailwind.config";

type ReminderEmailLayoutProps = PropsWithChildren<{
  subject: string;
  footer?: ReactNode;
}>;

export function ReminderEmailLayout({
  subject,
  children
}: ReminderEmailLayoutProps) {
  return (
    <Html lang="en">
      <Preview>{subject}</Preview>
      <Tailwind config={config}>
        {/*
        * This is a hack, the Head component is renamed during minification,
        in production react-email can not find it and throws an error
        * https://github.com/resend/react-email/issues/1112
        */}
        {/* eslint-disable-next-line @next/next/no-head-element */}
        <head>
          <meta content="text/html; charset=UTF-8" httpEquiv="Content-Type" />
          <meta name="color-scheme" content="light dark" />
          <meta name="supported-color-schemes" content="light dark" />
        </head>
        <Body
          className="bg-transparent p-0"
          style={{ wordSpacing: "normal", fontFamily: "Roboto, sans-serif" }}
        >
          <div
            role="article"
            aria-roledescription="email"
            lang="en"
            className="max-w-[640px] bg-transparent"
            style={{
              WebkitTextSizeAdjust: "100%",
              textSizeAdjust: "100%"
            }}
          >
            <table
              role="presentation"
              className="mx-auto w-[950px] max-w-[950px] border-0"
              style={{ borderSpacing: "0" }}
              cellPadding="0"
              cellSpacing="0"
            >
              <tr>
                <td align="left">
                  <div className="w-[96%]">
                    <table
                      role="presentation"
                      cellPadding="0"
                      cellSpacing="0"
                      className="w-full rounded-[18px] border-0 bg-[#f0f7f9]"
                      style={{
                        borderSpacing: "0",
                        boxShadow: "0px 4px 22px rgba(0, 0, 0, 0.1006)"
                      }}
                    >
                      <tbody>
                        <tr>
                          <td>
                            <table
                              role="presentation"
                              className="w-full border-0"
                              style={{ borderSpacing: "0" }}
                              cellPadding="0"
                              cellSpacing="0"
                            >
                              <tbody>
                                <tr className="rounded-t-[18px] bg-[#0b3f55]">
                                  <td
                                    align="left"
                                    valign="middle"
                                    className="pl-[10px]"
                                    style={{ borderTopLeftRadius: "18px" }}
                                  >
                                    <Img
                                      src="https://crm.watt.co.uk/img/email/header.png"
                                      className="h-[225px] w-[300px] align-top"
                                      alt="Header"
                                    />
                                  </td>
                                  <td
                                    align="right"
                                    valign="bottom"
                                    className="pr-[30px] align-middle"
                                    style={{ borderTopRightRadius: "18px" }}
                                  >
                                    <Img
                                      src="https://crm.watt.co.uk/img/email/logo-light.png"
                                      className="h-[134px] w-[510px] align-middle"
                                      alt="Watt Logo"
                                    />
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                        <tr>
                          <td className="px-[78px] py-[50px] text-left">
                            {children}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </Body>
      </Tailwind>
    </Html>
  );
}
