import { Link, Section, Text } from "@react-email/components";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { UtilityType } from "@watt/db/src/enums";
import { z } from "zod";
import { Layout } from "../../components/layout";
import { ReminderEmailFooter } from "../../components/reminder-email-footer";
import { baseUrl } from "../../config/base-url";

export const currentSupplierInformationEmailSchema = z.object({
  subject: z.string().optional(),
  companyName: z.string().optional(),
  siteAddress: z
    .object({
      displayName: z.string().optional(),
      postcode: z.string().optional()
    })
    .optional(),
  utilityType: z.nativeEnum(UtilityType).optional(),
  rejoinLink: z.string().optional()
});

type CurrentSupplierInformationEmailProps = z.infer<
  typeof currentSupplierInformationEmailSchema
>;

export const COMMON_TEXT_STYLE = "m-0 text-primary";
export const COMMON_DETAILS_HEADER_STYLE =
  "m-0 mb-1 font-semibold text-muted-foreground";

const CurrentSupplierInformationEmail = ({
  subject = "Customer Supplier Information",
  companyName,
  siteAddress,
  utilityType,
  rejoinLink = baseUrl
}: CurrentSupplierInformationEmailProps) => {
  return (
    <Layout
      subject={subject}
      baseUrl={baseUrl}
      footer={<ReminderEmailFooter />}
    >
      <Section className="px-4">
        <div className="my-4 rounded-lg bg-[#f0f7f9] p-6">
          {/* TODO(Bidur): Review the component again, reponsive currently works on gmail and outlook but not on watt mail */}
          <table className="w-full border-collapse">
            <tbody>
              <tr className="block sm:table-row">
                <td className="mb-4 block w-full align-top sm:mb-0 sm:table-cell sm:w-1/2 sm:pr-3">
                  <Section className="mt-4">
                    <Text
                      className={cn(
                        COMMON_TEXT_STYLE,
                        "mb-4 font-bold text-xl leading-tight"
                      )}
                    >
                      {companyName}, just a few details needed for{" "}
                      {siteAddress?.postcode}
                    </Text>

                    <div className="inline-block rounded-full bg-secondary px-6 py-3">
                      <Section>
                        <Link href={rejoinLink}>
                          <span className="font-semibold text-white">
                            Get quotes
                          </span>
                        </Link>
                      </Section>
                    </div>
                  </Section>
                </td>

                <td className="block w-full align-top sm:table-cell sm:w-1/2 sm:pl-3">
                  <Section className="rounded-lg bg-white p-4">
                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Site Address:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {getAddressDisplayName({
                          displayName: siteAddress?.displayName ?? null,
                          postcode: siteAddress?.postcode ?? ""
                        })}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Utility Type:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {humanize(utilityType)}
                      </Text>
                    </div>
                  </Section>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div>
          <Text className={cn(COMMON_TEXT_STYLE, "mb-4")}>
            Your energy quotes are currently available and waiting for you to
            complete in just a few more steps but firstly we just need to
            clarify a few more key bits of information in order to complete your
            quotes.
          </Text>

          <Text className={COMMON_TEXT_STYLE}>
            We need to confirm your current suppliers and usage information
            before we can generate your personalised quotes.
          </Text>
        </div>
      </Section>
    </Layout>
  );
};

CurrentSupplierInformationEmail.PreviewProps = {
  subject: "Customer Supplier Information",
  companyName: "Acme Corporation Ltd",
  siteAddress: {
    displayName: "123 Business Park, Industrial Estate",
    postcode: "M1 1AA"
  },
  utilityType: UtilityType.ELECTRICITY,
  rejoinLink: "https://watt.co.uk/continue-quote"
} as CurrentSupplierInformationEmailProps;

export default CurrentSupplierInformationEmail;
