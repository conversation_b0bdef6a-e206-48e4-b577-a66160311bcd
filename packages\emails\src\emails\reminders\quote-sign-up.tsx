import { Link, Section, Text } from "@react-email/components";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { UtilityType } from "@watt/db/src/enums";
import { z } from "zod";
import { Layout } from "../../components/layout";
import { ReminderEmailFooter } from "../../components/reminder-email-footer";
import { baseUrl } from "../../config/base-url";
import {
  COMMON_DETAILS_HEADER_STYLE,
  COMMON_TEXT_STYLE
} from "./current-supplier-information";

export const quoteSignUpEmailSchema = z.object({
  subject: z.string().optional(),
  companyName: z.string().optional(),
  siteAddress: z
    .object({
      displayName: z.string().optional(),
      postcode: z.string().optional()
    })
    .optional(),
  utilityType: z.nativeEnum(UtilityType).optional(),
  meterNumber: z.string().optional(),
  currentSupplier: z.string().optional(),
  totalAnnualUsage: z.string().optional(),
  rejoinLink: z.string().optional()
});

type QuoteSignUpEmailProps = z.infer<typeof quoteSignUpEmailSchema>;

export default function QuoteSignUpEmail({
  subject = "Quote Sign Up",
  companyName,
  siteAddress,
  utilityType,
  meterNumber,
  currentSupplier,
  totalAnnualUsage,
  rejoinLink = baseUrl
}: QuoteSignUpEmailProps) {
  return (
    <Layout
      subject={subject}
      baseUrl={baseUrl}
      footer={<ReminderEmailFooter />}
    >
      <Section className="px-4">
        <div className="my-4 rounded-lg bg-[#f0f7f9] p-6">
          <table className="w-full border-collapse">
            <tbody>
              <tr className="block sm:table-row">
                <td className="mb-4 block w-full align-top sm:mb-0 sm:table-cell sm:w-1/2 sm:pr-3">
                  <Section className="mt-4">
                    <Text
                      className={cn(
                        COMMON_TEXT_STYLE,
                        "mb-4 font-bold text-xl leading-tight"
                      )}
                    >
                      Hello {companyName}, your quotes are ready!
                    </Text>

                    <div className="inline-block rounded-full bg-secondary px-6 py-3">
                      <Section>
                        <Link href={rejoinLink}>
                          <span className="font-semibold text-white">
                            Select quote
                          </span>
                        </Link>
                      </Section>
                    </div>
                  </Section>
                </td>

                <td className="block w-full align-top sm:table-cell sm:w-1/2 sm:pl-3">
                  <Section className="rounded-lg bg-white p-4">
                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Site Address:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {getAddressDisplayName({
                          displayName: siteAddress?.displayName ?? null,
                          postcode: siteAddress?.postcode ?? ""
                        })}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Utility:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {humanize(utilityType)}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Current Supplier:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {currentSupplier}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        {utilityType === UtilityType.ELECTRICITY
                          ? "MPAN"
                          : "MPRN"}
                        :
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>{meterNumber}</Text>
                    </div>

                    <div>
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Total Annual Usage:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {totalAnnualUsage}
                      </Text>
                    </div>
                  </Section>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div>
          <Text className={cn(COMMON_TEXT_STYLE, "mb-4")}>
            Your energy quotes are currently available and waiting for you to
            complete in just a few more steps. In order to lock in a supplier of
            your choice, simply click on the link below and in a few simple
            steps you can get your quote secured.
          </Text>

          <Text className={COMMON_TEXT_STYLE}>
            We need to confirm your current suppliers and usage information
            before we can generate your personalised quotes.
          </Text>
        </div>
      </Section>
    </Layout>
  );
}

QuoteSignUpEmail.PreviewProps = {
  subject: "Quote Sign Up",
  companyName: "Acme Corporation Ltd",
  siteAddress: {
    displayName: "123 Business Park, Industrial Estate",
    postcode: "M1 1AA"
  },
  utilityType: UtilityType.ELECTRICITY,
  meterNumber: "*****************9520",
  currentSupplier: "British Gas",
  totalAnnualUsage: "0-5000",
  rejoinLink: "https://watt.co.uk/select-quotes"
} as QuoteSignUpEmailProps;
