import { Link, Section, Text } from "@react-email/components";
import { cn } from "@watt/common/src/utils/classname-tailwind-merge";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { UtilityType } from "@watt/db/src/enums";
import { z } from "zod";
import { Layout } from "../../components/layout";
import { ReminderEmailFooter } from "../../components/reminder-email-footer";
import { baseUrl } from "../../config/base-url";
import {
  COMMON_DETAILS_HEADER_STYLE,
  COMMON_TEXT_STYLE
} from "./current-supplier-information";

export const signContractEmailSchema = z.object({
  subject: z.string().optional(),
  companyName: z.string().optional(),
  utilityType: z.nativeEnum(UtilityType).optional(),
  siteAddress: z
    .object({
      displayName: z.string().optional(),
      postcode: z.string().optional()
    })
    .optional(),
  meterNumber: z.string().optional(),
  currentSupplier: z.string().optional(),
  selectedSupplier: z.string().optional(),
  totalAnnualUsage: z.string().optional(),
  rejoinLink: z.string().optional()
});

type SignContractEmailProps = z.infer<typeof signContractEmailSchema>;

export default function SignContractEmail({
  subject = "Sign Contract",
  companyName,
  siteAddress,
  utilityType,
  meterNumber,
  currentSupplier,
  selectedSupplier,
  totalAnnualUsage,
  rejoinLink = baseUrl
}: SignContractEmailProps) {
  return (
    <Layout
      subject={subject}
      baseUrl={baseUrl}
      footer={<ReminderEmailFooter />}
    >
      <Section className="px-4">
        <div className="my-4 rounded-lg bg-[#f0f7f9] p-6">
          <table className="w-full border-collapse">
            <tbody>
              <tr className="block sm:table-row">
                <td className="mb-4 block w-full align-top sm:mb-0 sm:table-cell sm:w-1/2 sm:pr-3">
                  <Section className="mt-4">
                    <Text
                      className={cn(
                        COMMON_TEXT_STYLE,
                        "mb-4 font-bold text-2xl leading-tight"
                      )}
                    >
                      Hello {companyName}, your contract is ready to sign!
                    </Text>

                    <div className="inline-block rounded-full bg-secondary px-6 py-3">
                      <Section>
                        <Link href={rejoinLink}>
                          <span className="font-semibold text-white">
                            Sign contract
                          </span>
                        </Link>
                      </Section>
                    </div>
                  </Section>
                </td>

                <td className="block w-full align-top sm:table-cell sm:w-1/2 sm:pl-3">
                  <Section className="rounded-lg bg-white p-4">
                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Site Address:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {getAddressDisplayName({
                          displayName: siteAddress?.displayName ?? null,
                          postcode: siteAddress?.postcode ?? ""
                        })}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Utility:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {humanize(utilityType)}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Current Supplier:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {currentSupplier}
                      </Text>
                    </div>

                    <div className="mb-3">
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        {utilityType === UtilityType.ELECTRICITY
                          ? "MPAN"
                          : "MPRN"}
                        :
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>{meterNumber}</Text>
                    </div>

                    <div>
                      <Text className={COMMON_DETAILS_HEADER_STYLE}>
                        Total Annual Usage:
                      </Text>
                      <Text className={COMMON_TEXT_STYLE}>
                        {totalAnnualUsage}
                      </Text>
                    </div>
                  </Section>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div>
          <Text className={cn(COMMON_TEXT_STYLE, "mb-4")}>
            Your quote for {selectedSupplier} is ready to sign and complete
            locking in your energy prices today. These prices will expire in the
            next 12 hours. Due to the volatility of the market, prices are on
            the rise and changing on an hourly basis. You just need to take the
            last step in order to secure your energy deal today.
          </Text>

          <Text className={COMMON_TEXT_STYLE}>
            Click the button above to complete your energy contract and secure
            your prices today.
          </Text>
        </div>
      </Section>
    </Layout>
  );
}

SignContractEmail.PreviewProps = {
  subject: "Sign Contract",
  companyName: "Acme Corporation Ltd",
  siteAddress: {
    displayName: "123 Business Park, Industrial Estate",
    postcode: "M1 1AA"
  },
  utilityType: UtilityType.ELECTRICITY,
  meterNumber: "*****************9520",
  currentSupplier: "British Gas",
  selectedSupplier: "EDF",
  totalAnnualUsage: "0-5000",
  rejoinLink: "https://watt.co.uk/sign-contract"
} as SignContractEmailProps;
