import { execSync } from "node:child_process";
import { Console, Data, Duration, Effect } from "effect";

export class InngestDevError extends Data.TaggedError("InngestDevError")<{
  message: string;
  error: unknown;
}> {}

const startInngestDev = Effect.gen(function* (_) {
  yield* _(Console.log("Waiting 60 seconds before starting Inngest …"));
  yield* _(Effect.sleep(Duration.seconds(60)));

  yield* _(
    Effect.try({
      try: () => {
        execSync(
          "inngest dev --no-discovery --poll-interval 120000 -u http://localhost:3000/api/inngest",
          { stdio: "inherit" }
        );
      },
      catch: error =>
        new InngestDevError({
          message: "Failed to start Inngest dev runner",
          error
        })
    })
  );
});

await Effect.runPromise(startInngestDev)
  .then(() => console.log("Inngest dev runner exited gracefully"))
  .catch(err => {
    console.error("Inngest dev runner failed:", err.message ?? err);
    process.exit(1);
  });
