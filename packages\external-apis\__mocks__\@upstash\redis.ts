/**
 * Minimalistic in-memory stand-in for @upstash/redis
 */
class InMemoryRedis {
  private store = new Map<string, unknown>();

  // biome-ignore lint/complexity/noUselessConstructor: <just a mock>
  constructor(_opts?: { url?: string; token?: string }) {}

  static fromEnv(): InMemoryRedis {
    return new InMemoryRedis();
  }

  /* commands used in production code */
  async ping() {
    return "PONG";
  }
  async get(key: string) {
    return this.store.has(key) ? this.store.get(key) : null;
  }
  async set(key: string, value: unknown) {
    this.store.set(key, value);
    return "OK";
  }
  async del(k: string | string[]) {
    const keys = Array.isArray(k) ? k : [k];
    let removed = 0;
    for (const key of keys) {
      removed += this.store.delete(key) ? 1 : 0;
    }
    return removed;
  }
}

export const Redis = InMemoryRedis;
