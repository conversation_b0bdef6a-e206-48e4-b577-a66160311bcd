/* Drop-in stub for KeyvUpstash that never makes HTTP requests */
import type { Store } from "keyv";

console.log("[jest-mock]  keyv-upstash loaded");

// biome-ignore lint/suspicious/noExplicitAny: <just a mock>
export class KeyvUpstash implements Store<any> {
  // biome-ignore lint/suspicious/noExplicitAny: <just a mock>
  private store = new Map<string, any>();

  // biome-ignore lint/complexity/noUselessConstructor: <just a mock>
  constructor(_opts?: { url?: string; token?: string }) {}

  get(key: string) {
    return Promise.resolve(this.store.get(key));
  }
  // biome-ignore lint/suspicious/noExplicitAny: <just a mock>
  set(key: string, value: any) {
    this.store.set(key, value);
    return Promise.resolve(true);
  }
  delete(key: string) {
    return Promise.resolve(this.store.delete(key));
  }
  clear() {
    this.store.clear();
    return Promise.resolve(undefined);
  }
}
