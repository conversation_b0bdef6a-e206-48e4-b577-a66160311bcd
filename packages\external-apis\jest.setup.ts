// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

jest.mock("@upstash/redis");

/**
 * Mock next-axiom to be a no-op
 * This uses the shared mock implementation from @watt/common/src/testing/next-axiom-mock
 * which properly handles async operations to prevent "Cannot log after tests are done" errors
 */
import nextAxiomMock from "@watt/common/src/testing/next-axiom-mock";
jest.mock("next-axiom", () => nextAxiomMock);

jest.mock("@watt/redis/src/cache", () => ({
  cacheWrap: async (_k: string, fn: () => Promise<unknown>) => fn()
}));

// Mock redis client
jest.mock("@watt/redis/src/client", () => {
  const mockRedis = {
    get: jest.fn().mockResolvedValue(null),
    setex: jest.fn().mockResolvedValue("OK"),
    del: jest.fn().mockResolvedValue(1)
  };
  return {
    redis: mockRedis,
    getRedisInstance: () => mockRedis
  };
});

// Mock fetch
import fetch from "jest-fetch-mock";
fetch.enableMocks();

// Mock console.info to prevent API call logs in tests
beforeAll(() => {
  jest.spyOn(console, "info").mockImplementation(() => {});
});

// Reset fetch mocks between tests
afterEach(() => {
  fetch.resetMocks();
});

// Clean up after the tests are finished
afterAll(() => {
  // Restore console.info
  jest.restoreAllMocks();
});
