import { createGenericBuilder } from "@watt/common/src/utils/generic-fluent-builder";
import { ElectricityMeterDataSchema } from "@watt/external-apis/common/aperture";
import fetch from "jest-fetch-mock";
import { testHttpErrors } from "../../test-utils/api-error-handling-test";
import { getElectricMeterData } from "./lookup-electric-meter";

fetch.enableMocks();

beforeEach(() => {
  fetch.resetMocks();
});

const mpanBottomLine = "1900000268965";

const builder = createGenericBuilder(ElectricityMeterDataSchema)
  .set("mpan", mpanBottomLine)
  .set("addressLine1", "")
  .set("addressLine2", "")
  .set("addressLine3", "2")
  .set("addressLine4", "")
  .set("addressLine5", "FAKE STREET")
  .set("addressLine6", "")
  .set("addressLine7", "")
  .set("addressLine8", "FAKE TOWN")
  .set("addressLine9", "HAMPSHIRE")
  .set("addressPostalCode", "SN7 7AA")
  .set("tradingStatus", "T")
  .set("tradingStatusEfd", "19983123")
  .set("profileClass", "01")
  .set("profileClassEfd", "20221123")
  .set("meterTimeswitchClass", "801")
  .set("meterTimeswitchClassEfd", "20221123")
  .set("lineLossFactor", "100")
  .set("lineLossFactorEfd", "19983123")
  .set("standardSettlementConfiguration", "0393")
  .set("standardSettlementConfigurationEfd", "20221123")
  .set("energisationStatus", "E")
  .set("energisationStatusEfd", "20221123")
  .set("gspGroupId", "_H")
  .set("gspGroupEfd", "19960401")
  .set("dataAggregatorMpid", "ACCU")
  .set("dataAggregatorEfd", "20221123")
  .set("dataCollectorMpid", "ACCU")
  .set("dataCollectorEfd", "20221123")
  .set("supplierMpid", "TILL")
  .set("supplierEfd", "20221123")
  .set("meterOperatorMpid", "BGAS")
  .set("meterOperatorEfd", "20221123")
  .set("measurementClass", "A")
  .set("measurementClassEfd", "20221123")
  .set("greenDealInEffect", "0")
  .set("smsoMpid", "BGAS")
  .set("smsoEfd", "20170405")
  .set("dccServiceFlag", "A")
  .set("dccServiceFlagEfd", "20230531")
  .set("ihdStatus", "I")
  .set("ihdStatusEfd", "20170405")
  .set("smetsVersion", "SMETS1")
  .set("distributorMpid", "SOUT")
  .set("meteredIndicator", "T")
  .set("meteredIndicatorEfd", "20101203")
  .set("meteredIndicatorEtd", "")
  .set("consumerType", "Domestic")
  .set("relationshipStatusIndicator", "None")
  .set("rmpState", "O")
  .set("rmpEfd", "19980629")
  .set("domesticConsumerIndicator", "T")
  .set("cssSupplierMpid", "TILL")
  .set("cssSupplyStartDate", "2022-12-15 00:00:00.000")
  .set("meterSerialNumber", "Z17N033331")
  .set("meterInstallDate", "20170405")
  .set("meterType", "S1")
  .set("mapMpid", "MFMP")
  .set("mapMpidEfd", "20170405")
  .set("installingSupplierMpid", "BGAS")
  .set("energyDirection", "")
  .set("energyDirectionEfd", "")
  .set("energyDirectionEtd", "")
  .set("connectionType", "")
  .set("connectionTypeEfd", "")
  .set("connectionTypeEtd", "")
  .set("esmeId", "")
  .set("meterLocation", "")
  .set("registerDigits", "")
  .set("uprn", "1234567890");

const camelCaseApiResponse = builder.build();
const snakeCaseMeterData = builder.build({ convertCase: "SNAKE_CASE" });

const mockApiResponse = {
  result: {
    more_results_available: false,
    confidence: "Verified match",
    suggestions: [
      {
        locality: {
          sub_region: { name: "FAKE COUNTY" },
          town: { name: "FAKE TOWN" }
        },
        postal_code: { full_name: "GU12 4SF" },
        postal_code_key:
          "aWQ9R1UxMjRTRiwgVW5pdGVkIE13dmdkb21-YWx0X2tleT1-ZGF0YXNldD1HQlJfUEFGfmZvcm1hdF9rZXk9R0JSJGdiLWFkZHJlc3MkJCQkfmdha190eXBlPXBvc3RhbF9jb2Rl",
        locality_key:
          "aWQ9QUxERVJTSE9ULCBIQU1QU0svakUsIFVuaXRlZCBLaW5nZG9tfmFsdF9rZXk9fmRhdGFzZXQ9R0JSX1BBRn5mb3JtYXRfa2V5PUdCUiRnYi1hZGRyZXNzJCQkJH5nYWtfdHlwZT1sb2NhbGl0eQ"
      }
    ],
    addresses_formatted: [
      {
        layout_name: "ElectricityUtilityLookup",
        address: { electricity_meters: [snakeCaseMeterData] }
      }
    ]
  }
};

describe("getElectricMeterData", () => {
  it("should handle invalid MPAN bottom line format", async () => {
    const invalidMpanFormat = "123";
    const result = await getElectricMeterData({ mpan: invalidMpanFormat });
    expect(result.data).toBeUndefined();
    expect(result.error?.message).toContain("Invalid parameters");
  });

  it("should capture and handle errors during response body parsing", async () => {
    fetch.mockResponseOnce(JSON.stringify({}));
    const result = await getElectricMeterData({ mpan: mpanBottomLine });
    expect(result.data).toBeUndefined();
    expect(result.error?.message).toContain("Invalid response body");
  });

  it("should retrieve half-hourly combined data for a valid MPAN", async () => {
    fetch.mockResponseOnce(JSON.stringify(mockApiResponse));
    const result = await getElectricMeterData({ mpan: mpanBottomLine });
    expect(result.error).toBeUndefined();
    expect(result.data).toEqual(camelCaseApiResponse);
  });

  testHttpErrors(getElectricMeterData, { mpan: mpanBottomLine });
});
