import { externalApiUrls } from "@watt/common/src/config/external-api-urls";
import type { ZodIssue, z } from "zod";

import {
  ElectricMeterApiResponseSchema,
  type ElectricMeterData,
  apertureApiHeaders,
  getApertureLookupPayloadForElectric
} from "../../common/aperture";
import { MpanBottomLineParam } from "../../common/electralink";
import {
  ApiError,
  type ApiProps,
  type ApiResponse,
  handleFetchExternalApi
} from "../../utils/handle-fetch-external-api";

/**
 * Gets electric meter details by mpan bottom line
 * @param mpan The mpan bottom line length is 13 characters
 * @returns
 */
export async function getElectricMeterData(
  params: z.infer<typeof MpanBottomLineParam>
): Promise<ApiResponse<ElectricMeterData>> {
  const safeParse = MpanBottomLineParam.safeParse(params);

  if (!safeParse.success) {
    return {
      data: undefined,
      error: new ApiError(
        "Aperture get electric meter data",
        "Invalid parameters",
        safeParse.error.issues.map((issue: ZodIssue) => ({
          error: issue.message,
          type: issue.code
        }))
      )
    };
  }

  const apiProps = {
    name: "Aperture get electric meter data",
    url: {
      baseUrl: externalApiUrls.apertureApiUrl,
      path: "/address/lookup/v2"
    },
    additionalData: {
      method: "POST",
      headers: apertureApiHeaders,
      body: getApertureLookupPayloadForElectric(safeParse.data.mpan)
    }
  } satisfies ApiProps;

  const response = await handleFetchExternalApi(
    apiProps,
    ElectricMeterApiResponseSchema
  );

  if (response.error) {
    return response;
  }

  const firstAddress = response.data?.result.addressesFormatted[0];
  if (!firstAddress) {
    return {
      data: undefined,
      error: new Error(`No addresses found for mpanBottomLine: ${params.mpan}`)
    };
  }

  const electricMeter = firstAddress.address.electricityMeters.find(
    meter => meter.mpan === params.mpan
  );

  if (!electricMeter) {
    return {
      data: undefined,
      error: new Error(
        `No electric meter data found for mpanBottomLine: ${params.mpan}`
      )
    };
  }

  return {
    data: electricMeter,
    error: undefined
  };
}
