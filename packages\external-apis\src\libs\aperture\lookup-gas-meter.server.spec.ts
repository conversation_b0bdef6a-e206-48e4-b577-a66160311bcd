import { toCamelCase } from "@watt/common/src/utils/record-to-camel-case";
import fetch from "jest-fetch-mock";

import { testHttpErrors } from "../../test-utils/api-error-handling-test";
import { getGasMeterData } from "./lookup-gas-meter";

fetch.enableMocks();

beforeEach(() => {
  fetch.resetMocks();
});

const validMprn = "4019135506";

const gasMeterData = {
  mprn: validMprn,
  uprn: "100060098769",
  rel_address_primary_name: "2",
  rel_address_secondary_name: "",
  rel_address_street1: "FAKE STREET",
  rel_address_street2: " ",
  rel_address_locality1: " ",
  rel_address_locality2: " ",
  rel_address_town: "FAKE TOWN",
  rel_address_postcode: "SN7 7AA",
  rel_address_logical_status: "1",
  rel_address_language: "ENG",
  rel_address_organisation: "",
  rel_address_address_type: "DPA",
  rel_address_confidence_score: "100",
  rel_address_classification: "RD04",
  rel_address_latitude: "51.44617",
  rel_address_longitude: "-0.71658",
  meter_serial: "E6S112011111760",
  offtake_quantity_annual: "9243",
  meter_point_status: "LI",
  installer_id: "BGT",
  network_name: "Southern Gas Networks Plc.",
  supplier_name: "British Gas Trading Limited",
  last_meter_read_date: "20240101",
  last_meter_read_type: "E",
  last_meter_read_value: "05887"
};

const mockApiResponse = {
  result: {
    more_results_available: false,
    confidence: "Verified match",
    suggestions: [
      {
        locality: {
          sub_region: {
            name: "FAKE COUNTY"
          },
          town: {
            name: "FAKE TOWN"
          }
        },
        postal_code: {
          full_name: "SN7 7AA"
        },
        postal_code_key:
          "aWQ9R1UxMjRTRiwgVW5pdGVkIEtpbmdkb21-YWx0X2tleT1-ZGF0YXNldD1HQadfUEFGfmZvcm1hdF9rZXk9R0JSJGdiLWFkZHJlc3MkJCQkfmdha190eXBlPXBvc3RhbF9jb2Rl",
        locality_key:
          "aWQ9QUxERVJTSE9ULCBIYW1wc2hpcmUsIFVuaXRlZCBLaW5nZwetfmFsdF9rZXk9fmRhdGFzZXQ9R0JSX1BBRn5mb3JtYXRfa2V5PUdCUiRnYi1hZGRyZXNzJCQkJH5nYWtfdHlwZT1sb2NhbGl0eQ"
      }
    ],
    addresses_formatted: [
      {
        layout_name: "GasUtilityLookup",
        address: {
          gas_meters: [
            {
              ...gasMeterData
            }
          ]
        }
      }
    ]
  }
};

const camelCaseApiResponse = toCamelCase(gasMeterData);

describe("getGasMeterData", () => {
  it("should handle invalid MPRN format", async () => {
    const invalidMprnFormat = "123";

    const result = await getGasMeterData({ mprn: invalidMprnFormat });

    expect(result.data).toBeUndefined();
    expect(result.error?.message).toContain("Invalid parameters");
  });

  it("should capture and handle errors during response body parsing", async () => {
    const invalidApiResponse = {};
    fetch.mockResponseOnce(JSON.stringify(invalidApiResponse));

    const result = await getGasMeterData({ mprn: validMprn });

    expect(result.data).toBeUndefined();
    expect(result.error?.message).toContain("Invalid response body");
  });

  it("should retrieve gas meter data for a valid MPRN", async () => {
    fetch.mockResponseOnce(JSON.stringify(mockApiResponse));

    const result = await getGasMeterData({ mprn: validMprn });

    expect(result.error).toBeUndefined();
    expect(result.data).toEqual(camelCaseApiResponse);
  });

  testHttpErrors(getGasMeterData, { mprn: validMprn });
});
