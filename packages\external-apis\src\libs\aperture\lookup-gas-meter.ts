import { externalApiUrls } from "@watt/common/src/config/external-api-urls";

import type { ZodIssue } from "zod";
import {
  GasMeterApiResponseSchema,
  type GasMeterData,
  GasMeterParam,
  apertureApiHeaders,
  getApertureLookupPayloadForGas
} from "../../common/aperture";
import {
  ApiError,
  type ApiProps,
  type ApiResponse,
  handleFetchExternalApi
} from "../../utils/handle-fetch-external-api";

/**
 * Gets gas meter details by mprn
 * @param mprn
 * @returns
 */
export async function getGasMeterData(
  params: GasMeterParam
): Promise<ApiResponse<GasMeterData>> {
  const safeParse = GasMeterParam.safeParse(params);

  if (!safeParse.success) {
    return {
      data: undefined,
      error: new ApiError(
        "Aperture get gas meter data",
        "Invalid parameters",
        safeParse.error.issues.map((issue: ZodIssue) => ({
          error: issue.message,
          type: issue.code
        }))
      )
    };
  }

  const apiProps = {
    name: "Aperture get gas meter data",
    url: {
      baseUrl: externalApiUrls.apertureApiUrl,
      path: "/address/lookup/v2"
    },
    additionalData: {
      method: "POST",
      headers: apertureApiHeaders,
      body: getApertureLookupPayloadForGas(safeParse.data.mprn)
    }
  } satisfies ApiProps;

  const response = await handleFetchExternalApi(
    apiProps,
    GasMeterApiResponseSchema
  );

  if (response.error) {
    return response;
  }

  const gasMeter =
    response.data?.result.addressesFormatted[0]?.address?.gasMeters?.find(
      meter => meter.mprn === params.mprn
    );

  if (!gasMeter) {
    return {
      data: undefined,
      error: new Error(`No gas meter data found for mprn: ${params.mprn}`)
    };
  }

  return {
    data: gasMeter,
    error: undefined
  };
}
