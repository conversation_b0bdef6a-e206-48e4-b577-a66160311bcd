import { externalApiUrls } from "@watt/common/src/config/external-api-urls";
import type { UtilityType } from "@watt/db/src/enums";
import {
  type ApertureAddressFormatted,
  type ApertureMeterByGlobalAddressKeyResponse,
  ApertureMeterByGlobalAddressKeyResponseSchema,
  apertureApiHeaders,
  getApertureFormatPayload
} from "../../common/aperture";
import {
  type ApiProps,
  type ApiResponse,
  handleFetchExternalApi
} from "../../utils/handle-fetch-external-api";

const callApertureFormat = async (
  name: string,
  globalAddressKey: string,
  body: string
): Promise<ApiResponse<ApertureMeterByGlobalAddressKeyResponse>> => {
  const apiProps = {
    name,
    url: {
      baseUrl: externalApiUrls.apertureApiUrl,
      path: `/address/format/v1/${globalAddressKey}`
    },
    additionalData: { method: "POST", headers: apertureApiHeaders, body }
  } satisfies ApiProps;

  const response = await handleFetchExternalApi(
    apiProps,
    ApertureMeterByGlobalAddressKeyResponseSchema
  );

  return response;
};

export async function getFormattedApertureAddressByGlobalAddressKey(
  utilityType: UtilityType,
  globalAddressKey: string | undefined
): Promise<ApertureAddressFormatted | null> {
  if (!globalAddressKey) {
    return null;
  }

  const response = await callApertureFormat(
    `Aperture get ${utilityType} meter by global address key`,
    globalAddressKey,
    getApertureFormatPayload(utilityType)
  );

  if (response.error) {
    throw response.error;
  }

  return response.data.result.addressesFormatted.at(0) ?? null;
}
