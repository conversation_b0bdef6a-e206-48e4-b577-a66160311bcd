import fetch from "jest-fetch-mock";
import type { z } from "zod";

import type { ApertureSearchResponseSchema } from "@watt/external-apis/common/aperture";
import { searchApertureAddresses } from "./search-meter-addresses";

type RawSearchJson = z.infer<typeof ApertureSearchResponseSchema>;
type JsonOrError = RawSearchJson | Error;

const makePayload = (text: string): RawSearchJson => ({
  result: {
    moreResultsAvailable: false,
    confidence: "Multiple matches",
    suggestionsKey: "suggestions-key",
    suggestionsPrompt: "suggestions-prompt",
    suggestions: [
      {
        globalAddressKey: `${text}-key`,
        text,
        format: "SN7 7WD",
        additionalAttributes: [{ name: "postcode", value: "SN7 7WD" }]
      }
    ]
  }
});

const emptyPayload: RawSearchJson = {
  result: {
    moreResultsAvailable: false,
    confidence: "",
    suggestionsKey: "",
    suggestionsPrompt: "",
    suggestions: []
  }
};

interface TestCase {
  desc: string;
  gasJson: JsonOrError;
  elecJson: JsonOrError;
  expectLen?: number;
}

const cases: readonly TestCase[] = [
  {
    desc: "gas + elec distinct",
    gasJson: makePayload("1 Gas St"),
    elecJson: makePayload("2 Elec St"),
    expectLen: 2
  },
  {
    desc: "dedupe identical texts",
    gasJson: makePayload("1 Same St"),
    elecJson: makePayload("1 Same St"),
    expectLen: 1
  },
  {
    desc: "gas only",
    gasJson: makePayload("Gas Only"),
    elecJson: emptyPayload,
    expectLen: 1
  },
  {
    desc: "invalid JSON from gas",
    gasJson: {} as unknown as RawSearchJson,
    elecJson: makePayload("Ok")
  },
  {
    desc: "network failure on elec",
    gasJson: makePayload("Ok"),
    elecJson: new Error("offline")
  }
];

fetch.enableMocks();
beforeEach(() => fetch.resetMocks());

describe("searchAddresses()", () => {
  test.each<TestCase>(cases)("$desc", async tc => {
    fetch.mockImplementation(async (_input, init) => {
      const rawBody =
        typeof _input === "string"
          ? (init?.body as string)
          : _input instanceof URL
            ? (init?.body as string)
            : await (_input as Request).text();

      const isGas = rawBody.includes("gb-additional-gas");
      const choice = isGas ? tc.gasJson : tc.elecJson;

      if (choice instanceof Error) {
        return Promise.reject(choice);
      }

      return Promise.resolve(
        new Response(JSON.stringify(choice), { status: 200 })
      );
    });

    if (tc.expectLen !== undefined) {
      const res = await searchApertureAddresses("SN77WD");
      expect(res).toHaveLength(tc.expectLen);
    } else {
      await expect(searchApertureAddresses("SN77WD")).rejects.toBeTruthy();
    }
  });
});
