import { randomUUID } from "node:crypto";
import { externalApiUrls } from "@watt/common/src/config/external-api-urls";
import { ONE_DAY_SEC } from "@watt/common/src/constants/time-durations";
import { getAddressDisplayName } from "@watt/common/src/utils/get-address-display-name";
import { cacheWrap } from "@watt/redis/src/cache";
import {
  type ApertureSearchResponse,
  ApertureSearchResponseSchema,
  type ApertureSearchResponseSuggestion,
  apertureApiHeaders,
  getApertureSearchPayloadElec,
  getApertureSearchPayloadGas,
  toSuggestions
} from "../../common/aperture";
import {
  type ApiProps,
  type ApiResponse,
  handleFetchExternalApi
} from "../../utils/handle-fetch-external-api";
import { getElectricMeterData } from "./lookup-electric-meter";
import { getGasMeterData } from "./lookup-gas-meter";
export interface ApertureCombinedAddressSearchSuggestion {
  id: string;
  text: string;
  postcode: string;
  electricGlobalAddressKey?: string;
  gasGlobalAddressKey?: string;
  additionalAttributes: ReadonlyArray<{ name: string; value: string }>;
}

const callApertureSearch = async (
  name: string,
  body: string
): Promise<ApiResponse<ApertureSearchResponse>> => {
  const apiProps = {
    name,
    url: {
      baseUrl: externalApiUrls.apertureApiUrl,
      path: "/address/search/v1"
    },
    additionalData: { method: "POST", headers: apertureApiHeaders, body }
  } satisfies ApiProps;

  return handleFetchExternalApi(apiProps, ApertureSearchResponseSchema);
};

/**
 * Search Aperture for gas addresses by postcode.
 * @param query - The query to search for.
 * @returns The addresses found in Aperture for gas.
 */

async function searchGasAddresses(query: string) {
  const res = await callApertureSearch(
    "Aperture gas search",
    getApertureSearchPayloadGas(query)
  );
  if (res.error) {
    throw res.error;
  }

  try {
    return toSuggestions(res.data);
  } catch (error) {
    if (error instanceof Error && error.message === "TOO_MANY_MATCHES") {
      throw new Error("TOO_MANY_MATCHES");
    }
    throw error;
  }
}

/**
 * Search Aperture for gas addresses by postcode.
 * @param query - The query to search for.
 * @returns The addresses found in Aperture for gas.
 */
async function searchElectricAddresses(query: string) {
  const res = await callApertureSearch(
    "Aperture electric search",
    getApertureSearchPayloadElec(query)
  );
  if (res.error) {
    throw res.error;
  }

  try {
    return toSuggestions(res.data);
  } catch (error) {
    if (error instanceof Error && error.message === "TOO_MANY_MATCHES") {
      throw new Error("TOO_MANY_MATCHES");
    }
    throw error;
  }
}

/**
 * Merge two Aperture single-fuel lists into one combined list while preserving both
 * utilities `globalAddressKey`s. Assumes the display text is same for both utilities.
 * @param gas - The gas suggestions.
 * @param elec - The electric suggestions.
 * @returns The merged address suggestions with both utilities `globalAddressKey`s.
 */
function zipDedupeAddressSuggestions(
  gas: readonly ApertureSearchResponseSuggestion[],
  elec: readonly ApertureSearchResponseSuggestion[]
): readonly ApertureCombinedAddressSearchSuggestion[] {
  const map = new Map<string, ApertureCombinedAddressSearchSuggestion>();

  for (const gasSuggestion of gas) {
    const postcode =
      gasSuggestion.additionalAttributes.find(
        attribute => attribute.name === "postcode"
      )?.value ?? "";
    map.set(gasSuggestion.text, {
      id: randomUUID(),
      text: getAddressDisplayName({
        displayName: gasSuggestion.text,
        postcode: postcode
      }),
      postcode: postcode,
      electricGlobalAddressKey: undefined,
      gasGlobalAddressKey: gasSuggestion.globalAddressKey,
      additionalAttributes: gasSuggestion.additionalAttributes
    });
  }

  for (const electricSuggestion of elec) {
    const existing = map.get(electricSuggestion.text);
    const postcode =
      electricSuggestion.additionalAttributes.find(
        attribute => attribute.name === "postcode"
      )?.value ?? "";

    if (existing) {
      map.set(electricSuggestion.text, {
        ...existing,
        postcode: existing.postcode || postcode,
        electricGlobalAddressKey: electricSuggestion.globalAddressKey
      });
    } else {
      map.set(electricSuggestion.text, {
        id: randomUUID(),
        text: electricSuggestion.text,
        postcode: postcode,
        electricGlobalAddressKey: electricSuggestion.globalAddressKey,
        gasGlobalAddressKey: undefined,
        additionalAttributes: electricSuggestion.additionalAttributes
      });
    }
  }

  return [...map.values()];
}

export async function getApertureAddresses(
  query: string,
  key: string
): Promise<readonly ApertureCombinedAddressSearchSuggestion[]> {
  if (key === "id") {
    return [];
  }

  if (key === "postcode") {
    return searchApertureAddresses(query);
  }

  if (key === "mpan" || key === "mprn") {
    const res = await lookupApertureMeterData(query, key);

    if (!res.data) {
      if (res.error) {
        throw res.error;
      }
      return [];
    }

    if ("mpan" in res.data) {
      const addressObj = {
        addressLine1: res.data.addressLine1,
        addressLine2: res.data.addressLine2,
        addressLine3: res.data.addressLine3,
        addressLine4: res.data.addressLine4,
        addressLine5: res.data.addressLine5,
        addressLine6: res.data.addressLine6,
        addressLine7: res.data.addressLine7,
        addressLine8: res.data.addressLine8,
        addressLine9: res.data.addressLine9,
        addressPostalCode: res.data.addressPostalCode
      };

      return [
        {
          id: randomUUID(),
          text: Object.values(addressObj).filter(Boolean).join(", "),
          postcode: addressObj.addressPostalCode,
          electricGlobalAddressKey: undefined,
          gasGlobalAddressKey: undefined,
          additionalAttributes: Object.entries(addressObj)
            .filter(([_, value]) => value !== undefined)
            .map(([key, value]) => ({ name: key, value }))
        }
      ];
    }

    if ("mprn" in res.data) {
      const addressObj = {
        addressLine1: [
          res.data.relAddressPrimaryName,
          res.data.relAddressSecondaryName,
          res.data.relAddressStreet1
        ]
          .filter(Boolean)
          .join(" "),
        addressLine2: res.data.relAddressStreet2,
        addressLine3: res.data.relAddressLocality1,
        addressLine4: res.data.relAddressLocality2,
        addressTown: res.data.relAddressTown,
        addressPostalCode: res.data.relAddressPostcode
      };

      return [
        {
          id: randomUUID(),
          text: Object.values(addressObj).filter(Boolean).join(", "),
          postcode: addressObj.addressPostalCode,
          electricGlobalAddressKey: undefined,
          gasGlobalAddressKey: undefined,
          additionalAttributes: Object.entries(addressObj)
            .filter(([_, value]) => value !== undefined)
            .map(([key, value]) => ({ name: key, value }))
        }
      ];
    }
  }

  return [];
}

export async function lookupApertureMeterData(query: string, key: string) {
  if (key === "mprn") {
    return getGasMeterData({ mprn: query });
  }

  if (key === "mpan") {
    return getElectricMeterData({ mpan: query });
  }

  return {
    data: undefined,
    error: new Error(`Unsupported key: ${key}`)
  };
}

/**
 * Search Aperture for addresses by postcode.
 * @param unspecifiedQueryComponent - Accepts a partial query, e.g. "SN7 7WD" or "10 Downing Street, London, Greater London, SW1A 2AA"
 * @returns A single merged list of addresses with both utilities `globalAddressKey`s.
 */
export async function searchApertureAddresses(
  unspecifiedQueryComponent: string
): Promise<readonly ApertureCombinedAddressSearchSuggestion[]> {
  try {
    const [gas, elec] = await Promise.all([
      searchGasAddresses(unspecifiedQueryComponent),
      searchElectricAddresses(unspecifiedQueryComponent)
    ]);

    return cacheWrap(
      `search-aperture-addresses-${unspecifiedQueryComponent.replaceAll(" ", "_")}`,
      async () => zipDedupeAddressSuggestions(gas, elec),
      ONE_DAY_SEC
    );
  } catch (error) {
    if (error instanceof Error && error.message === "TOO_MANY_MATCHES") {
      throw new Error("TOO_MANY_MATCHES");
    }
    throw error;
  }
}
