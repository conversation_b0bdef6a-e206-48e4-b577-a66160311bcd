import { execSync } from "node:child_process";
import { Console, Data, Duration, Effect } from "effect";

export class NovuDevError extends Data.TaggedError("NovuDevError")<{
  message: string;
  error: unknown;
}> {}

const startNovuDev = Effect.gen(function* (_) {
  yield* _(Console.log("Waiting 60 seconds before starting Novu Studio …"));
  yield* _(Effect.sleep(Duration.seconds(60)));

  yield* _(
    Effect.try({
      try: () =>
        execSync(
          "pnpm dotenv -e ../../.env.local -- novu dev --port 3000 --headless --dashboard-url $NOVU_DASHBOARD_URL",
          { stdio: "inherit" }
        ),
      catch: error =>
        new NovuDevError({
          message: "Failed to start Novu Studio",
          error
        })
    })
  );
});

await Effect.runPromise(startNovuDev)
  .then(() => console.log("Novu Studio exited gracefully"))
  .catch(err => {
    console.error("Novu Studio failed:", err.message ?? err);
    process.exit(1);
  });
