import { type ITriggerPayloadOptions, Novu } from "@novu/node";
import { env } from "@watt/common/src/config/env";
import type { NotificationType } from "@watt/db/src/enums";
import { z } from "zod";
import type { CallbackPayload } from "./schemas/common";
import {
  currentSupplierInformationEmailPayloadSchema,
  quoteSignUpEmailPayloadSchema,
  signContractEmailPayloadSchema
} from "./schemas/email";
import {
  type announcementFormPayloadSchema,
  announcementPayloadSchema,
  overdueCallbackPayloadSchema,
  todayCallbackPayloadSchema,
  upcomingCallbackPayloadSchema,
  verifyCallbackPayloadSchema
} from "./schemas/in-app";

// Use a lazy-loading pattern to avoid initializing Novu during build
let novuClient: Novu | null = null;

// Simple function to get or create the Novu client
export function getNovu(): Novu {
  if (!novuClient) {
    novuClient = new Novu(env.NOVU_SECRET_KEY, {
      backendUrl: env.NEXT_PUBLIC_NOVU_API_URL
    });
  }
  return novuClient;
}

const InAppNotificationPayloadSchema = z.discriminatedUnion("type", [
  todayCallbackPayloadSchema,
  upcomingCallbackPayloadSchema,
  verifyCallbackPayloadSchema,
  overdueCallbackPayloadSchema,
  announcementPayloadSchema
]);

export const ReminderEmailPayloadSchema = z.discriminatedUnion("type", [
  currentSupplierInformationEmailPayloadSchema,
  quoteSignUpEmailPayloadSchema,
  signContractEmailPayloadSchema
]);

export type InAppNotificationPayload = z.infer<
  typeof InAppNotificationPayloadSchema
>;
export type ReminderEmailPayload = z.infer<typeof ReminderEmailPayloadSchema>;
export type CallbackNotificationEvents = {
  id: string;
  payload: CallbackPayload;
}[];
export type VerifyCallbackPayload = z.infer<typeof verifyCallbackPayloadSchema>;
export type OverdueCallbackPayload = z.infer<
  typeof overdueCallbackPayloadSchema
>;
export type UpcomingCallbackPayload = z.infer<
  typeof upcomingCallbackPayloadSchema
>;
export type AnnouncementPayload = z.infer<typeof announcementPayloadSchema>;
export type AnnouncementFormPayload = z.infer<
  typeof announcementFormPayloadSchema
>;

type TriggerPayload = ITriggerPayloadOptions & {
  name: NotificationType;
  payload: InAppNotificationPayload | ReminderEmailPayload;
  replyTo?: string;
  tenant?: string;
};

export type TriggerRecipientsPayload = {
  subscriberId: string;
  email: string;
  firstName: string;
  lastName: string;
};

export type NotificationResult = {
  data: { data: { status: string; transactionId: string } };
};

export async function markAsRead(subscriberId: string, notificationId: string) {
  await getNovu().subscribers.markMessageSeen(subscriberId, notificationId);
}

export async function triggerNotification(data: TriggerPayload) {
  const { name, payload, to } = data;

  const result = await getNovu().trigger(name, {
    to: to,
    payload: {
      ...payload
    }
  });

  return result as NotificationResult;
}

export async function cancelNotification(transactionId: string) {
  try {
    await getNovu().events.cancel(transactionId);
  } catch (error) {
    console.log("Error cancelling notification:", error);
    throw error;
  }
}
