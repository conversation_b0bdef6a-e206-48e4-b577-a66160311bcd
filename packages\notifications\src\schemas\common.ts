import { z } from "zod";
import { NOTIFICATION_TAGS } from "../config";

// General - applies to all notifications
export const commonPayloadSchema = z.object({
  createdAt: z.string().min(1),
  action: z.string().optional(),
  // In our business logic tag is mandatory. So we should enfoce that for all.
  tag: z.nativeEnum(NOTIFICATION_TAGS)
});

// Callback - applies to Callback notifications
export const callbackPayloadSchema = commonPayloadSchema.extend({
  contactName: z.string().min(1),
  contactPhone: z.string().optional(),
  subject: z.string().min(1),
  comment: z.string().optional(),
  companyName: z.string().min(1),
  companyId: z.string().min(1),
  siteRefId: z.number().min(1),
  callbackScheduledTime: z.string().min(1),
  callbackTime: z.string().min(1)
});

// Email - applies to Email notifications
export const emailPayloadSchema = commonPayloadSchema.extend({
  recipientId: z.string().min(1),
  recipientEmail: z.string().email()
});

export type CallbackPayload = z.infer<typeof callbackPayloadSchema>;
export type EmailPayload = z.infer<typeof emailPayloadSchema>;
