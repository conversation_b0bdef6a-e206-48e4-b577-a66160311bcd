import { NotificationType } from "@watt/db/src/enums";
import { currentSupplierInformationEmailSchema } from "@watt/emails/src/emails/reminders/current-supplier-information";
import { quoteSignUpEmailSchema } from "@watt/emails/src/emails/reminders/quote-sign-up";
import { signContractEmailSchema } from "@watt/emails/src/emails/reminders/sign-contract";
import { z } from "zod";
import { emailPayloadSchema } from "./common";

export const currentSupplierInformationEmailPayloadSchema = emailPayloadSchema
  .merge(currentSupplierInformationEmailSchema)
  .extend({
    type: z.literal(NotificationType.CURRENT_SUPPLIER_INFORMATION_EMAIL)
  });

export const quoteSignUpEmailPayloadSchema = emailPayloadSchema
  .merge(quoteSignUpEmailSchema)
  .extend({
    type: z.literal(NotificationType.QUOTE_SIGNUP_EMAIL)
  });

export const signContractEmailPayloadSchema = emailPayloadSchema
  .merge(signContractEmailSchema)
  .extend({
    type: z.literal(NotificationType.SIGN_CONTRACT_EMAIL)
  });

export type CurrentSupplierInformationEmailPayload = z.infer<
  typeof currentSupplierInformationEmailPayloadSchema
>;
export type QuoteSignUpEmailPayload = z.infer<
  typeof quoteSignUpEmailPayloadSchema
>;
export type SignContractEmailPayload = z.infer<
  typeof signContractEmailPayloadSchema
>;
