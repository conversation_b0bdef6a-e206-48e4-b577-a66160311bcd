import { NotificationType, UserRole } from "@watt/db/src/enums";
import { z } from "zod";
import { callbackPayloadSchema, commonPayloadSchema } from "./common";

export const announcementFormPayloadSchema = z.object({
  teams: z.array(z.nativeEnum(UserRole).or(z.literal("ALL"))),
  message: z.string().min(1),
  category: z.string().min(1),
  subject: z.string().min(1)
});

export const announcementPayloadSchema = commonPayloadSchema.extend({
  type: z.literal(NotificationType.ANNOUNCEMENT_NOTIFICATION),
  createdBy: z.string().min(1),
  // properties set in the notifications form
  ...announcementFormPayloadSchema.shape
});

export const overdueCallbackPayloadSchema = callbackPayloadSchema.extend({
  type: z.literal(NotificationType.OVERDUE_CALLBACK_NOTIFICATION),
  deliveryTime: z.number().min(1)
});

export const todayCallbackPayloadSchema = callbackPayloadSchema.extend({
  type: z.literal(NotificationType.TODAY_CALLBACK_NOTIFICATION),
  notificationCron: z.string().min(1),
  digestKeyValue: z.string().min(1)
});

export const upcomingCallbackPayloadSchema = callbackPayloadSchema.extend({
  type: z.literal(NotificationType.UPCOMING_CALLBACK_NOTIFICATION),
  deliveryTime: z.number().min(1)
});

export const verifyCallbackPayloadSchema = callbackPayloadSchema.extend({
  type: z.literal(NotificationType.VERIFY_CALLBACK_NOTIFICATION),
  deliveryTime: z.number().min(1),
  callbackId: z.string().min(1),
  callbackTime: z.string().min(1)
});
