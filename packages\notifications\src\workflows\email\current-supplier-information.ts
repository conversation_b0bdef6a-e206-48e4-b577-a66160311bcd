import { workflow } from "@novu/framework";
import { render } from "@react-email/components";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import CurrentSupplierInformationEmail from "@watt/emails/src/emails/reminders/current-supplier-information";
import React from "react";
import { NOTIFICATION_TAGS, REMINDER_EMAIL_UNITS } from "../../config";
import {
  type CurrentSupplierInformationEmailPayload,
  currentSupplierInformationEmailPayloadSchema
} from "../../schemas/email";

const workflowName = NotificationType.CURRENT_SUPPLIER_INFORMATION_EMAIL;

// TODO(Bidur): refactor into a reusable render function and push down to the email package
function renderCurrentSupplierEmail(
  payload: CurrentSupplierInformationEmailPayload
) {
  return render(React.createElement(CurrentSupplierInformationEmail, payload));
}

export const currentSupplierInformationEmail = workflow(
  workflowName,
  async ({ step, payload }) => {
    await step.delay("delay", () => ({
      amount: 1,
      unit: REMINDER_EMAIL_UNITS
    }));

    await step.email("send-email", async () => ({
      subject:
        "Watt.co.uk: Your energy quote is waiting for you but we just need more information…",
      body: await renderCurrentSupplierEmail(payload)
    }));
  },
  {
    tags: [NOTIFICATION_TAGS.REMINDER_EMAILS],
    payloadSchema: currentSupplierInformationEmailPayloadSchema,
    name: humanize(workflowName),
    description:
      "Reminder email sent to customer to complete the current supplier information."
  }
);
