import { workflow } from "@novu/framework";
import { render } from "@react-email/components";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import QuoteSignUpEmail from "@watt/emails/src/emails/reminders/quote-sign-up";
import React from "react";
import { NOTIFICATION_TAGS, REMINDER_EMAIL_UNITS } from "../../config";
import {
  type QuoteSignUpEmailPayload,
  quoteSignUpEmailPayloadSchema
} from "../../schemas/email";

const workflowName = NotificationType.QUOTE_SIGNUP_EMAIL;

function renderQuoteSignUpEmail(payload: QuoteSignUpEmailPayload) {
  return render(React.createElement(QuoteSignUpEmail, payload));
}

export const quoteSignUpEmail = workflow(
  workflowName,
  async ({ step, payload }) => {
    await step.delay("delay", () => ({
      amount: 1,
      unit: REMINDER_EMAIL_UNITS
    }));

    await step.email("send-email", async () => ({
      subject: "Watt.co.uk: Select your quote to lock in your prices today",
      body: await renderQuoteSignUpEmail(payload)
    }));
  },
  {
    tags: [NOTIFICATION_TAGS.REMINDER_EMAILS],
    payloadSchema: quoteSignUpEmailPayloadSchema,
    name: humanize(workflowName),
    description:
      "Reminder email sent to customer to complete the quote sign up."
  }
);
