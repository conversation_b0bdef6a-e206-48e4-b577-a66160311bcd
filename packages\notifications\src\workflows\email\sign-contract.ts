import { workflow } from "@novu/framework";
import { render } from "@react-email/components";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import SignContractEmail from "@watt/emails/src/emails/reminders/sign-contract";
import React from "react";
import { NOTIFICATION_TAGS, REMINDER_EMAIL_UNITS } from "../../config";
import {
  type SignContractEmailPayload,
  signContractEmailPayloadSchema
} from "../../schemas/email";

const workflowName = NotificationType.SIGN_CONTRACT_EMAIL;

function renderSignContractEmail(payload: SignContractEmailPayload) {
  return render(React.createElement(SignContractEmail, payload));
}

export const signContractEmail = workflow(
  workflowName,
  async ({ step, payload }) => {
    await step.delay("delay", () => ({
      amount: 1,
      unit: REMINDER_EMAIL_UNITS
    }));

    await step.email("send-email", async () => ({
      subject:
        "Watt.co.uk: Your Contract is almost complete… just one last thing to secure your energy deal",
      body: await renderSignContractEmail(payload)
    }));
  },
  {
    tags: [NOTIFICATION_TAGS.REMINDER_EMAILS],
    payloadSchema: signContractEmailPayloadSchema,
    name: humanize(workflowName),
    description: "Reminder email sent to customer to sign the contract."
  }
);
