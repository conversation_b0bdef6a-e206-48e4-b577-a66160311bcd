import { workflow } from "@novu/framework";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import { NOTIFICATION_TAGS } from "../../config";
import { announcementPayloadSchema } from "../../schemas/in-app";
const workflowName = NotificationType.ANNOUNCEMENT_NOTIFICATION;

export const announcementNotification = workflow(
  workflowName,
  async ({ step, payload }) => {
    await step.inApp("in-app-step", async () => {
      // biome-ignore lint/suspicious/noExplicitAny: <later>
      const result: any = {
        subject: payload.subject,
        body: payload.message,
        data: {
          payload
        }
      };
      return result;
    });
  },
  {
    tags: [NOTIFICATION_TAGS.ANNOUNCEMENTS],
    payloadSchema: announcementPayloadSchema,
    name: humanize(workflowName),
    description:
      "A manually sent notification to the entire company or targeted teams to announce important changes or information"
  }
);
