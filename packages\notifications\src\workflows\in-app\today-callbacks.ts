import { workflow } from "@novu/framework";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import { NOTIFICATION_TAGS } from "../../config";
import { todayCallbackPayloadSchema } from "../../schemas/in-app";

const workflowName = NotificationType.TODAY_CALLBACK_NOTIFICATION;

export const todayCallbackNotification = workflow(
  workflowName,
  async ({ step, payload }) => {
    const digestResult = await step.digest("digest", async () => {
      const { notificationCron } = payload;

      return {
        cron: notificationCron,
        digestKey: "digestKeyValue" // This key value will group notification with the same delivery time, must match a key from the payload
      };
    });

    await step.inApp("in-app-step", () => ({
      subject: "Notification title",
      body: "Notification body",
      data: {
        events: digestResult.events,
        payload
      }
    }));
  },
  {
    tags: [NOTIFICATION_TAGS.CALLBACKS],
    // "zod-to-json-schema" needs to be added to the notifications package as it is a peer dep of the novu framework.
    // Currently it's int he crm not the packages/notifications
    // https://docs.novu.co/framework/schema/zod#integrate-zod-with-your-notification-workflows
    payloadSchema: todayCallbackPayloadSchema,
    name: humanize(workflowName),
    description:
      "Reminder notification of today's callbacks to be actioned by the creator of the callback."
  }
);
