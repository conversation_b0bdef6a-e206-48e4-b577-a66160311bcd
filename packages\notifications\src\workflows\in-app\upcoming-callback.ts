import { workflow } from "@novu/framework";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import { NOTIFICATION_TAGS } from "../../config";
import { upcomingCallbackPayloadSchema } from "../../schemas/in-app";

const workflowName = NotificationType.UPCOMING_CALLBACK_NOTIFICATION;

export const upcomingCallbackNotification = workflow(
  workflowName,
  async ({ step, payload }) => {
    await step.delay("delay", () => {
      const { deliveryTime } = payload;

      return {
        unit: "seconds",
        amount: deliveryTime
      };
    });

    await step.inApp("in-app-step", async () => {
      // biome-ignore lint/suspicious/noExplicitAny: <later>
      const result: any = {
        subject: "Notification title",
        body: "Notification body",
        data: {
          payload
        }
      };
      return result;
    });
  },
  {
    tags: [NOTIFICATION_TAGS.CALLBACKS],
    payloadSchema: upcomingCallbackPayloadSchema,
    name: humanize(workflowName),
    description:
      "Upcoming callback notification sent to the creator of the callback 1 hour before the callback is due."
  }
);
