import { workflow } from "@novu/framework";
import { humanize } from "@watt/common/src/utils/humanize-string";
import { NotificationType } from "@watt/db/src/enums";
import { NOTIFICATION_TAGS } from "../../config";
import { verifyCallbackPayloadSchema } from "../../schemas/in-app";

const workflowName = NotificationType.VERIFY_CALLBACK_NOTIFICATION;

export const verifyCallbackNotification = workflow(
  workflowName,
  async ({ step, payload }) => {
    await step.delay("delay", () => {
      const { deliveryTime } = payload;

      return {
        unit: "seconds",
        amount: deliveryTime
      };
    });

    await step.inApp("in-app-step", async () => {
      // biome-ignore lint/suspicious/noExplicitAny: <later>
      const result: any = {
        subject: "Notification subject",
        body: "Notification body",
        data: {
          payload
        }
      };
      return result;
    });
  },
  {
    tags: [NOTIFICATION_TAGS.CALLBACKS],
    payloadSchema: verifyCallbackPayloadSchema,
    name: humanize(workflowName),
    description:
      "Verify callback is completed notification sent to the creator of the callback 1 hour after the callback is due."
  }
);
