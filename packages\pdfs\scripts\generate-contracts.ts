import fs from "node:fs";
import path from "node:path";
import { UtilityType } from "@prisma/client";
import { STORAGE_BUCKETS } from "@watt/common/src/constants/storage-buckets";
import { writtenContractTemplates } from "@watt/db/src/constants/providers/providers-sold-by-us";
import { supabaseAdmin } from "@watt/db/src/supabase/supabase";
import { PDFDocument } from "pdf-lib";
import { createContractTemplate } from "../src/features/generate-contract";
import { createLOATemplateData } from "../src/features/generate-contract/data/loa-template";
import { fillGeneric } from "../src/features/generate-contract/fill-generic";
import electricSampleData from "../src/features/generate-contract/sample-payloads/electric.json";
import gasSampleData from "../src/features/generate-contract/sample-payloads/gas.json";
import type { InputPDFTemplateData } from "../src/features/generate-contract/types";
import { parseDates } from "../src/utils/parse-dates";

const OUTPUT_DIR = path.resolve("./dist/generated-contracts");

function setupOutputDirectory() {
  try {
    if (fs.existsSync(OUTPUT_DIR)) {
      fs.rmSync(OUTPUT_DIR, { recursive: true });
      console.log(`Cleared existing dist directory: ${OUTPUT_DIR}`);
    }

    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    console.log(`Created dist directory: ${OUTPUT_DIR}`);
  } catch (error) {
    console.error(
      `Failed to setup output directory: ${error instanceof Error ? error.message : String(error)}`
    );
    throw error;
  }
}

async function generateContract(
  templateName: string,
  utilityType: UtilityType
): Promise<Buffer | null> {
  try {
    const templateFound = writtenContractTemplates.find(
      t => t.templateName.toLowerCase() === templateName.toLowerCase()
    );

    if (!templateFound) {
      console.error(`Template key not found for ${templateName}`);
      return null;
    }

    console.log(
      `Generating contract for template: ${templateName} (${utilityType})`
    );

    const { data, error } = await supabaseAdmin.storage
      .from(STORAGE_BUCKETS.WRITTEN_CONTRACT_TEMPLATES)
      .download(`${templateName}.pdf`);

    if (error) {
      console.error(`Failed to download template ${templateName}:`, error);
      return null;
    }

    const buffer = await data.arrayBuffer();
    const template = await PDFDocument.load(buffer);

    const payload =
      utilityType === UtilityType.GAS
        ? gasSampleData.data
        : electricSampleData.data;

    const templateData = createContractTemplate(
      utilityType,
      parseDates(payload as unknown as InputPDFTemplateData)
    );

    const filledTemplate = await fillGeneric({
      pdf: template,
      data: templateData,
      templateKey: templateName
    });

    const pdfBytes = await filledTemplate.save();
    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error(`Error generating contract for ${templateName}:`, error);
    return null;
  }
}

async function generateLOA(utilityType: UtilityType): Promise<Buffer | null> {
  try {
    console.log(`Generating LOA template for ${utilityType}`);

    // Load LOA template from local file system
    const loaTemplatePath = path.resolve(
      "../../data/pdfs/written-contracts/loa_template.pdf"
    );

    if (!fs.existsSync(loaTemplatePath)) {
      console.error(`LOA template not found at: ${loaTemplatePath}`);
      return null;
    }

    const templateBuffer = fs.readFileSync(loaTemplatePath);
    const template = await PDFDocument.load(templateBuffer);

    const payload =
      utilityType === UtilityType.GAS
        ? gasSampleData.data
        : electricSampleData.data;

    const auditData =
      utilityType === UtilityType.GAS
        ? gasSampleData.audit_data
        : electricSampleData.audit_data;

    // Add the audit signature to the payload if it doesn't have one
    const payloadWithSignature = {
      ...payload,
      contract_signature:
        (payload as unknown as InputPDFTemplateData).contract_signature ||
        auditData?.contract_signature ||
        "Test Signature"
    };

    const loaData = createLOATemplateData(
      parseDates(payloadWithSignature as unknown as InputPDFTemplateData)
    );

    const filledTemplate = await fillGeneric({
      pdf: template,
      data: loaData,
      templateKey: "loa_template"
    });

    const pdfBytes = await filledTemplate.save();
    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error(`Error generating LOA for ${utilityType}:`, error);
    return null;
  }
}

async function main() {
  setupOutputDirectory();

  // Generate written contracts
  for (const { templateName: template, utility } of writtenContractTemplates) {
    const pdf = await generateContract(template, utility);

    if (pdf) {
      const localFilePath = path.join(OUTPUT_DIR, `${template}.pdf`);
      fs.writeFileSync(localFilePath, pdf);
    }
  }

  // Generate LOA templates
  for (const utilityType of [UtilityType.ELECTRICITY, UtilityType.GAS]) {
    const loaPdf = await generateLOA(utilityType);

    if (loaPdf) {
      const fileName = `loa_template_${utilityType.toLowerCase()}.pdf`;
      const localFilePath = path.join(OUTPUT_DIR, fileName);
      fs.writeFileSync(localFilePath, loaPdf);
      console.log(`Generated LOA: ${fileName}`);
    }
  }

  console.log(`Generated contracts saved to: ${OUTPUT_DIR}`);
}

main().catch(error => {
  console.error("Error in contract generation script:", error);
  process.exit(1);
});
