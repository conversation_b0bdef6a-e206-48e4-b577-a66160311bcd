import { env } from "@watt/common/src/config/env";
import type { Response } from "@watt/common/src/types/response";
import { log } from "@watt/common/src/utils/axiom-logger";
import type { PDFOptions } from "puppeteer";
import { getBrowser } from "../../utils/get-browser";

type ConvertUrlToPdfReturn = Promise<Response<Buffer, Error>>;

type ConvertUrlToPdfOptions = {
  includePrivateApiKey: boolean;
  landscape: boolean;
};

type ConvertUrlToPdfProps = {
  url: string;
  options: ConvertUrlToPdfOptions;
};

/**
 * Renders the page at the given URL into a PDF with page numbers in the footer.
 * Had issues getting this to run in Vercel Serverless environment article below was helpful.
 * https://gist.github.com/kettanaito/56861aff96e6debc575d522dd03e5725
 * @param url The URL of the page to render as PDF.
 * @param includePrivateApiKey Whether to include the private API key in the request headers.
 * @returns The PDF buffer.
 */
export async function convertUrlToPdf({
  url,
  options
}: ConvertUrlToPdfProps): ConvertUrlToPdfReturn {
  try {
    const browser = await getBrowser();

    const page = await browser.newPage();

    if (options.includePrivateApiKey) {
      await page.setExtraHTTPHeaders({
        "x-api-key": env.CRM_PRIVATE_API_KEY || "",
        "x-render-pdf": "true"
      });
    }

    await page.goto(url, { waitUntil: "networkidle0" });
    await page.emulateMediaType("screen");

    const pdfOptions: PDFOptions = {
      format: "Legal", // 8.27in x 11.7in / 595 x 842 pixels
      landscape: options.landscape,
      footerTemplate: `
        <div style="font-family: Arial, sans-serif; font-size: 14px; text-align: right; width: 100%; padding: 5px; padding-right: 50px;">
          <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
        </div>
      `,
      printBackground: true,
      displayHeaderFooter: true,
      margin: {
        top: "0mm",
        bottom: "20mm",
        left: "0mm",
        right: "0mm"
      }
    };

    const pdfBuffer = await page.pdf(pdfOptions);

    return {
      data: pdfBuffer,
      error: undefined
    };
  } catch (e) {
    const error = e as Error;
    log.error("Failed to create PDF", { error });
    return {
      data: undefined,
      error
    };
  }
}
