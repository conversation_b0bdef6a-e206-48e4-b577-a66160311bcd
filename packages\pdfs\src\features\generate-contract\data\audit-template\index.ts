import { formattedDateLong } from "../../mutations/date";
import type { AuditData, InputAuditData } from "../../types";

export function createAuditTemplate(data: InputAuditData) {
  const auditLogHistoryFields = getAuditLogHistory(data);

  const fields: AuditData[] = [];

  fields.push({
    key: "issuer",
    value: "Watt Utilities"
  });

  fields.push({
    key: "document_generated_at",
    value: formattedDateLong(data.contract_generated_at)
  });

  fields.push({
    key: "document_finger_print",
    value: data.document_finger_print
  });

  fields.push({
    key: "ip_address",
    value: data.user_ip
  });

  fields.push({
    key: "document_signed_date",
    value: formattedDateLong(data.contract_signed_at)
  });

  fields.push({
    key: "party_and_fingerprint",
    value: `${data.contract_signature} - Signer (${data.document_finger_print})`
  });

  return [...auditLogHistoryFields, ...fields];
}

function getAuditLogHistory(data: InputAuditData) {
  const fields: AuditData[] = [];

  fields.push({
    key: "audit_history_log_0_date",
    value: formattedDateLong(data.contract_generated_at)
  });

  fields.push({
    key: "audit_history_log_1_date",
    value: formattedDateLong(data.contract_signed_at)
  });

  fields.push({
    key: "audit_history_log_2_date",
    value: formattedDateLong(data.contract_signed_at)
  });

  fields.push({
    key: "audit_history_log_0_action",
    value: `${data.contract_signature} contract created (${data.user_ip})`
  });

  fields.push({
    key: "audit_history_log_1_action",
    value: `${data.contract_signature} contract signed (${data.user_ip})`
  });

  fields.push({
    key: "audit_history_log_2_action",
    value: `${data.contract_signature} confirmed authorized to sign contract (${data.user_ip})`
  });

  return fields;
}
