import { getNumericSplitFields } from "../../mutations/numeric";
import { removeFromString } from "../../mutations/string";
import type { BankingInfo } from "../../types";

export function getBankingFields(bankingInfo: BankingInfo, prefix = "banking") {
  return [
    // account number fields
    ...getNumericSplitFields(
      bankingInfo?.account_number ?? "",
      "account_number",
      prefix
    ),
    ...getNumericSplitFields(
      removeFromString(bankingInfo?.sort_code ?? "", "-"),
      "sort_code",
      prefix
    ),
    ...getNumericSplitFields(
      removeFromString(bankingInfo?.sort_code ?? "", "-"),
      "sort_code",
      prefix,
      2
    ),
    {
      key: `${prefix}_account_name`,
      value: bankingInfo?.account_name ?? ""
    },
    {
      key: `${prefix}_name`,
      value: bankingInfo?.bank_name ?? ""
    }
  ];
}
