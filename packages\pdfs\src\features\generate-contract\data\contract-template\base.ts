import { BusinessType } from "@prisma/client";
import { getCheckboxFields, getRenewalFields } from "../../mutations/checkbox";
import { getDateFields } from "../../mutations/date";
import type { PDFTemplateData, PDFTemplateFieldData } from "../../types";

const BusinessTypes = {
  limited_company: "Limited Company",
  charity: "Charity",
  sole_trader: "Sole Trader",
  public_limited_company: "Public Limited Company",
  limited_liability_partnership: "Limited Liability Partnership"
} as const;

export const BusinessTypeMap = {
  [BusinessType.LTD]: BusinessTypes.limited_company,
  [BusinessType.CHARITY]: BusinessTypes.charity,
  [BusinessType.SOLE_TRADER]: BusinessTypes.sole_trader,
  [BusinessType.PLC]: BusinessTypes.public_limited_company,
  [BusinessType.LLP]: BusinessTypes.limited_liability_partnership
} as const;

function ensureValue(value: string | number | undefined): string | number {
  return value ?? "-";
}

export function getBaseFields(data: PDFTemplateData): PDFTemplateFieldData[] {
  if (!data.created_at) {
    throw new Error("Created at date is required");
  }

  if (!data.new_supplier) {
    throw new Error("New supplier is required");
  }

  if (!data.business_type) {
    throw new Error("Business type is required");
  }

  return [
    ...getDateFields(data.created_at, "created_at"),
    ...getCheckboxFields(
      Object.values(BusinessTypes),
      data.business_type,
      "business_type"
    ),
    ...getRenewalFields(data.current_supplier ?? "", data.new_supplier),
    {
      // TODO (maks): this is 'industry' for now but PDF's all need updating so this can be 'business_type'
      key: "business_type",
      value: data.business_type
    },
    {
      key: "industries",
      value: ensureValue(data.industries)
    },
    {
      key: "broker_id",
      value: "1AW" // Smartest ONLY!
      //EDF use UPS as broker name
      // BGAS Watt utilities
    },
    {
      key: "broker_name",
      value: "Watt Utilities"
    },
    {
      key: "broker_name_ups",
      value: "Utility Preferred Services"
    },
    {
      key: "broker_contact_number",
      value: "0161 833 8661"
    },
    {
      key: "sales_agent_name",
      value: "Watt Quotation Tool"
    },
    {
      key: "payment_method",
      value: "Direct Debit"
    },
    {
      key: "sales_channel",
      value: "Online Quote App"
    },
    {
      key: "current_supplier",
      value: data.current_supplier ?? "-"
    }
  ];
}
