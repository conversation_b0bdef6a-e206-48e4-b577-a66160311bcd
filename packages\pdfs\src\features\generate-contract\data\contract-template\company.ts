import { getAddress<PERSON>ields } from "../../mutations/address";
import { getBusinessRegNumber } from "../../mutations/businessNumber";
import type { PDFTemplateData, PDFTemplateFieldData } from "../../types";

export function getCompanyFields(
  data: PDFTemplateData,
  prefix: string
): PDFTemplateFieldData[] {
  return [
    // First address in company sites using self.sites[0].address_object
    ...getAddressFields(
      data.address.company_address_line_1,
      data.address.company_postal_town,
      data.address.company_county,
      data.address.company_postcode,
      `${prefix}_address`,
      data.address.company_display_name
    ),
    // Company address using main_address_object
    ...getAddressFields(
      data.address.business_address_line_1,
      data.address.business_postal_town,
      data.address.business_county,
      data.address.business_postcode,
      "business_address",
      data.address.business_display_name
    ),
    {
      key: `${prefix}_charity_number`,
      value: data.charity_number ?? ""
    },
    {
      key: `${prefix}_name`,
      value: data.company_name
    },
    {
      key: `${prefix}_contact_number`,
      value: data.company_contact_phone
    },
    {
      key: `${prefix}_contact_email`,
      value: data.company_contact_email
    },
    ...getBusinessRegNumber(data, `${prefix}_registration_number`),
    {
      key: `${prefix}_signature_name`,
      value: data.company_name
    }
  ];
}
