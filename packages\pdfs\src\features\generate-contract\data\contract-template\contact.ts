import type { PDFTemplateData } from "../../types";

export function getContactFields(data: PDFTemplateData, prefix: string) {
  return [
    {
      key: `${prefix}_name_forename`,
      value: data.company_contact_forename
    },
    {
      key: `${prefix}_name_surname`,
      value: data.company_contact_surname
    },
    {
      key: `${prefix}_name_full`,
      value: data.company_contact_fullname
    },
    {
      key: `${prefix}_name_full_caps`,
      value: data.company_contact_fullname.toUpperCase()
    },
    {
      key: `${prefix}_position`,
      value: data.company_contact_position
    },
    {
      key: `${prefix}_phone`,
      value: data.company_contact_phone
    },
    {
      key: `${prefix}_email`,
      value: data.company_contact_email
    }
  ];
}
