import { getDateFields } from "../../mutations/date";
import {
  getContractCommisionFields,
  getContractLengthFields,
  getNumericSplitFields,
  getPricePerMonth,
  getStandingChargeFields
} from "../../mutations/numeric";
import type { PDFTemplateData, PDFTemplateFieldData } from "../../types";

function ensureValue(value: string | number | undefined): string | number {
  return value ?? "-";
}

export function getContractFields(
  data: PDFTemplateData,
  prefix: string
): PDFTemplateFieldData[] {
  const usage = data.usage_total_annual ?? "-";
  const duration = data.quote_duration_months;

  if (!duration) {
    throw new Error("Quote duration months is required");
  }

  return [
    ...getContractLengthFields(duration, `${prefix}_length`),
    ...getContractCommisionFields(usage, duration, prefix),
    ...getDateFields(data.contract_start_date, `${prefix}_start_date`),
    ...(data.current_contract_end_date
      ? getDateFields(
          data.current_contract_end_date,
          `${prefix}_current_end_date`
        )
      : []),
    ...(data.contract_end_date_notice
      ? getDateFields(
          data.contract_end_date_notice,
          `${prefix}_end_date_notice`
        )
      : []),
    ...(data.contract_price_review_date
      ? getDateFields(
          data.contract_price_review_date,
          `${prefix}_price_review_date`
        )
      : []),
    ...(data.contract_price_review_notice_date
      ? getDateFields(
          data.contract_price_review_notice_date,
          `${prefix}_price_review_notice_date`
        )
      : []),
    ...getDateFields(data.contract_end_date, `${prefix}_end_date`),
    ...getNumericSplitFields(usage, "eac", prefix),
    ...getStandingChargeFields(data.quote_standing_charge ?? "-", prefix),
    {
      key: `${prefix}_unit_rate`,
      value: ensureValue(data.quote_unit_rate)
    },
    {
      key: `${prefix}_day_rate`,
      value: ensureValue(data.quote_day_unit_rate)
    },
    {
      key: `${prefix}_night_rate`,
      value: ensureValue(data.quote_night_unit_rate)
    },
    {
      key: `${prefix}_weekend_rate`,
      value: ensureValue(data.quote_weekend_unit_rate)
    },
    {
      key: `${prefix}_eac`,
      value: ensureValue(data.usage_total_annual)
    },
    {
      key: `${prefix}_day_eac`,
      value: ensureValue(data.usage_day_annual)
    },
    {
      key: `${prefix}_night_eac`,
      value: ensureValue(data.usage_night_annual)
    },
    {
      key: `${prefix}_wend_eac`,
      value: ensureValue(data.usage_weekend_annual)
    },
    {
      key: `${prefix}_kva`,
      value: ensureValue(data.quote_kva_charge)
    },
    {
      key: `${prefix}_annual_price`,
      value: ensureValue(data.quote_annual_price)
    },
    {
      key: `${prefix}_price_per_month`,
      value: getPricePerMonth(data.quote_annual_price ?? "0")
    },
    {
      key: `${prefix}_type`,
      value: ensureValue(data.quote_contract_type)
    },
    {
      key: `${prefix}_microbusiness`,
      value: Number.parseFloat(usage) < 100000 ? "Yes" : "No"
    },
    {
      key: `${prefix}_first_payment`, // specific to VALDA
      value: "0"
    }
  ];
}
