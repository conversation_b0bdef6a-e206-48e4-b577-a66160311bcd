import { getNumericSplitFields } from "../../../mutations/numeric";
import type { MPAN } from "../../../types";

export function getMPANFields(mpanInfo: MPAN, prefix: string) {
  return [
    ...getNumericSplitFields(mpanInfo.full, "mpan", ""),
    {
      key: `${prefix}_top_line`,
      value: mpanInfo.top_line
    },
    {
      key: `${prefix}_bottom_line`,
      value: mpanInfo.bottom_line
    },
    {
      key: `${prefix}_profile_class`,
      value: mpanInfo.profile_class
    },
    {
      key: `${prefix}_meter_time_switch_code`,
      value: mpanInfo.meter_time_switch_code
    },
    {
      key: `${prefix}_line_loss_factor_class`,
      value: mpanInfo.line_loss_factor_class
    },
    {
      key: `${prefix}_distributor_id`,
      value: mpanInfo.distributor
    },
    {
      key: `${prefix}_unique_1`,
      value: mpanInfo.unique_1
    },
    {
      key: `${prefix}_unique_2`,
      value: mpanInfo.unique_2
    },
    {
      key: `${prefix}_checksum`,
      value: mpanInfo.checksum
    }
  ];
}
