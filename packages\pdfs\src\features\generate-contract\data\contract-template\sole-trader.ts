import { getAddressFields } from "../../mutations/address";
import { formattedDate } from "../../mutations/date";
import type {
  PDFTemplateData,
  PDFTemplateFieldData,
  PreviousAddresses
} from "../../types";

export function getSoleTraderFields(
  data: PDFTemplateData,
  prefix: string
): PDFTemplateFieldData[] {
  if (!data.sole_trader) {
    return [];
  }

  const addresses = data.sole_trader.addresses;
  const homeAddress = addresses.length > 0 ? addresses[0] : null;

  return [
    ...(homeAddress
      ? getAddressFields(
          homeAddress.address.customer_address_line_1,
          homeAddress.address.customer_postal_town,
          homeAddress.address.customer_county,
          homeAddress.address.customer_postcode,
          `${prefix}_address`,
          homeAddress.address.customer_display_name
        )
      : []),
    ...getSoleTraderAddressFields(addresses, prefix),
    {
      key: `${prefix}_date_of_birth`,
      value: data.sole_trader.date_of_birth
        ? formattedDate(data.sole_trader.date_of_birth)
        : ""
    },
    { key: `${prefix}_full_name`, value: data.company_contact_fullname },
    {
      key: `${prefix}_name_forename`,
      value: data.company_contact_forename
    },
    {
      key: `${prefix}_name_surname`,
      value: data.company_contact_surname
    },
    ...(homeAddress
      ? [
          {
            key: `${prefix}_postcode`,
            value: homeAddress.address.customer_postcode
          }
        ]
      : [])
  ];
}

function getSoleTraderAddressFields(
  data: PreviousAddresses[],
  prefix: string
): PDFTemplateFieldData[] {
  const fields: PDFTemplateFieldData[] = [];

  if (data.length === 0) {
    return [];
  }

  // map over the addresses and return an array of address fields
  data.map((address, index) => {
    fields.push(
      ...getAddressFields(
        address.address.customer_address_line_1,
        address.address.customer_postal_town,
        address.address.customer_county,
        address.address.customer_postcode,
        `${prefix}_address_${index}`,
        address.address.customer_display_name
      )
    );
    fields.push({
      key: `${prefix}_address_${index}_postcode`,
      value: address.address.customer_postcode
    });
  });

  return fields;
}
