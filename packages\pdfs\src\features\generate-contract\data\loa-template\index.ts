import { format as formatDate } from "date-fns";
import { getBusinessRegNumber } from "../../mutations/businessNumber";
import type { PDFTemplateData, PDFTemplateFieldData } from "../../types";

export function createLOATemplateData(
  data: PDFTemplateData
): PDFTemplateFieldData[] {
  const dateFields = getDateFields(data);

  if (!data.mpan?.full && !data.mprn?.full) {
    throw new Error("MPAN or MPRN must be provided");
  }

  if (!data.quote_duration_months) {
    throw new Error("Quote duration months is required");
  }

  const signatureValue = data.contract_signature ? data.contract_signature : "";

  console.log("[createLOATemplateData] Signature field:", {
    signatureValue,
    signatureLength: signatureValue.length
  });

  return [
    ...dateFields,
    { key: "company_name", value: data.company_name },
    {
      key: "full_registered_address",
      value: data.address.company_display_name
    },
    { key: "company_name_permission", value: data.company_name },
    {
      key: "signature",
      value: signatureValue
    },
    { key: "officer_name", value: data.company_contact_fullname },
    { key: "officer_position", value: data.company_contact_position },
    ...getBusinessRegNumber(data, "company_reg_num"),
    { key: "signed_company_name", value: data.company_name },
    { key: "meter_id", value: data.mpan?.bottom_line || data.mprn?.full || "" },
    {
      key: "loa_valid_for",
      value: `${data.quote_duration_months.toString()} months`
    },
    {
      key: "timestamp",
      value: `${data.company_contact_fullname} (${data.created_at})`
    }
  ];
}

function getDateFields(data: PDFTemplateData): PDFTemplateFieldData[] {
  const dateFields: PDFTemplateFieldData[] = [];

  const formattedStartDate = formatDate(
    new Date(data.contract_start_date),
    "ddMMyyyy"
  );

  for (let i = 0; i < 6; i++) {
    // get the last part of the year field
    const charIndex = i < 4 ? i : i + 2;
    const value = formattedStartDate[charIndex];

    if (value === undefined) {
      throw new Error(
        `Invalid date format: missing character at index ${charIndex}`
      );
    }

    dateFields.push({
      key: `date_digits_nr${i + 1}`,
      value
    });
  }

  return dateFields;
}
