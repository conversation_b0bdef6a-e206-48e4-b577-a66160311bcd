import { readFile } from "node:fs/promises";
import fontkit from "@pdf-lib/fontkit";
import { log } from "@watt/common/src/utils/axiom-logger";
import type { PDFDocument, PDFFont } from "pdf-lib";
import { type FONTS, getFontPath } from "../../fonts";

type EmbedFontProps = {
  pdf: PDFDocument;
  fontName: keyof typeof FONTS;
};

export async function embedFont({
  pdf,
  fontName
}: EmbedFontProps): Promise<PDFFont> {
  console.log("[embedFont] Starting font embed:", { fontName });

  try {
    pdf.registerFontkit(fontkit);
    const fontPath = getFontPath(fontName);
    console.log("[embedFont] Font path:", fontPath);

    const fontBytes = await readFile(fontPath);
    console.log("[embedFont] Font loaded:", {
      size: fontBytes.length,
      fontName
    });

    const font = await pdf.embedFont(fontBytes);
    console.log("[embedFont] Font embedded successfully:", {
      name: font.name,
      height: font.heightAtSize(12),
      ref: font.ref?.toString()
    });

    return font;
  } catch (e) {
    const error = e as Error;
    console.error("[embedFont] Failed to embed font:", {
      fontName,
      error: error.message
    });
    log.error("Failed to embed font", { error });
    throw error;
  }
}
