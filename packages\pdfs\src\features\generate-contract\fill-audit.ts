import { log } from "@watt/common/src/utils/axiom-logger";
import type { PDFDocument, PDFTextField } from "pdf-lib";
import type { AuditData } from "./types";

const constantFields: AuditData[] = [
  { key: "issuer", value: "Watt Utilities" }
];

type FillAuditProps = {
  originalPdf: PDFDocument;
  auditPdf: PDFDocument;
  auditValues: AuditData[];
};

export async function fillAudit({
  originalPdf,
  auditPdf,
  auditValues
}: FillAuditProps) {
  const form = auditPdf.getForm();

  const allValues = constantFields.concat(auditValues);

  for (const { key, value } of allValues) {
    try {
      const field = form.getField(key) as PDFTextField;

      if (value instanceof Date) {
        field.setText(value.toLocaleString());
      } else {
        field.setText(value);
      }
    } catch {
      log.info(`Field ${key} doesn't exist, skipping...`);
    }
  }

  const [copiedAuditPage] = await originalPdf.copyPages(auditPdf, [0]);

  originalPdf.addPage(copiedAuditPage);

  return originalPdf;
}
