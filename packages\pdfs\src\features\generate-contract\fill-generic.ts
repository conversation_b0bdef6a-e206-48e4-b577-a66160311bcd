import { existsSync, readFileSync, readdirSync } from "node:fs";
import { join } from "node:path";
import { cwd } from "node:process";
import fontkit from "@pdf-lib/fontkit";
import { log } from "@watt/common/src/utils/axiom-logger";
import {
  PDFCheckBox,
  type PDFDocument,
  type PDFField,
  PDFTextField
} from "pdf-lib";
import type { PDFTemplateFieldData } from "./types";

type FillGenericProps = {
  pdf: PDFDocument;
  data: PDFTemplateFieldData[];
  templateKey: string | undefined;
};

export async function embedPdfFont(originalPdf: PDFDocument, fullPath: string) {
  originalPdf.registerFontkit(fontkit);
  const fontBytes = readFileSync(fullPath);
  return await originalPdf.embedFont(fontBytes);
}

export async function fillGeneric({
  pdf,
  data,
  templateKey
}: FillGenericProps): Promise<PDFDocument> {
  try {
    if (!templateKey) {
      throw new Error("templateKey is undefined");
    }

    const form = pdf.getForm();

    if (!form) {
      throw new Error("no form found");
    }

    const fieldNames = form.getFields().map(e => e.getName());

    // TODO: Disabled until we fix issue (bad path?) in Vercel production
    // const signatureFont = await embedFont({
    //   pdf,
    //   fontName: FONTS.BRUSH_SCRIPT_STD
    // });

    // console.log("[fillGeneric] Font embedded:", {
    //   fontName: signatureFont.name,
    //   fontRef: signatureFont.ref?.toString(),
    //   isEmbedded: !!signatureFont
    // });

    data = data.filter(e => fieldNames.includes(e.key));

    for (const { key, value, isCheckbox } of data) {
      if (!value) {
        log.warn(
          `No value found for field '${key}' on '${templateKey}' value of: '${value}'`
        );

        continue;
      }

      let field: PDFField | undefined;

      try {
        field = form.getField(key);
      } catch {
        log.warn(`Field ${key} doesn't exist, skipping...`);
        continue;
      }

      if (field instanceof PDFCheckBox && isCheckbox) {
        if (value === "true") {
          field.defaultUpdateAppearances();
          field.check();
        } else {
          field.uncheck();
        }
      } else {
        if (!(field instanceof PDFTextField)) {
          log.error(
            `Field '${key}' was not a PDFTextField for template '${templateKey}'`
          );

          continue;
        }

        if (typeof value === "number") {
          field.setText(value.toString());
        } else {
          field.setText(value);
        }

        if (key === "signature" && templateKey === "loa_template") {
          // console.log("[fillGeneric] Signature field found:", {
          //   fieldName: field.getName(),
          //   signatureFontName: signatureFont.name,
          //   signatureFontRef: signatureFont.ref?.toString(),
          //   value: value
          // });

          console.log("[fillGeneric] CWD:", cwd());
          console.log(
            "[fillGeneric] LS in current directory:",
            readdirSync(cwd())
          );
          const fullPath = join(
            process.cwd(),
            "src",
            "fonts",
            "BrushScriptStd.otf"
          );

          console.log("[fillGeneric] fullPath:", fullPath);

          const fileExists = existsSync(fullPath);
          console.log("[fillGeneric] fileExists:", fileExists);

          const signatureFont = await embedPdfFont(pdf, fullPath);

          // Then update with our custom font
          field.updateAppearances(signatureFont);

          log.info(
            `[fillGeneric] Updated signature field appearance with font: ${signatureFont.name}`
          );
        }

        field.enableReadOnly();
      }
    }

    // Note: pdf-lib doesn't have a getFonts() method
    // console.log("[fillGeneric] Signature font embedded:", {
    //   fontName: signatureFont.name,
    //   fontRef: signatureFont.ref?.toString()
    // });

    return pdf;
  } catch (e) {
    const error = e as Error;
    log.error("Failed to fill generic", { error });
    throw error;
  }
}
