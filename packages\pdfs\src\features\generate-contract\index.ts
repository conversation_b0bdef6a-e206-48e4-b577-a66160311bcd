import { UtilityType } from "@prisma/client";
import { getBanking<PERSON>ields } from "./data/contract-template/banking";
import { getBaseFields } from "./data/contract-template/base";
import { getCompanyFields } from "./data/contract-template/company";
import { getContactFields } from "./data/contract-template/contact";
import { getContractFields } from "./data/contract-template/contract";
import { getMPANFields } from "./data/contract-template/electric/mpan";
import { getMPRNFields } from "./data/contract-template/gas/mprn";
import { getMiscFields } from "./data/contract-template/misc";
import { getSoleTraderFields } from "./data/contract-template/sole-trader";
import { getTPIFields } from "./data/contract-template/tpi";
import type {
  PDFTemplateData,
  PDFTemplateDataElectric,
  PDFTemplateDataGas,
  PDFTemplateFieldData
} from "./types";

export function createContractTemplate(
  utilityType: UtilityType,
  data: PDFTemplateData
) {
  switch (utilityType) {
    case UtilityType.GAS:
      return createGasTemplate(data as PDFTemplateDataGas);
    default:
      return createElectricTemplate(data as PDFTemplateDataElectric);
  }
}

function createBaseTemplate(data: PDFTemplateData) {
  return [
    ...getBaseFields(data),
    ...getTPIFields("tpi"),
    ...getCompanyFields(data, "company"),
    ...getSoleTraderFields(data, "customer"),
    ...getContactFields(data, "contact"),
    ...getContractFields(data, "contract"),
    ...getBankingFields(data),
    ...getMiscFields("misc")
  ];
}

export function createElectricTemplate(
  data: PDFTemplateDataElectric
): PDFTemplateFieldData[] {
  return [...createBaseTemplate(data), ...getMPANFields(data.mpan, "mpan")];
}

export function createGasTemplate(data: PDFTemplateDataGas) {
  return [...createBaseTemplate(data), ...getMPRNFields(data.mprn)];
}
