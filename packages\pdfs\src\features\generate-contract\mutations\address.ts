export function getAddressFields(
  address_line_1: string,
  city: string,
  county: string,
  postcode: string,
  prefix: string,
  display_name?: string
) {
  return [
    {
      key: `${prefix}_city`,
      value: city
    },
    {
      key: `${prefix}_county`,
      value: county
    },
    {
      key: `${prefix}_line_1`,
      value: address_line_1
    },
    {
      key: `${prefix}_postcode`,
      value: postcode
    },
    {
      key: `${prefix}_full`,
      value: getAddressDisplayName(postcode, display_name)
    }
  ];
}

// TODO: Looks like a duplicate function
export function getAddressDisplayName(
  postcode: string,
  displayName: string | undefined
): string {
  if (!displayName) {
    return postcode;
  }

  const cleanedDisplayName = displayName.replace(`, ${postcode}`, "").trim();

  return `${cleanedDisplayName}, ${postcode}`;
}
