import type { PDFTemplateData, PDFTemplateFieldData } from "../types";

/**
 * Function to get the business registration number.
 *
 * - `charity_number` got priority precedence
 * - `company registration_number` served then (if found)
 * - absence of or no data for either `charity_number` or `company_registration_number` falls back to sole trader
 *
 * @param data
 * @param pdfKey: eg "company_registration_number"
 * @returns PDFTemplateFieldData[] dependant on the input data
 */
export function getBusinessRegNumber(data: PDFTemplateData, pdfKey: string) {
  const { company_registration_number, charity_number } = data;

  const fields: PDFTemplateFieldData[] = [];

  fields.push({
    key: "company_registration_number_only",
    value: company_registration_number ?? ""
  });

  if (charity_number) {
    fields.push({
      key: pdfKey,
      value: charity_number
    });

    return fields;
  }

  if (company_registration_number) {
    fields.push({
      key: pdfKey,
      value: company_registration_number
    });

    return fields;
  }

  // Returns for Sole Traders
  return [
    {
      key: pdfKey,
      value: " "
    }
  ];
}
