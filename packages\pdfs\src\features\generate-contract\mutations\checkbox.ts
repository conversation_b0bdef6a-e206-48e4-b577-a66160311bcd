import type { PDFTemplateFieldData } from "../types";
import { formatString } from "./string";

export function getRenewalFields(
  current_supplier: string,
  new_supplier: string
): PDFTemplateFieldData[] {
  const renewal = current_supplier === new_supplier;

  return [
    {
      key: "renewal_check_yes",
      value: renewal ? "true" : "false",
      isCheckbox: true
    },
    {
      key: "renewal_check_no",
      value: renewal ? "false" : "true",
      isCheckbox: true
    },
    {
      key: "sales_type",
      value: renewal ? "Renewal" : "Acquisition"
    }
  ];
}

export function getCheckboxFields(
  values: string[],
  targetValue: string,
  prefix: string
) {
  const fields: PDFTemplateFieldData[] = [];

  for (let i = 0; i < values.length; i += 1) {
    const currentValue = values[i];

    if (currentValue === undefined) {
      continue; // Skip undefined values
    }

    let fieldMatchesTargetValue = false;

    if (currentValue === targetValue) {
      fieldMatchesTargetValue = true;
    }

    const key = formatString(currentValue);

    fields.push({
      key: `${prefix}_${key}_yes`,
      value: fieldMatchesTargetValue ? "true" : "false",
      isCheckbox: true
    });

    fields.push({
      key: `${prefix}_${key}_no`,
      value: fieldMatchesTargetValue ? "false" : "true",
      isCheckbox: true
    });
  }

  return fields;
}
