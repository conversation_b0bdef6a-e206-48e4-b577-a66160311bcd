import { format } from "date-fns";
import type { PDFTemplateFieldData } from "../types";

export function getDateFields(
  date: Date,
  prefix: string
): PDFTemplateFieldData[] {
  const formattedDate = format(new Date(date), "dd-MM-yy");
  const formattedDateFull = format(new Date(date), "dd-MM-yyyy");

  const date3 = getSeperateDateFields(formattedDate, 3, prefix);
  const date6 = getSeperateDateFields(formattedDate, 6, prefix);

  return [
    ...date3,
    ...date6,
    {
      key: `${prefix}_full`,
      value: formattedDateFull
    }
  ];
}

function getSeperateDateFields(
  formattedDate: string,
  count: number,
  prefix: string
): PDFTemplateFieldData[] {
  const parts = formattedDate.split("").filter(e => e !== "-");
  const partLength = parts.length / count;

  const fields: PDFTemplateFieldData[] = [];
  for (let i = 0; i < count; i += 1) {
    fields.push({
      key: `${prefix}_seperate_${count}_${i}`,
      value: parts.slice(i * partLength, i * partLength + partLength).join("")
    });
  }

  return fields;
}

export function formattedDate(date: Date) {
  return format(new Date(date), "dd-MM-yyyy");
}

export function formattedDateLong(date: Date) {
  return format(new Date(date), "dd-MM-yyyy HH:mm:ss OOOO");
}
