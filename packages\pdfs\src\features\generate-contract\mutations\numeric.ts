import type { PDFTemplateFieldData } from "../types";
import { fieldWithSuffix } from "./string";

export function getNumericSplitFields(
  value: string,
  name: string,
  prefix?: string,
  groupSize = 1
) {
  const fields: PDFTemplateFieldData[] = [];
  const fullPrefix = prefix ? `${prefix}_` : "";

  // Generating individual character fields
  for (let i = 0; i < value.length; i += 1) {
    const char = value[i];

    if (char === undefined) {
      throw new Error(`Invalid numeric value: missing character at index ${i}`);
    }

    fields.push({
      key: `${fullPrefix}${name}_seperate_${i}`,
      value: char
    });
  }

  // Generating grouped character fields
  const numberOfGroups = Math.ceil(value.length / groupSize);
  for (let i = 0; i < numberOfGroups; i += 1) {
    const start = i * groupSize;
    const end = start + groupSize;
    const groupValue = value.slice(start, end);

    fields.push({
      key: `${fullPrefix}${name}_seperate_${groupSize}_${i}`,
      value: groupValue
    });
  }

  // Adding the full value field
  fields.push({
    key: `${fullPrefix}${name}_full`,
    value
  });

  return fields;
}

/**
 * Calculate the contract length in years and months based on a given number of months,
 * and return an array of PDFTemplateFieldData objects that can be used to populate
 * fields in a PDF template.
 *
 * @param valueInMonths - The length of the contract in months.
 * @param prefix - The prefix to use for the field keys in the returned array.
 * @returns An array of PDFTemplateFieldData objects containing the contract length in years and months,
 *          along with the suffixes for each field (i.e. "Year" or "Years", "Month" or "Months").
 */
export function getContractLengthFields(
  valueInMonths: number,
  prefix: string
): PDFTemplateFieldData[] {
  const months = valueInMonths;
  const years = valueInMonths / 12;

  const yearsPrefix = years > 1 ? "Years" : "Year";
  const monthsPrefix = months > 1 ? "Months" : "Month";

  return [
    {
      key: `${prefix}_years`,
      value: years.toString()
    },
    {
      key: `${prefix}_months`,
      value: months.toString()
    },
    {
      key: `${prefix}_years_suffix`,
      value: fieldWithSuffix(years, yearsPrefix)
    },
    {
      key: `${prefix}_months_suffix`,
      value: fieldWithSuffix(months, monthsPrefix)
    },
    ...getNumericSplitFields(months.toString(), "months", prefix)
  ];
}

/**
 * Calculate the commission amounts in pounds and pence based on a given value,
 * and return an array of PDFTemplateFieldData objects that can be used to populate
 * fields in a PDF template.
 *
 * @param value - The value used to calculate the commission amounts.
 * @param prefix - The prefix to use for the field keys in the returned array.
 * @returns An array of PDFTemplateFieldData objects containing the commission amounts in pounds and pence,
 *          as well as the full commission amount.
 */

export function getContractCommisionFields(
  value: string,
  duration: number,
  prefix: string
): PDFTemplateFieldData[] {
  const fields: PDFTemplateFieldData[] = [];

  // comission pounds/pence = all units used x 0.01
  const commissionPounds = Math.floor(
    Number.parseFloat(value) * 0.01 * (duration / 12)
  );
  const commissionPence = Math.round(
    (Number.parseFloat(value) * 0.01 * (duration / 12) - commissionPounds) * 100
  );

  fields.push({
    key: `${prefix}_commission_pounds`,
    value: commissionPounds.toString()
  });

  fields.push({
    key: `${prefix}_commission_pence`,
    value: commissionPence.toString()
  });

  fields.push({
    key: `${prefix}_commission_full`,
    value: `${commissionPounds.toString()}.${commissionPence
      .toString()
      .padStart(2, "0")}`
  });

  return fields;
}

/**
 * Take the annual price and divide it by 12 to get the monthly price.
 *
 * TODO (James) This should come down from the backend as a string, rather than
 *            parsing + manipulating the float "annual price".
 *
 * @param annualPrice The annual price, as a string.
 * @returns The monthly price to 2 decimal places, as a string.
 */
export function getPricePerMonth(annualPrice: string) {
  return (Number.parseFloat(annualPrice) / 12).toFixed(2).toString();
}

export function getStandingChargeFields(
  standing_charge: string,
  prefix: string
) {
  const fields: PDFTemplateFieldData[] = [];

  // convert pence per day to pounds per day
  const standingChargePoundsPerDay = (Number.parseFloat(standing_charge) / 100)
    .toFixed(2)
    .toString();

  const standingChargePoundsPerQuarter = (
    Number.parseFloat(standingChargePoundsPerDay) * 90
  )
    .toFixed(2)
    .toString();

  fields.push({
    key: `${prefix}_standing_charge`, // pence per day
    value: standing_charge.toString()
  });

  fields.push({
    key: `${prefix}_standing_charge_pound_per_day`,
    value: standingChargePoundsPerDay.toString()
  });

  fields.push({
    key: `${prefix}_standing_charge_pound_per_quarter`,
    value: standingChargePoundsPerQuarter.toString()
  });

  return fields;
}
