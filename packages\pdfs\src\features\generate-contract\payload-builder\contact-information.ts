import { composeCompanyContact } from "@watt/common/src/utils/company-contact";
import type { FindFirstCompanyContactSelectCompanyContactPrefilledContractGetPayload } from "@watt/db/src/types/contract";
import type { PDFTemplateData } from "../types";
import type { TransformationResult } from "./types";

export type ContactInformation = Pick<
  PDFTemplateData,
  | "company_contact_fullname"
  | "company_contact_forename"
  | "company_contact_surname"
  | "company_contact_email"
  | "company_contact_phone"
  | "company_contact_position"
>;

export type ContactInformationInput =
  FindFirstCompanyContactSelectCompanyContactPrefilledContractGetPayload;

type TransformContactInformationInputResult =
  TransformationResult<ContactInformation>;

export function transformContactInformationInput(
  input: ContactInformationInput
): TransformContactInformationInputResult {
  try {
    const company_contact_fullname = composeCompanyContact(
      input.forename,
      input.surname,
      input.salutation
    );

    if (typeof company_contact_fullname !== "string") {
      throw new Error("Forename and surname are required");
    }

    const company_contact_email =
      input.emails.find(email => email.isPrimary)?.email ?? "";

    const company_contact_phone =
      input.phoneNumbers.find(phoneNumber => phoneNumber.isPrimary)
        ?.phoneNumber ?? "";

    return {
      success: true,
      data: {
        company_contact_fullname,
        company_contact_forename: input.forename,
        company_contact_surname: input.surname,
        company_contact_email,
        company_contact_phone,
        company_contact_position: input.position
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
