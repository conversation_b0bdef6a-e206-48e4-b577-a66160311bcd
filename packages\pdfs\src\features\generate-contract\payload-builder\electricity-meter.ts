import { type MPANLong, parseMpan } from "@watt/common/src/mpan/mpan";
import { getFullMeterIdentifier } from "@watt/common/src/utils/get-full-meter-identifier";
import type { FindFirstQuoteSelectQuoteListPrefilledContractGetPayload } from "@watt/db/src/types/contract";
import type { MPAN } from "../types";
import type { TransformationResult } from "./types";

export type ElectricityMeter = MPAN;

export type ElectricityMeterInput =
  FindFirstQuoteSelectQuoteListPrefilledContractGetPayload["quoteList"]["siteMeter"]["electricSiteMeter"];

type TransformElectricityMeterInputResult =
  TransformationResult<ElectricityMeter>;

export function transformElectricyMeterInput(
  input: ElectricityMeterInput
): TransformElectricityMeterInputResult {
  try {
    if (!input) {
      throw new Error("Invalid input");
    }

    const fullMeterIdentifier = getFullMeterIdentifier(input.mpan);

    const { data, error } = parseMpan(fullMeterIdentifier);

    if (!data) {
      throw new Error(error?.message ?? "Failed to parse MPAN");
    }

    const mpan = data as MPANLong;

    const uniqueIdentifiers = transformUniqueIdentifier(mpan.uniqueIdentifier);

    if (!uniqueIdentifiers.success) {
      throw new Error(uniqueIdentifiers.error);
    }

    const electricityMeter: ElectricityMeter = {
      full: mpan.mpan,
      top_line: mpan.mpan.slice(0, 8),
      bottom_line: mpan.mpan.slice(8),
      profile_class: mpan?.profileClass ?? "",
      meter_time_switch_code: mpan?.meterTimeSwitchCode ?? "",
      line_loss_factor_class: mpan?.lineLossFactorClass ?? "",
      distributor: mpan.distributor,
      checksum: mpan.checksum,
      ...uniqueIdentifiers.data
    };

    return {
      success: true,
      data: electricityMeter
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

type TransformUniqueIdentifierResult = TransformationResult<{
  unique_1: ElectricityMeter["unique_1"];
  unique_2: ElectricityMeter["unique_2"];
}>;

type TransformUniqueIdentifierInput = string;

function transformUniqueIdentifier(
  input: TransformUniqueIdentifierInput
): TransformUniqueIdentifierResult {
  try {
    if (input.length !== 8) {
      throw new Error("Invalid uniqueIdentifier length");
    }

    return {
      success: true,
      data: {
        unique_1: input.slice(0, 4),
        unique_2: input.slice(4)
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
