import {
  type BankingInformationInput,
  transformBankingInformationInput
} from "./banking-information";
import { transformBuild } from "./build-transformer";
import {
  type ContactInformationInput,
  transformContactInformationInput
} from "./contact-information";
import {
  type ElectricityMeterInput,
  transformElectricyMeterInput
} from "./electricity-meter";
import { type GasMeterInput, transformGasMeterInput } from "./gas-meter";
import {
  type QuoteInformationInput,
  transformQuoteInformationInput
} from "./quote-information";
import { type SignatureInput, transformSignatureInput } from "./signature";
import {
  type SoleTraderInformationInput,
  transformSoleTraderInformationInput
} from "./sole-trader-information";
import type {
  ContractPayload,
  ContractPayloadResult,
  ErrorList
} from "./types";

class ContractPayloadBuilder {
  private contract: ContractPayload = {};
  private errors: ErrorList = {};

  public addSignature(input: SignatureInput): ContractPayloadBuilder {
    const result = transformSignatureInput(input);

    if (!result.success) {
      this.errors.signature = result.error;
    } else {
      this.contract.signature = result.data;
    }

    return this;
  }

  public addBankingInformation(
    input: BankingInformationInput
  ): ContractPayloadBuilder {
    const result = transformBankingInformationInput(input);

    if (!result.success) {
      this.errors.$banking_information = result.error;
    } else {
      this.contract = {
        ...this.contract,
        ...result.data
      };
    }

    return this;
  }

  public addElectricityMeter(
    input: ElectricityMeterInput
  ): ContractPayloadBuilder {
    const result = transformElectricyMeterInput(input);

    if (!result.success) {
      this.errors.mpan = result.error;
    } else {
      this.contract.mpan = result.data;
    }

    return this;
  }

  public addGasMeter(input: GasMeterInput): ContractPayloadBuilder {
    const result = transformGasMeterInput(input);

    if (!result.success) {
      this.errors.mprn = result.error;
    } else {
      this.contract.mprn = result.data;
    }

    return this;
  }

  public addSoleTraderInformation(
    input: SoleTraderInformationInput
  ): ContractPayloadBuilder {
    const result = transformSoleTraderInformationInput(input);

    if (!result.success) {
      this.errors.sole_trader = result.error;
    } else {
      this.contract.sole_trader = result.data;
    }

    return this;
  }

  public addContactInformation(
    input: ContactInformationInput
  ): ContractPayloadBuilder {
    const result = transformContactInformationInput(input);

    if (!result.success) {
      this.errors.$contact_information = result.error;
    } else {
      this.contract = {
        ...this.contract,
        ...result.data
      };
    }

    return this;
  }

  public addQuoteInformation(
    input: QuoteInformationInput
  ): ContractPayloadBuilder {
    const result = transformQuoteInformationInput(input);

    if (!result.success) {
      this.errors.$quote_information = result.error;
    } else {
      this.contract = {
        ...this.contract,
        ...result.data
      };
    }

    return this;
  }

  public build(): ContractPayloadResult {
    const result = transformBuild(this.contract);

    if (Object.keys(this.errors).length > 0 || !result.success) {
      if (!result.success) {
        this.errors.$payload_builder = result.error;
      }

      return {
        success: false,
        errors: this.errors
      };
    }

    return {
      success: true,
      data: result.data
    };
  }
}

export { ContractPayloadBuilder as PrefilledContractPayloadBuilder };
