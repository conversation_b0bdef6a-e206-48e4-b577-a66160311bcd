import { BusinessType } from "@prisma/client";
import { calculateCommission } from "@watt/common/src/utils/calculate-commission";
import type { FindFirstQuoteSelectQuoteListPrefilledContractGetPayload } from "@watt/db/src/types/contract";
import { BusinessTypeMap } from "../data/contract-template/base";
import type { PDFTemplateData } from "../types";
import type { TransformationResult } from "./types";

export type QuoteInformationInput =
  FindFirstQuoteSelectQuoteListPrefilledContractGetPayload;

type QuoteRatesInfo = Pick<
  PDFTemplateData,
  | "quote_standing_charge"
  | "quote_day_unit_rate"
  | "quote_annual_price"
  | "quote_night_unit_rate"
  | "quote_weekend_unit_rate"
  | "quote_unit_rate"
  | "quote_kva_charge"
  | "quote_duration_months"
  | "quote_contract_type"
>;

function transformQuoteRates(
  input: QuoteInformationInput
): TransformationResult<QuoteRatesInfo> {
  try {
    const contractType =
      (input.electricQuote?.contractType || input.gasQuote?.contractType) ??
      "Standard";
    const standingCharge =
      input.electricQuote?.standingCharge ?? input.gasQuote?.standingCharge;
    const dayUnitRate =
      input.electricQuote?.unitRate ?? input.gasQuote?.unitRate;
    const annualPrice =
      input.electricQuote?.annualPrice ?? input.gasQuote?.annualPrice;
    const nightUnitRate = input.electricQuote?.nightUnitRate;
    const weekendUnitRate = input.electricQuote?.weekendUnitRate;
    const unitRate = input.electricQuote?.unitRate ?? input.gasQuote?.unitRate;
    const kvaCharge = input.electricQuote?.capacityChargeKva;

    return {
      success: true,
      data: {
        quote_contract_type: contractType,
        quote_duration_months: input.duration,
        quote_standing_charge: standingCharge
          ? String(standingCharge.toFixed(2))
          : undefined,
        quote_day_unit_rate: dayUnitRate
          ? String(dayUnitRate.toFixed(2))
          : undefined,
        quote_annual_price: annualPrice
          ? String(annualPrice.toFixed(2))
          : undefined,
        quote_night_unit_rate: nightUnitRate
          ? String(nightUnitRate.toFixed(2))
          : undefined,
        quote_weekend_unit_rate: weekendUnitRate
          ? String(weekendUnitRate.toFixed(2))
          : undefined,
        quote_unit_rate: unitRate ? String(unitRate.toFixed(2)) : undefined,
        quote_kva_charge: kvaCharge ? Number(kvaCharge).toFixed(2) : undefined
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

type ContractInfo = Pick<
  PDFTemplateData,
  | "contract_start_date"
  | "contract_end_date"
  | "contract_end_date_notice"
  | "contract_price_review_date"
  | "contract_price_review_notice_date"
  | "current_contract_end_date"
  | "current_supplier"
  | "new_supplier"
  | "contract_commission_pounds"
  | "contract_commission_pence"
  | "contract_commission_full"
>;

function transformContractInfo(
  input: QuoteInformationInput,
  usageAnnual: number | undefined,
  unitRate: number | undefined,
  standingCharge: number | undefined
): TransformationResult<ContractInfo> {
  try {
    const commission = calculateCommission(
      usageAnnual ?? 0,
      unitRate ?? 0,
      standingCharge ?? 0,
      input.duration / 12
    ).toFixed(2);
    const commissionPounds = Math.floor(Number(commission));
    const commissionPence = Math.round((Number(commission) * 100) % 100);

    return {
      success: true,
      data: {
        contract_start_date: input.quoteList.contractStartDate,
        contract_end_date: input.endDate,
        contract_end_date_notice: undefined,
        contract_price_review_date: undefined,
        contract_price_review_notice_date: undefined,
        current_contract_end_date: undefined,
        current_supplier: input.quoteList.currentProvider.udcoreId ?? undefined,
        new_supplier: input.provider.udcoreId ?? "",
        contract_commission_full: commission,
        contract_commission_pounds: commissionPounds.toString(),
        contract_commission_pence: commissionPence.toString()
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

type UsageInfo = Pick<
  PDFTemplateData,
  | "usage_total_annual"
  | "usage_day_annual"
  | "usage_night_annual"
  | "usage_weekend_annual"
>;

function transformUsageInfo(
  input: QuoteInformationInput
): TransformationResult<UsageInfo> {
  try {
    const usageAnnual =
      input.quoteList.electricityUsage?.totalUsage ??
      input.quoteList.gasUsage?.totalUsage;
    const usageDayAnnual = input.quoteList.electricityUsage?.dayUsage;
    const usageNightAnnual = input.quoteList.electricityUsage?.nightUsage;
    const usageWeekendAnnual = input.quoteList.electricityUsage?.weekendUsage;

    return {
      success: true,
      data: {
        usage_total_annual: usageAnnual ? String(usageAnnual) : undefined,
        usage_day_annual: usageDayAnnual ? String(usageDayAnnual) : undefined,
        usage_night_annual: usageNightAnnual
          ? String(usageNightAnnual)
          : undefined,
        usage_weekend_annual: usageWeekendAnnual
          ? String(usageWeekendAnnual)
          : undefined
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export type AddressInfo = Pick<
  PDFTemplateData,
  | "address"
  | "company_main_address"
  | "company_main_postcode"
  | "company_main_city"
  | "company_main_county"
>;

function transformAddressInfo(
  input: QuoteInformationInput
): TransformationResult<AddressInfo> {
  try {
    const companyAddress =
      input.quoteList.siteMeter.companySite.company.entityAddress;
    const siteAddress = input.quoteList.siteMeter.companySite.entityAddress;

    return {
      success: true,
      data: {
        address: {
          company_address_line_1: siteAddress.addressLine1 ?? "",
          company_postal_town: siteAddress.postalTown ?? "",
          company_county: siteAddress.county ?? "",
          company_postcode: siteAddress.postcode,
          company_display_name: siteAddress.displayName ?? "",
          business_address_line_1: companyAddress.addressLine1 ?? "",
          business_postal_town: companyAddress.postalTown ?? "",
          business_county: companyAddress.county ?? "",
          business_postcode: companyAddress.postcode,
          business_display_name: companyAddress.displayName ?? ""
        },
        company_main_address: companyAddress.displayName ?? "",
        company_main_postcode: companyAddress.postcode,
        company_main_city: companyAddress.postalTown ?? "",
        company_main_county: companyAddress.county ?? ""
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

type CompanyInfo = Pick<
  PDFTemplateData,
  | "company_name"
  | "company_registration_number"
  | "charity_number"
  | "business_type"
  | "industries"
>;

function transformCompanyInfo(
  input: QuoteInformationInput
): TransformationResult<CompanyInfo> {
  try {
    const company = input.quoteList.siteMeter.companySite.company;

    const data: CompanyInfo = {
      company_name: company.name,
      company_registration_number: company.registrationNumber,
      business_type: BusinessTypeMap[company.businessType],
      charity_number: undefined,
      industries: undefined
    };

    if (company.businessType === BusinessType.SOLE_TRADER) {
      data.company_registration_number = undefined;
    }

    return {
      success: true,
      data
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export type QuoteInformation = QuoteRatesInfo &
  ContractInfo &
  UsageInfo &
  AddressInfo &
  CompanyInfo;

type TransformQuoteInformationInputResult =
  TransformationResult<QuoteInformation>;

export function transformQuoteInformationInput(
  input: QuoteInformationInput
): TransformQuoteInformationInputResult {
  try {
    const quoteRates = transformQuoteRates(input);

    if (!quoteRates.success) {
      throw new Error(quoteRates.error);
    }

    const usageInfo = transformUsageInfo(input);

    if (!usageInfo.success) {
      throw new Error(usageInfo.error);
    }

    const contractInfo = transformContractInfo(
      input,
      input.quoteList.electricityUsage?.totalUsage ??
        input.quoteList.gasUsage?.totalUsage,
      input.electricQuote?.unitRate ?? input.gasQuote?.unitRate,
      input.electricQuote?.standingCharge ?? input.gasQuote?.standingCharge
    );

    if (!contractInfo.success) {
      throw new Error(contractInfo.error);
    }

    const addressInfo = transformAddressInfo(input);

    if (!addressInfo.success) {
      throw new Error(addressInfo.error);
    }

    const companyInfo = transformCompanyInfo(input);

    if (!companyInfo.success) {
      throw new Error(companyInfo.error);
    }

    return {
      success: true,
      data: {
        ...quoteRates.data,
        ...contractInfo.data,
        ...usageInfo.data,
        ...addressInfo.data,
        ...companyInfo.data
      }
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
