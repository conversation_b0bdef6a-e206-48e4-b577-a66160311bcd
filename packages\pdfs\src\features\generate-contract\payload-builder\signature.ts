import type { PDFTemplateData } from "../types";
import type { TransformationResult } from "./types";

export type Signature = PDFTemplateData["signature"];

export type SignatureInput = string;

type TransformSignatureInputResult = TransformationResult<Signature>;

export function transformSignatureInput(
  input: SignatureInput
): TransformSignatureInputResult {
  try {
    return {
      success: true,
      data: input
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
