import type { CompanyContact } from "@prisma/client";
import { differenceInYears, isFuture } from "date-fns";
import type { FindManyCompanyContactAddressSelectCompanyContactAddressPrefilledContractGetPayload } from "../../../../../api/src/types/contract";
import type { SoleTraderDetails } from "../types";
import type { TransformationResult } from "./types";

export type SoleTraderInformation = SoleTraderDetails;

export type SoleTraderInformationInput = {
  dateOfBirth: CompanyContact["dateOfBirth"];
  addresses: Partial<FindManyCompanyContactAddressSelectCompanyContactAddressPrefilledContractGetPayload>[];
};

type TransformSoleTraderInformationInputResult =
  TransformationResult<SoleTraderInformation>;

export function transformSoleTraderInformationInput(
  input: SoleTraderInformationInput
): TransformSoleTraderInformationInputResult {
  try {
    const data: SoleTraderInformation = {
      date_of_birth: undefined,
      addresses: []
    };

    if (input.dateOfBirth) {
      const ageResult = calculateAge(input.dateOfBirth);

      if (!ageResult.success) {
        return {
          success: false,
          error: ageResult.error
        };
      }

      if (ageResult.data < 16) {
        throw new Error("Sole trader must be at least 16 years old");
      }

      data.date_of_birth = input.dateOfBirth;
    }

    if (input.addresses.length > 0) {
      const sortedAddresses = [...input.addresses].sort((a, b) => {
        if (!a.movedOutDate && !b.movedOutDate) {
          return 0;
        }

        if (!a.movedOutDate) {
          return -1;
        }

        if (!b.movedOutDate) {
          return 1;
        }

        return (
          new Date(b.movedOutDate).getTime() -
          new Date(a.movedOutDate).getTime()
        );
      });

      data.addresses = sortedAddresses.map(iteration => {
        const { address, movedInDate, movedOutDate } = iteration;

        if (!address) {
          throw new Error("Invalid address history for sole trader");
        }

        return {
          address: {
            customer_address_line_1: address.addressLine1 ?? "",
            customer_postal_town: address.postalTown ?? "",
            customer_county: address.county ?? "",
            customer_postcode: address.postcode,
            customer_display_name: address.displayName ?? ""
          },
          move_in_date: movedInDate,
          move_out_date: movedOutDate ?? undefined
        };
      });
    }

    return {
      success: true,
      data
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

type CalculateAgeResult = TransformationResult<number>;

type CalculateAgeInput = Date;

function calculateAge(input: CalculateAgeInput): CalculateAgeResult {
  try {
    if (isFuture(input)) {
      throw new Error("Date of birth cannot be in the future");
    }

    return {
      success: true,
      data: differenceInYears(new Date(), input)
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
