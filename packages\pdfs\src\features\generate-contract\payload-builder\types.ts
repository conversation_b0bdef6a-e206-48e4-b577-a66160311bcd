import type { BankingInformation } from "./banking-information";
import type { ElectricityMeter } from "./electricity-meter";
import type { GasMeter } from "./gas-meter";
import type { AddressInfo } from "./quote-information";
import type { SoleTraderInformation } from "./sole-trader-information";

export type ContractPayload = {
  created_at?: Date;
  current_contract_end_date?: Date;
  contract_start_date?: Date;
  contract_end_date?: Date;
  contract_end_date_notice?: Date;
  contract_price_review_date?: Date;
  contract_price_review_notice_date?: Date;
  contract_commission_pounds?: string;
  contract_commission_pence?: string;
  contract_commission_full?: string;
  company_name?: string;
  company_registration_number?: string;
  charity_number?: string;
  contact_title?: string;
  company_contact_fullname?: string;
  company_contact_forename?: string;
  company_contact_surname?: string;
  company_contact_email?: string;
  company_contact_phone?: string;
  company_contact_position?: string;
  address?: AddressInfo["address"];
  company_main_address?: string;
  company_main_postcode?: string;
  company_main_city?: string;
  company_main_county?: string;
  account_name?: string;
  account_number?: string;
  sort_code?: string;
  bank_name?: string;
  mpan?: ElectricityMeter;
  mprn?: GasMeter;
  sole_trader?: SoleTraderInformation;
  quote_contract_type?: string;
  signature?: string;
  // Fields for error collection
  $payload_builder?: undefined;
  $contact_information?: undefined;
  $quote_information?: undefined;
  $banking_information?: undefined;
};

export type ErrorList = Partial<Record<keyof ContractPayload, string>>;

export type TransformationResult<T, E = string> =
  | {
      success: true;
      data: T;
    }
  | ({
      success: false;
    } & (E extends ErrorList ? { errors: E } : { error: E }));

export type ContractPayloadResult = TransformationResult<
  ContractPayload,
  ErrorList
>;
