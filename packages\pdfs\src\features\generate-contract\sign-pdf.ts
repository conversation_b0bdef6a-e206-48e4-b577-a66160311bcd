import { log } from "@watt/common/src/utils/axiom-logger";
import { type PDFDocument, type PDFField, PDFTextField } from "pdf-lib";
import { FONTS } from "../../fonts";
import { embedFont } from "./embed-font";
import { getDateFields } from "./mutations/date";

export async function signPdf(originalPdf: PDFDocument, signature: string) {
  const form = originalPdf.getForm();

  const signatureFont = await embedFont({
    pdf: originalPdf,
    fontName: FONTS.BRUSH_SCRIPT_STD
  });

  const fieldNames = form.getFields().map(e => e.getName());

  const signedAtField = getDateFields(new Date(), "signed_at").filter(e =>
    fieldNames.includes(e.key)
  );

  let signatureField: PDFTextField | undefined;

  try {
    signatureField = form.getField("signature") as PDFTextField;
  } catch (error) {
    log.warn("Field 'signature' doesn't exist, skipping...");
  }

  if (signatureField) {
    signatureField.setText(signature);
    signatureField.updateAppearances(signatureFont);
  }

  for (const { key, value } of signedAtField) {
    if (!value) {
      console.warn(`[No value found for field '${key}' on 'signed_at'`);
      continue;
    }

    let field: PDFField | undefined;

    try {
      field = form.getField(key);
    } catch (error) {
      console.error(`Field ${key} doesn't exist, skipping...`);
      continue;
    }

    if (!(field instanceof PDFTextField)) {
      log.error(`Field '${key}' was not a PDFTextField`);
      continue;
    }

    field.setText(value.toString());
  }

  return originalPdf;
}
