export type MPRN = {
  /**
   * The complete MPRN number
   */
  full: string;
};

export type MPAN = {
  /**
   * The complete MPAN number
   */
  full: string;

  /**
   * The top line of the MPAN number
   */
  top_line: string;

  /**
   * The bottom line of the MPAN number
   */
  bottom_line: string;

  /**
   * Two digit code
   */
  profile_class: string;

  /**
   * Three-digit code
   */
  meter_time_switch_code: string;

  /**
   * Line loss factor identifier, a three-digit string
   */
  line_loss_factor_class: string;

  /**
   * Distributor id, a two-digit string
   */
  distributor: string;

  /**
   * Left side of the unique MPAN identifier.
   */
  unique_1: string;

  /**
   * Right side of the unique MPAN identifier.
   */
  unique_2: string;

  /**
   * Three-digit checksum
   */
  checksum: string;
};

/**
 * Banking information for the contract template.
 */
export type BankingInfo = {
  /**
   * The name of the account holder.
   */
  account_name: string;

  /**
   * The bank account number.
   */
  account_number: string;

  /**
   * The sort code.
   */
  sort_code: string;

  /**
   * The name of the bank.
   */
  bank_name: string;
};

export type AuditTemplateData = {
  issuer: string;
  contract_signed_at: Date;
  contract_generated_at: Date;
  contract_signature: string;
  document_finger_print: string;
  user_ip: string;
};

export type EntityAddress = {
  company_address_line_1: string;
  company_postal_town: string;
  company_county: string;
  company_postcode: string;
  company_display_name: string;
};

export type BusinessAddress = {
  business_address_line_1: string;
  business_postal_town: string;
  business_county: string;
  business_postcode: string;
  business_display_name: string;
};

export type SoleTraderAddress = {
  customer_address_line_1: string;
  customer_postal_town: string;
  customer_county: string;
  customer_postcode: string;
  customer_display_name: string;
};

export type PreviousAddresses = {
  address: SoleTraderAddress;
  move_in_date?: Date;
  move_out_date?: Date;
};

export type SoleTraderDetails = {
  date_of_birth?: Date;
  addresses: PreviousAddresses[];
};

export type LOATemplateData = {
  contract_start_date: string;
  company_name: string;
  company_main_address: string;
  contract_signature: string;
  company_contact_fullname: string;
  company_contact_position: string;
  company_registration_number: string;
  mpan?: MPAN;
  mprn?: MPRN;
  quote_duration_months: string;
};

export type PDFTemplateData = {
  created_at: Date;

  contract_start_date: Date;
  contract_end_date: Date;
  current_contract_end_date?: Date;
  contract_end_date_notice?: Date;
  contract_price_review_date?: Date;
  contract_price_review_notice_date?: Date;

  contract_commission_pounds: string;
  contract_commission_pence: string;
  contract_commission_full: string;

  company_name: string;
  company_registration_number?: string;
  charity_number?: string;
  address: EntityAddress & BusinessAddress;

  contract_signature?: string;

  business_type: string;
  industries?: string;

  contact_title: string;
  company_contact_fullname: string;
  company_contact_forename: string;
  company_contact_surname: string;
  company_contact_email: string;
  company_contact_phone: string;
  company_contact_position: string;

  company_main_address: string;
  company_main_postcode: string;
  company_main_city: string;
  company_main_county: string;

  usage_total_annual?: string;
  usage_day_annual?: string;
  usage_night_annual?: string;
  usage_weekend_annual?: string;

  /**
   * The duration of the contract in months.
   * @type {number}
   * @example 12
   */
  quote_duration_months?: number;
  quote_standing_charge?: string;
  quote_day_unit_rate?: string;
  quote_annual_price?: string;

  quote_contract_type?: string;

  quote_night_unit_rate?: string;
  quote_weekend_unit_rate?: string;
  quote_unit_rate?: string;

  quote_kva_charge?: string;

  current_supplier?: string;
  new_supplier: string;

  mpan?: MPAN;
  mprn?: MPRN;

  account_name: string;
  account_number: string;
  sort_code: string;
  bank_name: string;

  sole_trader?: SoleTraderDetails;

  signature?: string;
};

export type PDFTemplateDataElectric = PDFTemplateData & { mpan: MPAN };
export type PDFTemplateDataGas = PDFTemplateData & { mprn: MPRN };

export type InputAuditData = Omit<AuditTemplateData, "issuer">;

/**
 * Contract data input.
 *
 * Mostly the same as `PDFTemplateData` but the dates are strings.
 */
export type InputPDFTemplateData = Omit<
  PDFTemplateData,
  "created_at" | "contract_start_date" | "contract_end_date"
> & {
  created_at: string;
  contract_start_date: string;
  contract_end_date: string;
};

export type PDFTemplateFieldData = {
  key: string;
  value: string | number;
  isCheckbox?: boolean;
};

export type AuditData = { key: string; value: string | Date };
