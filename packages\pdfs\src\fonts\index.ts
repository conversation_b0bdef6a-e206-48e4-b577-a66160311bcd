const FONT_DATA = {
  BRUSH_SCRIPT_STD: "BrushScriptStd.otf"
} as const;

type FontName = keyof typeof FONT_DATA;

export function getFontPath(fontName: FontName): string {
  if (!(fontName in FONT_DATA)) {
    throw new Error(`Invalid font name: ${fontName}`);
  }

  return `src/fonts/${FONT_DATA[fontName]}`;
}

export const FONTS = Object.fromEntries(
  Object.entries(FONT_DATA).map(([k]) => [k, k])
) as { [K in FontName]: K };
