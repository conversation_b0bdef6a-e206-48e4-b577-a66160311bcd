import { log } from "@watt/common/src/utils/axiom-logger";
import { isVercelEnvironment } from "@watt/common/src/utils/is-production-environment";

/**
 * Initializes and returns a Puppeteer browser instance optimized for the current environment
 *
 * @description
 * In Vercel environment:
 * - Uses @sparticuz/chromium for optimized serverless performance
 * - Configures browser with Chromium-specific args and viewport settings
 *
 * In local/other environments:
 * - Uses standard Puppeteer installation
 * - Launches with default configuration
 *
 * @returns {Promise<import('puppeteer').Browser | import('puppeteer-core').Browser>}
 * A configured Puppeteer browser instance
 *
 * @throws {Error} If browser initialization fails
 */
export async function getBrowser() {
  try {
    if (isVercelEnvironment()) {
      const chromium = await import("@sparticuz/chromium").then(
        mod => mod.default
      );

      const puppeteerCore = await import("puppeteer-core").then(
        mod => mod.default
      );

      const executablePath = await chromium.executablePath();

      const browser = await puppeteerCore.launch({
        args: chromium.args,
        defaultViewport: chromium.defaultViewport,
        executablePath,
        headless: chromium.headless
      });

      return browser;
    }

    const puppeteer = await import("puppeteer").then(mod => mod.default);

    const browser = await puppeteer.launch();

    return browser;
  } catch (error) {
    log.error("Error spawning puppeteer browser", { error });
    throw error;
  }
}
