import type { PDFTemplateData } from "../features/generate-contract/types";
import type { InputPDFTemplateData } from "../features/generate-contract/types";

/**
 * Converts date string fields in template data to Date objects
 *
 * @description
 * Transforms the following string date fields to Date objects:
 * - created_at
 * - contract_start_date
 * - contract_end_date
 *
 * All other fields from the input are preserved as-is.
 *
 * @param {InputPDFTemplateData} input - Raw template data with date strings
 * @returns {PDFTemplateData} Template data with parsed Date objects
 *
 * @example
 * const input = {
 *   created_at: "2024-03-20T10:00:00Z",
 *   contract_start_date: "2024-04-01",
 *   contract_end_date: "2025-03-31",
 *   // ... other template fields
 * };
 * const parsed = parseDates(input);
 * // Returns: { ...input, created_at: Date, contract_start_date: Date, contract_end_date: Date }
 */
export function parseDates(input: InputPDFTemplateData): PDFTemplateData {
  return {
    ...input,
    created_at: new Date(input.created_at),
    contract_start_date: new Date(input.contract_start_date),
    contract_end_date: new Date(input.contract_end_date)
  };
}
