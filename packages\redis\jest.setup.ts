// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

/**
 * Mock next-axiom to be a no-op
 * This uses the shared mock implementation from @watt/common/src/testing/next-axiom-mock
 * which properly handles async operations to prevent "Cannot log after tests are done" errors
 */
import nextAxiomMock from "@watt/common/src/testing/next-axiom-mock";
jest.mock("next-axiom", () => nextAxiomMock);

jest.mock("@watt/redis/src/cache", () => ({
  cacheWrap: async (_k: string, fn: () => Promise<unknown>) => fn()
}));
