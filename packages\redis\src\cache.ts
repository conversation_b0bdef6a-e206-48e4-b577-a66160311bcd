import { env } from "@watt/common/src/config/env";
import { isLocalEnvironment } from "@watt/common/src/utils/is-production-environment";
import { createCache } from "cache-manager";
import { KeyvCacheableMemory } from "cacheable";
import Keyv from "keyv";
import { KeyvUpstash } from "keyv-upstash";

const l1Store = new Keyv({
  store: new KeyvCacheableMemory({ ttl: 60_000, lruSize: 5_000 }),
  namespace: ""
});

const redisRestUrl = env.UPSTASH_REDIS_REST_URL;
const redisRestToken = env.UPSTASH_REDIS_REST_TOKEN;

const l2Store = new Keyv({
  store: new KeyvUpstash({
    url: redisRestUrl,
    token: redisRestToken
  }),
  namespace: ""
});

// Avoid using the in-memory cache on local environments. Makes testing easier.
const isLocal = isLocalEnvironment();

const stores = isLocal ? [l2Store] : [l1Store, l2Store];

export const cache = createCache({ stores });

export async function cacheWrap<T>(
  key: string,
  loader: () => Promise<T>,
  ttlSeconds: number,
  { cacheFalsy = false }: { cacheFalsy?: boolean } = {}
): Promise<T> {
  return cache
    .wrap(
      key,
      async () => {
        const data = await loader();

        // ⚠️  skip-cache rule
        if (!cacheFalsy && (data === null || data === undefined)) {
          // throw a sentinel error so `wrap` won’t store the value
          throw new Error("__SKIP_CACHE__");
        }

        return data;
      },
      { ttl: ttlSeconds * 1_000 }
    )
    .catch(err => {
      // unwrap the sentinel error → just return the original falsy value
      if (err instanceof Error && err.message === "__SKIP_CACHE__") {
        return null as unknown as T;
      }
      throw err;
    });
}
