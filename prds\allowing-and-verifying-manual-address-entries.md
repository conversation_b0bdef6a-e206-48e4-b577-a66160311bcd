# Allowing and verifying manual address entries

Currently the address picker allows users to enter their postcode and this pulls back a list of addresses from our static address database list. Customers can then proceed to pick an existing address from the drop down. Each address is associated with it's electric or gas meters allowing us to identify their meter for the site address they wish to get a quote for.

However, since the addresses are static their address might not be present in the list. We need to allow customers to enter their address manually and then verify that the address is correct against Experian Aperture API.

## Resources

<https://api.experianaperture.io/address/search/v1>

Headers:

```json
{
    "Auth-Token": "301be824-070c-41bb-90b5-1e74b4036fb9"
}
```

Body (electricity):

```json
{
    "country_iso": "GBR",
    "datasets": [
        "gb-additional-electricity"
    ],
    "components": {
        "unspecified": [
            "M2 7LP"
        ]
    },
    "options": [
        {
            "name": "search_type",
            "value": "singleline"
        },
        {
            "name": "prompt_set",
            "value": "optimal"
        }
    ]
}
```

Body (gas):

```json
{
  "country_iso": "GBR",
  "datasets": [
    "gb-additional-gas"
  ],
  "components": {
    "unspecified": [
      "M2 7LP"
    ]
  },
  "options":[
      {
          "name":"search_type",
          "value":"singleline"
      },
      {
          "name": "prompt_set",
          "value": "optimal"
      }
  ]
}
```

Response:

```json
{
    "result": {
        "more_results_available": true,
        "confidence": "Multiple matches",
        "suggestions_key": "aWQ9fmFsdF9rZXk9fmRhdGFzZXQ9R0JSfmZvcm1hdF9rZXk9R0JSJEdCUiQ3LjczME5NR0JSRlFQcEJ3QUFBQUFCQVFnQUFRQUFBQURTRVV1U0FDRVFBZ0FBQUFBQUFBQUFBUC4uWkFBQUFBRC4uLi4uQUFBQUFBQUFBQUFBQUFBQUFBQUJBQUFBQU5JUlRaSUFJUkFDQUFBQUFBQUFBQUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUVBQUFBQTBoRlBrZ0FoRUFJQUFBQUFBQUFBQUFELi4yUUFBQUFBLi4uLi53QUFBQUFBQUFBQUFBQUFBQUFBQVFBQUFBRFNFVlJTQUNFQWdnQUFBQUFBQUFBQUFQLi5aQUFBQUFELi4uLi5BQUFBQUFBQUFBQUFBQUFBQUFBQkFBQUFBTklSVlZJQUlRQ0NBQUFBQUFBQUFBQUEuLjlrQUFBQUFQLi4uLjhBQUFBQUFBQUFBQUFBQUFBQUFBRUFBQUFBMGhGWFVnQWhBSUlSQUNBQUFBQUFBQUFBQVAuLlpBQUFBQUQuLi4uLkFBQUFBQUFBQUFBQUFBQUFBQUFCQUFBQUFOSVJXaElBSVFDQ0VRQWdBQUFBQUFBQUFBRC4uMlFBQUFBQS4uLi4ud0FBQUFBQUFBQUFBQUFBQUFBQUFRQUFBQURTRVZMU0FDRUFoaEVBSUFBQUFBQUFBQUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQS0tfmVsZWM9dHJ1ZX5nYWtfdHlwZT1zaW5nbGVsaW5lfmxvY2FsaXR5PX5wb3N0YWxfY29kZT1-UUw9Nn5tYXhfc3VnZ2VzdGlvbnM9Nw",
        "suggestions_prompt": "Enter selection",
        "suggestions": [
          // ...
        ]
    }
}
```

Each suggestions looks like the following and does not provide us with the individual address fields. It only provides us with postcode and picklist_display.

```json
{
    "global_address_key": "aWQ9SW5nZXVzLCBTdC4gQW5ucyBIb3VzZSwgU3QuIEFubnMgUGxhY2UsIE1BTkNIRVNURVIgTTIgN0xQLCBVbml0ZWQgS2luZ2RvbX5hbHRfa2V5PTU2ODQyMzkxfmRhdGFzZXQ9R0JSfmZvcm1hdF9rZXk9R0JSJEdCUiQ3LjczMHlPR0JSRlFQcEJ3QUFBQUFCQXdFQUFBQUEwaEZMa2dBaEVBSUFBQUFBQUFBQUFBRC4uMlFBQUFBQS4uLi4ud0FBQUFBQUFBQUFBQUFBQUFBQUFFMHlJRGRNVUFBQUFBQUF-ZWxlYz10cnVlfnBvcz0xfmdha190eXBlPXNpbmdsZWxpbmV-bG9jYWxpdHk9TUFOQ0hFU1RFUn5wb3N0YWxfY29kZT1NMiA3TFB-UUw9Nn5tYXhfc3VnZ2VzdGlvbnM9Nw",
    "text": "Ingeus, St. Anns House, St. Anns Place, MANCHESTER M2 7LP",
    "format": "https://api.experianaperture.io/address/format/v1/aWQ9SW5nZXVzLCBTdC4gQW5ucyBIb3VzZSwgU3QuIEFubnMgUGxhY2UsIE1BTkNIRVNURVIgTTIgN0xQLCBVbml0ZWQgS2luZ2RvbX5hbHRfa2V5PTU2ODQyMzkxfmRhdGFzZXQ9R0JSfmZvcm1hdF9rZXk9R0JSJEdCUiQ3LjczMHlPR0JSRlFQcEJ3QUFBQUFCQXdFQUFBQUEwaEZMa2dBaEVBSUFBQUFBQUFBQUFBRC4uMlFBQUFBQS4uLi4ud0FBQUFBQUFBQUFBQUFBQUFBQUFFMHlJRGRNVUFBQUFBQUF-ZWxlYz10cnVlfnBvcz0xfmdha190eXBlPXNpbmdsZWxpbmV-bG9jYWxpdHk9TUFOQ0hFU1RFUn5wb3N0YWxfY29kZT1NMiA3TFB-UUw9Nn5tYXhfc3VnZ2VzdGlvbnM9Nw",
    "additional_attributes": [
        {
            "name": "picklist_display",
            "value": "Ingeus, St. Anns House, St. Anns Place, MANCHESTER"
        },
        {
            "name": "score",
            "value": "100"
        },
        {
            "name": "postcode",
            "value": "M2 7LP"
        },
        {
            "name": "full_address",
            "value": "true"
        }
    ]
}
```

The second individual address look up endpoint provides us with the individual address fields.

<https://api.experianaperture.io/address/format/v1/${global_address_key}>

Body:

```json
{
  "layouts": [
    "ElectricityUtility"
  ],
  "layout_format": "default"
}
```

```json
{
  "layouts": [
    "GasUtility"
  ],
  "layout_format": "default"
}
```

Response;

```json
{
    "result": {
        "global_address_key": "aWQ9Um9va2VyeSBIaWxsIEZhcm0sIFJvb2tlcnkgRHJpdmUsIFdlc3Rjb3R0LCBET1JLSU5HLCBTdXJyZXkgUkg0IDNMUSwgVW5pdGVkIEtpbmdkb21-YWx0X2tleT0yMDEwODk2M35kYXRhc2V0PUdCUn5mb3JtYXRfa2V5PUdCUiRHQlIkNy43MzBRT0dCUkZRUHBCd0FBQUFBQkF3RUFBQUFCUTgzajBnQWhBQVlBQUFBQUFBQUFBQUQuLjJRQUFBQUEuLi4uLndBQUFBQUFBQUFBQUFBQUFBQUFBRkpJTkNBelRGRUFBQUFBQUEtLX5lbGVjPXRydWV-cG9zPTd-Z2FrX3R5cGU9c2luZ2xlbGluZX5sb2NhbGl0eT1ET1JLSU5HfnBvc3RhbF9jb2RlPVJINCAzTFF-UUw9N35tYXhfc3VnZ2VzdGlvbnM9Nw",
        "confidence": "Verified match",
        "addresses_formatted": [
            {
                "layout_name": "ElectricityUtility",
                "address": {
                    "address_line_1": "Rookery Hill Farm",
                    "address_line_2": "Rookery Drive",
                    "address_line_3": "Westcott",
                    "locality": "Dorking",
                    "region": "Surrey",
                    "postal_code": "RH4 3LQ",
                    "country": "United Kingdom",
                    "electricity_meters": [
                        {
                            "mpan": "1900035068009",
                            "uprn": "100062495055",
                            "address_line_1": "",
                            "address_line_2": "",
                            "address_line_3": "ROOKERY HILL FARM",
                            "address_line_4": "",
                            "address_line_5": "ROOKERY DRIVE",
                            "address_line_6": "",
                            "address_line_7": "WESTCOTT",
                            "address_line_8": "DORKING",
                            "address_line_9": "",
                            "address_postal_code": "RH4 3LQ",
                            "trading_status": "T",
                            "trading_status_efd": "19971121",
                            "profile_class": "01",
                            "profile_class_efd": "20230315",
                            "meter_timeswitch_class": "801",
                            "meter_timeswitch_class_efd": "20230315",
                            "line_loss_factor": "001",
                            "line_loss_factor_efd": "20230315",
                            "standard_settlement_configuration": "0393",
                            "standard_settlement_configuration_efd": "20230315",
                            "energisation_status": "E",
                            "energisation_status_efd": "20210503",
                            "gsp_group_id": "_J",
                            "gsp_group_efd": "19971119",
                            "data_aggregator_mpid": "MANW",
                            "data_aggregator_efd": "20210503",
                            "data_collector_mpid": "MANW",
                            "data_collector_efd": "20210503",
                            "supplier_mpid": "SPOW",
                            "supplier_efd": "20210503",
                            "meter_operator_mpid": "LBSL",
                            "meter_operator_efd": "20210503",
                            "measurement_class": "A",
                            "measurement_class_efd": "20210503",
                            "green_deal_in_effect": "0",
                            "smso_mpid": "",
                            "smso_efd": "",
                            "dcc_service_flag": "",
                            "dcc_service_flag_efd": "",
                            "ihd_status": "",
                            "ihd_status_efd": "",
                            "smets_version": "",
                            "distributor_mpid": "SEEB",
                            "metered_indicator": "T",
                            "metered_indicator_efd": "20190315",
                            "metered_indicator_etd": "",
                            "consumer_type": "",
                            "relationship_status_indicator": "None",
                            "rmp_state": "O",
                            "rmp_efd": "19971121",
                            "domestic_consumer_indicator": "T",
                            "css_supplier_mpid": "",
                            "css_supply_start_date": "",
                            "meter_serial_number": "E12Z053193",
                            "meter_install_date": "20151215",
                            "meter_type": "RCAMY",
                            "map_mpid": "ACCU",
                            "map_mpid_efd": "20151215",
                            "installing_supplier_mpid": "ENRD",
                            "energy_direction": "I",
                            "energy_direction_efd": "19971119",
                            "energy_direction_etd": "",
                            "connection_type": "W",
                            "connection_type_efd": "19971208",
                            "connection_type_etd": "",
                            "esme_id": "",
                            "meter_location": "",
                            "register_digits": "",
                            "rel_address_primary_name": "ROOKERY HILL FARM",
                            "rel_address_secondary_name": "",
                            "rel_address_street1": "ROOKERY DRIVE",
                            "rel_address_street2": "",
                            "rel_address_locality1": "WESTCOTT",
                            "rel_address_locality2": "",
                            "rel_address_town": "DORKING",
                            "rel_address_postcode": "RH4 3LQ",
                            "rel_address_logical_status": "1",
                            "rel_address_language": "ENG",
                            "rel_address_organisation": "",
                            "rel_address_address_type": "DPA",
                            "rel_address_confidence_score": "100",
                            "rel_address_classification": "RD02",
                            "rel_address_latitude": "51.221603",
                            "rel_address_longitude": "-0.377399"
                        },
                        {
                            "mpan": "1900035068018",
                            "uprn": "100062495055",
                            "address_line_1": "",
                            "address_line_2": "",
                            "address_line_3": "ROOKERY HILL FARM",
                            "address_line_4": "",
                            "address_line_5": "ROOKERY DRIVE",
                            "address_line_6": "",
                            "address_line_7": "WESTCOTT",
                            "address_line_8": "DORKING",
                            "address_line_9": "",
                            "address_postal_code": "RH4 3LQ",
                            "trading_status": "T",
                            "trading_status_efd": "19971121",
                            "profile_class": "01",
                            "profile_class_efd": "20220511",
                            "meter_timeswitch_class": "801",
                            "meter_timeswitch_class_efd": "20210403",
                            "line_loss_factor": "001",
                            "line_loss_factor_efd": "20220511",
                            "standard_settlement_configuration": "0393",
                            "standard_settlement_configuration_efd": "20220511",
                            "energisation_status": "E",
                            "energisation_status_efd": "20210403",
                            "gsp_group_id": "_J",
                            "gsp_group_efd": "19971119",
                            "data_aggregator_mpid": "MANW",
                            "data_aggregator_efd": "20210403",
                            "data_collector_mpid": "MANW",
                            "data_collector_efd": "20210403",
                            "supplier_mpid": "SPOW",
                            "supplier_efd": "20210403",
                            "meter_operator_mpid": "LBSL",
                            "meter_operator_efd": "20210403",
                            "measurement_class": "A",
                            "measurement_class_efd": "20210403",
                            "green_deal_in_effect": "0",
                            "smso_mpid": "",
                            "smso_efd": "",
                            "dcc_service_flag": "",
                            "dcc_service_flag_efd": "",
                            "ihd_status": "",
                            "ihd_status_efd": "",
                            "smets_version": "",
                            "distributor_mpid": "SEEB",
                            "metered_indicator": "T",
                            "metered_indicator_efd": "20190315",
                            "metered_indicator_etd": "",
                            "consumer_type": "",
                            "relationship_status_indicator": "None",
                            "rmp_state": "O",
                            "rmp_efd": "19971121",
                            "domestic_consumer_indicator": "T",
                            "css_supplier_mpid": "",
                            "css_supply_start_date": "",
                            "meter_serial_number": "E15Z005745",
                            "meter_install_date": "20151204",
                            "meter_type": "RCAMY",
                            "map_mpid": "ACCU",
                            "map_mpid_efd": "20151204",
                            "installing_supplier_mpid": "ENRD",
                            "energy_direction": "I",
                            "energy_direction_efd": "19971119",
                            "energy_direction_etd": "",
                            "connection_type": "W",
                            "connection_type_efd": "19971208",
                            "connection_type_etd": "",
                            "esme_id": "",
                            "meter_location": "",
                            "register_digits": "",
                            "rel_address_primary_name": "ROOKERY HILL FARM",
                            "rel_address_secondary_name": "",
                            "rel_address_street1": "ROOKERY DRIVE",
                            "rel_address_street2": "",
                            "rel_address_locality1": "WESTCOTT",
                            "rel_address_locality2": "",
                            "rel_address_town": "DORKING",
                            "rel_address_postcode": "RH4 3LQ",
                            "rel_address_logical_status": "1",
                            "rel_address_language": "ENG",
                            "rel_address_organisation": "",
                            "rel_address_address_type": "DPA",
                            "rel_address_confidence_score": "100",
                            "rel_address_classification": "RD02",
                            "rel_address_latitude": "51.221603",
                            "rel_address_longitude": "-0.377399"
                        }
                    ]
                }
            }
        ]
    }
}
```

And for gas;

```json
{
    "result": {
        "global_address_key": "aWQ9MSBDaGFwbWFuIENyZXNjZW50LCBGQVJJTkdET04sIE94Zm9yZHNoaXJlIFNONyA3V0QsIFVuaXRlZCBLaW5nZG9tfmFsdF9rZXk9NTYxNDIzODZ-ZGF0YXNldD1HQlJ-Zm9ybWF0X2tleT1HQlIkR0JSJDcuNzMwa09HQlJGUVBwQndBQUFBQUJBd0VBQUFBQkYuT1kwZ0FnQUFBQUFBQUFNUUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUJUVGpjM1YwUUFBQUFBQUEtLX5nYXM9dHJ1ZX5wb3M9MX5nYWtfdHlwZT1zaW5nbGVsaW5lfmxvY2FsaXR5PUZBUklOR0RPTn5wb3N0YWxfY29kZT1TTjcgN1dEflFMPTZ-bWF4X3N1Z2dlc3Rpb25zPTc",
        "confidence": "Verified match",
        "addresses_formatted": [
            {
                "layout_name": "GasUtility",
                "address": {
                    "address_line_1": "1 Chapman Crescent",
                    "address_line_2": "",
                    "address_line_3": "",
                    "locality": "Faringdon",
                    "region": "Oxfordshire",
                    "postal_code": "SN7 7WD",
                    "country": "United Kingdom",
                    "gas_meters": [
                        {
                            "mprn": "7841924105",
                            "uprn": "10094281298",
                            "rel_address_primary_name": "1",
                            "rel_address_secondary_name": "",
                            "rel_address_street1": "CHAPMAN CRESCENT",
                            "rel_address_street2": "",
                            "rel_address_locality1": "",
                            "rel_address_locality2": "",
                            "rel_address_town": "FARINGDON",
                            "rel_address_postcode": "SN7 7WD",
                            "rel_address_logical_status": "1",
                            "rel_address_language": "ENG",
                            "rel_address_organisation": "",
                            "rel_address_address_type": "LPI",
                            "rel_address_confidence_score": "100",
                            "rel_address_classification": "RD02",
                            "rel_address_latitude": "51.649945",
                            "rel_address_longitude": "-1.582484",
                            "meter_serial": "E6S22222572161",
                            "offtake_quantity_annual": "8165",
                            "meter_point_status": "LI",
                            "installer_id": "SGL",
                            "network_name": "Last Mile Gas Limited",
                            "supplier_name": "Octopus Energy Limited",
                            "last_meter_read_date": "20240305",
                            "last_meter_read_type": "T",
                            "last_meter_read_value": "01435"
                        }
                    ]
                }
            }
        ]
    }
}
```

Our database already has the following tables created for the electric and gas metering points.

- ElectricMeteringPoint
  - MeteringPointAdditionalDetail
  - MeteringPointTPRData
- GasMeteringPoint

```schema.prisma
model ElectralinkAddress {
  /// Masked MPAN for the address first 2 and last 2 characters of bottom line
  mask_mpan      String @id
  /// Detailed Address string.
  address        String
  /// Postal code of the metering point's physical address.
  postcode       String
  /// The encryped MPAN
  encrypted_mpan String

  @@map("electralink_address")
  @@schema("public")
}

/// The main model representing metering point details.
model ElectricMeteringPoint {
  /// Unique identifier for the metering point.
  id                                 String                          @id @default(uuid())
  /// Meter Point Administration Number, unique identifier for every electricity metering point in the UK.
  mpan                               Mpan                            @relation(fields: [mpanId], references: [id])
  /// Unique identifier for the metering point.
  mpanId                             String
  /// Unique Property Reference Number.
  uprn                               String?
  /// Unique Delivery Point Reference Number.
  udprn                              String?
  /// First line of the physical address associated with the metering point.
  addressLine1                       String?
  /// Second line of the physical address associated with the metering point.
  addressLine2                       String?
  /// Third line of the physical address associated with the metering point.
  addressLine3                       String
  /// Fourth line of the physical address associated with the metering point.
  addressLine4                       String?
  /// Fifth line of the physical address associated with the metering point.
  addressLine5                       String
  /// Sixth line of the physical address associated with the metering point.
  addressLine6                       String?
  /// Seventh line of the physical address associated with the metering point.
  addressLine7                       String?
  /// Eighth line of the physical address associated with the metering point.
  addressLine8                       String
  /// Ninth line of the physical address associated with the metering point.
  addressLine9                       String
  /// Postal code of the metering point's physical address.
  postCode                           String
  /// Identifier for the electricity supplier at this MPAN.
  supplierId                         String
  /// Name of the electricity supplier.
  supplierName                       String?
  /// Effective From Date for when the current supplier information becomes valid.
  supplierEfd                        DateTime
  /// Estimated Annual Consumption, the estimated total electricity consumed over a year (kWh).
  totalEac                           Float?
  /// Effective From Date for the EAC value.
  eacEfd                             DateTime
  /// A Boolean flag that could represent electric tariff related information (specific to your system).
  et                                 Boolean
  /// Reference number identifying the settlement configuration for this MPAN.
  standardSettlementConfigurationId  String?
  /// Effective From Date for the standard settlement configuration.
  standardSettlementConfigurationEfd DateTime?
  /// Identifier for the class used to account for electricity loss in transmission.
  lineLossFactorClassId              String?
  /// Effective From Date for the Line Loss Factor Class ID.
  lineLossFactorClassEfd             DateTime?
  /// Indicates the status of the supply point, e.g., 'E' for energised (connected) or 'D' for de-energised (disconnected).
  energisationStatus                 String?
  /// Effective From Date for the energisation status.
  energisationStatusEfd              DateTime?
  /// The agreed capacity for electricity supply at that metering point, in kilowatts (kW).
  capacity                           String?
  /// Indicates if the MPAN has been disconnected from the network.
  disconnectedMpan                   String?
  /// The date when the metering point was disconnected from the network.
  disconnectionDate                  DateTime?
  /// Related model capturing additional detailed entries.
  additionalDetails                  MeteringPointAdditionalDetail[]
  /// Related model capturing Time Pattern Regime data entries.
  tprData                            MeteringPointTPRData[]

  // Fields from Aperture
  /// The distributor market Participant ID.
  distributorMpid             String?
  /// Effective From Date for the distributor mpid.
  distributorEfd              DateTime?
  /// A flag indicating if the metering point is part of the DCC (Data Communications Company) service.
  dccServiceFlag              String?
  /// Effective From Date for the DCC service flag.
  dccServiceFlagEfd           DateTime?
  /// Status of the In-Home Display (IHD) for smart meters.
  ihdStatus                   String?
  /// Effective From Date for the In-Home Display (IHD) status.
  ihdStatusEfd                DateTime?
  /// The version of the Smart Metering Equipment Technical Specifications (SMETS) for this metering point.
  smetsVersion                String?
  /// Indicates whether the metering point is operating, trading status.
  tradingStatus               String?
  /// Effective From Date for the trading status.
  tradingStatusEfd            DateTime?
  /// Type of consumer at the metering point, e.g., "Non-Domestic".
  consumerType                String?
  /// Indicator of the relationship status.
  relationshipStatusIndicator String?
  /// State of the Relationship Management Process (RMP).
  rmpState                    String?
  /// Effective From Date for the RMP state.
  rmpEfd                      DateTime?
  /// Indicator if the consumer is a domestic consumer.
  domesticConsumerIndicator   String?
  /// CSS (Central Switching Service) Supplier ID.
  cssSupplierMpid             String?
  /// Start date of the supply from CSS Supplier.
  cssSupplyStartDate          DateTime?
  /// Metering point access provider identifier.
  mapMpid                     String?
  /// Effective From Date for the MAP (Metering point access provider) ID.
  mapMpidEfd                  DateTime?
  /// Identifier for the supplier responsible for installing the meter.
  installingSupplierMpid      String?
  /// Identifier for the Data Aggregator (party responsible for data aggregation).
  dataAggregatorMpid          String?
  /// Effective From Date for when the data aggregator ID becomes valid.
  dataAggregatorEfd           DateTime?
  /// Identifier for the Data Collector (party responsible for meter data collection).
  dataCollectorMpid           String?
  /// Effective From Date for when the data collector ID becomes valid.
  dataCollectorEfd            DateTime?
  /// Identifier for the Meter Operator (party responsible for meter operation).
  meterOperatorMpid           String?
  /// Effective From Date for when the meter operator ID becomes valid.
  meterOperatorEfd            DateTime?
  /// Flag indicating the effectivity of Green Deal (energy efficiency scheme) on the metering point.
  greenDealInEffect           String?
  /// Identifier for the Meter Operator responsible for smart meters within the Smart Metering System.
  smsoMpid                    String?
  /// Effective From Date for when the SMOP ID becomes valid.
  smsoEfd                     DateTime?
  /// Indicator of whether the metering point is considered to have a meter based on the meter point administration system.
  meteredIndicator            String?
  /// Effective From Date for when the metered indicator becomes valid.
  meteredIndicatorEfd         DateTime?
  /// Effective To Date for when the metered indicator ceases to be valid.
  meteredIndicatorEtd         DateTime?

  /// Profile Class Effective From Date.
  profileClassEfd     DateTime?
  /// Meter Timeswitch Class Effective From Date.
  mtcEfd              DateTime?
  /// GSP Group Effective From Date.
  gspGroupEfd         DateTime?
  /// Measurement Class Effective From Date.
  measurementClassEfd DateTime?

  /// Timestamp when the metering point record was created.
  createdAt DateTime @default(now())
  /// Timestamp when the metering point record was last updated.
  updatedAt DateTime @updatedAt

  @@index([udprn], name: "idx_metering_point_udprn")
  @@index([uprn], name: "idx_metering_point_uprn")
  @@index([postCode], name: "idx_metering_point_post_code")
  @@index([supplierId], name: "idx_metering_point_supplier_id")
  @@index([disconnectionDate], name: "idx_metering_point_disconnection_date")
  @@index([supplierEfd], name: "idx_metering_point_supplier_efd")
  @@index([energisationStatus], name: "idx_metering_point_energisation_status")
  @@index([supplierId, supplierEfd], name: "idx_metering_point_supplier_id_supplier_efd")
  @@map("metering_points")
  @@schema("public")
}

/// Model for additional detailed entries about a metering point.
model MeteringPointAdditionalDetail {
  /// Unique identifier for the additional detail entry.
  id               String                @id @default(uuid())
  /// Foreign key connecting back to the MeteringPoint model.
  meteringPointId  String
  /// Meter Serial Number, the unique identifier of the electricity meter.
  msn              String
  /// Type of electricity meter installed (e.g., smart meter, single rate meter).
  meterType        String
  /// Date the meter was installed.
  makeInstallDate  DateTime
  /// Make and model of the electricity meter.
  makeModel        String
  /// ID for the Meter Operator (MOp), the company responsible for installing and maintaining the meter.
  smsoId           String?
  /// Distribution Network Operator (DNO) identification number.
  dnoId            String
  /// Profile class categorising the expected electricity usage pattern.
  profileClass     String
  /// Meter Timeswitch Code, indicating the type of metering in terms of the time of day rate switches.
  mtcId            String
  /// Grid Supply Point group identifier, linking to the National Grid supply group.
  gspGroupId       String
  /// Class indicating the precision of measurement for energy consumption.
  measurementClass String
  /// Effective From Date for the measurement class.
  mcEfd            DateTime
  /// Relation to the MeteringPoint this additional detail is associated with.
  meteringPoint    ElectricMeteringPoint @relation(fields: [meteringPointId], references: [id])

  /// Timestamp when the additional detail record was created.
  createdAt DateTime @default(now())
  /// Timestamp when the additional detail record was last updated.
  updatedAt DateTime @updatedAt

  @@index([meteringPointId], name: "idx_metering_point_additional_detail_metering_point_id")
  @@index([msn], name: "idx_metering_point_additional_detail_msn")
  @@index([makeInstallDate], name: "idx_metering_point_additional_detail_make_install_date")
  @@index([dnoId, profileClass], name: "idx_metering_point_additional_detail_dno_id_profile_class")
  @@index([measurementClass], name: "idx_metering_point_additional_detail_measurement_class")
  @@map("metering_point_additional_details")
  @@schema("public")
}

/// Model for Time Pattern Regime (TPR) data entries related to a metering point.
model MeteringPointTPRData {
  /// Unique identifier for the TPR data entry.
  id              String                @id @default(uuid())
  /// Foreign key connecting back to the MeteringPoint model.
  meteringPointId String
  /// Estimated Annual Consumption (EAC) for a specific Time Pattern Regime (TPR).
  tprEac          Float
  /// Identifier for the Time Pattern Regime, segmenting the day for different usage rates.
  eacTpr          String
  /// Effective From Date for the TPR's Estimated Annual Consumption.
  eacEfd          DateTime
  /// Relation to the MeteringPoint model that this TPR data is associated with.
  meteringPoint   ElectricMeteringPoint @relation(fields: [meteringPointId], references: [id])

  /// Timestamp when the TPR data record was created.
  createdAt DateTime @default(now())
  /// Timestamp when the TPR data record was last updated.
  updatedAt DateTime @updatedAt

  @@index([meteringPointId], name: "idx_metering_point_tpr_data_metering_point_id")
  @@index([eacTpr], name: "idx_metering_point_tpr_data_eac_tpr")
  @@index([meteringPointId, eacEfd], name: "idx_metering_point_tpr_data_metering_point_id_eac_efd")
  @@index([eacEfd], name: "idx_metering_point_tpr_data_eac_efd")
  @@map("metering_point_tpr_data")
  @@schema("public")
}

/// Model representing the details of a gas metering point.
model GasMeteringPoint {
  /// Unique identifier for the gas metering point.
  id                        String   @id @default(uuid())
  /// Meter Point Reference Number, unique identifier for every gas metering point in the UK.
  mprn                      Mprn     @relation(fields: [mprnId], references: [id])
  /// Unique Property Reference Number.
  uprn                      String
  /// Unique Delivery Point Reference Number.
  udprn                     String?
  /// Primary name of the related address.
  relAddressPrimaryName     String
  /// Secondary name of the related address.
  relAddressSecondaryName   String?
  /// First street line of the related address.
  relAddressStreet1         String
  /// Second street line of the related address.
  relAddressStreet2         String?
  /// First locality of the related address.
  relAddressLocality1       String?
  /// Second locality of the related address.
  relAddressLocality2       String?
  /// Town of the related address.
  relAddressTown            String
  /// Postcode of the related address.
  relAddressPostcode        String
  /// Logical status of the related address.
  relAddressLogicalStatus   String
  /// Language of the related address.
  relAddressLanguage        String
  /// Organisation of the related address.
  relAddressOrganisation    String?
  /// EntityAddress type of the related address.
  relAddressAddressType     String
  /// Confidence score of the related address.
  relAddressConfidenceScore Int
  /// Classification of the related address.
  relAddressClassification  String
  /// Latitude of the related address.
  relAddressLatitude        Float
  /// Longitude of the related address.
  relAddressLongitude       Float
  /// Serial number of the meter.
  meterSerial               String
  /// Annual offtake quantity.
  offtakeQuantityAnnual     Int
  /// Status of the meter point.
  meterPointStatus          String
  /// Installer ID.
  installerId               String
  /// Name of the network.
  networkName               String
  /// Name of the supplier.
  supplierName              String
  /// Date of the last meter reading.
  lastMeterReadDate         DateTime
  /// Type of the last meter reading.
  lastMeterReadType         String
  /// Value of the last meter reading.
  lastMeterReadValue        String

  /// Timestamp when the gas metering point record was created.
  createdAt DateTime @default(now())
  /// Timestamp when the gas metering point record was last updated.
  updatedAt DateTime @updatedAt
  mprnId    String

  @@index([udprn], name: "idx_gas_metering_point_udprn")
  @@index([uprn], name: "idx_gas_metering_point_uprn")
  @@index([relAddressPostcode], name: "idx_gas_metering_point_rel_address_postcode")
  @@index([supplierName], name: "idx_gas_metering_point_supplier_name")
  @@index([lastMeterReadDate], name: "idx_gas_metering_point_last_meter_read_date")
  @@map("gas_metering_points")
  @@schema("public")
}
```

## Requirements

1. Enhance the existing Address selection component to allow the user to enter their address manually.
2. Users can click a button with link style text 'Enter address manually' to swap the address drop down menu picker with the individual address fields. The text is left aligned right below the address drop down menu picker.
3. Which reveals 'Select Address' link text to swap the address fields with the address drop down menu picker.
4. Ensure the input fields follow the existing EntityAddress schema.
5. The user must then enter their address manually and then click 'Submit' to verify the address.
6. This then sends the users manually entered address to the Experian Aperture API and returns a list of suggestions.
7. The suggestions are then displayed to the user in a list and the user can select one of the suggestions or proceed with the manually entered address
8. If the user select the suggestion it calls the second endpoint
9. The individual fields for the manually entered address remain visible even if the user selects a suggestion from the list.
