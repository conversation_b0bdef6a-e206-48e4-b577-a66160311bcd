# Requirements and Goal

Files

<addresses/route.ts>
import {
  AddressLookupError,
  searchAddressesByPostcode
} from "@watt/api/src/service/search-addresses";
import {
  ErrorResponseSchema,
  log,
  parseRequestQueryParamsToResponse
} from "@watt/common";
import { ResponseHelper } from "@watt/common/src/utils/server";
import type { NextRequest } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const { data, error } = parseRequestQueryParamsToResponse(
    request,
    z.object({
      postcode: z.string(),
      take: z.number().optional()
    })
  );

  if (error) {
    log.error("addresses/route.GET", { error });
    return ResponseHelper.badRequest({
      message: "Invalid query parameters"
    });
  }

  try {
    const results = await searchAddressesByPostcode(data.postcode, data.take);
    return ResponseHelper.ok(results);
  } catch (err) {
    if (err === AddressLookupError.BAD_POSTCODE) {
      return ResponseHelper.badRequest(
        ErrorResponseSchema.parse({ message: "Invalid postcode" })
      );
    }
    if (err === AddressLookupError.NONE_FOUND) {
      return ResponseHelper.notFound(
        ErrorResponseSchema.parse({
          message: `No addresses found for ${data.postcode}`
        })
      );
    }
    log.error("addresses/route.GET", { err });
    return ResponseHelper.internalServerError(
      ErrorResponseSchema.parse({
        message: "Internal server error",
        description: err instanceof Error ? err.message : undefined
      })
    );
  }
}

</addresses/route.ts>

<addresses/[guid]/route.ts>
import { log, parseRequestRouteParams } from "@watt/common";
import type { Prisma } from "@watt/db/src";
import { prisma } from "@watt/db/src/client";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const dynamic = "force-dynamic";

const ParamsSchema = z.object({
  guid: z.string()
});

type Params = z.infer<typeof ParamsSchema>;

// DROP DOWN PERSPECTIVE
// fn(guid) - calls db + redis(aperture + random guid) - finds the address in redis
// 1. if found in db, no extra work.
// 2. after() => if found in redis('guid'), get the 'globalAddressKey' and call Aperture /format for the address + meter info
// 2.1 Persist the Aperture address into the entity address table
// 2.2 createOrConnect to the meter via its value
//
//
//
// Manual entry perspective - Customer enters manual address with a random guid not stored anywhere.
// fn(guid) - calls db + redis(aperture + random guid) - finds the address in redis
// 1. if found in db, no extra work.
// 2. after() => if found in redis('guid'), get the 'globalAddressKey' and call Aperture /format for the address + meter info
// 2.1 Persist the Aperture address into the entity address table
// 2.2 createOrConnect to the meter via its value
// 3. if not found in both.
// 3.1 create the address in the 'CustomerAddressSubmission' table.

const entityAddressQueryOptions: Prisma.EntityAddressSelect = {
  guid: true,
  postcode: true,
  county: true,
  postalTown: true,
  country: true,
  displayName: true,
  addressLine1: true,
  addressLine2: true,
  houseName: true,
  houseNumber: true,
  flatNumber: true,
  mpans: {
    select: { value: true }
  },
  mprns: {
    select: { value: true }
  }
};

const formatAddress = (
  entityAddress: Prisma.EntityAddressGetPayload<{
    select: typeof entityAddressQueryOptions;
  }>
) => {
  const { displayName, ...rest } = entityAddress;
  return {
    ...rest,
    // Display the id as guid
    id: entityAddress.guid,
    // Hide guid
    guid: undefined,
    // Rename displayName to address - legacy field to support quote app address lookup API
    address: displayName,
    mpans: entityAddress.mpans.map(mpan => mpan.value),
    mprns: entityAddress.mprns.map(mprn => mprn.value)
  };
};

export async function GET(
  request: NextRequest,
  props: { params: Promise<Params> }
) {
  const params = await props.params;
  try {
    const { guid } = parseRequestRouteParams(params, ParamsSchema);

    const address = await prisma.entityAddress.findFirst({
      select: {
        ...entityAddressQueryOptions
      },
      where: {
        guid,
        createdById: null
      }
    });

    if (!address) {
      return new NextResponse(`No address found for ID: ${guid}`, {
        status: 404
      });
    }

    return new NextResponse(JSON.stringify(formatAddress(address)), {
      headers: {
        "content-type": "application/json"
      }
    });
  } catch (error) {
    log.error("address/[id]/route.GET: ", { error });
    return new NextResponse("Internal server error", { status: 500 });
  }
}
</addresses/[guid]/route.ts>

<packages/api/service/search-addresses.ts>
import { formatPostcode } from "@watt/common";
import { prisma } from "@watt/db/src/client";
import { cacheWrap } from "@watt/redis/src/cache";
import { redis } from "@watt/redis/src/client";
import { randomUUID } from "node:crypto";

import {
  entityAddressQueryOptions,
  formatEntityAddresses,
  type Address,
  type EntityAddressWithMeterData
} from "./entityAddress";

import {
  type CombinedSuggestion,
  searchAddresses as apertureSearch
} from "@watt/external-apis/src/libs/aperture/search-addresses";

/** Errors surfaced to the route handler. */
export enum AddressLookupError {
  BAD_POSTCODE = "BAD_POSTCODE",
  DATA_SOURCE_FAILED = "DATA_SOURCE_FAILED",
  NONE_FOUND = "NONE_FOUND"
}

interface StoredPayload {
  gasKey?: string;
  elecKey?: string;
  raw: CombinedSuggestion;
}

/**

* Convert a combined Aperture suggestion into the public DTO, **generating a
* fresh GUID** and writing the mapping (GUID → keys) to Redis for 7 days.
 */
function toDto(s: Readonly<CombinedSuggestion>): EntityAddressWithMeterData {
  const guid = randomUUID();

  const payload: StoredPayload = {
    gasKey: s.gasKey,
    elecKey: s.elecKey,
    raw: s
  };
  /*Redis TTL = 7 days*/
  void redis.set(`addr:${guid}`, JSON.stringify(payload), {
    ex: 60 *60* 24 * 7
  });

  return {
    id: guid,
    displayName: s.text,
    postcode: s.postcode,
    siteMeters: { 1: 0, 2: 0 },
    meterPoints: { 1: 0, 2: 0 },
    address: s.text,
    meterNumbers: undefined
  };
}

/** Five-minute cached DB lookup. */
function dbAddresses(postcode: string) {
  return cacheWrap(
    `ea:${postcode}`,
    () =>
      prisma.entityAddress.findMany({
        ...entityAddressQueryOptions,
        where: { postcode, createdById: null }
      }),
    300
  );
}

function compareDisplay(a: string, b: string) {
  return a.localeCompare(b, "en-GB", { numeric: true, sensitivity: "base" });
}

function merge(
  dbRows: readonly Address[],
  apiRows: readonly CombinedSuggestion[]
): readonly EntityAddressWithMeterData[] {
  const dbDtos = formatEntityAddresses([...dbRows]);
  const apiDtos = apiRows.map(toDto);

  const map = new Map<string, EntityAddressWithMeterData>();

  for (const row of [...dbDtos, ...apiDtos]) {
    if (row.displayName === null || row.displayName === undefined) {
      continue;
    }
    const key = row.displayName.toLowerCase();
    if (!map.has(key)) {
      map.set(key, row);
    }
  }

  return [...map.values()].sort((x, y) =>
    compareDisplay(x.displayName ?? "", y.displayName ?? "")
  );
}

/**

* Federation entry-point. Throws typed errors for HTTP mapping.
 */
export async function searchAddressesByPostcode(
  rawPostcode: string,
  take?: number
): Promise<readonly EntityAddressWithMeterData[]> {
  const postcode = formatPostcode(rawPostcode);
  if (!postcode) {
    throw AddressLookupError.BAD_POSTCODE;
  }

  const [dbRes, apiRes] = await Promise.allSettled([
    dbAddresses(postcode),
    apertureSearch(postcode)
  ]);

  if (dbRes.status === "rejected" && apiRes.status === "rejected") {
    throw AddressLookupError.DATA_SOURCE_FAILED;
  }

  const dbData = dbRes.status === "fulfilled" ? dbRes.value : [];
  const apiData = apiRes.status === "fulfilled" ? apiRes.value : ([] as const);

  const combined = merge(dbData, apiData);
  if (combined.length === 0) {
    throw AddressLookupError.NONE_FOUND;
  }

  return take ? combined.slice(0, take) : combined;
}
</packages/api/service/search-addresses.ts>

<packages/external-apis/src/common/aperture.ts>
import { env } from "@watt/common/src/config/env";
import { z } from "zod";

export const apertureApiHeaders = {
  "Auth-Token": env.APERTURE_API_KEY,
  "Content-Type": "application/json",
  "Add-FinalAddress": "true"
} as const;

export const getApertureSearchPayloadGas = (postcode: string) => {
  return JSON.stringify({
    country_iso: "GBR",
    datasets: ["gb-additional-gas"],
    components: { unspecified: [postcode] },
    options: [
      { name: "search_type", value: "singleline" },
      { name: "prompt_set", value: "optimal" }
    ]
  });
};

export const getApertureSearchPayloadElec = (singleLine: string) => {
  return JSON.stringify({
    country_iso: "GBR",
    datasets: ["gb-additional-electricity"],
    components: { unspecified: [singleLine] }, // full address string
    options: [
      { name: "search_type", value: "singleline" },
      { name: "prompt_set", value: "optimal" }
    ]
  });
};

export const getApertureLookupPayloadForGas = (mprn: string) => {
  return JSON.stringify({
    country_iso: "GBR",
    datasets: ["gb-additional-gas"],
    key: {
      type: "mprn",
      value: mprn
    },
    layouts: ["GasUtilityLookup"]
  });
};

export const getApertureLookupPayloadForElectric = (mpan: string) => {
  return JSON.stringify({
    country_iso: "GBR",
    datasets: ["gb-additional-electricity"],
    key: {
      type: "mpan",
      value: mpan
    },
    layouts: ["ElectricityUtilityLookup"]
  });
};

const LocalitySubRegionSchema = z.object({
  name: z.string()
});

const LocalityTownSchema = z.object({
  name: z.string()
});

const LocalitySchema = z.object({
  subRegion: LocalitySubRegionSchema.optional(),
  town: LocalityTownSchema
});

const PostalCodeSchema = z.object({
  fullName: z.string()
});

const SuggestionSchema = z.object({
  locality: LocalitySchema,
  postalCode: PostalCodeSchema,
  postalCodeKey: z.string(),
  localityKey: z.string()
});

export const ElectricityMeterDataSchema = z.object({
  mpan: z.string(),
  addressLine1: z.string(),
  addressLine2: z.string(),
  addressLine3: z.string(),
  addressLine4: z.string(),
  addressLine5: z.string(),
  addressLine6: z.string(),
  addressLine7: z.string(),
  addressLine8: z.string(),
  addressLine9: z.string(),
  addressPostalCode: z.string(),
  tradingStatus: z.string(),
  tradingStatusEfd: z.string(),
  profileClass: z.string(),
  profileClassEfd: z.string(),
  meterTimeswitchClass: z.string(),
  meterTimeswitchClassEfd: z.string(),
  lineLossFactor: z.string(),
  lineLossFactorEfd: z.string(),
  standardSettlementConfiguration: z.string(),
  standardSettlementConfigurationEfd: z.string(),
  energisationStatus: z.string(),
  energisationStatusEfd: z.string(),
  gspGroupId: z.string(),
  gspGroupEfd: z.string(),
  dataAggregatorMpid: z.string(),
  dataAggregatorEfd: z.string(),
  dataCollectorMpid: z.string(),
  dataCollectorEfd: z.string(),
  supplierMpid: z.string(),
  supplierEfd: z.string(),
  meterOperatorMpid: z.string(),
  meterOperatorEfd: z.string(),
  measurementClass: z.string(),
  measurementClassEfd: z.string(),
  greenDealInEffect: z.string(),
  smsoMpid: z.string(),
  smsoEfd: z.string(),
  dccServiceFlag: z.string(),
  dccServiceFlagEfd: z.string(),
  ihdStatus: z.string(),
  ihdStatusEfd: z.string(),
  smetsVersion: z.string(),
  distributorMpid: z.string(),
  meteredIndicator: z.string(),
  meteredIndicatorEfd: z.string(),
  meteredIndicatorEtd: z.string(),
  consumerType: z.string(),
  relationshipStatusIndicator: z.string(),
  rmpState: z.string(),
  rmpEfd: z.string(),
  domesticConsumerIndicator: z.string(),
  cssSupplierMpid: z.string(),
  cssSupplyStartDate: z.string(),
  meterSerialNumber: z.string(),
  meterInstallDate: z.string(),
  meterType: z.string(),
  mapMpid: z.string(),
  mapMpidEfd: z.string(),
  installingSupplierMpid: z.string(),
  energyDirection: z.string(),
  energyDirectionEfd: z.string(),
  energyDirectionEtd: z.string(),
  connectionType: z.string(),
  connectionTypeEfd: z.string(),
  connectionTypeEtd: z.string(),
  esmeId: z.string(),
  meterLocation: z.string(),
  registerDigits: z.string()
});

export const GasMeterDataSchema = z.object({
  mprn: z.string(),
  uprn: z.string(),
  relAddressPrimaryName: z.string(),
  relAddressSecondaryName: z.string(),
  relAddressStreet1: z.string(),
  relAddressStreet2: z.string(),
  relAddressLocality1: z.string(),
  relAddressLocality2: z.string(),
  relAddressTown: z.string(),
  relAddressPostcode: z.string(),
  relAddressLogicalStatus: z.string(),
  relAddressLanguage: z.string(),
  relAddressOrganisation: z.string(),
  relAddressAddressType: z.string(),
  relAddressConfidenceScore: z.string(),
  relAddressClassification: z.string(),
  relAddressLatitude: z.string(),
  relAddressLongitude: z.string(),
  meterSerial: z.string(),
  offtakeQuantityAnnual: z.string(),
  meterPointStatus: z.string(),
  installerId: z.string(),
  networkName: z.string(),
  supplierName: z.string(),
  lastMeterReadDate: z.string(),
  lastMeterReadType: z.string(),
  lastMeterReadValue: z.string()
});

const BaseResultSchema = z.object({
  moreResultsAvailable: z.boolean(),
  confidence: z.string(),
  suggestions: z.array(SuggestionSchema)
});

export const GasMeterApiResponseSchema = z.object({
  result: BaseResultSchema.extend({
    addressesFormatted: z.array(
      z.object({
        layoutName: z.string(),
        address: z.object({
          gasMeters: z.array(GasMeterDataSchema)
        })
      })
    )
  })
});

export const ElectricMeterApiResponseSchema = z.object({
  result: BaseResultSchema.extend({
    addressesFormatted: z.array(
      z.object({
        layoutName: z.string(),
        address: z.object({
          electricityMeters: z.array(ElectricityMeterDataSchema)
        })
      })
    )
  })
});

const SuggestionAttr = z.object({ name: z.string(), value: z.string() });

export const ApertureSearchResponseSchema = z.object({
  result: z.object({
    moreResultsAvailable: z.boolean(),
    confidence: z.string(),
    suggestionsKey: z.string(),
    suggestionsPrompt: z.string(),
    suggestions: z.array(
      z.object({
        globalAddressKey: z.string(),
        text: z.string(),
        format: z.string(),
        additionalAttributes: z.array(SuggestionAttr)
      })
    )
  })
});
</packages/external-apis/src/common/aperture.ts>

<packages/redis/src/cache.ts>
import { env } from "@watt/common/src/config/env";
import { createCache } from "cache-manager";
import { KeyvCacheableMemory } from "cacheable";
import Keyv from "keyv";
import { KeyvUpstash } from "keyv-upstash";

const l1Store = new Keyv({
  store: new KeyvCacheableMemory({ ttl: 60_000, lruSize: 5_000 })
});

const redisRestUrl = env.UPSTASH_REDIS_REST_URL;
const redisRestToken = env.UPSTASH_REDIS_REST_TOKEN;

const l2Store = new Keyv({
  store: new KeyvUpstash({
    url: redisRestUrl,
    token: redisRestToken
  })
});

export const cache = createCache({ stores: [l1Store, l2Store] });

export function cacheWrap<T>(
  key: string,
  loader: () => Promise<T>,
  ttlSeconds: number
) {
  return cache.wrap(key, loader, { ttl: ttlSeconds * 1_000 });
}
</packages/redis/src/cache.ts>

<packages/external-apis/src/libs/aperture/search-addresses.ts>
import { externalApiUrls } from "@watt/common";
import { cacheWrap } from "@watt/redis/src/cache";
import type { z } from "zod";
import {
  ApertureSearchResponseSchema,
  apertureApiHeaders,
  getApertureSearchPayloadElec,
  getApertureSearchPayloadGas
} from "../../common";
import {
  type ApiProps,
  type ApiResponse,
  handleFetchExternalApi
} from "../../utils";

export type ApertureAddressSuggestion = z.infer<
  typeof ApertureSearchResponseSchema
>["result"]["suggestions"][number];

const callApertureSearch = async (
  name: string,
  body: string
): Promise<ApiResponse<typeof ApertureSearchResponseSchema._type>> => {
  const apiProps = {
    name,
    url: {
      baseUrl: externalApiUrls.apertureApiUrl,
      path: "/address/search/v1"
    },
    additionalData: { method: "POST", headers: apertureApiHeaders, body }
  } satisfies ApiProps;

  return handleFetchExternalApi(apiProps, ApertureSearchResponseSchema);
};

const searchGasAddresses = async (postcode: string) => {
  return cacheWrap(
    `ap-search-gas:${postcode}`,
    async () => {
      const res = await callApertureSearch(
        "Aperture gas search",
        getApertureSearchPayloadGas(postcode)
      );
      if (res.error) {
        throw res.error;
      }
      return res.data!.result
        .suggestions as readonly ApertureAddressSuggestion[];
    },
    60 *60* 24 * 7
  );
};

const searchElectricAddresses = async (query: string) => {
  return cacheWrap(
    `ap-search-elec:${query}`,
    async () => {
      const res = await callApertureSearch(
        "Aperture electric search",
        getApertureSearchPayloadElec(query)
      );
      if (res.error) {
        throw res.error;
      }
      return res.data!.result
        .suggestions as readonly ApertureAddressSuggestion[];
    },
    60 *60* 24 * 7
  );
};

const zipDedupeSuggestions = (
  a: readonly ApertureAddressSuggestion[],
  b: readonly ApertureAddressSuggestion[]
): readonly ApertureAddressSuggestion[] => {
  const map = new Map<string, ApertureAddressSuggestion>();
  for (const s of [...a, ...b]) {
    if (!map.has(s.text)) {
      map.set(s.text, s);
    }
  }
  return [...map.values()];
};

export const searchAddresses = async (postcode: string) => {
  const [gas, elec] = await Promise.all([
    searchGasAddresses(postcode),
    searchElectricAddresses(postcode)
  ]);

  console.log("gas", gas);
  console.log("elec", elec);

  return zipDedupeSuggestions(gas, elec);
};
</packages/external-apis/src/libs/aperture/search-addresses.ts>

Now if I call the public endpoint and get back;

<http://localhost:3000/api/addresses?postcode=sn77wd>

```json
[
  {
    "id": "848b8bf1-46bd-45ea-8066-9abdf123ebad",
    "displayName": "1 Chapman Crescent, FARINGDON, Oxfordshire SN7 7WD",
    "postcode": "SN7 7WD",
    "siteMeters": {
      "1": 0,
      "2": 0
    },
    "meterPoints": {
      "1": 0,
      "2": 0
    },
    "address": "1 Chapman Crescent, FARINGDON, Oxfordshire SN7 7WD"
  },
...
```

I should then be able to call the private one.

<http://localhost:3000/api/addresses/74b99de8-b959-46da-9e91-5a2f5c06beeb>

And get back this address

Of course currently it only speaks to the entity address database table. So we get back;

```
No address found for ID: 74b99de8-b959-46da-9e91-5a2f5c06beeb
```

The payload from redis is below and we likely already have a type definition for it.

```json
{
  "gasKey": "aWQ9NSBDaGFwbWFuIENyZXNjZW50LCBGQVJJTkdET04sIE94Zm9yZHNoaXJlIFNONyA3V0QsIFVuaXRlZCBLaW5nZG9tfmFsdF9rZXk9NTYxNDIzODh-ZGF0YXNldD1HQlJ-Zm9ybWF0X2tleT1HQlIkR0JSJDcuNzMwak9HQlJGUVBwQndBQUFBQUJBd0VBQUFBQkYuT1kwZ0FnQUFBQUFBQUFOUUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUJUVGpjZ04xZEVBQUFBQUFBLX5nYXM9dHJ1ZX5wb3M9NX5nYWtfdHlwZT1zaW5nbGVsaW5lfmxvY2FsaXR5PUZBUklOR0RPTn5wb3N0YWxfY29kZT1TTjcgN1dEflFMPTd-bWF4X3N1Z2dlc3Rpb25zPTc",
  "elecKey": "aWQ9NSBDaGFwbWFuIENyZXNjZW50LCBGQVJJTkdET04sIE94Zm9yZHNoaXJlIFNONyA3V0QsIFVuaXRlZCBLaW5nZG9tfmFsdF9rZXk9NTYxNDIzODh-ZGF0YXNldD1HQlJ-Zm9ybWF0X2tleT1HQlIkR0JSJDcuNzMwak9HQlJGUVBwQndBQUFBQUJBd0VBQUFBQkYuT1kwZ0FnQUFBQUFBQUFOUUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUJUVGpjZ04xZEVBQUFBQUFBLX5lbGVjPXRydWV-cG9zPTV-Z2FrX3R5cGU9c2luZ2xlbGluZX5sb2NhbGl0eT1GQVJJTkdET05-cG9zdGFsX2NvZGU9U043IDdXRH5RTD03fm1heF9zdWdnZXN0aW9ucz03",
  "raw": {
    "text": "5 Chapman Crescent, FARINGDON, Oxfordshire SN7 7WD",
    "postcode": "SN7 7WD",
    "gasKey": "aWQ9NSBDaGFwbWFuIENyZXNjZW50LCBGQVJJTkdET04sIE94Zm9yZHNoaXJlIFNONyA3V0QsIFVuaXRlZCBLaW5nZG9tfmFsdF9rZXk9NTYxNDIzODh-ZGF0YXNldD1HQlJ-Zm9ybWF0X2tleT1HQlIkR0JSJDcuNzMwak9HQlJGUVBwQndBQUFBQUJBd0VBQUFBQkYuT1kwZ0FnQUFBQUFBQUFOUUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUJUVGpjZ04xZEVBQUFBQUFBLX5nYXM9dHJ1ZX5wb3M9NX5nYWtfdHlwZT1zaW5nbGVsaW5lfmxvY2FsaXR5PUZBUklOR0RPTn5wb3N0YWxfY29kZT1TTjcgN1dEflFMPTd-bWF4X3N1Z2dlc3Rpb25zPTc",
    "additionalAttributes": [
      {
        "name": "picklist_display",
        "value": "5 Chapman Crescent, FARINGDON, Oxfordshire"
      },
      {
        "name": "score",
        "value": "100"
      },
      {
        "name": "postcode",
        "value": "SN7 7WD"
      },
      {
        "name": "full_address",
        "value": "true"
      }
    ],
    "elecKey": "aWQ9NSBDaGFwbWFuIENyZXNjZW50LCBGQVJJTkdET04sIE94Zm9yZHNoaXJlIFNONyA3V0QsIFVuaXRlZCBLaW5nZG9tfmFsdF9rZXk9NTYxNDIzODh-ZGF0YXNldD1HQlJ-Zm9ybWF0X2tleT1HQlIkR0JSJDcuNzMwak9HQlJGUVBwQndBQUFBQUJBd0VBQUFBQkYuT1kwZ0FnQUFBQUFBQUFOUUFBLi45a0FBQUFBUC4uLi44QUFBQUFBQUFBQUFBQUFBQUFBQUJUVGpjZ04xZEVBQUFBQUFBLX5lbGVjPXRydWV-cG9zPTV-Z2FrX3R5cGU9c2luZ2xlbGluZX5sb2NhbGl0eT1GQVJJTkdET05-cG9zdGFsX2NvZGU9U043IDdXRH5RTD03fm1heF9zdWdnZXN0aW9ucz03"
  }
}
```

<preferences>
1. Strongly typed, immutable and declarative code.
2. Avoid unsafe ! it ignores type strong.
3. Use generics where suitable
4. Avoid use of any and correctly use unknown if all else fails. But you should attempt to use the proper types first.
5. Avoid unsafe array accessing prefer `.at(number)` and use guard statements to carefully handle unhappy code paths.
6. Do not clutter the code with inline comments, use suitable variable names so the code is self documenting
7. Use JSDocs to explain the purpose of functions and avoid putting any comments at the top of the file.
8. Make use of generics where suitable e.g. `And `get: <TData>(key: string) => Promise<TData | null>;` is a generic.`

// 1. Import redis if needed from this location
import { redis } from "@watt/redis/src/client";

// 2. follow 'Prefer for...of instead of forEach.biomelint/complexity/noForEach'

To avoid silly things like `Argument of type '{}' is not assignable to parameter of type 'string'.ts(2345)` you must carefully consider what the payloads are.

</preferences>

1. We need to refactor this as currently we can see there are direct calls to the entity address table using prisma. It does not make use of a service layer and it should be following the same onion / N-tier approach we have for the above public API.
2. The comments left in the file for the refactoring instructions need to be reviewed and understood before proceeding.
3. Explain what changes are required to meet the new requirements.
4. Explain how these align with the content cached in Redis and how it is performant
5. Explain how the approach protects exposing sensitive data and operations of writing to the database. E.g. the new `CustomerAddressSubmission` table is only written to by the private endpoint when the address was not found in Aperture and was provided by the customer and is not verified / trusted. The manually entered addresses that are found in Aperture are written directly into EntityAddress table as they are trusted along with the meter information.
6. Explain how the drop down will be displaying de-duped and sorted results from both EntityAddress table (if we have the address) and if not it displays it from Aperture.
7. Explain how the dropdown with both result sets allow the user to select their address even if we're missing it. Then explain how the address search result found in Aperture is not immediately stored in the db but is cached in Redis along with the GAK (global_address_key) reference to allow the private endpoint to fetch the full address along with the MPAN and MPRN so we can store it.
8. Notice how the public and private addresses endpoints working together like this with Redis avoids writing low quality data into the db and how the `CustomerAddressSubmission` table provides a mechanism to triage and approve the addresses provided by customers.
9. If the customer does not find their address in the drop down (either entity_address or aperture address search result) then they can enter it manually. Critically we need a new endpoint for this `/addresses/verify` this allows us to do a search for the address on Aperture to try and get the GAK for it. Which will be stored in Redis. This is then fetched by the addresses/[guid] endpoint when it tries to return the full address for the customer. If the manually entered address was found in Aperture then the Redis store will have a search result for it and both GAKs for electric and gas. This allows the addresses/[guid] endpoint to make a call to Aperture to get the full address and the meter numbers and persist them into the EntityAddress table.

## Process

Start by reviewing and listing and confirming my requirements by presenting them in a logical order.

Then state all the assets I have provided you and briefly list what fails will be involved in meeting the requirements.

Plan out a solution that meets the requirements and preferences.
