# Performance Optimization Phase 2 - PRD

## Executive Summary

This document outlines the second phase of performance optimization for the Watt CRM and Quote applications. Building upon the 40+ performance issues already documented, we've identified 10 additional critical performance bottlenecks that impact user experience, application responsiveness, and resource utilization.

## Problem Statement

Current performance issues are causing:

- Unnecessary re-renders leading to UI jank and poor responsiveness
- Excessive memory usage from unmemoized computations and missing cleanup
- Large bundle sizes from non-lazy loaded components
- Poor performance with large datasets due to missing virtualization
- Degraded user experience from synchronous operations that block the UI

## Goals

1. **Reduce unnecessary re-renders by 70%** through proper memoization and optimization
2. **Improve initial page load time by 40%** through code splitting and lazy loading
3. **Enhance data handling performance by 60%** with virtualization and server-side operations
4. **Decrease memory footprint by 50%** through proper cleanup and optimization
5. **Improve Time to Interactive (TTI) by 35%** by eliminating render-blocking operations

## Success Metrics

- First Contentful Paint (FCP) < 1.5s
- Time to Interactive (TTI) < 3.5s
- Total Blocking Time (TBT) < 300ms
- Cumulative Layout Shift (CLS) < 0.1
- React DevTools Profiler showing < 10ms render times for optimized components

## Identified Performance Issues

### 1. Missing React.memo on Heavy Components

**Location**: `apps/crm/src/components/files/files-grid/files-grid.tsx`

- FilesGrid component re-renders on every parent update
- Impact: 15-20ms unnecessary renders

### 2. Client-side Filtering Without Virtualization

**Location**: `apps/crm/src/components/files/files-grid/files-grid.tsx:42-67`

- Complex nested filtering on every render
- No pagination or windowing for large file lists
- Impact: O(n²) performance degradation with dataset size

### 3. UUID Generation in Render Loop

**Location**: `apps/quote/components/data-table/data-table-skeleton.tsx`

- Generates new UUIDs as keys on every render
- Breaks React reconciliation
- Impact: Forces full re-mount of skeleton rows

### 4. Heavy Computations Without Memoization

**Location**: `apps/crm/src/app/account/companies/[id]/components/site/payment-details-card.tsx`

- Payment method mapping on every render
- Multiple Object.keys().map() operations
- Impact: 5-10ms blocking computation per render

### 5. Missing useEffect Cleanup

**Location**: `apps/crm/src/app/account/components/notification-menu.tsx:64-69`

- Force re-renders by key manipulation
- No cleanup of pending requests
- Impact: Memory leaks and zombie children

### 6. Inline Styles in Virtualized Lists

**Location**: `apps/crm/src/components/data-table/data-table-infinite-scroll.tsx:138-153`

- Style objects created in render loop
- getCommonPinningStyles called per cell
- Impact: GC pressure and re-render cascades

### 7. Unbatched State Updates

**Location**: `apps/quote/app/(wizard)/usage/usage-form.tsx`

- Multiple sequential state updates
- Triggers multiple re-renders
- Impact: 3-4x render cycles per user action

### 8. Event Handler Recreation

**Location**: `apps/crm/src/app/account/companies/[id]/components/site/payment-details-card.tsx`

- Complex callbacks recreated every render
- No useCallback optimization
- Impact: Child component re-renders

### 9. Large Objects in Render Path

**Location**: `apps/crm/src/app/account/components/sidebar.tsx:26-139`

- Navigation configuration recreated every render
- Should be static or memoized
- Impact: 2-3ms per render

### 10. Synchronous Array Operations

**Location**: `apps/quote/app/(wizard)/usage/usage-form.tsx:434-442`

- Maps through provider arrays synchronously
- No virtualization for large lists
- Impact: Linear performance degradation

## Technical Approach

### Phase 1: Quick Wins (Week 1)

1. Add React.memo to components receiving stable props
2. Move static objects outside render functions
3. Fix UUID generation in skeletons
4. Add useCallback to event handlers

### Phase 2: Data Optimization (Week 2)

1. Implement virtualization for long lists
2. Move filtering to server-side where appropriate
3. Add pagination to data tables
4. Batch state updates with unstable_batchedUpdates

### Phase 3: Bundle Optimization (Week 3)

1. Lazy load heavy components
2. Code split by route
3. Implement dynamic imports
4. Optimize import statements

### Phase 4: Memory Management (Week 4)

1. Add cleanup to all useEffect hooks
2. Implement proper error boundaries
3. Fix memory leaks from closures
4. Add request cancellation

## Implementation Plan

### Task 1: Optimize FilesGrid Component

- Wrap in React.memo with proper comparison function
- Move filter logic to server-side API
- Implement virtual scrolling for file lists
- Add loading and error states

### Task 2: Fix Data Table Skeleton

- Generate stable IDs outside render
- Use index-based keys for skeleton rows
- Memoize skeleton row components

### Task 3: Optimize Payment Details Card

- Memoize payment method options
- Use useCallback for event handlers
- Extract static computations
- Implement field-level memoization

### Task 4: Fix Notification Menu

- Remove force re-render pattern
- Add proper cleanup to effects
- Implement request cancellation
- Use React Query for caching

### Task 5: Optimize Infinite Scroll Table

- Memoize style calculations
- Use CSS classes instead of inline styles
- Implement style object pooling
- Add intersection observer

### Task 6: Batch Form State Updates

- Use unstable_batchedUpdates
- Combine related state updates
- Implement form state reducer
- Add debouncing to inputs

### Task 7: Memoize Event Handlers

- Add useCallback to all handlers
- Extract static callbacks
- Implement event delegation
- Use stable references

### Task 8: Optimize Sidebar Navigation

- Move navigation config to constants
- Implement route-based code splitting
- Add lazy loading for icons
- Memoize active state calculations

### Task 9: Virtualize Select Components

- Implement react-window for dropdowns
- Add search/filter functionality
- Lazy load options
- Implement keyboard navigation

### Task 10: Add Performance Monitoring

- Implement React DevTools integration
- Add performance marks
- Set up monitoring dashboard
- Create performance budgets

## Migration Strategy

1. **Incremental Adoption**: Fix one component at a time
2. **Feature Flags**: Use toggles for risky optimizations
3. **A/B Testing**: Measure impact on real users
4. **Rollback Plan**: Keep old code paths available
5. **Documentation**: Update component docs with performance notes

## Risk Mitigation

- **Regression Testing**: Comprehensive test suite for each change
- **Performance Benchmarks**: Before/after measurements
- **User Acceptance**: Gradual rollout with monitoring
- **Code Review**: Senior engineer review for all changes
- **Monitoring**: Real-time performance metrics

## Timeline

- Week 1: Quick wins and low-hanging fruit
- Week 2: Data optimization and virtualization
- Week 3: Bundle optimization and code splitting
- Week 4: Memory management and cleanup
- Week 5: Testing, monitoring, and documentation

## Resources Required

- 2 Senior Frontend Engineers
- 1 Performance Engineer
- Access to performance monitoring tools
- Testing environment with production-like data

## Success Criteria

- All identified issues resolved
- Performance metrics meet targets
- No regression in functionality
- Positive user feedback
- Reduced infrastructure costs

## Next Steps

1. Initialize task-master for this PRD
2. Break down into specific implementation tasks
3. Prioritize based on impact and effort
4. Begin implementation with quick wins
5. Set up continuous performance monitoring
