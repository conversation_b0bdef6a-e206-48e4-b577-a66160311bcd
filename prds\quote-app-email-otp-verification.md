# PRD: Email OTP Verification for Quote App

## Overview

Implement email verification via OTP (One-Time Password) in the quote application to confirm user email addresses before proceeding with the quote wizard. This will ensure valid contact information and create a foundation for future authentication features.

## Background

Currently, the quote app (`/apps/quote`) does not authenticate users. Email addresses are collected on the `/company` page but are not verified. The CRM app (`/apps/crm`) has a complete Supabase authentication implementation that can serve as a reference.

## Goals

1. Verify user email addresses are valid and accessible
2. Create Supabase user accounts for quote requesters
3. Establish a foundation for future authentication features
4. Maintain a smooth user experience in the quote wizard flow

## User Flow

### Current Flow

1. User lands on `/company` page
2. User fills in company and contact details including email
3. User clicks "Next >" to proceed to `/site`

### New Flow

1. User lands on `/company` page
2. User enters email address
3. **NEW**: "Verify" button appears next to email input (disabled until valid email format)
4. **NEW**: User clicks "Verify" - triggers OTP email
5. **NEW**: U<PERSON> switches to OTP-only view (email input hidden, OTP input shown)
6. **NEW**: User enters 6-digit OTP code from email
7. **NEW**: <PERSON><PERSON> auto-submits when all 6 digits are entered
8. **NEW**: Email is verified ✓
9. **NEW**: "Change Email" button appears (text link style)
10. User completes remaining form fields
11. User clicks "Next >" to proceed
12. **NEW**: If email not verified, inline error shows immediately (even if other fields invalid)

## Technical Requirements

### 1. Dependencies

- [ ] Add `input-otp` package: `pnpm --filter @watt/quote add input-otp`
- [ ] Ensure `@supabase/ssr` and `@supabase/supabase-js` are available

### 2. Supabase Setup

#### 2.1 Environment Variables

- [ ] Ensure these are configured in `.env.local`:

  ```
  NEXT_PUBLIC_SUPABASE_URL
  NEXT_PUBLIC_SUPABASE_ANON_KEY
  SUPABASE_JWT_SECRET
  SUPABASE_SERVICE_ROLE_KEY
  ```

#### 2.2 Supabase Client

- [ ] Create `/apps/quote/utils/supabase/client.ts`
  - Copy pattern from `/apps/crm/src/utils/supabase/client.ts`
  - Use `@supabase/ssr` for browser client

#### 2.3 Supabase Middleware

- [ ] Create `/apps/quote/utils/supabase/middleware.ts`
  - Implement session management
  - Handle auth redirects for protected routes (future)

### 3. TRPC Routes

#### 3.1 Send OTP Route

- [ ] Create `sendEmailOtp` procedure in `packages/api/src/router/pcw.ts`
  - Input: email address
  - Process:
    1. Validate email format
    2. Check if user exists in Supabase
    3. Call `supabase.auth.signInWithOtp({ email })`
    4. Return success/error status
  - Rate limiting: Max 3 attempts per email per hour

#### 3.2 Verify OTP Route

- [ ] Create `verifyEmailOtp` procedure
  - Input: email, OTP token
  - Process:
    1. Call `supabase.auth.verifyOtp({ email, token, type: 'email' })`
    2. Create/update session
    3. Return verification status and user data
  - Handle errors: invalid OTP, expired OTP

### 4. UI Components

#### 4.1 Email Input with Verify Button

- [ ] Modify email input in `/apps/quote/app/(wizard)/company/company-form.tsx`
- [ ] Use provided component structure:

  ```tsx
  <div className="flex gap-2">
    <Input className="flex-1" placeholder="Email" type="email" />
    <Button variant="outline" disabled={!isValidEmail}>Verify</Button>
  </div>
  ```

- [ ] Import Button from `@watt/quote/components/ui/button`
- [ ] Verify button behavior:
  - Disabled when email input is empty or invalid format
  - Enabled only when valid email format detected (use regex validation)
  - Clicking with invalid email shows error: "Please enter a valid email address"
- [ ] After clicking "Verify", hide email input and show OTP input only

#### 4.2 OTP Input Component

- [ ] Create OTP input using provided `input-otp` component
- [ ] 6-digit OTP code implementation:

  ```tsx
  <OTPInput
    maxLength={6}
    containerClassName="flex items-center gap-3"
    render={({ slots }) => (
      <div className="flex gap-2">
        {slots.map((slot, idx) => (
          <Slot key={idx} {...slot} />
        ))}
      </div>
    )}
    onComplete={(value) => {
      // Auto-submit when 6 digits entered
      handleVerifyOtp(value)
    }}
  />
  ```

- [ ] Auto-focus on first digit when displayed
- [ ] Auto-submit verification when all 6 digits entered (no submit button)
- [ ] Show in place of email input (not below) when verification initiated

#### 4.3 Verification States UI

- [ ] **Initial**: Email input with disabled "Verify" button
- [ ] **Valid email entered**: "Verify" button becomes enabled
- [ ] **Invalid email + Verify clicked**: Show error below email input
- [ ] **OTP sent**: 
  - Hide email input completely
  - Show OTP input only
  - Show "Cancel" button (returns to email input)
  - Show "Resend OTP" button (text link style, appears after 10 seconds)
- [ ] **Verified**: 
  - Show verified email with green checkmark
  - Show "Change Email" button (text link style)
  - Hide OTP input
- [ ] **Error states**: Invalid OTP, expired OTP, network errors

### 5. Form Validation

#### 5.1 Update Zod Schema

- [ ] Add `emailVerified: boolean` to form schema
- [ ] Add custom validation: email must be verified before submission

#### 5.2 Form State Management

- [ ] Track verification status in React Hook Form
- [ ] Persist verification status in query params
- [ ] Clear verification on email change

### 6. Error Handling

#### 6.1 Inline Validation

- [ ] Email validation errors:
  - Invalid format on Verify click: "Please enter a valid email address"
  - Show below email input field
- [ ] Form submission validation:
  - Check email verification status FIRST (before other field validations)
  - If not verified: "Please verify your email address before continuing"
  - Show immediately when "Next" clicked, regardless of other form field states
  - Display below email input field

#### 6.2 OTP Errors

- [ ] Invalid OTP: "Invalid verification code. Please try again."
- [ ] Expired OTP: "Verification code expired. Please request a new one."
- [ ] Network errors: "Unable to verify. Please check your connection."

### 7. Email Template

- [ ] Use existing Supabase email templates or customize
- [ ] Include: OTP code, expiry time (10 minutes), company branding

## Implementation Tasks

### Phase 1: Foundation (2-3 days)

- [ ] Set up Supabase client and utilities
- [ ] Create TRPC routes for OTP send/verify
- [ ] Add `input-otp` dependency
- [ ] Create basic middleware structure

### Phase 2: UI Integration (2-3 days)

- [ ] Modify company form email input with Verify button
- [ ] Implement OTP input component with auto-submit
- [ ] Add UI state transitions (email → OTP → verified)
- [ ] Implement resend OTP timer (10 seconds)
- [ ] Add Change Email functionality
- [ ] Update form validation to check verification first

### Phase 3: Testing & Polish (1-2 days)

- [ ] Test error scenarios
- [ ] Add loading states
- [ ] Implement rate limiting
- [ ] Cross-browser testing
- [ ] Mobile responsiveness

### Phase 4: Future Considerations

- [ ] Session persistence across wizard steps
- [ ] Auto-fill user data from previous quotes
- [ ] Account dashboard for viewing past quotes
- [ ] Social login options

## Success Metrics

- Email verification rate > 90%
- OTP verification success rate > 95%
- No increase in form abandonment rate
- Reduction in invalid/fake email submissions

## Security Considerations

- Rate limit OTP requests per email
- OTP expiry time: 10 minutes
- Secure session management via httpOnly cookies
- No PII logged in client-side code

## Testing Plan

1. Unit tests for TRPC routes
2. Component tests for UI elements
3. E2E tests for complete flow
4. Manual testing of edge cases:
   - Multiple OTP requests
   - Expired OTPs
   - Network interruptions
   - Browser back/forward navigation

## Rollback Plan

- Feature flag to disable OTP verification
- Fallback to current non-verified flow
- Monitor error rates and user feedback

## UI/UX Specifications

### Button Styles

- **Verify Button**: Outline variant, disabled state when email invalid
- **Cancel Button**: Standard button to return from OTP to email input
- **Resend OTP**: Text link style button, appears after 10-second delay
- **Change Email**: Text link style button, shown after verification

### Component Layout

- Email and OTP inputs are mutually exclusive (never shown together)
- OTP replaces email input in the same location
- Error messages appear directly below the relevant input field
- Verification status indicators appear inline with the email

## Open Questions

1. Should we allow users to proceed without verification in certain cases?
2. How long should verified emails remain valid in the session?
3. Should we integrate with existing CRM user accounts?
4. What happens if user already has a Supabase account?
