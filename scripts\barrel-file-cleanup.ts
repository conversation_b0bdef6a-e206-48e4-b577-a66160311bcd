import * as fs from "node:fs";
import * as path from "node:path";

import * as ts from "typescript";

import type { API, FileInfo, Options, Transform } from "jscodeshift";

// List of barrel files to process; in an ideal world this shouldn't be
// hardcoded, but it's a good starting point.
const BARREL_IMPORTS = [
  // Package-level barrel files (workspace aliases)
  "@watt/external-apis",
  "@watt/common",
  "@watt/db",
  "@watt/redis",
  "@watt/emails",
  "@watt/pdfs",

  // Sub-directory barrel files within packages
  "@watt/external-apis/libs",
  "@watt/external-apis/libs/companies-house",
  "@watt/external-apis/libs/experian",
  "@watt/external-apis/libs/electralink",
  "@watt/external-apis/libs/aperture",
  "@watt/external-apis/libs/udcore",
  "@watt/external-apis/common",
  "@watt/external-apis/test-utils",
  "@watt/external-apis/config",
  "@watt/external-apis/utils",

  "@watt/common/types",
  "@watt/common/config",
  "@watt/common/constants",

  "@watt/emails/components",
  "@watt/emails/config"
];

// Optional - List of paths where we want to drop the last segment
const DROP_LAST_SEGMENT_PATHS = [
  // Component directories that should be imported by directory name
  "@watt/emails/components",

  // External API lib directories
  "@watt/external-apis/libs/companies-house",
  "@watt/external-apis/libs/experian",
  "@watt/external-apis/libs/electralink",
  "@watt/external-apis/libs/aperture",
  "@watt/external-apis/libs/udcore"
];

// This map will store the real paths of all exported components, types, and enums
const exportedItemsMap = new Map<
  string,
  { path: string; kind: "value" | "type" }
>();

function getCompilerOptions(filePath: string): ts.CompilerOptions {
  const configPath = ts.findConfigFile(
    path.dirname(filePath),
    ts.sys.fileExists,
    "tsconfig.json"
  );
  if (!configPath) {
    throw new Error("Could not find a valid 'tsconfig.json'.");
  }

  const { config } = ts.readConfigFile(configPath, ts.sys.readFile);
  if (!config) {
    throw new Error("Could not read tsconfig.json file.");
  }

  const { options } = ts.parseJsonConfigFileContent(
    config,
    ts.sys,
    path.dirname(configPath)
  );
  if (!options) {
    throw new Error("Could not parse tsconfig.json content.");
  }

  return options;
}

function resolveModule(
  importPath: string,
  containingFile: string
): string | null {
  if (!importPath || !containingFile) {
    return null;
  }

  const options = getCompilerOptions(containingFile);
  const moduleResolutionHost: ts.ModuleResolutionHost = {
    fileExists: ts.sys.fileExists,
    readFile: ts.sys.readFile,
    realpath: ts.sys.realpath,
    directoryExists: ts.sys.directoryExists,
    getCurrentDirectory: () => process.cwd(),
    getDirectories: ts.sys.getDirectories
  };

  const resolved = ts.resolveModuleName(
    importPath,
    containingFile,
    options,
    moduleResolutionHost
  );

  return resolved.resolvedModule?.resolvedFileName || null;
}

function buildExportMap(filePath: string, visited = new Set<string>()) {
  if (!filePath || visited.has(filePath)) {
    return;
  }
  visited.add(filePath);

  const fileContent = fs.readFileSync(filePath, "utf-8");
  if (!fileContent) {
    console.warn(`Could not read file content for ${filePath}`);
    return;
  }

  const sourceFile = ts.createSourceFile(
    filePath,
    fileContent,
    ts.ScriptTarget.Latest,
    true
  );
  if (!sourceFile) {
    console.warn(`Could not create source file for ${filePath}`);
    return;
  }

  function visit(node: ts.Node) {
    if (!node) {
      return;
    }

    if (ts.isExportDeclaration(node)) {
      if (node.exportClause && ts.isNamedExports(node.exportClause)) {
        for (const element of node.exportClause.elements) {
          if (!element.name) {
            continue;
          }

          const kind = element.isTypeOnly ? "type" : "value";
          if (node.moduleSpecifier) {
            const modulePath = (node.moduleSpecifier as ts.StringLiteral).text;
            const resolvedPath = resolveModule(modulePath, filePath);
            if (resolvedPath) {
              exportedItemsMap.set(element.name.text, {
                path: resolvedPath,
                kind
              });
            }
          } else {
            exportedItemsMap.set(element.name.text, { path: filePath, kind });
          }
        }
      } else if (node.moduleSpecifier) {
        const modulePath = (node.moduleSpecifier as ts.StringLiteral).text;
        const resolvedPath = resolveModule(modulePath, filePath);
        if (resolvedPath) {
          buildExportMap(resolvedPath, visited);
        }
      }
    } else if (ts.isExportAssignment(node)) {
      exportedItemsMap.set("default", { path: filePath, kind: "value" });
    } else if (
      (ts.isFunctionDeclaration(node) ||
        ts.isClassDeclaration(node) ||
        ts.isVariableStatement(node) ||
        ts.isInterfaceDeclaration(node) ||
        ts.isTypeAliasDeclaration(node) ||
        ts.isEnumDeclaration(node)) &&
      node.modifiers?.some(m => m.kind === ts.SyntaxKind.ExportKeyword)
    ) {
      if (
        ts.isFunctionDeclaration(node) ||
        ts.isClassDeclaration(node) ||
        ts.isInterfaceDeclaration(node) ||
        ts.isTypeAliasDeclaration(node) ||
        ts.isEnumDeclaration(node)
      ) {
        if (node.name) {
          const kind =
            ts.isInterfaceDeclaration(node) || ts.isTypeAliasDeclaration(node)
              ? "type"
              : "value";
          exportedItemsMap.set(node.name.text, { path: filePath, kind });
        }
      } else if (ts.isVariableStatement(node)) {
        for (const decl of node.declarationList.declarations) {
          if (ts.isIdentifier(decl.name)) {
            exportedItemsMap.set(decl.name.text, {
              path: filePath,
              kind: "value"
            });
          }
        }
      }
    }

    ts.forEachChild(node, visit);
  }

  visit(sourceFile);
}

const transform: Transform = (
  fileInfo: FileInfo,
  api: API,
  options: Options
) => {
  if (!fileInfo?.source || !api?.jscodeshift) {
    return null;
  }

  const j = api.jscodeshift;
  const root = j(fileInfo.source);

  // Build the export map if it hasn't been built yet
  if (exportedItemsMap.size === 0) {
    for (const barrelImport of BARREL_IMPORTS) {
      const barrelPath = resolveModule(barrelImport, fileInfo.path);
      if (barrelPath) {
        buildExportMap(barrelPath);
      } else {
        console.warn(`Could not resolve barrel file: ${barrelImport}`);
      }
    }
  }

  let modified = false;

  for (const nodePath of root.find(j.ImportDeclaration).paths()) {
    if (!nodePath?.node?.source?.value) {
      continue;
    }

    const importPath = nodePath.node.source.value;

    if (typeof importPath !== "string") {
      continue;
    }

    const matchingBarrel = BARREL_IMPORTS.find(
      barrel => importPath === barrel || importPath.endsWith(`/${barrel}`)
    );

    if (matchingBarrel) {
      const newImports = new Map<
        string,
        // biome-ignore lint/suspicious/noExplicitAny: <ignore>
        { valueSpecifiers: any[]; typeSpecifiers: any[] }
      >();

      if (!nodePath.node.specifiers) {
        continue;
      }

      for (const specifier of nodePath.node.specifiers) {
        if (specifier.type === "ImportSpecifier") {
          if (!specifier.imported?.name || !specifier.local?.name) {
            continue;
          }

          const itemName = specifier.imported.name;

          if (typeof itemName !== "string") {
            continue;
          }

          const localName = specifier.local.name;

          if (typeof localName !== "string") {
            continue;
          }

          const exportedItem = exportedItemsMap.get(itemName);

          if (exportedItem) {
            // Get the path relative to the barrel file
            const resolvedBarrelPath = resolveModule(
              matchingBarrel,
              fileInfo.path
            );
            if (!resolvedBarrelPath) {
              console.warn(
                `Could not resolve barrel path for ${matchingBarrel}`
              );
              continue;
            }

            const barrelDir = path.dirname(resolvedBarrelPath);
            let relativePath = path.relative(barrelDir, exportedItem.path);

            // If the relative path is empty, it means the export is from the barrel file itself
            if (relativePath === "") {
              relativePath = ".";
            }

            // Remove the file extension
            relativePath = relativePath.replace(/\.(ts|tsx|js|jsx)$/, "");

            // Ensure the path starts with the correct barrel import
            let newImportPath = path
              .join(matchingBarrel, relativePath)
              .replace(/\\/g, "/");

            // Check if we need to drop the last segment
            const shouldDropLastSegment = DROP_LAST_SEGMENT_PATHS.some(
              dropPath => newImportPath.startsWith(dropPath)
            );

            if (shouldDropLastSegment) {
              newImportPath = path.dirname(newImportPath);
            }

            if (!newImports.has(newImportPath)) {
              newImports.set(newImportPath, {
                valueSpecifiers: [],
                typeSpecifiers: []
              });
            }

            const importGroup = newImports.get(newImportPath);
            if (!importGroup) {
              continue;
            }

            const newSpecifier = j.importSpecifier(
              j.identifier(itemName),
              itemName !== localName ? j.identifier(localName) : null
            );

            if (
              exportedItem.kind === "type" ||
              nodePath.node.importKind === "type"
            ) {
              importGroup.typeSpecifiers.push(newSpecifier);
            } else {
              importGroup.valueSpecifiers.push(newSpecifier);
            }
          } else {
            console.warn(`Could not find export information for ${itemName}`);
          }
        }
      }

      const newImportNodes = [...newImports.entries()].flatMap(
        ([importPath, { valueSpecifiers, typeSpecifiers }]) => {
          const imports = [];
          if (valueSpecifiers.length > 0) {
            imports.push(
              j.importDeclaration(valueSpecifiers, j.literal(importPath))
            );
          }
          if (typeSpecifiers.length > 0) {
            imports.push(
              j.importDeclaration(typeSpecifiers, j.literal(importPath), "type")
            );
          }
          return imports;
        }
      );

      if (newImportNodes.length > 0) {
        j(nodePath).replaceWith(newImportNodes);
        modified = true;
      }
    }
  }

  if (modified) {
    console.log(`Modified imports in ${fileInfo.path}`);
    return root.toSource();
  }

  return null;
};

export default transform;
