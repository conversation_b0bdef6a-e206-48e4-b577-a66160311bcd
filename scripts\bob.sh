#!/usr/bin/env bash
set -euo pipefail

# 1) Your Supabase URL on port 6543 (pooler-only)
DIRECT_URL="postgres://postgres.vomrghiulbmrfvmhlflk:<EMAIL>:5432/postgres"

# 2) Disable any per-statement timeout in the backend
export PGOPTIONS='--statement_timeout=0'

# 3) Directory-format dump produced by pg_restore --format=directory
DUMP_DIR="/tmp/addr_dump_dir"

# 4) Loop over each chunk file (each has exactly one "TABLE DATA …" line)
for listfile in ./chunk_*; do
  echo "Restoring from $(basename "$listfile")…"
  pg_restore \
    --use-list="$listfile" \
    --data-only \
    --no-owner \
    --no-privileges \
    --jobs=10 \
    --verbose \
    --dbname="$DIRECT_URL" \
    "$DUMP_DIR"
done
