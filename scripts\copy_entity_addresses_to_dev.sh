#!/usr/bin/env bash
set -euo pipefail
JOBS=${1:-8}
DUMP_DIR="/tmp/addr_dump_dir"
TABLES=(public.entity_address public.mpans public.mprns)

# ─── load envs ───────────────────────────────────────────────────────────────
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

source "$ROOT_DIR/.env.prod"
PROD_URL="$DIRECT_URL"
source "$ROOT_DIR/.env.dev"
DEV_URL="$DIRECT_URL"

[[ -z "$PROD_URL" || -z "$DEV_URL" ]] && {
  echo "DIRECT_URL missing in env files"
  exit 1
}

# ► use the **direct** host, not the pooler  ◄
[[ $PROD_URL == *pooler* || $DEV_URL == *pooler* ]] && {
  echo "❗ Replace pooler URLs with <project>.db.supabase.co:6543"
  exit 1
}

export PGOPTIONS='-c statement_timeout=0'

rm -rf "$DUMP_DIR" && mkdir -p "$DUMP_DIR"

echo "--- pg_dump (data-only) -----------------------------"
pg_dump --format=directory --jobs="$JOBS" --data-only \
  --no-owner --no-privileges --verbose --progress \
  "${TABLES[@]/#/--table=}" \
  --dbname="$PROD_URL" --file="$DUMP_DIR"

echo "--- pg_restore (data-only) --------------------------"
pg_restore --jobs="$JOBS" --data-only \
  --no-owner --no-privileges --disable-triggers \
  --exit-on-error --verbose \
  --dbname="$DEV_URL" "$DUMP_DIR"

echo "--- ANALYZE ----------------------------------------"
psql "$DEV_URL" -q -c 'ANALYZE VERBOSE public.entity_address;'

echo "✅  copy complete."
