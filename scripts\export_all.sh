#!/usr/bin/env bash
set -euo pipefail

# how many parallel workers to use (default 8)
JOBS=${1:-8}

# locate project root & load prod .env
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
source "$ROOT_DIR/.env.prod"
PROD_URL="$DIRECT_URL"
[[ -z "$PROD_URL" ]] && {
  echo "❌ DIRECT_URL not set in .env.prod"
  exit 1
}

# ensure no statement timeouts
export PGOPTIONS='
  -c maintenance_work_mem=4GB
  -c checkpoint_completion_target=0.9
  -c synchronous_commit=off
  -c statement_timeout=0
  -c idle_in_transaction_session_timeout=0
  -c lock_timeout=0
  -c tcp_keepalives_idle=300          # start probing after 5 min idle
  -c tcp_keepalives_interval=60       # probe every minute thereafter
  -c tcp_keepalives_count=20          # give it 20 chances before giving up
'

# prepare dump directory
DUMP_DIR="$ROOT_DIR/dumps"
rm -rf "$DUMP_DIR" && mkdir -p "$DUMP_DIR"

# 1) entity_address
echo "➡️  Dumping entity_address (directory format)…"
ENTITY_DIR="$DUMP_DIR/entity_address"
rm -rf "$ENTITY_DIR" && mkdir -p "$ENTITY_DIR"
pg_dump \
  --format=directory \
  --compress=0 \
  --jobs="$JOBS" \
  --data-only \
  --no-owner \
  --no-privileges \
  --table=public.entity_address \
  --file="$ENTITY_DIR" \
  --dbname="$PROD_URL"
echo "✅  entity_address → $ENTITY_DIR"

# 2) mpans
echo "➡️  Dumping mpans (directory format)…"
MPANS_DIR="$DUMP_DIR/mpans"
rm -rf "$MPANS_DIR" && mkdir -p "$MPANS_DIR"
pg_dump \
  --format=directory \
  --compress=0 \
  --jobs="$JOBS" \
  --data-only \
  --no-owner \
  --no-privileges \
  --table=public.mpans \
  --file="$MPANS_DIR" \
  --dbname="$PROD_URL"
echo "✅  mpans → $MPANS_DIR"

# 3) mprns
echo "➡️  Dumping mprns (directory format)…"
MPRNS_DIR="$DUMP_DIR/mprns"
rm -rf "$MPRNS_DIR" && mkdir -p "$MPRNS_DIR"
pg_dump \
  --format=directory \
  --compress=0 \
  --jobs="$JOBS" \
  --data-only \
  --no-owner \
  --no-privileges \
  --table=public.mprns \
  --file="$MPRNS_DIR" \
  --dbname="$PROD_URL"
echo "✅  mprns → $MPRNS_DIR"

echo "🎉  All exports complete in $DUMP_DIR"

# Use this to monitor the progress
# SELECT
#   p.pid,
#   a.query              AS sql,
#   p.relid::regclass    AS table_name,
#   p.command,
#   p.type,
#   pg_size_pretty(p.bytes_processed) AS bytes_done,
#   pg_size_pretty(p.bytes_total)     AS bytes_total,
#   p.tuples_processed,
#   p.tuples_excluded,
#   now() - a.query_start            AS elapsed
# FROM pg_stat_progress_copy p
# JOIN pg_stat_activity a ON a.pid = p.pid
# WHERE p.command = 'COPY TO'
#   AND a.state   = 'active';
# \watch 1
