#!/usr/bin/env bash
################################################################################
# create_sample_dump.sh – build 100 k-row sample tables and dump them, step-by-step
# Runs from   : project_root/scripts
# Needs       : ../.env.prod  with DIRECT_URL=postgres://…
# Usage       : ./create_sample_dump.sh [<sample_size>] [<dump_file>]
################################################################################
set -euo pipefail

###############################################################################
# 0 ── Environment
###############################################################################
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}/.."

# shellcheck source=/dev/null
if [[ -f "${PROJECT_ROOT}/.env.prod" ]]; then
  set -o allexport
  source "${PROJECT_ROOT}/.env.prod"
  set +o allexport
else
  echo "❌  .env.prod not found in ${PROJECT_ROOT}"
  exit 1
fi
: "${DIRECT_URL:?DIRECT_URL must be defined in .env.prod}"

SAMPLE_SIZE="${1:-100000}"
DUMP_FILE="${2:-${PROJECT_ROOT}/sample_dataset_$(date +%Y%m%d_%H%M%S).dump}"

# Never time-out — locally or on the server - DOES NOT WORK if we go via the pooler
export PGOPTIONS='
  -c maintenance_work_mem="4GB"
  -c synchronous_commit=off
  -c statement_timeout=0
  -c idle_in_transaction_session_timeout=0
  -c lock_timeout=0
  -c tcp_keepalives_idle=300
  -c tcp_keepalives_interval=60
  -c tcp_keepalives_count=20
'

echo "🔗  DB        : ${DIRECT_URL}"
echo "📦  Rows      : ${SAMPLE_SIZE}"
echo "📄  Dump file : ${DUMP_FILE}"
echo "⏱   All time-outs disabled via PGOPTIONS"
echo

###############################################################################
# 1 ── Helper: run SQL with the current options
###############################################################################
run_psql() {
  psql "${DIRECT_URL}" "$@"
}

###############################################################################
# 2 ── Step functions
###############################################################################
create_tables() {
  echo "⚙️  Creating constraint-free clone tables (if missing)…"
  run_psql <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

CREATE TABLE IF NOT EXISTS public.entity_address_sample
  (LIKE public.entity_address INCLUDING DEFAULTS EXCLUDING CONSTRAINTS EXCLUDING INDEXES);
CREATE TABLE IF NOT EXISTS public.mpans_sample
  (LIKE public.mpans          INCLUDING DEFAULTS EXCLUDING CONSTRAINTS EXCLUDING INDEXES);
CREATE TABLE IF NOT EXISTS public.mprns_sample
  (LIKE public.mprns          INCLUDING DEFAULTS EXCLUDING CONSTRAINTS EXCLUDING INDEXES);
SQL
  echo "✅  Tables ready."
}

seed_entity_ids() {
  echo "🌱  Building fresh entity-id list (${SAMPLE_SIZE} rows)…"
  run_psql -v sample_size="${SAMPLE_SIZE}" <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

DROP TABLE IF EXISTS public.sample_entity_ids;
CREATE TABLE public.sample_entity_ids AS
SELECT id
FROM   public.entity_address
ORDER  BY random()
LIMIT  :sample_size;
SQL
  echo "✅  seed complete."
}

populate_entity_addresses() {
  echo "➕  Loading rows into entity_address_sample…"
  run_psql <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

INSERT INTO public.entity_address_sample
SELECT e.*
FROM   public.entity_address       e
JOIN   public.sample_entity_ids    s USING (id)
ON CONFLICT DO NOTHING;            -- no effect unless you add PKs later
SQL
  echo "✅  entity_address_sample populated."
}

populate_mpans() {
  echo "➕  Loading rows into mpans_sample…"
  run_psql <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

INSERT INTO public.mpans_sample
SELECT DISTINCT ON (m."entityAddressId") m.*
FROM   public.mpans              m
JOIN   public.sample_entity_ids  s ON s.id = m."entityAddressId"
ORDER  BY m."entityAddressId", random();
SQL
  echo "✅  mpans_sample populated."
}

populate_mprns() {
  echo "➕  Loading rows into mprns_sample…"
  run_psql <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

INSERT INTO public.mprns_sample
SELECT DISTINCT ON (r."entityAddressId") r.*
FROM   public.mprns              r
JOIN   public.sample_entity_ids  s ON s.id = r."entityAddressId"
ORDER  BY r."entityAddressId", random();
SQL
  echo "✅  mprns_sample populated."
}

sanity_check() {
  echo "🔍  Sanity check…"
  local counts
  counts=$(
    run_psql -v sample_size="${SAMPLE_SIZE}" -t -A -F',' <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

WITH c AS (
  SELECT 'EA' AS src, count(*) AS cnt FROM public.entity_address_sample
  UNION ALL
  SELECT 'MP', count(*) FROM public.mpans_sample
  UNION ALL
  SELECT 'MR', count(*) FROM public.mprns_sample
)
SELECT string_agg(cnt::text, ',') FROM c;
SQL
  )
  IFS=',' read -r ea mp mr <<<"${counts}"
  echo "• entity_address_sample : ${ea}"
  echo "• mpans_sample          : ${mp}"
  echo "• mprns_sample          : ${mr}"
  if [[ "${ea}" == "${SAMPLE_SIZE}" && "${mp}" == "${SAMPLE_SIZE}" && "${mr}" == "${SAMPLE_SIZE}" ]]; then
    echo "✅  All good."
  else
    echo "❌  Row mismatch – expected ${SAMPLE_SIZE} each."
    return 1
  fi
}

cleanup_tables() {
  echo "🧹  Dropping sample & helper tables…"
  run_psql <<'SQL'
SET maintenance_work_mem = "4GB";
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 300;
SET tcp_keepalives_interval = 60;
SET tcp_keepalives_count = 20;

DROP TABLE IF EXISTS public.entity_address_sample;
DROP TABLE IF EXISTS public.mpans_sample;
DROP TABLE IF EXISTS public.mprns_sample;
DROP TABLE IF EXISTS public.sample_entity_ids;
SQL
  echo "✅  Cleanup complete."
}

dump_tables() {
  echo "💾  Running pg_dump…"
  pg_dump "${DIRECT_URL}" \
    --format=custom --compress=9 \
    --no-owner --no-privileges \
    --table=public.entity_address_sample \
    --table=public.mpans_sample \
    --table=public.mprns_sample \
    --file="${DUMP_FILE}"
  echo "🎉  Dump written to ${DUMP_FILE}"
}

###############################################################################
# 3 ── Interactive menu
###############################################################################
menu() {
  cat <<EOF

Choose an action
────────────────
1  – Create sample tables
2  – Seed sample_entity_ids (${SAMPLE_SIZE})
3  – Populate entity_address_sample
4  – Populate mpans_sample
5  – Populate mprns_sample
6  – Sanity check counts
7  – Cleanup (drop sample tables)
8  – pg_dump the sample tables
9  – Quit

EOF
}

while true; do
  menu
  read -rp "Enter choice [1-9]: " choice
  case "${choice}" in
  1) create_tables ;;
  2) seed_entity_ids ;;
  3) populate_entity_addresses ;;
  4) populate_mpans ;;
  5) populate_mprns ;;
  6) sanity_check ;;
  7) cleanup_tables ;;
  8) dump_tables ;;
  9)
    echo "👋  Bye!"
    exit 0
    ;;
  *) echo "🔸  Invalid choice." ;;
  esac
  echo
done
