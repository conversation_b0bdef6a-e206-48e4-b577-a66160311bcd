#!/usr/bin/env bash
set -euo pipefail

JOBS=${1:-4}

# ─── tune connections ─────────────────────────────────────────────────────────
# Use extreme keepalive and user-timeout settings to approximate "infinite" idle tolerance
export PGOPTIONS='
  -c maintenance_work_mem=4GB
  -c synchronous_commit=off
  -c statement_timeout=0
  -c tcp_keepalives_idle=600
  -c tcp_keepalives_interval=60
  -c tcp_keepalives_count=2147483647
  -c tcp_user_timeout=2147483647
'

# ─── load DEV URL ─────────────────────────────────────────────────────────────
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
source "$ROOT_DIR/.env.dev"
# Append libpq keepalive & timeout params to your direct IPv4 DB URL
DEV_URL="${DATABASE_DIRECT_URL:?DATABASE_DIRECT_URLq must be set in .env.dev}?keepalives=1&keepalives_idle=600&keepalives_interval=60&keepalives_count=2147483647&tcp_user_timeout=2147483647"

DUMP_DIR="$ROOT_DIR/dumps"

# ─── 0) Confirm config settings ────────────────────────────────────────────────
echo "➡️  Confirming config settings…"
psql "$DEV_URL" <<SQL
SHOW statement_timeout;
SHOW lock_timeout;
SHOW idle_in_transaction_session_timeout;
SHOW maintenance_work_mem;
SHOW checkpoint_completion_target;
SHOW synchronous_commit;
SHOW tcp_keepalives_idle;
SHOW tcp_keepalives_interval;
SHOW tcp_keepalives_count;
SHOW tcp_user_timeout;
SQL

# ─── 1) Create UNLOGGED staging tables ────────────────────────────────────────
echo "➡️  Creating UNLOGGED staging tables…"
psql "$DEV_URL" <<SQL
DROP TABLE IF EXISTS entity_address_stg, mpans_stg, mprns_stg;

CREATE UNLOGGED TABLE entity_address_stg (
  LIKE public.entity_address
    INCLUDING DEFAULTS
    INCLUDING STORAGE
    INCLUDING COMMENTS
    EXCLUDING INDEXES
    EXCLUDING CONSTRAINTS
);
CREATE UNLOGGED TABLE mpans_stg (
  LIKE public.mpans
    INCLUDING DEFAULTS
    INCLUDING STORAGE
    INCLUDING COMMENTS
    EXCLUDING INDEXES
    EXCLUDING CONSTRAINTS
);
CREATE UNLOGGED TABLE mprns_stg (
  LIKE public.mprns
    INCLUDING DEFAULTS
    INCLUDING STORAGE
    INCLUDING COMMENTS
    EXCLUDING INDEXES
    EXCLUDING CONSTRAINTS
);
SQL

# ─── 2) Restore into staging ──────────────────────────────────────────────────
echo "➡️  Restoring into staging…"
for tbl in entity_address mpans mprns; do
  echo "   • $tbl → ${tbl}_stg"
  pg_restore \
    --dbname="$DEV_URL" \
    --jobs="$JOBS" \
    --data-only \
    --no-owner \
    --no-privileges \
    --exit-on-error \
    --verbose \
    --use-list=<(pg_restore --list "$DUMP_DIR/$tbl" |
      sed -E "s/public\.$tbl/public.${tbl}_stg/") \
    "$DUMP_DIR/$tbl"
done

# ─── 3) Atomic swap staging → production ─────────────────────────────────────
echo "➡️  Swapping staging → production…"
psql "$DEV_URL" <<SQL
BEGIN;

ALTER TABLE public.entity_address RENAME TO entity_address_old;
ALTER TABLE public.mpans            RENAME TO mpans_old;
ALTER TABLE public.mprns            RENAME TO mprns_old;

ALTER TABLE public.entity_address_stg RENAME TO entity_address;
ALTER TABLE public.mpans_stg           RENAME TO mpans;
ALTER TABLE public.mprns_stg          RENAME TO mprns;

DROP TABLE entity_address_old;
DROP TABLE mpans_old;
DROP TABLE mprns_old;

COMMIT;
SQL

# ─── 4) Rebuild non-PK indexes concurrently ──────────────────────────────────
echo "➡️  Rebuilding indexes on new tables…"
psql "$DEV_URL" <<SQL
CREATE INDEX CONCURRENTLY IF NOT EXISTS address_postcode_idx
  ON public.entity_address(postcode);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_uprn_idx
  ON public.entity_address(uprn);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS entity_address_guid_key
  ON public.entity_address(guid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_postcode_guid_idx
  ON public.entity_address(postcode, guid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_displayName_guid_idx
  ON public.entity_address("displayName", guid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_postcode_apertureOrigin_idx
  ON public.entity_address(postcode, "apertureOrigin");
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_createdById_idx
  ON public.entity_address("createdById");

CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_uprn_idx
  ON public.mpans(uprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_udprn_idx
  ON public.mpans(udprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_entity_address_id_idx
  ON public.mpans("entityAddressId");
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS mpans_value_key
  ON public.mpans(value);

CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_uprn_idx
  ON public.mprns(uprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_udprn_idx
  ON public.mprns(udprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_entity_address_id_idx
  ON public.mprns("entityAddressId");
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS mprns_value_key
  ON public.mprns(value);
SQL

echo "✅ UNLOGGED‐staged import & reindex complete!"
