#!/usr/bin/env bash
set -euo pipefail

JOBS=${1:-4}

# ─── tune connections ─────────────────────────────────────────────────────────
export PGOPTIONS='
  -c maintenance_work_mem=4GB
  -c synchronous_commit=off
  -c statement_timeout=0
  -c idle_in_transaction_session_timeout=0
  -c lock_timeout=0
  -c tcp_keepalives_idle=300          # start probing after 5 min idle
  -c tcp_keepalives_interval=60       # probe every minute thereafter
  -c tcp_keepalives_count=20          # give it 20 chances before giving up
'

# ─── load DEV URL ─────────────────────────────────────────────────────────────
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
source "$ROOT_DIR/.env.dev"
DEV_URL="${DIRECT_URL:?DIRECT_URL must be set in .env.dev}"
DUMP_DIR="$ROOT_DIR/dumps"

# ─── 1) Create UNLOGGED staging tables ────────────────────────────────────────
echo "➡️  Creating UNLOGGED staging tables..."
psql "$DEV_URL" <<SQL
-- Drop old staging if present
DROP TABLE IF EXISTS entity_address_stg, mpans_stg, mprns_stg;

-- Create new UNLOGGED clones
CREATE UNLOGGED TABLE entity_address_stg (
  LIKE public.entity_address
    INCLUDING DEFAULTS    -- copy each column’s DEFAULT expressions
    INCLUDING STORAGE     -- copy any per-column TOAST/storage settings
    INCLUDING COMMENTS    -- copy table/column comments
    EXCLUDING INDEXES     -- skip indexes
    EXCLUDING CONSTRAINTS -- skip PKs, FKs, uniques, etc.
);
CREATE UNLOGGED TABLE mpans_stg (
  LIKE public.mpans
    INCLUDING DEFAULTS    -- copy each column’s DEFAULT expressions
    INCLUDING STORAGE     -- copy any per-column TOAST/storage settings
    INCLUDING COMMENTS    -- copy table/column comments
    EXCLUDING INDEXES     -- skip indexes
    EXCLUDING CONSTRAINTS -- skip PKs, FKs, uniques, etc.
);

CREATE UNLOGGED TABLE mprns_stg (
  LIKE public.mprns
    INCLUDING DEFAULTS    -- copy each column’s DEFAULT expressions
    INCLUDING STORAGE     -- copy any per-column TOAST/storage settings
    INCLUDING COMMENTS    -- copy table/column comments
    EXCLUDING INDEXES     -- skip indexes
    EXCLUDING CONSTRAINTS -- skip PKs, FKs, uniques, etc.
);
SQL

# ─── 2) Restore into staging ──────────────────────────────────────────────────
echo "➡️  Restoring into staging..."
for tbl in entity_address mpans mprns; do
  echo "   • $tbl -> ${tbl}_stg"
  # note: we restore into public.${tbl}_stg by hacking the TOC
  pg_restore \
    --dbname="$DEV_URL" \
    --jobs="$JOBS" \
    --data-only \
    --no-owner \
    --no-privileges \
    --exit-on-error \
    --verbose \
    --use-list=<(pg_restore --list "$DUMP_DIR/$tbl" |
      sed -E "s/public\.$tbl/public.${tbl}_stg/") \
    "$DUMP_DIR/$tbl"
done

# ─── 3) Atomic swap staging -> production ─────────────────────────────────────
echo "➡️  Swapping staging tables to the production copies..."
psql "$DEV_URL" <<SQL
BEGIN;

-- 1) Rename current production out of the way
ALTER TABLE public.entity_address RENAME TO entity_address_old;
ALTER TABLE public.mpans            RENAME TO mpans_old;
ALTER TABLE public.mprns            RENAME TO mprns_old;

-- 2) Rename staging into place
ALTER TABLE public.entity_address_stg RENAME TO entity_address;
ALTER TABLE public.mpans_stg           RENAME TO mpans;
ALTER TABLE public.mprns_stg          RENAME TO mprns;

-- 3) Clean up old tables
DROP TABLE entity_address_old;
DROP TABLE mpans_old;
DROP TABLE mprns_old;

COMMIT;
SQL

# ─── 4) Rebuild non-PK indexes concurrently ──────────────────────────────────
echo "➡️  Rebuilding indexes on new tables..."
psql "$DEV_URL" <<SQL
-- entity_address indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS address_postcode_idx
  ON public.entity_address(postcode);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_uprn_idx
  ON public.entity_address(uprn);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS entity_address_guid_key
  ON public.entity_address(guid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_postcode_guid_idx
  ON public.entity_address(postcode, guid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_displayName_guid_idx
  ON public.entity_address("displayName", guid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_postcode_apertureOrigin_idx
  ON public.entity_address(postcode, "apertureOrigin");
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_createdById_idx
  ON public.entity_address("createdById");

-- mpans indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_uprn_idx
  ON public.mpans(uprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_udprn_idx
  ON public.mpans(udprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_entity_address_id_idx
  ON public.mpans("entityAddressId");
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS mpans_value_key
  ON public.mpans(value);

-- mprns indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_uprn_idx
  ON public.mprns(uprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_udprn_idx
  ON public.mprns(udprn);
CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_entity_address_id_idx
  ON public.mprns("entityAddressId");
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS mprns_value_key
  ON public.mprns(value);
SQL

echo "✅ UNLOGGED‐staged import & reindex complete!"
