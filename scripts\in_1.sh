#!/usr/bin/env bash
set -euo pipefail

# ─── Parameters ────────────────────────────────────────────────────────────────
BATCH_SIZE=${1:-1000000}
IMPORT_JOBS=${2:-1}

# ─── Locate project & load prod env ────────────────────────────────────────────
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
source "$ROOT_DIR/.env.prod"

# Append client‐side keepalive & timeout params
PROD_URL="${DIRECT_URL:?DIRECT_URL must be set in .env.prod}"
PROD_URL+="?sslmode=require&keepalives=1&keepalives_idle=600&keepalives_interval=60&keepalives_count=2147483647&tcp_user_timeout=2147483647"

EXPORT_DIR="$ROOT_DIR/dumps"
rm -rf "$EXPORT_DIR" && mkdir -p "$EXPORT_DIR"

# GUCs we want in every session
SQL_SETTINGS="
SET maintenance_work_mem = '4GB';
SET synchronous_commit = off;
SET statement_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET lock_timeout = 0;
SET tcp_keepalives_idle = 600;
SET tcp_keepalives_interval = 60;
SET tcp_user_timeout = 2147483647;
"

echo "➡️ EXPORT: batching CSVs (prod → $EXPORT_DIR) …"
declare -A TABLES=([entity_address]=id [mpans]=id [mprns]=id)

for tbl in "${!TABLES[@]}"; do
  pk="${TABLES[$tbl]}"
  tbldir="$EXPORT_DIR/$tbl"
  mkdir -p "$tbldir"

  echo "→ $tbl (PK=$pk):"
  # -q suppresses the SET status messages, -t returns tuples only
  read MIN_ID MAX_ID < <(
    psql -q -t -c "$SQL_SETTINGS SELECT MIN($pk), MAX($pk) FROM public.$tbl" "$PROD_URL"
  )
  MIN_ID=${MIN_ID//[[:space:]]/}
  MAX_ID=${MAX_ID//[[:space:]]/}
  echo "   ID range: $MIN_ID → $MAX_ID"

  start="$MIN_ID"
  while [ "$start" -le "$MAX_ID" ]; do
    end=$((start + BATCH_SIZE - 1))
    file="$tbldir/${tbl}_${start}_to_${end}.csv"
    echo "   • Exporting $start–$end → $file"
    psql "$PROD_URL" -c "$SQL_SETTINGS \copy (SELECT * FROM public.$tbl WHERE $pk BETWEEN $start AND $end ORDER BY $pk) TO '$file' WITH CSV HEADER"
    start=$((end + 1))
  done

  echo " ✅ Done $tbl → $tbldir/*.csv"
done

# ─── Load DEV env & set GUCs ─────────────────────────────────────────────────
source "$ROOT_DIR/.env.dev"
DEV_URL="${DATABASE_DIRECT_URL:?DATABASE_DIRECT_URL must be set in .env.dev}"
DEV_URL+="?sslmode=require&keepalives=1&keepalives_idle=600&keepalives_interval=60&keepalives_count=2147483647&tcp_user_timeout=2147483647"

export PGOPTIONS="
-c maintenance_work_mem=4GB
-c synchronous_commit=off
-c statement_timeout=0
-c idle_in_transaction_session_timeout=0
-c lock_timeout=0
-c tcp_keepalives_idle=600
-c tcp_keepalives_interval=60
-c tcp_user_timeout=2147483647
"

# ─── Create UNLOGGED staging tables ────────────────────────────────────────────
echo "➡️ CREATING UNLOGGED staging tables…"
psql "$DEV_URL" <<SQL
$SQL_SETTINGS
DROP TABLE IF EXISTS entity_address_stg, mpans_stg, mprns_stg;

CREATE UNLOGGED TABLE entity_address_stg (LIKE public.entity_address INCLUDING ALL EXCLUDING INDEXES);
CREATE UNLOGGED TABLE mpans_stg          (LIKE public.mpans          INCLUDING ALL EXCLUDING INDEXES);
CREATE UNLOGGED TABLE mprns_stg          (LIKE public.mprns          INCLUDING ALL EXCLUDING INDEXES);
SQL

# ─── Import CSVs into staging ─────────────────────────────────────────────────
echo "➡️ IMPORT: loading CSVs into staging…"
for tbl in "${!TABLES[@]}"; do
  stg="${tbl}_stg"
  echo "→ loading $tbl → $stg (jobs=$IMPORT_JOBS)"
  for csv in "$EXPORT_DIR/$tbl"/*.csv; do
    echo "   • \COPY $stg FROM '$csv' WITH CSV HEADER"
    # here-doc ensures psql treats \copy as a client meta-command
    psql "$DEV_URL" <<EOF &
$SQL_SETTINGS
\copy $stg FROM '$csv' WITH CSV HEADER
EOF
    # throttle parallel imports
    while [ "$(jobs -r | wc -l)" -ge "$IMPORT_JOBS" ]; do
      wait -n
    done
  done
  wait
  echo " ✅ $stg done"
done

# ─── Atomic swap staging → production ─────────────────────────────────────────
echo "➡️ SWAPPING staging → production…"
psql "$DEV_URL" <<SQL
$SQL_SETTINGS
BEGIN;

ALTER TABLE public.entity_address     RENAME TO entity_address_old;
ALTER TABLE public.mpans               RENAME TO mpans_old;
ALTER TABLE public.mprns               RENAME TO mprns_old;

ALTER TABLE public.entity_address_stg  RENAME TO entity_address;
ALTER TABLE public.mpans_stg           RENAME TO mpans;
ALTER TABLE public.mprns_stg           RENAME TO mprns;

DROP TABLE IF EXISTS mpans_old, mprns_old, entity_address_old;

COMMIT;
SQL

# ─── Rebuild indexes ──────────────────────────────────────────────────────────
echo "➡️ REBUILDING indexes …"
psql "$DEV_URL" <<SQL
$SQL_SETTINGS
CREATE INDEX CONCURRENTLY IF NOT EXISTS address_postcode_idx         ON public.entity_address(postcode);
CREATE INDEX CONCURRENTLY IF NOT EXISTS entity_address_uprn_idx      ON public.entity_address(uprn);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS entity_address_guid_key
                                                                   ON public.entity_address(guid);

CREATE INDEX CONCURRENTLY IF NOT EXISTS mpan_uprn_idx               ON public.mpans(uprn);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS mpans_value_key       ON public.mpans(value);

CREATE INDEX CONCURRENTLY IF NOT EXISTS mprn_uprn_idx               ON public.mprns(uprn);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS mprns_value_key       ON public.mprns(value);
SQL

echo "✅ All done!"
