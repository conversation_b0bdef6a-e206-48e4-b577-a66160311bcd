**Listing and managing indexes in PostgreSQL**

The user wants to list indexes in PostgreSQL, identify abandoned ones, and explore potential ways to salvage them without dropping/recreating. I think the best way to list them in Postgres is via psql metacommands (`\d <table>`, `\di+`), or by querying the `pg_indexes` view and catalog tables. For abandoned indexes, check flags like `indisready` or `indisvalid` being false, or indexes with `relsic` but no entries in `pg_depend`. I should also consider providing doc references, though they’re not strictly necessary.

Thought for 11 seconds

Below is a mini-playbook you can copy-paste in psql whenever you need to inspect the state of **all** indexes, spot unfinished ones, and decide whether they can be salvaged or should simply be rebuilt.

---

## 1  List every index in the database

### a) psql meta-command (quick & human-readable)

```text
\di+                    -- all indexes in the current schema
\di+ *.*                -- every index in every schema
\di+ public.entity_address*  -- just one table’s indexes
```

`+` shows size, tablespace, etc.

### b) System view (machine-friendly)

```sql
SELECT schemaname,
       indexname,
       tablename,
       pg_size_pretty(pg_relation_size(indexrelid)) AS size
FROM   pg_indexes
ORDER  BY schemaname, tablename, indexname;
```

`pg_indexes` is just a convenient wrapper around the catalog tables.

---

## 2  Spot **abandoned / half-built** indexes

```sql
SELECT n.nspname  AS schema,
       c.relname  AS index_name,
       t.relname  AS table_name,
       i.indisready,
       i.indisvalid,
       pg_size_pretty(pg_relation_size(c.oid)) AS size_on_disk
FROM   pg_index        i
JOIN   pg_class        c ON c.oid = i.indexrelid
JOIN   pg_class        t ON t.oid = i.indrelid
JOIN   pg_namespace    n ON n.oid = c.relnamespace
WHERE  NOT i.indisready     -- physical build never finished
    OR NOT i.indisvalid;    -- build finished but was never validated
```

*If the query returns no rows, you have no abandoned indexes.*
Otherwise you’ll get something like:

| schema | index\_name                     | table\_name     | indisready | indisvalid | size\_on\_disk |
| ------ | ------------------------------- | --------------- | ---------- | ---------- | -------------- |
| public | idx\_entity\_address\_uprn      | entity\_address | f          | f          | 780 MB         |
| public | idx\_big\_table\_partial\_build | big\_table      | t          | f          | 1.1 GB         |

---

## 3  What exactly **is** an “abandoned” index?

| Flag(s)                            | Situation                                                                                                                      | How it happens                                        |
| ---------------------------------- | ------------------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------- |
| `indisready = f`, `indisvalid = f` | The first phase of `CREATE INDEX CONCURRENTLY` (heap scan) was **interrupted** before it finished. No usable structure exists. | Session cancelled, network drop, crash, `ROLLBACK`, … |
| `indisready = t`, `indisvalid = f` | The physical index is complete but **was never validated** (second pass didn’t finish).                                        | Same interruptions, but after the heap scan finished. |
| Neither flag is `f`                | The index is complete and the planner can use it.                                                                              |                                                       |

> Plain `CREATE INDEX` (non-concurrent) **cannot** leave an index half-built: it either finishes or rolls the whole transaction back.

---

## 4  Can you “un-abandon” an index?

| Case                               | Is rescue possible?                                       | What to do                                                                                   |
| ---------------------------------- | --------------------------------------------------------- | -------------------------------------------------------------------------------------------- |
| `indisready = f`, `indisvalid = f` | **No** – the on-disk structure is incomplete.             | `DROP INDEX CONCURRENTLY …` and create it again.                                             |
| `indisready = t`, `indisvalid = f` | **Yes** – the data are there, only validation is missing. | `REINDEX INDEX CONCURRENTLY idx_name;`<br>or simply drop & recreate if faster/safer for you. |

`REINDEX INDEX CONCURRENTLY` rewinds through both phases and will flip `indisvalid` to `true` when it finishes.

---

## 5  Handy one-liners

### Drop every truly abandoned index

```sql
DO
$$
DECLARE
    r record;
BEGIN
    FOR r IN
        SELECT c.oid, quote_ident(n.nspname)||'.'||quote_ident(c.relname) AS fqname
        FROM   pg_index i
        JOIN   pg_class c ON c.oid = i.indexrelid
        JOIN   pg_namespace n ON n.oid = c.relnamespace
        WHERE  NOT i.indisready OR NOT i.indisvalid
    LOOP
        RAISE NOTICE 'dropping %', r.fqname;
        EXECUTE format('DROP INDEX IF EXISTS %s;', r.fqname);
    END LOOP;
END;
$$;
```

### Re-index every “ready-but-not-valid” index

```sql
DO
$$
DECLARE
    r record;
BEGIN
    FOR r IN
        SELECT quote_ident(n.nspname)||'.'||quote_ident(c.relname) AS fqname
        FROM   pg_index i
        JOIN   pg_class c ON c.oid = i.indexrelid
        JOIN   pg_namespace n ON n.oid = c.relnamespace
        WHERE  i.indisready AND NOT i.indisvalid
    LOOP
        RAISE NOTICE 'reindexing %', r.fqname;
        EXECUTE format('REINDEX INDEX CONCURRENTLY %s;', r.fqname);
    END LOOP;
END;
$$;
```

---

## 6  Workflow you can keep around

1. **List progress** (optional monitoring window)

   ```sql
   SELECT * FROM pg_stat_progress_create_index;
   ```

2. **Detect abandoned**
   Run the query from section 2.

3. **Decide**

   * `ready = f` & `valid = f` → drop & recreate.
   * `ready = t` & `valid = f` → `REINDEX … CONCURRENTLY`.

4. **Verify**

   ```sql
   SELECT indexrelid::regclass, indisready, indisvalid
   FROM   pg_index
   WHERE  NOT indisvalid;   -- should now return 0 rows
   ```

---

### Recap

* **Abandoned index** = catalog entry whose build never completed (one or both flags `false`).
* Use the catalog flags to **find** them.
* **Re-index** if only validation is missing, otherwise **drop & recreate**.
* Keep the progress view open in another session if you want real-time feedback while the new build runs.

That’s all the tooling you need to keep your Supabase/PostgreSQL indexes tidy and fully usable.
