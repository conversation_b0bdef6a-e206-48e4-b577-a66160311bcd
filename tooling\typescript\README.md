# Typescript Configs

Read the docs on understanding [performance issues](https://github.com/microsoft/TypeScript/wiki/Performance)

Can use the following to output trace resolution issues;

```shell
mkdir -p .tsc-trace-resolution/ && find packages apps tooling -maxdepth 1 -type d -not -path 'packages' -not -path 'apps' -not -path 'tooling' | while read dir; do npx tsc --traceResolution -p "$dir" > ".tsc-trace-resolution/$(basename "$dir").txt"; cat ".tsc-trace-resolution/$(basename "$dir").txt" >> .tsc-trace-resolution/all.txt; done
```

## ShadCN's TSConfig

```jsonc
// Their base.json (ShadCN monorepo)
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Default",
  "compilerOptions": {
    "declaration": true,
    "declarationMap": true,
    "esModuleInterop": true,
    "incremental": false,
    "isolatedModules": true,
    "lib": ["es2022", "DOM", "DOM.Iterable"],
    "module": "NodeNext",
    "moduleDetection": "force",
    "moduleResolution": "NodeNext",
    "noUncheckedIndexedAccess": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "ES2022"
  }
}
```

```jsonc
// Their nextjs-app/tsconfig.json
{
  "extends": "@workspace/typescript-config/nextjs.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts",
    "next.config.mjs",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}
```

## Ours before aligning with ShadCN

```jsonc
// Our base.json before aligning with ShadCN
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Default",
  "compilerOptions": {
    "esModuleInterop": true,
    "incremental": true,
    "isolatedModules": true,
    // Makes sense to include DOM and DOM.Iterable as we are targeting the web.
    "lib": ["ES2022"],
    // Without both module and moduleResolution set to `NodeNext` it will not work properly with ESM (which is the case in our monorepo)
    // Currently requires us to change all our import paths to have "Relative import paths need explicit file extensions"
    "module": "Preserve", // TODO change to NodeNext
    "moduleDetection": "force",
    "moduleResolution": "node", // TODO change to NodeNext
    "noUncheckedIndexedAccess": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "ES2022",

    // Below is stuff we have that they do NOT

    "allowJs": true,
    "disableSourceOfProjectReferenceRedirect": true,
    "tsBuildInfoFile": "${configDir}/.cache/tsbuildinfo.json",

    "checkJs": true,
    // Prevents js and d.ts files from being emitted. Currently I don't see why we would want these.
    "noEmit": true,
    // We need this until we remove the jsx components out of @watt/common namely (icons)
    "jsx": "react-jsx",

    // Below is stuff they have that we do NOT
    // "declaration": true,
    // "declarationMap": true,
  },
  // Pointless to include as it is always overwritten by the tsconfig.json and is not extended.
  "exclude": ["node_modules", "build", "dist", ".next", ".expo"]
}
```

```jsonc
// Our nextjs.json before aligning with ShadCN
{
  "extends": "@watt/typescript/nextjs.json",
  "compilerOptions": {
    "lib": ["ES2022", "dom", "dom.iterable"],
    "jsx": "preserve",
    "baseUrl": ".",
    "paths": {
      "@watt/crm/*": ["./src/*"]
    },
    "plugins": [{ "name": "next" }],
    "module": "esnext",
    "moduleResolution": "bundler"
  },
  "include": [
    "global.d.ts",
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/build/types/**/*.ts",
    ".next/dev/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    ".next",
    ".turbo",
    ".cache",
    "public",
    "next.config.js"
  ]
}
```

<https://www.typescriptlang.org/tsconfig/#disableReferencedProjectLoad>

| Option                         | Ours         | ShadCN                            | Why "better"?                                                                                         |
| ------------------------------ | ------------ | --------------------------------- | ----------------------------------------------------------------------------------------------------- |
| **`declaration`**              | ––           | `true`                            | Emits `.d.ts` files so downstream consumers (and editors) get correct types.                          |
| **`declarationMap`**           | ––           | `true`                            | Generates maps for your declaration files, making debugging into `.ts` sources possible.              |
| **`incremental`**              | `true`       | `false`                           | Disables incremental builds so each CI/packaging run is a clean compile (no stale cache).             |
| **`lib`**                      | `["ES2022"]` | `["es2022","DOM","DOM.Iterable"]` | Adds browser-DOM typings. If your package targets web environments, you need `DOM`, `DOM.Iterable`.   |
| **`module`**                   | `"Preserve"` | `"NodeNext"`                      | Targets Node's "ESM with .js/.mjs/.cts/.mts" resolution rules. Better alignment with modern Node ESM. |
| **`moduleResolution`**         | `"node"`     | `"NodeNext"`                      | Matches the `module` strategy; enables correct extension-based lookups for Node's ESM loader.         |
| **`noUncheckedIndexedAccess`** | `false`      | `true`                            | Forces `foo[maybeKey]` to be typed as `T \| undefined`, catching missing-key bugs at compile time.    |

## Debugging

```shell
TRACE=.ts-trace
rm -rf "$TRACE"
npx tsc --noEmit --generateTrace "$TRACE" -p tsconfig.json
```
